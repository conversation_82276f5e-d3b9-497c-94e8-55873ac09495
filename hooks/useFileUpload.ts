"use client";
import { useCallback } from "react";
import { toast } from "sonner";

export function useFileUpload() {
  const handleFileUpload = useCallback(
    (
      file: File | null,
      type: "cv" | "jd",
      onSetFile: (file: File | null) => void
    ) => {
      if (file === null) {
        onSetFile(null);
      } else if (file.type === "application/pdf") {
        onSetFile(file);
        toast.success(`${file.name} uploaded successfully!`);
      } else {
        toast.error("Please upload only PDF files");
      }
    },
    []
  );

  const handleDragOver = useCallback((e: React.DragEvent, type: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
  }, []);

  const handleDrop = useCallback(
    (
      e: React.DragEvent,
      type: "cv" | "jd",
      onFileUpload: (file: File, type: "cv" | "jd") => void
    ) => {
      e.preventDefault();
      const files = Array.from(e.dataTransfer.files);

      if (files.length === 0) return;

      const file = files[0];

      if (file.type !== "application/pdf") {
        toast.error("Please upload only PDF files");
        return;
      }

      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        toast.error("File size should be less than 10MB");
        return;
      }

      onFileUpload(file, type);
    },
    []
  );

  const fileToBase64 = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  }, []);

  return {
    handleFileUpload,
    handleDragOver,
    handleDrop,
    fileToBase64,
  };
}
