import { useEffect, useRef, useState } from "react";

export const useCamera = () => {
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [videoKey, setVideoKey] = useState(0); // Key để force re-render video element
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const initCamera = async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: false,
      });
      streamRef.current = mediaStream;

      // Force re-render video element bằng cách thay đổi key
      setVideoKey((prev) => prev + 1);

      // Đợi một chút để video element được re-render
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
          videoRef.current.play().catch(console.error);
        }
      }, 100);

      setIsCameraOn(true);
    } catch (error) {
      console.error("Không thể truy cập camera:", error);
      setIsCameraOn(false);
    } finally {
      setIsLoading(false);
    }
  };

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setIsCameraOn(false);
  };

  const toggleCamera = async () => {
    if (isLoading) return;

    if (isCameraOn) {
      stopCamera();
    } else {
      await initCamera();
    }
  };

  useEffect(() => {
    initCamera();

    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    isCameraOn,
    videoRef,
    toggleCamera,
    isLoading,
    videoKey, // Export videoKey để component có thể sử dụng
  };
};
