import Vapi from "@vapi-ai/web";
import { useEffect, useRef, useState } from "react";
import { CreateAssistantDTO } from "@vapi-ai/web/dist/api";
import { InterviewQuestionSessionType } from "@/common/types/interview-question";

export const useVapi = () => {
  const [assistantSpeech, setAssistantSpeech] = useState<string>("");
  const [userSpeech, setUserSpeech] = useState<string>("");
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [conversationLog, setConversationLog] = useState<
    Array<{
      timestamp: string;
      role: "assistant" | "user";
      message: string;
      isComplete: boolean;
    }>
  >([]);
  const [converstationLatest, setConverstationLatest] = useState<
    Array<{
      timestamp: string;
      role: "assistant" | "user" | "system";
      message: string;
    }>
  >([]);

  // Use useRef to create VAPI instance only once
  const vapiRef = useRef<Vapi | null>(null);
  const currentSpeakerRef = useRef<"assistant" | "user" | null>(null);

  // Initialize VAPI instance once

  useEffect(() => {
    if (!vapiRef.current) {
      vapiRef.current = new Vapi(process.env.NEXT_PUBLIC_VAPI_KEY || "");
    }

    const vapi = vapiRef.current;

    // Add comprehensive event listeners
    const handleCallStart = () => {
      console.log("🟢 VAPI Call Started");
      setIsCallActive(true);
      setConversationLog([]);
      currentSpeakerRef.current = null;
    };

    const handleCallEnd = () => {
      console.log("🔴 VAPI Call Ended");
      setIsCallActive(false);
      setIsMuted(false);
    };

    const handleTranscript = (transcript: any) => {
      console.log("📝 Transcript received:", transcript);

      const message = transcript.transcript || transcript.text || "";
      const role = transcript.role as "assistant" | "user";
      const isPartial = transcript.type === "transcript-partial";
      const isFinal = transcript.type === "transcript";
      const conversationLatest = transcript?.conversation;

      if (conversationLatest) {
        setConverstationLatest(conversationLatest);
      }

      if (!message || message.trim().length < 1) {
        return;
      }

      const timestamp = new Date().toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      });

      console.log(
        `📝 ${role} ${isPartial ? "partial" : "final"}: "${message}"`
      );

      // Update real-time speech indicators
      if (role === "assistant") {
        setAssistantSpeech(message);
      } else {
        setUserSpeech(message);
      }

      setConversationLog((prev) => {
        const newLog = [...prev];

        // Check if we need to create a new message or update existing one
        const lastMessage = newLog[newLog.length - 1];
        const isSameSpeaker =
          lastMessage && lastMessage.role === role && !lastMessage.isComplete;

        if (isSameSpeaker) {
          // Update existing message (real-time typing effect)
          newLog[newLog.length - 1] = {
            ...lastMessage,
            message: message.trim(),
            timestamp: isFinal ? timestamp : lastMessage.timestamp, // Only update timestamp when final
            isComplete: isFinal,
          };
          console.log(
            "🔄 Updated existing message:",
            newLog[newLog.length - 1]
          );
        } else {
          // Create new message (new speaker or previous message was complete)
          const newMessage = {
            timestamp,
            role,
            message: message.trim(),
            isComplete: isFinal,
          };
          newLog.push(newMessage);
          console.log("✨ Created new message:", newMessage);
        }

        currentSpeakerRef.current = role;
        return newLog;
      });
    };

    const handleError = (error: any) => {
      console.error("❌ VAPI Error:", error);
      setIsCallActive(false);
    };

    // Add all event listeners
    vapi.on("call-start", handleCallStart);
    vapi.on("call-end", handleCallEnd);
    vapi.on("message", handleTranscript); // Use "message" instead of "transcript"
    vapi.on("error", handleError);

    // Cleanup
    return () => {
      vapi.off("call-start", handleCallStart);
      vapi.off("call-end", handleCallEnd);
      vapi.off("message", handleTranscript); // Use "message" instead of "transcript"
      vapi.off("error", handleError);
    };
  }, []);

  const createAssistantOptions = (
    interviewQuestions: InterviewQuestionSessionType,
    userName: string
  ) => {
    let questionList = "";
    interviewQuestions.interviewQuestions.map((item) => {
      questionList = item.question + "," + questionList;
    });

    console.log("💬 Question List:", questionList);
    let language = "";
    language = "en";

    return {
      name: "Learn Vox AI",
      firstMessage:
        language === "vi"
          ? `Chào ${userName}! Bạn đã sẵn sàng cho buổi phỏng vấn vị trí ${interviewQuestions.jobPosition} chưa?`
          : `Hi ${userName}, how are you? Ready for your interview for ${interviewQuestions.jobPosition}?`,

      transcriber: {
        provider: "google",
        model: "gemini-2.0-flash",
        language: language === "vi" ? "Vietnamese" : "English",
      },

      voice: {
        provider: language === "vi" ? "azure" : "playht",
        voiceId: language === "vi" ? "hoaimy" : "jennifer",
      },

      model: {
        provider: "openai",
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `
              You are an AI voice assistant conducting a technical mock interview for the position of ${
                interviewQuestions.jobPosition
              }. Your job is to ask **only** the interview questions provided, in the **exact order given**, one at a time.
                        
              Questions:
              ${questionList}
                        
              Language: Speak and listen in ${
                language === "vi" ? "Vietnamese" : "English"
              } only.
                        
              Instructions:
              - Start with a short and friendly greeting. Mention that this is a mock interview for the role of ${
                interviewQuestions.jobPosition
              }.
              - Ask one question at a time, following the exact order above.
              - **After asking a question, wait patiently for the candidate's response. Give them time to think and answer.**
              - **Wait at least 8 seconds before prompting again if there's no response.**
              - **If the candidate is silent for 8+ seconds, you can gently encourage them with phrases like "Take your time" or "Think about it carefully".**
              - **Do not ask any questions that are not on the list.**
              - If the candidate seems stuck or confused, gently offer a hint or rephrase **only that specific question** without changing its original meaning.
              - After the answer, give short and supportive feedback (e.g., "Good job", "That's a valid point", etc.).
              - Keep responses concise, friendly, and professional.
              - **Speak slowly and clearly to ensure the candidate can understand.**
              - **Pause naturally between sentences to give a conversational flow.**
                        
              Rules:
              - **Do not invent or add new questions.**
              - **Do not interrupt the candidate while they are speaking or thinking.**
              - **Give candidates adequate time to think and respond (minimum 8 seconds).**
              - **Be patient and encouraging, especially if candidates need time to think.**
              - **Speak only when it's your turn (e.g., greeting, question, feedback).**
              - Speak **slowly and clearly**.
              - Use **a slower, calm, and steady pace** to improve comprehension.
              - Emphasize key words and **pause between sentences**.
              - **Respond strictly in ${
                language === "vi" ? "Vietnamese" : "English"
              } only.**
              - **Speak at a moderate, conversational pace - not too fast.**
              - **Use natural pauses and inflections to sound more human-like.**
                        
              Encouraging phrases for silence:
              ${
                language === "vi"
                  ? '- "Bạn có thể suy nghĩ thêm một chút"\n- "Không vội, hãy cứ từ từ"\n- "Bạn có muốn mình nhắc lại câu hỏi không?"'
                  : '- "Take your time to think about it"\n- "No rush, think it through"\n- "Would you like me to repeat the question?"'
              }
                        
              End the interview by summarizing the session positively and thanking the candidate.
              Closing example:
              "${
                language === "vi"
                  ? "Cảm ơn bạn đã tham gia buổi phỏng vấn thử. Chúc bạn học tốt và thành công trong sự nghiệp!"
                  : "Thank you for participating in this mock interview. Keep learning and best of luck in your career!"
              }"
            `.trim(),
          },
        ],
      },
    };
  };

  const startCall = async (
    interviewQuestions: InterviewQuestionSessionType,
    userName: string
  ) => {
    if (!interviewQuestions || !vapiRef.current) return;

    try {
      console.log("🚀 Starting VAPI call...");
      const assistantOptions = createAssistantOptions(
        interviewQuestions,
        userName
      );
      await vapiRef.current.start(assistantOptions as CreateAssistantDTO);
    } catch (error) {
      console.error("❌ Error starting call:", error);
      setIsCallActive(false);
    }
  };

  const endCall = async () => {
    if (!vapiRef.current) return;

    try {
      console.log("🛑 Attempting to end call...");
      vapiRef.current.stop();
      setIsCallActive(false);
    } catch (error) {
      console.error("❌ Error ending call:", error);
      setIsCallActive(false);
    }
  };

  const toggleMute = () => {
    if (!vapiRef.current) return;

    try {
      if (isMuted) {
        console.log("🔊 Unmuting...");
        vapiRef.current.setMuted(false);
        setIsMuted(false);
      } else {
        console.log("🔇 Muting...");
        vapiRef.current.setMuted(true);
        setIsMuted(true);
      }
    } catch (error) {
      console.error("❌ Error toggling mute:", error);
    }
  };

  return {
    assistantSpeech,
    userSpeech,
    isCallActive,
    isMuted,
    conversationLog,
    startCall,
    endCall,
    toggleMute,
    setAssistantSpeech,
    setUserSpeech,
    converstationLatest,
  };
};
