"use client";

import { motion } from "framer-motion";
import { TrendingUp } from "lucide-react";
import { getScoreColor } from "@/services/data/analystStyle";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";

interface OverallScoreCardProps {
  score: number;
}

const OverallScoreCard = ({ score }: OverallScoreCardProps) => {
  return (
    <motion.div
      variants={cardHoverVariants}
      whileHover="hover"
      className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <TrendingUp className="w-6 h-6 text-green-500" />
        <h3 className="text-lg font-bold text-slate-800">Overall Score</h3>
      </div>
      <div className="text-center space-y-4">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.5, type: "spring" }}
          className={`w-24 h-24 mx-auto rounded-full bg-gradient-to-br ${getScoreColor(
            score
          )} flex items-center justify-center`}
        >
          <span className="text-2xl font-bold text-white">{score}%</span>
        </motion.div>
        <motion.div
          className="w-full bg-gray-200 rounded-full h-3"
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ delay: 0.7, duration: 0.8 }}
        >
          <motion.div
            className={`h-3 rounded-full bg-gradient-to-r ${getScoreColor(
              score
            )}`}
            initial={{ width: 0 }}
            animate={{ width: `${score}%` }}
            transition={{ delay: 1, duration: 1.2 }}
          />
        </motion.div>
      </div>
    </motion.div>
  );
};

export default OverallScoreCard;
