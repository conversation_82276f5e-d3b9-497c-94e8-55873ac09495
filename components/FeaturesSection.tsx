"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>3, Sparkles } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

interface FeaturesSectionProps {
  variants?: any;
}

const FeaturesSection = ({ variants }: FeaturesSectionProps) => {
  const features = [
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description:
        "Advanced machine learning algorithms analyze your resume content, structure, and formatting to provide comprehensive feedback.",
      gradient: "from-blue-50 to-cyan-50",
      borderColor: "border-blue-200",
      iconBg: "bg-blue-100 group-hover:bg-blue-200",
      iconColor: "text-blue-600",
    },
    {
      icon: BarChart3,
      title: "Detailed Scoring",
      description:
        "Get precise scores across multiple categories including content quality, formatting, keywords, and ATS compatibility.",
      gradient: "from-green-50 to-emerald-50",
      borderColor: "border-green-200",
      iconBg: "bg-green-100 group-hover:bg-green-200",
      iconColor: "text-green-600",
    },
    {
      icon: Sparkles,
      title: "Smart Recommendations",
      description:
        "Receive personalized suggestions for improvements, keyword optimization, and industry-specific enhancements.",
      gradient: "from-purple-50 to-violet-50",
      borderColor: "border-purple-200",
      iconBg: "bg-purple-100 group-hover:bg-purple-200",
      iconColor: "text-purple-600",
    },
  ];

  return (
    <motion.section className="mt-16" variants={variants}>
      <div className="text-center mb-12">
        <motion.h3
          className="text-3xl font-bold text-gray-900 mb-4"
          initial={{ y: 20, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          Powerful Resume Analysis Features
        </motion.h3>
        <motion.p
          className="text-gray-600 text-lg max-w-2xl mx-auto"
          initial={{ y: 20, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          viewport={{ once: true }}
        >
          Discover advanced tools designed to help you create the perfect resume
        </motion.p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 * (index + 1), duration: 0.6 }}
            viewport={{ once: true }}
            whileHover={{ scale: 1.05 }}
            className="group"
          >
            <Card
              className={`h-full bg-gradient-to-br ${feature.gradient} ${feature.borderColor} hover:shadow-xl transition-all duration-300`}
            >
              <CardContent className="p-8 text-center">
                <div
                  className={`w-16 h-16 ${feature.iconBg} rounded-2xl flex items-center justify-center mx-auto mb-6 transition-colors`}
                >
                  <feature.icon className={`w-8 h-8 ${feature.iconColor}`} />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </motion.section>
  );
};

export default FeaturesSection;
