"use client";

import React from "react";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

export interface SearchComponentProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  searchValue?: string;
  className?: string;
}

export default function SearchComponent({
  placeholder = "Search...",
  onSearch,
  searchValue = "",
  className = "",
}: SearchComponentProps) {
  const handleClear = () => {
    onSearch("");
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`relative ${className}`}
    >
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
      <Input
        type="text"
        placeholder={placeholder}
        value={searchValue}
        onChange={(e) => onSearch(e.target.value)}
        className="pl-10 pr-10 py-2 border-slate-200 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
      />
      {searchValue && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100"
          onClick={handleClear}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </motion.div>
  );
}
