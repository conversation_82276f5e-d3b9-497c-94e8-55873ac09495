"use client";
import { Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { FileText } from "lucide-react";
import { motion } from "framer-motion";

export function AnalysisModalHeader() {
  return (
    <DialogHeader className="text-center pb-6">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-cyan-500 to-teal-500 flex items-center justify-center mx-auto mb-4"
      >
        <FileText className="w-8 h-8 text-white" />
      </motion.div>
      <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-cyan-600 to-teal-600 bg-clip-text text-transparent">
        Upload Resume for Analysis
      </DialogTitle>
      <p className="text-slate-600 font-medium">
        Get AI-powered insights on your resume with detailed recommendations
      </p>
    </DialogHeader>
  );
}
