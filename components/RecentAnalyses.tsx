"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>2, <PERSON>ert<PERSON>ir<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { motion } from "framer-motion";
import { getCandidateName, getPositionApplied } from "@/lib/cvParser";
import {
  Analyst,
  RecentAnalysesProps,
} from "@/common/types/recentHistoryAnalyst";
import { getScoreColors } from "@/services/data/analystStyle";

const RecentAnalyses = ({
  analysts,
  displayedAnalysts,
  isLoading,
  error,
  showAllHistory,
  onShowAllHistory,
  onViewReport,
  onRefetch,
}: RecentAnalysesProps) => {
  const getAnalysisType = (analyst: Analyst): "role" | "jd" => {
    if (analyst.analysisType) {
      return analyst.analysisType;
    }

    if (analyst.agentType === "AI_RESUME_ANALYSIS_JD") {
      return "jd";
    }
    return "role";
  };

  const getAnalysisTypeLabel = (type: "role" | "jd") => {
    return type === "jd" ? "JD Analysis" : "Role Analysis";
  };

  const getAnalysisTypeBadge = (type: "role" | "jd") => {
    return type === "jd"
      ? "bg-purple-100 text-purple-800"
      : "bg-blue-100 text-blue-800";
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading analyses...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center py-8 text-red-600">
          <AlertCircle className="h-6 w-6 mr-2" />
          <span>Error loading analyses</span>
          <Button
            variant="outline"
            size="sm"
            className="ml-4"
            onClick={onRefetch}
          >
            Retry
          </Button>
        </div>
      );
    }

    if (displayedAnalysts.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No analyses found. Upload your first resume to get started!</p>
        </div>
      );
    }

    return (
      <>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Candidate Name</TableHead>
              <TableHead>Position Applied</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Score</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayedAnalysts.map((analyst, index) => {
              const analysisType = getAnalysisType(analyst);
              return (
                <motion.tr
                  key={analyst._id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{
                    delay: 0.1 * index,
                    duration: 0.3,
                  }}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onViewReport(analyst._id, analysisType)}
                >
                  <TableCell className="font-medium">
                    {getCandidateName(analyst.cvText)}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {getPositionApplied(analyst.cvText)}
                  </TableCell>
                  <TableCell>
                    <Badge className={getAnalysisTypeBadge(analysisType)}>
                      {getAnalysisTypeLabel(analysisType)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      className={getScoreColors(analyst.content.overall_score)}
                    >
                      {analyst.content.overall_score}/100
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewReport(analyst._id, analysisType);
                      }}
                    >
                      View Report
                    </Button>
                  </TableCell>
                </motion.tr>
              );
            })}
          </TableBody>
        </Table>

        {analysts.length > 4 && (
          <div className="mt-4">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => onShowAllHistory(!showAllHistory)}
            >
              {showAllHistory
                ? "Show Less"
                : `See All History (${analysts.length - 4} more)`}
            </Button>
          </div>
        )}
      </>
    );
  };

  return (
    <motion.div whileHover={{ y: -2 }} transition={{ duration: 0.2 }}>
      <Card>
        <CardHeader>
          <CardTitle>Recent Analyses</CardTitle>
          <CardDescription>Your latest resume analysis results</CardDescription>
        </CardHeader>
        <CardContent>{renderContent()}</CardContent>
      </Card>
    </motion.div>
  );
};

export default RecentAnalyses;
