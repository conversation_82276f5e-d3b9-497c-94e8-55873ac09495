"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { motion } from "framer-motion";
import { AnalysisModalActionsProps } from "@/features/analyst/analyst";

export function AnalysisModalActions({
  isAnalyzing,
  isFormValid,
  onAnalyze,
  onClose,
}: Readonly<AnalysisModalActionsProps>) {
  return (
    <div className="flex space-x-3 pt-6 mt-8 border-t border-slate-200">
      <Button
        variant="outline"
        onClick={onClose}
        className="flex-1 h-12 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200"
        disabled={isAnalyzing}
      >
        Cancel
      </Button>
      <Button
        onClick={onAnalyze}
        disabled={!isFormValid || isAnalyzing}
        className="flex-1 h-12 rounded-xl bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-600 hover:via-cyan-600 hover:to-teal-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
      >
        {isAnalyzing ? (
          <>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full"
            />
            Analyzing...
          </>
        ) : (
          <>
            <Upload className="w-4 h-4 mr-2" />
            Upload & Analyze
          </>
        )}
      </Button>
    </div>
  );
}
