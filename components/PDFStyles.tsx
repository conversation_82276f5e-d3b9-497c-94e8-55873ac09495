// components/PDFStyles.tsx
import React from "react";

const PDFStyles = () => {
  // Inject global styles
  React.useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      .pdf-container {
        position: relative;
        overflow: hidden;
      }

      .pdf-container iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: transparent;
        pointer-events: auto;
      }

      /* Hide PDF toolbar/header completely */
      .pdf-container iframe::-webkit-scrollbar {
        display: none;
      }

      .pdf-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 40px;
        background: white;
        z-index: 10;
        pointer-events: none;
      }

      /* Custom scrollbar styles */
      .pdf-scroll-container {
        position: relative;
        overflow-y: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
      }

      .pdf-scroll-container::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      .pdf-scroll-container:hover::-webkit-scrollbar {
        width: 8px;
      }

      .pdf-scroll-container:hover::-webkit-scrollbar-track {
        background: transparent;
      }

      .pdf-scroll-container:hover::-webkit-scrollbar-thumb {
        background: rgba(148, 163, 184, 0.5);
        border-radius: 4px;
        border: none;
      }

      .pdf-scroll-container:hover::-webkit-scrollbar-thumb:hover {
        background: rgba(148, 163, 184, 0.7);
      }

      /* Hide scrollbar arrows/buttons */
      .pdf-scroll-container::-webkit-scrollbar-button {
        display: none;
      }

      .pdf-scroll-container:hover::-webkit-scrollbar-button {
        display: none;
      }

      /* Alternative approach - overlay container */
      .pdf-overlay-container {
        position: relative;
        overflow: hidden;
      }

      .pdf-overlay-scroll {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }

      .pdf-overlay-scroll::-webkit-scrollbar {
        width: 0px;
        background: transparent;
      }

      .pdf-overlay-container:hover .pdf-overlay-scroll::-webkit-scrollbar {
        width: 8px;
      }

      .pdf-overlay-container:hover
        .pdf-overlay-scroll::-webkit-scrollbar-track {
        background: transparent;
      }

      .pdf-overlay-container:hover
        .pdf-overlay-scroll::-webkit-scrollbar-thumb {
        background: rgba(148, 163, 184, 0.5);
        border-radius: 4px;
      }

      .pdf-overlay-container:hover
        .pdf-overlay-scroll::-webkit-scrollbar-thumb:hover {
        background: rgba(148, 163, 184, 0.7);
      }

      .pdf-overlay-scroll::-webkit-scrollbar-button {
        display: none;
      }
    `;
    document.head.appendChild(style);

    // Cleanup function
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  return null;
};

export default PDFStyles;
