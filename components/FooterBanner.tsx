"use client";
import { motion } from "framer-motion";
import { MessageSquare, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export default function FooterBanner() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.8 }}
      className="mt-12 p-8 bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 rounded-2xl text-white text-center"
    >
      <div className="max-w-2xl mx-auto space-y-4">
        <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mx-auto">
          <MessageSquare className="w-8 h-8" />
        </div>
        <h3 className="text-2xl font-bold">Ready for the Next Step?</h3>
        <p className="text-blue-100 text-lg">
          Your resume analysis is complete! Take your application to the next
          level with our AI-powered interview preparation.
        </p>
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button
            size="lg"
            className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <CheckCircle className="w-5 h-5 mr-2" />
            Start Interview Preparation
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
}
