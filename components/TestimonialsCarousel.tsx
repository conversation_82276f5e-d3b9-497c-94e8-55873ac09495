"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { Star, ChevronLeft, ChevronRight } from "lucide-react";
import thang from "@/assets/images/thang.png";
import nhan from "@/assets/images/nhan.jpg";
import toan from "@/assets/images/toan.jpg";
import khang from "@/assets/images/khang.png";
import khai from "@/assets/images/khai.jpg";
import tien from "@/assets/images/tien.jpg";
import Image from "next/image";

const FALLBACK_AVATAR = "https://via.placeholder.com/300x300?text=User";

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Software Engineer",
    company: "Tech Solutions Inc",
    feedback:
      "The AI career guidance helped me transition into tech. The personalized roadmap was invaluable for my success.",
    avatar: thang,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Product Manager",
    company: "Innovation Labs",
    feedback:
      "The resume analyzer gave me insights that helped me land my dream job. Highly recommended for career advancement!",
    avatar: tien,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Marketing Director",
    company: "Global Brands",
    feedback:
      "The AI-generated cover letters saved me hours while maintaining a personal touch. Game-changing platform!",
    avatar: khang,
  },
  {
    name: "To Thien Nhan",
    role: "Data Scientist",
    company: "AI Innovations Corp",
    feedback:
      "LearnVoxAI's career roadmap feature guided me through my data science transition perfectly. Exceptional tool!",
    avatar: nhan,
  },
  {
    name: "Nguyen Ngoc Toan",
    role: "UX Designer",
    company: "Creative Studios",
    feedback:
      "The personalized feedback and AI insights transformed my approach to job hunting. Absolutely incredible results!",
    avatar: toan,
  },
  {
    name: "Nguyen Minh Khai",
    role: "DevOps Engineer",
    company: "Cloud Systems",
    feedback:
      "The career transition guidance was spot-on. I successfully moved from traditional IT to cloud engineering!",
    avatar: khai,
  },
];

const TestimonialsCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const totalImages = testimonials.length;

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % totalImages);
  };

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev - 1 + totalImages) % totalImages);
  };

  return (
    <motion.div
      className="relative w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pb-12 sm:pb-16"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
    >
      <motion.h2
        className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-4 sm:mb-6 text-center"
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        Success Stories
      </motion.h2>

      <motion.p
        className="text-gray-300 mb-8 sm:mb-10 lg:mb-12 text-base sm:text-lg lg:text-xl text-center max-w-2xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
        viewport={{ once: true }}
      >
        See how CareerAI has helped professionals succeed
      </motion.p>

      {/* Navigation Buttons */}
      <button
        onClick={handlePrev}
        className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 active:bg-white/30 w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full text-white transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white/50 flex items-center justify-center"
      >
        <ChevronLeft size={20} className="sm:w-6 sm:h-6 lg:w-7 lg:h-7" />
      </button>
      <button
        onClick={handleNext}
        className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 z-10 bg-white/10 hover:bg-white/20 active:bg-white/30 w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full text-white transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white/50 flex items-center justify-center"
      >
        <ChevronRight size={20} className="sm:w-6 sm:h-6 lg:w-7 lg:h-7" />
      </button>

      {/* Testimonial Carousel - Simple Responsive Design */}
      <div className="relative w-full min-h-[400px] sm:min-h-[450px] flex flex-col justify-center">
        {/* Current Testimonial */}
        <motion.div
          key={currentIndex}
          className="w-full"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          <div className="max-w-4xl mx-auto">
            <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-600/50 rounded-2xl sm:rounded-3xl p-6 sm:p-8 hover:bg-slate-800/70 transition-all duration-300 hover:border-purple-500/50">
              <div className="flex flex-col lg:flex-row items-center gap-6 lg:gap-8">
                {/* Avatar */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 sm:w-32 sm:h-32 lg:w-40 lg:h-40 rounded-full overflow-hidden border-4 border-purple-500/30">
                    <Image
                      src={testimonials[currentIndex].avatar.src}
                      alt={testimonials[currentIndex].name}
                      width={160}
                      height={160}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = FALLBACK_AVATAR;
                      }}
                    />
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 text-center lg:text-left">
                  <div className="mb-4">
                    <h3 className="font-bold text-xl sm:text-2xl text-white mb-2">
                      {testimonials[currentIndex].name}
                    </h3>
                    <p className="text-cyan-400 font-semibold text-sm sm:text-base">
                      {testimonials[currentIndex].role}
                    </p>
                    <p className="text-gray-400 text-sm">
                      {testimonials[currentIndex].company}
                    </p>
                  </div>

                  <blockquote className="text-gray-300 text-base sm:text-lg leading-relaxed mb-4">
                    &quot;{testimonials[currentIndex].feedback}&quot;
                  </blockquote>

                  <div className="flex justify-center lg:justify-start space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-5 h-5 text-yellow-400 fill-current"
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Dots Indicator */}
        <div className="flex justify-center items-center space-x-3 mt-8 sm:mt-10 py-4">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 sm:w-4 sm:h-4 rounded-full transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-purple-500/50 ${
                index === currentIndex
                  ? "bg-gradient-to-r from-cyan-400 to-purple-500 scale-125 shadow-lg shadow-purple-500/50"
                  : "bg-gray-500 hover:bg-gray-400 border border-gray-400"
              }`}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default TestimonialsCarousel;
