"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Bell, Globe, KeyRound, LogOut, User } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useAuth } from "@/features/dashboard/hooks/useAuth";
import { useState } from "react";
import { useGetProfileQuery } from "@/services/api/profile";
import ChangePasswordModal from "@/features/auth/components/ChangePasswordModal";
import Link from "next/link";
import { MobileSidebar } from "./DashboardSidebar";

export function Header() {
  const [showChangePassword, setShowChangePassword] = useState(false);
  const { user, handleLogout } = useAuth();
  const { data: profileData } = useGetProfileQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const handleChangePassword = () => {
    setShowChangePassword(true);
  };
  const handleChangePasswordSuccess = () => {
    console.log("Password changed successfully");
    setShowChangePassword(false);
    handleLogout();
  };

  return (
    <>
      <header className="sticky top-0 z-50 flex h-16 w-full items-center justify-between border-b bg-background px-6">
        <div className="flex items-center gap-4 lg:gap-6">
          <SidebarTrigger className="-ml-1" />
          <MobileSidebar />
          <h1 className="text-xl font-semibold">Dashboard</h1>
        </div>

        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Bell className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Globe className="h-5 w-5" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  {profileData?.avatar && profileData.avatar.trim() !== "" ? (
                    <AvatarImage src={profileData.avatar} alt="Profile" />
                  ) : (
                    <AvatarFallback>
                      {user?.firstName?.charAt(0).toUpperCase() || "?"}
                      {user?.lastName?.charAt(0).toUpperCase() || "?"}
                    </AvatarFallback>
                  )}
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {profileData?.firstName || user?.firstName} {profileData?.lastName || user?.lastName}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    ({profileData?.email || user?.email})
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="flex items-center gap-3 cursor-pointer py-2 text-base">
                <User className="w-5 h-5" />
                <span>
                  <Link href="/profile">Profile </Link>
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex items-center gap-3 cursor-pointer py-2 text-base"
                onClick={handleChangePassword}
              >
                <KeyRound className="w-5 h-5" />
                <span>Change Password</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="flex items-center gap-3 text-red-600 cursor-pointer py-2 text-base"
                onClick={handleLogout}
              >
                <LogOut className="w-5 h-5" />
                <span>Logout</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>
      <ChangePasswordModal
        open={showChangePassword}
        onOpenChange={setShowChangePassword}
        onSuccess={handleChangePasswordSuccess}
      />
    </>
  );
}
