import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TableSkeletonProps {
  columns: number;
  rows?: number;
  showActions?: boolean;
  title?: string;
  titleClassName?: string;
  className?: string;
  tableClassName?: string;
}

export default function TableSkeleton({
  columns,
  rows = 5,
  showActions = false,
  title,
  titleClassName = "",
  className = "",
  tableClassName = "",
}: TableSkeletonProps) {
  const totalColumns = showActions ? columns + 1 : columns;

  return (
    <div className={`space-y-4 ${className}`}>
      {title && (
        <div className={`${titleClassName}`}>
          <div className="h-8 w-48 bg-slate-200 rounded animate-pulse"></div>
        </div>
      )}

      <div className="rounded-md border">
        <Table className={tableClassName}>
          <TableHeader>
            <TableRow className="bg-slate-100">
              {Array.from({ length: totalColumns }).map((_, index) => (
                <TableHead
                  key={index}
                  className="px-4 py-3 text-xs uppercase text-slate-700 font-medium"
                >
                  <div className="h-4 bg-slate-200 rounded animate-pulse w-20"></div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: rows }).map((_, rowIndex) => (
              <TableRow key={rowIndex} className="hover:bg-slate-50">
                {Array.from({ length: columns }).map((_, colIndex) => (
                  <TableCell key={colIndex} className="px-4 py-3">
                    <div className="h-4 bg-slate-200 rounded animate-pulse w-full max-w-[120px]"></div>
                  </TableCell>
                ))}
                {showActions && (
                  <TableCell className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-16 bg-slate-200 rounded animate-pulse"></div>
                      <div className="h-8 w-16 bg-slate-200 rounded animate-pulse"></div>
                    </div>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination skeleton */}
      <div className="flex justify-center mt-4">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-20 bg-slate-200 rounded animate-pulse"></div>
          <div className="h-8 w-8 bg-slate-200 rounded animate-pulse"></div>
          <div className="h-8 w-8 bg-slate-200 rounded animate-pulse"></div>
          <div className="h-8 w-8 bg-slate-200 rounded animate-pulse"></div>
          <div className="h-8 w-20 bg-slate-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
