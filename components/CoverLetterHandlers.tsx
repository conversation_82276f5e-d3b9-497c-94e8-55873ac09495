import {
  CoverLetterState,
  CoverLetterActions,
} from "../common/types/cover-letter";

export const useCoverLetterHandlers = (
  state: CoverLetterState,
  actions: CoverLetterActions
) => {
  const handleInputChange = (field: string, value: string) => {
    actions.setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleThemeChange = (theme: string) => {
    const themes: Record<
      string,
      { header: string; contact: string; panel: string; text: string }
    > = {
      blue: {
        header: "#E8F4FD",
        contact: "#E8F4FD",
        panel: "#74b9ff",
        text: "#74b9ff",
      },
      green: {
        header: "#F0F8E8",
        contact: "#F0F8E8",
        panel: "#00b894",
        text: "#00b894",
      },
      yellow: {
        header: "#FFF8E8",
        contact: "#FFF8E8",
        panel: "#fdcb6e",
        text: "#fdcb6e",
      },
    };

    const selectedTheme = themes[theme] || {
      header: "#F4E8F8",
      contact: "#F4E8F8",
      panel: "#74b9ff",
      text: "#74b9ff",
    };

    actions.setHeaderColor(selectedTheme.header);
    actions.setContactColor(selectedTheme.contact);
    if (actions.setPanelColor) actions.setPanelColor(selectedTheme.panel);
    if (actions.setTextColor) actions.setTextColor(selectedTheme.text);
  };

  const handleFontSizeChange = (size: string) => {
    actions.setFontSize(size);
  };

  const handleFontFamilyChange = (family: string) => {
    actions.setFontFamily(family);
  };

  const handleLineHeightChange = (height: string) => {
    actions.setLineHeight(height);
  };

  const handleTextAlignChange = (align: string) => {
    actions.setTextAlign(align);
  };

  const handleBoldToggle = () => {
    actions.setIsBold(!state.isBold);
  };

  const handleItalicToggle = () => {
    actions.setIsItalic(!state.isItalic);
  };

  const handleUnderlineToggle = () => {
    actions.setIsUnderline(!state.isUnderline);
  };

  const handleClearFormat = () => {
    actions.setIsBold(false);
    actions.setIsItalic(false);
    actions.setIsUnderline(false);
    actions.setTextAlign("left");
    actions.setFontSize("M");
    actions.setLineHeight("1.5");
  };

  const toggleToolbar = (section: string) => {
    actions.setShowToolbar(state.showToolbar === section ? null : section);
  };

  return {
    handleInputChange,
    handleThemeChange,
    handleFontSizeChange,
    handleFontFamilyChange,
    handleLineHeightChange,
    handleTextAlignChange,
    handleBoldToggle,
    handleItalicToggle,
    handleUnderlineToggle,
    handleClearFormat,
    toggleToolbar,
  };
};
