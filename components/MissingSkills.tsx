"use client";

import { motion } from "framer-motion";
import { XCircle } from "lucide-react";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";

interface MissingSkillsProps {
  skills: string[];
}

const MissingSkills = ({ skills }: MissingSkillsProps) => {
  return (
    <motion.div
      variants={cardHoverVariants}
      whileHover="hover"
      className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <XCircle className="w-6 h-6 text-red-500" />
        <h3 className="text-lg font-bold text-slate-800">Missing Skills</h3>
      </div>
      <div className="flex flex-wrap gap-3">
        {skills.map((skill, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center space-x-2 px-3 py-2 bg-red-50 border border-red-200 rounded-lg"
          >
            <XCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-700">{skill}</span>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default MissingSkills;
