// learn_vox_fe/components/common/FormInputField.tsx
"use client";

import React from "react";
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ControllerRenderProps, FieldPath, FieldValues } from "react-hook-form";

interface FormInputFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  field: ControllerRenderProps<TFieldValues, TName>;
  label: string;
  placeholder?: string;
  required?: boolean;
  description?: string;
  type?: "text" | "email" | "password" | "number" | "tel" | "url";
  className?: string;
  disabled?: boolean;
}

export function FormInputField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  field,
  label,
  placeholder,
  required = false,
  description,
  type = "text",
  className = "",
  disabled = false,
}: FormInputFieldProps<TFieldValues, TName>) {
  return (
    <FormItem className={className}>
      <FormLabel>
        {label} {required && <span className="text-red-500">*</span>}
      </FormLabel>
      <FormControl>
        <Input
          type={type}
          placeholder={placeholder}
          disabled={disabled}
          {...field}
        />
      </FormControl>
      {description && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  );
}
