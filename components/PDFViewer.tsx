import React from "react";

interface PDFViewerProps {
  fileUrl: string;
  title: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({ fileUrl, title }) => {
  return (
    <div className="pdf-overlay-container relative aspect-[3/4] bg-white rounded-xl overflow-hidden">
      <div className="pdf-overlay-scroll">
        <iframe
          src={`${fileUrl}#toolbar=0&navpanes=0&scrollbar=0&view=FitH&zoom=page-fit`}
          className="w-full h-full rounded-xl min-h-full"
          title={title}
          style={{
            border: "none",
            background: "transparent",
            minHeight: "100%",
            transform: "translateY(-40px)", 
            height: "calc(100% + 40px)",
          }}
        />
      </div>
    </div>
  );
};

export default PDFViewer;
