"use client";
import { Al<PERSON>Circle, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export function LoadingState() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading analysis...</p>
        </div>
      </div>
    </div>
  );
}

interface ErrorStateProps {
  error: any;
  analysisId: string;
}

export function ErrorState({ error, analysisId }: Readonly<ErrorStateProps>) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50 flex items-center justify-center">
      <div className="text-center space-y-4 max-w-md">
        <AlertCircle className="w-16 h-16 text-amber-500 mx-auto" />
        <h2 className="text-xl font-semibold text-slate-800">
          {error ? "Failed to Load Analysis" : "No Analysis Data Found"}
        </h2>
        <p className="text-slate-600">
          {error
            ? "There was an error loading the analysis data. Please try again."
            : `Analysis with ID "${analysisId}" not found. Please go back and run the analysis again.`}
        </p>
        <Link href="/analyst">
          <Button className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>
    </div>
  );
}
