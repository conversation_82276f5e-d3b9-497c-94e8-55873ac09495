"use client";

import React from "react";
import {
    Sidebar,
    SidebarContent,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@/components/ui/sidebar";
import { usePathname } from "next/navigation";
import {
    Home,
    CreditCard,
    MessageSquare,
    MessageCircle,
    FileText,
    Map,
    Mail,
    Sparkles,
} from "lucide-react";
import { v4 as uuidv4 } from "uuid";

const uuid = uuidv4();
// Menu configuration
const mainMenuItems = [
    {
        title: "Dashboard",
        url: "/dashboard",
        icon: Home,
    },

    {
        title: "Billing",
        url: "/billing",
        icon: CreditCard,
    },
];

const aiToolsItems = [
    {
        title: "AI Chat",
        url: `/chat-ai/${uuid}`,
        icon: MessageSquare,
    },
    {
        title: "Mock Interview",
        url: "/interview",
        icon: MessageCircle,
    },
    {
        title: "Resume Analyzer",
        url: "/analyst",
        icon: FileText,
    },
    {
        title: "Career Roadmap",
        url: "/career-roadmap",
        icon: Map,
    },
    {
        title: "Cover Letter",
        url: `/cover-letter`,
        icon: Mail,
    },
];

export function AppSidebar() {
    const pathname = usePathname();

    return (
        <Sidebar collapsible="icon" className="border-r">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <div className="p-2">
                                <div className="flex items-center gap-2 ">
                                    <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                                        <Sparkles className="h-4 w-4" />
                                    </div>
                                    <div>
                                        <h2 className="text-lg font-semibold">
                                            LearnVox AI
                                        </h2>
                                    </div>
                                </div>
                            </div>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                {/* Main Navigation */}
                <SidebarGroup>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {mainMenuItems.map((item) => (
                                <SidebarMenuItem key={item.title}>
                                    <SidebarMenuButton
                                        asChild
                                        isActive={pathname === item.url}
                                        tooltip={item.title}
                                    >
                                        <a
                                            href={item.url}
                                            className="flex items-center gap-2"
                                        >
                                            <item.icon className="h-4 w-4" />
                                            <span>{item.title}</span>
                                        </a>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            ))}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>

                {/* AI Tools Section */}
                <SidebarGroup>
                    <SidebarGroupLabel>AI Tools</SidebarGroupLabel>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {aiToolsItems.map((item) => (
                                <SidebarMenuItem key={item.title}>
                                    <SidebarMenuButton
                                        asChild
                                        isActive={pathname === item.url}
                                        tooltip={item.title}
                                    >
                                        <a
                                            href={item.url}
                                            className="flex items-center gap-2"
                                        >
                                            <item.icon className="h-4 w-4" />
                                            <span>{item.title}</span>
                                        </a>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            ))}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>
            </SidebarContent>
        </Sidebar>
    );
}
export function MobileSidebar() {
    return (
        <div className="md:hidden">
            <AppSidebar />
        </div>
    );
}
