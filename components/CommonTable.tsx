import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import TableSkeleton from "./TableSkeleton";

export interface TableColumn<T = any> {
  key: string;
  label: string;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  sortable?: boolean;
  className?: string;
}

export interface TableAction<T = any> {
  label: string;
  onClick: (row: T, index: number) => void;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  icon?: React.ReactNode;
  className?: string;
  disabled?: (row: T) => boolean;
}

export interface CommonTableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  actions?: TableAction<T>[];
  isLoading?: boolean;
  isError?: boolean;
  emptyMessage?: string;
  errorMessage?: string;
  className?: string;
  tableClassName?: string;
  pagination?: {
    enabled: boolean;
    pageSize?: number;
    currentPage?: number;
    onPageChange?: (page: number) => void;
  };
  title?: string;
  titleClassName?: string;
}

function CommonTable<T = any>({
  data = [],
  columns,
  actions = [],
  isLoading = false,
  isError = false,
  emptyMessage = "Không có dữ liệu",
  errorMessage = "Có lỗi xảy ra khi tải dữ liệu",
  className = "",
  tableClassName = "",
  pagination,
  title,
  titleClassName = "",
}: CommonTableProps<T>) {
  // Pagination logic
  const pageSize = pagination?.pageSize || 5;
  const currentPage = pagination?.currentPage || 1;
  const totalPages = Math.ceil(data.length / pageSize);

  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentItems = pagination?.enabled
    ? data.slice(startIndex, endIndex)
    : data;

  const handlePageChange = (page: number) => {
    if (pagination?.onPageChange) {
      pagination.onPageChange(page);
    }
  };

  const getNestedValue = (obj: any, path: string) => {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  };

  if (isLoading) {
    return (
      <TableSkeleton
        columns={columns.length}
        rows={pagination?.pageSize || 5}
        showActions={actions.length > 0}
        title={title}
        titleClassName={titleClassName}
        className={className}
        tableClassName={tableClassName}
      />
    );
  }

  if (isError) {
    return (
      <div className={`space-y-4 ${className}`}>
        {title && (
          <h2
            className={`text-2xl font-semibold text-slate-700 ${titleClassName}`}
          >
            {title}
          </h2>
        )}
        <div className="flex justify-center items-center p-8 text-red-500">
          {errorMessage}
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={`space-y-4 ${className}`}>
        {title && (
          <h2
            className={`text-2xl font-semibold text-slate-700 ${titleClassName}`}
          >
            {title}
          </h2>
        )}
        <div className="flex justify-center items-center p-8 text-slate-500">
          {emptyMessage}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {title && (
        <h2
          className={`text-2xl font-semibold text-slate-700 ${titleClassName}`}
        >
          {title}
        </h2>
      )}

      <div className="rounded-md border">
        <Table className={tableClassName}>
          <TableHeader>
            <TableRow className="bg-slate-100">
              {columns.map((column) => (
                <TableHead
                  key={column.key}
                  className={`px-4 py-3 text-xs uppercase text-slate-700 font-medium ${
                    column.className || ""
                  }`}
                >
                  {column.label}
                </TableHead>
              ))}
              {actions.length > 0 && (
                <TableHead className="px-4 py-3 text-xs uppercase text-slate-700 font-medium">
                  Actions
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentItems.map((item, index) => (
              <TableRow key={index} className="hover:bg-slate-50 transition">
                {columns.map((column) => (
                  <TableCell
                    key={column.key}
                    className="px-4 py-3 text-slate-700"
                  >
                    {column.render
                      ? column.render(
                          getNestedValue(item, column.key),
                          item,
                          index
                        )
                      : getNestedValue(item, column.key)}
                  </TableCell>
                ))}
                {actions.length > 0 && (
                  <TableCell className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      {actions.map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          variant={action.variant || "ghost"}
                          className={`flex items-center ${
                            action.className || ""
                          }`}
                          onClick={() => action.onClick(item, index)}
                          disabled={
                            action.disabled ? action.disabled(item) : false
                          }
                        >
                          {action.icon && (
                            <span className="mr-1">{action.icon}</span>
                          )}
                          {action.label}
                        </Button>
                      ))}
                    </div>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination?.enabled && totalPages > 1 && (
        <Pagination className="mt-4">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage > 1) handlePageChange(currentPage - 1);
                }}
                className={
                  currentPage === 1 ? "pointer-events-none opacity-50" : ""
                }
              />
            </PaginationItem>

            {[...Array(totalPages)].map((_, i) => (
              <PaginationItem key={i + 1}>
                <PaginationLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(i + 1);
                  }}
                  isActive={currentPage === i + 1}
                >
                  {i + 1}
                </PaginationLink>
              </PaginationItem>
            ))}

            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage < totalPages)
                    handlePageChange(currentPage + 1);
                }}
                className={
                  currentPage === totalPages
                    ? "pointer-events-none opacity-50"
                    : ""
                }
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}

export default CommonTable;
