"use client";

import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";

interface MatchingPointsProps {
  points: string[];
}

const MatchingPoints = ({ points }: MatchingPointsProps) => {
  return (
    <motion.div
      variants={cardHoverVariants}
      whileHover="hover"
      className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <CheckCircle className="w-6 h-6 text-green-500" />
        <h3 className="text-lg font-bold text-slate-800">Matching Points</h3>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {points.map((point, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg"
          >
            <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
            <span className="text-sm text-green-800">{point}</span>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default MatchingPoints;
