import React from "react";
import { Button } from "@/components/ui/button";
import { Eye, Download, FileType, Calendar, LucideIcon } from "lucide-react";

interface DocumentHeaderProps {
  title: string;
  fileName: string;
  fileSize: number;
  timestamp: number;
  fileUrl: string;
  icon: LucideIcon;
  onViewFile: (url: string) => void;
  onDownloadFile: (url: string, fileName: string) => void;
  colorScheme: {
    iconBg: string;
    buttonBorder: string;
    buttonHoverBorder: string;
    buttonText: string;
    buttonHoverText: string;
  };
}

const DocumentHeader: React.FC<DocumentHeaderProps> = ({
  title,
  fileName,
  fileSize,
  timestamp,
  fileUrl,
  icon: Icon,
  onViewFile,
  onDownloadFile,
  colorScheme,
}) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center space-x-4">
        <div
          className={`w-14 h-14 rounded-2xl ${colorScheme.iconBg} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
        >
          <Icon className="w-7 h-7 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-slate-800 mb-1">{title}</h3>
          <div className="flex items-center space-x-3 text-sm text-slate-600">
            <span className="flex items-center space-x-1">
              <FileType className="w-4 h-4" />
              <span>{formatFileSize(fileSize)}</span>
            </span>
            <span className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>{new Date(timestamp).toLocaleDateString()}</span>
            </span>
          </div>
        </div>
      </div>

      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onViewFile(fileUrl)}
          disabled={!fileUrl}
          className={`h-10 px-4 bg-white/80 hover:bg-white ${colorScheme.buttonBorder} ${colorScheme.buttonHoverBorder} ${colorScheme.buttonText} ${colorScheme.buttonHoverText} transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          <Eye className="w-4 h-4 mr-2" />
          View
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onDownloadFile(fileUrl, fileName)}
          disabled={!fileUrl}
          className={`h-10 px-4 bg-white/80 hover:bg-white ${colorScheme.buttonBorder} ${colorScheme.buttonHoverBorder} ${colorScheme.buttonText} ${colorScheme.buttonHoverText} transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          <Download className="w-4 h-4 mr-2" />
          Download
        </Button>
      </div>
    </div>
  );
};

export default DocumentHeader;
