"use client";

import { FileText, TrendingUp, Clock, Loader2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

interface MetricsCardsProps {
  totalAnalyses: number;
  averageScore: number;
  thisMonthCount: number;
  isLoading?: boolean;
  variants?: any;
}

const MetricsCards = ({
  totalAnalyses,
  averageScore,
  thisMonthCount,
  isLoading,
  variants,
}: MetricsCardsProps) => {
  const metrics = [
    {
      title: "Total Analyses",
      value: isLoading ? null : totalAnalyses,
      icon: FileText,
      color: "blue",
    },
    {
      title: "Average Score",
      value: isLoading ? null : `${averageScore}/100`,
      icon: TrendingUp,
      color: "green",
    },
    {
      title: "This Month",
      value: isLoading ? null : thisMonthCount,
      icon: Clock,
      color: "purple",
    },
  ];

  return (
    <motion.section
      className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
      variants={variants}
    >
      {metrics.map((metric) => (
        <motion.div
          key={metric.title}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {metric.title}
                  </p>
                  <p
                    className={`text-3xl font-bold ${
                      metric.color === "green"
                        ? "text-green-600"
                        : metric.color === "purple"
                        ? "text-purple-600"
                        : "text-gray-900"
                    }`}
                  >
                    {isLoading ? (
                      <Loader2 className="h-8 w-8 animate-spin" />
                    ) : (
                      metric.value
                    )}
                  </p>
                </div>
                <div
                  className={`h-12 w-12 ${
                    metric.color === "blue"
                      ? "bg-blue-100"
                      : metric.color === "green"
                      ? "bg-green-100"
                      : "bg-purple-100"
                  } rounded-lg flex items-center justify-center`}
                >
                  <metric.icon
                    className={`h-6 w-6 ${
                      metric.color === "blue"
                        ? "text-blue-600"
                        : metric.color === "green"
                        ? "text-green-600"
                        : "text-purple-600"
                    }`}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </motion.section>
  );
};

export default MetricsCards;
