"use client";

import { motion } from "framer-motion";
import { AlertCircle } from "lucide-react";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";

interface MissingExperienceProps {
  experience: string[];
}

const MissingExperience = ({ experience }: MissingExperienceProps) => {
  return (
    <motion.div
      variants={cardHoverVariants}
      whileHover="hover"
      className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <AlertCircle className="w-6 h-6 text-orange-500" />
        <h3 className="text-lg font-bold text-slate-800">Missing Experience</h3>
      </div>
      <div className="space-y-3">
        {experience.map((exp, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-start space-x-3 p-3 bg-orange-50 rounded-lg"
          >
            <AlertCircle className="w-5 h-5 text-orange-500 flex-shrink-0 mt-0.5" />
            <span className="text-sm text-orange-800">{exp}</span>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default MissingExperience;
