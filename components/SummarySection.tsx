"use client";

import { motion } from "framer-motion";
import { FileText, Target, Award } from "lucide-react";
import { getVerdictColor } from "@/services/data/analystStyle";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";

interface SummarySectionProps {
  summaryComment: string;
  fitScore: number;
  verdict: string;
}

const SummarySection = ({
  summaryComment,
  fitScore,
  verdict,
}: SummarySectionProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <motion.div
        variants={cardHoverVariants}
        whileHover="hover"
        className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
      >
        <div className="flex items-center space-x-3 mb-4">
          <FileText className="w-6 h-6 text-purple-500" />
          <h3 className="text-lg font-bold text-slate-800">Summary Comment</h3>
        </div>
        <p className="text-slate-600 text-sm leading-relaxed">
          {summaryComment}
        </p>
      </motion.div>

      <motion.div
        variants={cardHoverVariants}
        whileHover="hover"
        className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
      >
        <div className="flex items-center space-x-3 mb-4">
          <Target className="w-6 h-6 text-orange-500" />
          <h3 className="text-lg font-bold text-slate-800">Fit Score</h3>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-orange-600 mb-2">
            {fitScore}%
          </div>
          <div className="text-sm text-slate-600">Job Compatibility</div>
        </div>
      </motion.div>

      <motion.div
        variants={cardHoverVariants}
        whileHover="hover"
        className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
      >
        <div className="flex items-center space-x-3 mb-4">
          <Award className="w-6 h-6 text-green-500" />
          <h3 className="text-lg font-bold text-slate-800">Verdict</h3>
        </div>
        <div className="text-center">
          <div
            className={`inline-block px-4 py-2 rounded-full text-sm font-semibold border ${getVerdictColor(
              verdict
            )}`}
          >
            {verdict}
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SummarySection;
