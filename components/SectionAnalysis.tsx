"use client";

import { motion } from "framer-motion";
import { ThumbsUp, <PERSON><PERSON><PERSON><PERSON>gle, Lightbulb } from "lucide-react";
import { getScoreColor, getSectionIcon } from "@/services/data/analystStyle";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";
import { SectionData } from "@/features/analyst/analyst";

interface SectionAnalysisProps {
  sections: {
    contact_info: SectionData;
    experience: SectionData;
    education: SectionData;
    skills: SectionData;
  };
}

const SectionAnalysis = ({ sections }: SectionAnalysisProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {Object.entries(sections).map(([sectionName, sectionData], index) => (
        <motion.div
          key={sectionName}
          variants={cardHoverVariants}
          whileHover="hover"
          className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              {getSectionIcon(sectionName)}
              <h3 className="text-lg font-bold text-slate-800 capitalize">
                {sectionName.replace("_", " ")}
              </h3>
            </div>
            <div
              className={`px-3 py-1 rounded-full text-sm font-semibold ${getScoreColor(
                sectionData.score
              )}`}
            >
              {sectionData.score}%
            </div>
          </div>

          <p className="text-slate-600 text-sm mb-4">{sectionData.comment}</p>

          {sectionData.whats_good.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-2">
                <ThumbsUp className="w-4 h-4 text-green-500" />
                <span className="text-sm font-semibold text-green-700">
                  What{"'"}s Good
                </span>
              </div>
              <ul className="space-y-1">
                {sectionData.whats_good.map((item, i) => (
                  <li
                    key={i}
                    className="text-sm text-green-600 flex items-start space-x-2"
                  >
                    <span className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {sectionData.needs_improvement.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="w-4 h-4 text-red-500" />
                <span className="text-sm font-semibold text-red-700">
                  Needs Improvement
                </span>
              </div>
              <ul className="space-y-1">
                {sectionData.needs_improvement.map((item, i) => (
                  <li
                    key={i}
                    className="text-sm text-red-600 flex items-start space-x-2"
                  >
                    <span className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {sectionData.tips_for_improvement.length > 0 && (
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Lightbulb className="w-4 h-4 text-yellow-500" />
                <span className="text-sm font-semibold text-yellow-700">
                  Tips for Improvement
                </span>
              </div>
              <ul className="space-y-2">
                {sectionData.tips_for_improvement.map((tip, i) => (
                  <li
                    key={i}
                    className="text-sm text-slate-600 flex items-start space-x-2"
                  >
                    <span className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>
      ))}
    </div>
  );
};

export default SectionAnalysis;
