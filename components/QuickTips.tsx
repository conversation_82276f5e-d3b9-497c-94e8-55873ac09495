"use client";

import { Award, Zap, Target, FileText, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { motion } from "framer-motion";



const QuickTips = () => {
  const tips = [
    {
      icon: Zap,
      color: "text-blue-600",
      text: "Use power verbs and action words",
    },
    {
      icon: Target,
      color: "text-green-600",
      text: "Quantify your achievements with numbers",
    },
    {
      icon: FileText,
      color: "text-purple-600",
      text: "Tailor content to job descriptions",
    },
    {
      icon: TrendingUp,
      color: "text-orange-600",
      text: "Keep format clean and ATS-friendly",
    },
  ];

  return (
    <motion.div whileHover={{ y: -2 }} transition={{ duration: 0.2 }}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-yellow-600" />
            Quick Tips
          </CardTitle>
          <CardDescription>Make your resume stand out</CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible>
            <AccordionItem value="tips">
              <AccordionTrigger>Resume Best Practices</AccordionTrigger>
              <AccordionContent>
                <ul className="space-y-2 text-sm">
                  {tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <tip.icon
                        className={`h-4 w-4 ${tip.color} mt-0.5 flex-shrink-0`}
                      />
                      {tip.text}
                    </li>
                  ))}
                </ul>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <Button variant="link" className="p-0 h-auto text-blue-600 mt-2">
            Read our complete guide →
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default QuickTips;
