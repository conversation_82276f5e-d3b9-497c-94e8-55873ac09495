"use client";

import { motion } from "framer-motion";
import { Star, Lightbulb } from "lucide-react";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";

interface RecommendationsProps {
  recommendations: string[];
}

const Recommendations = ({ recommendations }: RecommendationsProps) => {
  return (
    <motion.div
      variants={cardHoverVariants}
      whileHover="hover"
      className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
    >
      <div className="flex items-center space-x-3 mb-6">
        <Star className="w-6 h-6 text-yellow-500" />
        <h3 className="text-lg font-bold text-slate-800">Recommendations</h3>
      </div>
      <div className="space-y-4">
        {recommendations.map((rec, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.15 }}
            className="border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-start space-x-3">
              <Lightbulb className="w-5 h-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <p className="text-sm text-slate-600">{rec}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default Recommendations;
