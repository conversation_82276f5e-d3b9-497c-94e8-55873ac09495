"use client";

import { MessageCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { motion } from "framer-motion";



const AIAssistant = () => {
  return (
    <motion.div whileHover={{ y: -2 }} transition={{ duration: 0.2 }}>
      <Card className="bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-indigo-600" />
            AI Assistant
          </CardTitle>
          <CardDescription>Get instant help with your resume</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-white rounded-lg p-4 mb-4 border border-indigo-100">
            <p className="text-sm text-gray-600 italic">
              &quot;How can I improve my skills section?&quot;
            </p>
          </div>
          <Button className="w-full bg-indigo-600 hover:bg-indigo-700">
            <MessageCircle className="mr-2 h-4 w-4" />
            Chat Now
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AIAssistant;
