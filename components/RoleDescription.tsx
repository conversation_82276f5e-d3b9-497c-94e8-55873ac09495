import React from "react";
import { motion } from "framer-motion";
import { Target } from "lucide-react";

interface RoleDescriptionProps {
  roleDescription: string;
}

const RoleDescription: React.FC<RoleDescriptionProps> = ({
  roleDescription,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="mt-6 p-4 bg-gradient-to-r from-purple-100 via-purple-50 to-pink-50 rounded-xl border border-purple-200/50"
    >
      <div className="flex items-center space-x-2 mb-2">
        <Target className="w-4 h-4 text-purple-600" />
        <span className="text-sm font-semibold text-purple-800">
          Target Role
        </span>
      </div>
      <p className="text-sm text-purple-700 leading-relaxed">
        {roleDescription}
      </p>
    </motion.div>
  );
};

export default RoleDescription;
