"use client";

import { motion } from "framer-motion";
import { MessageSquare } from "lucide-react";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";

interface FeedbackCardProps {
  feedback: string;
}

const FeedbackCard = ({ feedback }: FeedbackCardProps) => {
  return (
    <motion.div
      variants={cardHoverVariants}
      whileHover="hover"
      className="bg-white rounded-xl shadow-lg border border-slate-200 p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <MessageSquare className="w-6 h-6 text-blue-500" />
        <h3 className="text-lg font-bold text-slate-800">Overall Feedback</h3>
      </div>
      <p className="text-slate-600 leading-relaxed">{feedback}</p>
    </motion.div>
  );
};

export default FeedbackCard;
