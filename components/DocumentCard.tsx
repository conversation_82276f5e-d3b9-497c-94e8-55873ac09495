import React from "react";
import { motion } from "framer-motion";
import { cardHoverVariants } from "@/styles/animations/analystRole.variants";
import { LucideIcon } from "lucide-react";
import DocumentHeader from "./DocumentHeader";
import PDFViewer from "./PDFViewer";
import DocumentPlaceholder from "./DocumentPlaceholder";
import RoleDescription from "./RoleDescription";

interface DocumentCardProps {
  title: string;
  fileName: string;
  fileSize: number;
  timestamp: number;
  fileUrl: string;
  icon: LucideIcon;
  bgGradient: string;
  borderColor: string;
  onViewFile: (url: string) => void;
  onDownloadFile: (url: string, fileName: string) => void;
  colorScheme: {
    iconBg: string;
    buttonBorder: string;
    buttonHoverBorder: string;
    buttonText: string;
    buttonHoverText: string;
    placeholderFrom: string;
    placeholderTo: string;
  };
  roleDescription?: string;
  showRoleDescription?: boolean;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  title,
  fileName,
  fileSize,
  timestamp,
  fileUrl,
  icon,
  bgGradient,
  borderColor,
  onViewFile,
  onDownloadFile,
  colorScheme,
  roleDescription,
  showRoleDescription = false,
}) => {
  return (
    <motion.div
      variants={cardHoverVariants}
      whileHover="hover"
      className={`group relative ${bgGradient} rounded-2xl shadow-xl ${borderColor} overflow-hidden hover:shadow-2xl transition-all duration-300`}
    >
      <div className="relative p-8">
        <DocumentHeader
          title={title}
          fileName={fileName}
          fileSize={fileSize}
          timestamp={timestamp}
          fileUrl={fileUrl}
          icon={icon}
          onViewFile={onViewFile}
          onDownloadFile={onDownloadFile}
          colorScheme={colorScheme}
        />

        {/* Document Preview */}
        <div className="relative rounded-xl overflow-hidden shadow-2xl bg-white">
          {fileUrl ? (
            <PDFViewer fileUrl={fileUrl} title={`${title} Preview`} />
          ) : (
            <DocumentPlaceholder
              fileName={fileName}
              timestamp={timestamp}
              icon={icon}
              colorFrom={colorScheme.placeholderFrom}
              colorTo={colorScheme.placeholderTo}
            />
          )}
        </div>

        {/* Role Description */}
        {showRoleDescription && roleDescription && (
          <RoleDescription roleDescription={roleDescription} />
        )}
      </div>
    </motion.div>
  );
};

export default DocumentCard;
