import {
  FilePlus2,
  Save,
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Undo2,
  Redo2,
  RotateCcw,
  Plus,
} from "lucide-react";
import React from "react";

interface CoverLetterToolbarProps {
  fontSize: string;
  fontFamily: string;
  lineHeight: string;
  textAlign: string;
  isBold: boolean;
  isItalic: boolean;
  isUnderline: boolean;
  currentTheme?: string;
  onThemeChange: (theme: string) => void;
  onFontSizeChange: (size: string) => void;
  onFontFamilyChange: (family: string) => void;
  onLineHeightChange: (height: string) => void;
  onTextAlignChange: (align: string) => void;
  onBoldToggle: () => void;
  onItalicToggle: () => void;
  onUnderlineToggle: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onAddSection?: () => void;
  onClearFormat: () => void;
  onSave?: () => void;
  isSaving?: boolean;
  onChangeTemplate?: () => void;
  isHidden?: boolean;
  canUndo?: boolean;
  canRedo?: boolean;
}

const CoverLetterToolbar: React.FC<CoverLetterToolbarProps> = ({
  fontSize,
  fontFamily,
  lineHeight,
  textAlign,
  isBold,
  isItalic,
  isUnderline,
  currentTheme = "blue",
  onThemeChange,
  onFontSizeChange,
  onFontFamilyChange,
  onLineHeightChange,
  onTextAlignChange,
  onBoldToggle,
  onItalicToggle,
  onUnderlineToggle,
  onUndo,
  onRedo,
  onAddSection,
  onClearFormat,
  onSave,
  isSaving = false,
  onChangeTemplate,
  isHidden = false,
  canUndo = false,
  canRedo = false,
}) => {
  // Don't render toolbar if it's hidden
  if (isHidden) {
    return null;
  }
  return (
    <div
      data-toolbar
      className="cover-letter-toolbar sticky top-4 bg-white border border-gray-200 rounded-xl shadow-lg p-4 mb-6 w-full"
      style={{ zIndex: 999 }}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Top Row */}
      <div className="flex items-center gap-6 flex-wrap justify-center mb-4">
        {/* Color Theme */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Color Theme</span>
          <div className="flex gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onThemeChange("blue");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 shadow-sm ${
                isSaving ? "opacity-50 cursor-not-allowed" : "hover:scale-105"
              } ${
                currentTheme === "blue"
                  ? "border-blue-400 ring-2 ring-blue-200"
                  : "border-gray-200 hover:border-blue-400"
              }`}
              style={{ backgroundColor: "#E8F4FD" }}
            />
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onThemeChange("green");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 shadow-sm ${
                isSaving ? "opacity-50 cursor-not-allowed" : "hover:scale-105"
              } ${
                currentTheme === "green"
                  ? "border-green-400 ring-2 ring-green-200"
                  : "border-gray-200 hover:border-green-400"
              }`}
              style={{ backgroundColor: "#F0F8E8" }}
            />
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onThemeChange("yellow");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 shadow-sm ${
                isSaving ? "opacity-50 cursor-not-allowed" : "hover:scale-105"
              } ${
                currentTheme === "yellow"
                  ? "border-yellow-400 ring-2 ring-yellow-200"
                  : "border-gray-200 hover:border-yellow-400"
              }`}
              style={{ backgroundColor: "#FFF8E8" }}
            />
          </div>
        </div>

        {/* Font Family */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Font Family</span>
          <select
            value={fontFamily}
            onChange={(e) => {
              e.stopPropagation();
              if (!isSaving) onFontFamilyChange(e.target.value);
            }}
            disabled={isSaving}
            className={`text-sm border border-gray-200 rounded-lg px-3 py-2 min-w-[100px] bg-white transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed"
                : "hover:border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-100"
            }`}
          >
            <option value="Calibri">Calibri</option>
            <option value="Arial">Arial</option>
            <option value="Times New Roman">Times</option>
            <option value="Cambria">Cambria</option>
          </select>
        </div>

        {/* Font Size */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Font Size</span>
          <div className="flex items-center gap-1 p-1 bg-gray-50 rounded-lg">
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onFontSizeChange("S");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-md text-sm font-medium flex items-center justify-center transition-all duration-200 ${
                isSaving
                  ? "opacity-50 cursor-not-allowed"
                  : fontSize === "S"
                  ? "bg-blue-500 text-white shadow-md"
                  : "bg-white text-gray-600 hover:bg-gray-100"
              }`}
            >
              S
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onFontSizeChange("M");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-md text-sm font-medium flex items-center justify-center transition-all duration-200 ${
                isSaving
                  ? "opacity-50 cursor-not-allowed"
                  : fontSize === "M"
                  ? "bg-blue-500 text-white shadow-md"
                  : "bg-white text-gray-600 hover:bg-gray-100"
              }`}
            >
              M
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onFontSizeChange("L");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-md text-sm font-medium flex items-center justify-center transition-all duration-200 ${
                isSaving
                  ? "opacity-50 cursor-not-allowed"
                  : fontSize === "L"
                  ? "bg-blue-500 text-white shadow-md"
                  : "bg-white text-gray-600 hover:bg-gray-100"
              }`}
            >
              L
            </button>
          </div>
        </div>

        {/* Line Height */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Line Height</span>
          <div className="flex items-center gap-1 p-1 bg-gray-50 rounded-lg">
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onLineHeightChange("1.2");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-md text-xs font-medium flex items-center justify-center transition-all duration-200 ${
                isSaving
                  ? "opacity-50 cursor-not-allowed"
                  : lineHeight === "1.2"
                  ? "bg-blue-500 text-white shadow-md"
                  : "bg-white text-gray-600 hover:bg-gray-100"
              }`}
            >
              1.2
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onLineHeightChange("1.5");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-md text-xs font-medium flex items-center justify-center transition-all duration-200 ${
                isSaving
                  ? "opacity-50 cursor-not-allowed"
                  : lineHeight === "1.5"
                  ? "bg-blue-500 text-white shadow-md"
                  : "bg-white text-gray-600 hover:bg-gray-100"
              }`}
            >
              1.5
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!isSaving) onLineHeightChange("1.8");
              }}
              disabled={isSaving}
              className={`w-8 h-8 rounded-md text-xs font-medium flex items-center justify-center transition-all duration-200 ${
                isSaving
                  ? "opacity-50 cursor-not-allowed"
                  : lineHeight === "1.8"
                  ? "bg-blue-500 text-white shadow-md"
                  : "bg-white text-gray-600 hover:bg-gray-100"
              }`}
            >
              1.8
            </button>
          </div>
        </div>

        {/* Add Section */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Add Section</span>
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onAddSection?.();
            }}
            disabled={isSaving}
            className={`w-10 h-10 border border-gray-200 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "bg-gray-100 opacity-50 cursor-not-allowed"
                : "bg-gray-50 hover:bg-gray-100 hover:scale-105"
            }`}
          >
            <Plus className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Change Template */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Template</span>
          <button
            onClick={() => {
              if (!isSaving) onChangeTemplate?.();
            }}
            disabled={isSaving}
            className={`w-10 h-10 border rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "bg-gray-100 border-gray-200 opacity-50 cursor-not-allowed"
                : "bg-purple-50 hover:bg-purple-100 border-purple-200 hover:scale-105"
            }`}
          >
            <FilePlus2
              className={`w-5 h-5 ${
                isSaving ? "text-gray-400" : "text-purple-600"
              }`}
            />
          </button>
        </div>

        {/* Save Cover Letter */}
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Save</span>
          <button
            onClick={onSave}
            disabled={isSaving}
            className="w-10 h-10 bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? (
              <div className="w-5 h-5 border-2 border-green-600 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <Save className="w-5 h-5 text-green-600" />
            )}
          </button>
        </div>
      </div>

      {/* Divider */}
      <div className="w-full h-px bg-gray-200 mb-4"></div>

      {/* Bottom Row */}
      <div className="flex items-center gap-6 flex-wrap justify-center">
        {/* Bold, Italic, Underline */}
        <div className="flex items-center gap-1 p-1 bg-gray-50 rounded-lg">
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onBoldToggle();
            }}
            disabled={isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed bg-white text-gray-400"
                : isBold
                ? "bg-blue-500 text-white shadow-md"
                : "bg-white text-gray-600 hover:bg-gray-100"
            }`}
          >
            <Bold className="w-4 h-4" />
          </button>
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onItalicToggle();
            }}
            disabled={isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed bg-white text-gray-400"
                : isItalic
                ? "bg-blue-500 text-white shadow-md"
                : "bg-white text-gray-600 hover:bg-gray-100"
            }`}
          >
            <Italic className="w-4 h-4" />
          </button>
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onUnderlineToggle();
            }}
            disabled={isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed bg-white text-gray-400"
                : isUnderline
                ? "bg-blue-500 text-white shadow-md"
                : "bg-white text-gray-600 hover:bg-gray-100"
            }`}
          >
            <Underline className="w-4 h-4" />
          </button>
        </div>

        {/* Divider */}
        <div className="h-8 w-px bg-gray-200"></div>

        {/* Text Alignment */}
        <div className="flex items-center gap-1 p-1 bg-gray-50 rounded-lg">
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onTextAlignChange("left");
            }}
            disabled={isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed bg-white text-gray-400"
                : textAlign === "left"
                ? "bg-blue-500 text-white shadow-md"
                : "bg-white text-gray-600 hover:bg-gray-100"
            }`}
          >
            <AlignLeft className="w-4 h-4" />
          </button>
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onTextAlignChange("center");
            }}
            disabled={isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed bg-white text-gray-400"
                : textAlign === "center"
                ? "bg-blue-500 text-white shadow-md"
                : "bg-white text-gray-600 hover:bg-gray-100"
            }`}
          >
            <AlignCenter className="w-4 h-4" />
          </button>
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onTextAlignChange("right");
            }}
            disabled={isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed bg-white text-gray-400"
                : textAlign === "right"
                ? "bg-blue-500 text-white shadow-md"
                : "bg-white text-gray-600 hover:bg-gray-100"
            }`}
          >
            <AlignRight className="w-4 h-4" />
          </button>
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onTextAlignChange("justify");
            }}
            disabled={isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              isSaving
                ? "opacity-50 cursor-not-allowed bg-white text-gray-400"
                : textAlign === "justify"
                ? "bg-blue-500 text-white shadow-md"
                : "bg-white text-gray-600 hover:bg-gray-100"
            }`}
          >
            <AlignJustify className="w-4 h-4" />
          </button>
        </div>

        {/* Divider */}
        <div className="h-8 w-px bg-gray-200"></div>

        {/* Undo/Redo */}
        <div className="flex items-center gap-1 p-1 bg-gray-50 rounded-lg">
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onUndo?.();
            }}
            disabled={!canUndo || isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              canUndo && !isSaving
                ? "bg-white hover:bg-gray-100 text-gray-600"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            }`}
          >
            <Undo2 className="w-4 h-4" />
          </button>
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onRedo?.();
            }}
            disabled={!canRedo || isSaving}
            className={`w-9 h-9 rounded-lg flex items-center justify-center transition-all duration-200 ${
              canRedo && !isSaving
                ? "bg-white hover:bg-gray-100 text-gray-600"
                : "bg-gray-100 text-gray-400 cursor-not-allowed"
            }`}
          >
            <Redo2 className="w-4 h-4" />
          </button>
        </div>

        {/* Divider */}
        <div className="h-8 w-px bg-gray-200"></div>

        {/* Clear Format */}
        <div className="flex items-center">
          <button
            data-toolbar-button
            onClick={(e) => {
              e.stopPropagation();
              if (!isSaving) onClearFormat();
            }}
            disabled={isSaving}
            className={`px-4 h-9 border rounded-lg text-sm font-medium flex items-center gap-2 transition-all duration-200 ${
              isSaving
                ? "bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed opacity-50"
                : "bg-red-50 hover:bg-red-100 border-red-200 text-red-600 hover:scale-105"
            }`}
          >
            <RotateCcw className="w-4 h-4" />
            Clear
          </button>
        </div>
      </div>
    </div>
  );
};

export default CoverLetterToolbar;
