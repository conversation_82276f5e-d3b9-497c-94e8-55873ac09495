"use client";

import React from "react";
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { ControllerRenderProps, FieldPath, FieldValues } from "react-hook-form";

interface ToggleOption {
  value: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  disabled?: boolean;
}

interface FormToggleGroupFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  field: ControllerRenderProps<TFieldValues, TName>;
  label: string;
  required?: boolean;
  description?: string;
  options: ToggleOption[];
  type?: "single" | "multiple";
  className?: string;
  disabled?: boolean;
  itemClassName?: string;
}

export function FormToggleGroupField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  field,
  label,
  required = false,
  description,
  options,
  type = "multiple",
  className = "",
  disabled = false,
  itemClassName = "",
}: FormToggleGroupFieldProps<TFieldValues, TName>) {
  return (
    <FormItem className={className}>
      <FormLabel>
        {label} {required && <span className="text-red-500">*</span>}
      </FormLabel>
      <FormControl>
        <ToggleGroup
          type={type}
          className="justify-start flex-wrap"
          value={field.value}
          onValueChange={field.onChange}
          disabled={disabled}
        >
          {options.map((option) => (
            <ToggleGroupItem
              key={option.value}
              value={option.value}
              disabled={option.disabled}
              className={`gap-2 border border-gray-300 data-[state=on]:border-blue-500 data-[state=on]:bg-blue-100 data-[state=on]:text-blue-700 m-1 ${itemClassName}`}
            >
              {option.icon && <option.icon className="h-4 w-4" />}
              {option.label}
            </ToggleGroupItem>
          ))}
        </ToggleGroup>
      </FormControl>
      {description && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  );
}
