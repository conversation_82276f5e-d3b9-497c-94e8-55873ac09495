import React from "react";

const LoadingSkeleton = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <div className="bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl p-8 border border-slate-200/50">
        <div className="animate-pulse space-y-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-slate-300 rounded-xl"></div>
            <div className="space-y-2">
              <div className="h-5 bg-slate-300 rounded w-24"></div>
              <div className="h-3 bg-slate-300 rounded w-16"></div>
            </div>
          </div>
          <div className="h-96 bg-slate-300 rounded-xl"></div>
        </div>
      </div>
      <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-8 border border-orange-200/50">
        <div className="animate-pulse space-y-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-orange-300 rounded-xl"></div>
            <div className="space-y-2">
              <div className="h-5 bg-orange-300 rounded w-32"></div>
              <div className="h-3 bg-orange-300 rounded w-16"></div>
            </div>
          </div>
          <div className="h-96 bg-orange-300 rounded-xl"></div>
        </div>
      </div>
    </div>
  );
};

export default LoadingSkeleton;
