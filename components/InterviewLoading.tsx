"use client";

import { motion } from "framer-motion";
import { Loader2, <PERSON>, MessageSquare, Target } from "lucide-react";
import {
  loadingContainerVariants,
  loadingItemVariants,
  loadingIconAnimations,
} from "@/styles/animations/loading.variants";

export default function InterviewLoading() {
  return (
    <motion.div
      variants={loadingContainerVariants}
      initial="hidden"
      animate="visible"
      className="flex-1 min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-8"
    >
      <div className="max-w-md w-full text-center">
        <motion.div variants={loadingItemVariants} className="mb-8">
          <motion.div
            animate={loadingIconAnimations.rotate.animate}
            transition={loadingIconAnimations.rotate.transition}
            className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full flex items-center justify-center"
          >
            <Brain className="w-8 h-8 text-white" />
          </motion.div>

          <motion.h2
            animate={loadingIconAnimations.scale.animate}
            transition={loadingIconAnimations.scale.transition}
            className="text-2xl font-bold text-slate-800 mb-2"
          >
            Preparing Your Interview Dashboard
          </motion.h2>

          <motion.p variants={loadingItemVariants} className="text-slate-600">
            Loading your interview sessions and feedback data...
          </motion.p>
        </motion.div>

        <motion.div
          variants={loadingItemVariants}
          className="grid grid-cols-3 gap-4 mb-8"
        >
          {[
            { icon: <MessageSquare className="w-5 h-5" />, label: "Sessions" },
            { icon: <Target className="w-5 h-5" />, label: "Analytics" },
            { icon: <Brain className="w-5 h-5" />, label: "Feedback" },
          ].map((item, index) => (
            <motion.div
              key={index}
              variants={loadingItemVariants}
              className="bg-white p-4 rounded-lg shadow-sm border border-slate-100"
            >
              <div className="text-blue-600 mb-2 flex justify-center">
                {item.icon}
              </div>
              <div className="text-sm text-slate-600">{item.label}</div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          variants={loadingItemVariants}
          className="flex items-center justify-center space-x-2 text-slate-500"
        >
          <Loader2 className="w-4 h-4 animate-spin" />
          <span className="text-sm">Just a moment...</span>
        </motion.div>
      </div>
    </motion.div>
  );
}
