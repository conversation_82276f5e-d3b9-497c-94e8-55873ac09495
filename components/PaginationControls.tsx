'use client';

import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PaginationData {
    currentItems: any[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
}

interface PaginationControlsProps {
    paginationData: PaginationData;
    currentPage: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
    onPrevPage: () => void;
    onNextPage: () => void;
    itemName?: string;
}

export function PaginationControls({
    paginationData,
    currentPage,
    itemsPerPage,
    onPageChange,
    onPrevPage,
    onNextPage,
    itemName = "items"
}: PaginationControlsProps) {
    if (paginationData.totalPages <= 1) return null;

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="space-y-8 mt-12"
        >
            {/* Info Section */}
            <div className="flex items-center justify-center">
                <div className="px-6 py-3 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 shadow-lg">
                    <div className="text-sm text-gray-600 font-medium">
                        Showing <span className="text-blue-600 font-semibold">{((currentPage - 1) * itemsPerPage) + 1}</span> to{" "}
                        <span className="text-blue-600 font-semibold">{Math.min(currentPage * itemsPerPage, paginationData.totalItems)}</span> of{" "}
                        <span className="text-blue-600 font-semibold">{paginationData.totalItems}</span> {itemName}
                    </div>
                </div>
            </div>
            
            {/* Navigation Controls */}
            <div className="flex items-center justify-center">
                <div className="flex items-center gap-3 p-3 bg-white/80 backdrop-blur-sm rounded-3xl border border-gray-200/60 shadow-lg">
                    {/* Previous Button */}
                    <motion.div
                        whileHover={{ scale: paginationData.hasPrevPage ? 1.05 : 1 }}
                        whileTap={{ scale: paginationData.hasPrevPage ? 0.95 : 1 }}
                    >
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onPrevPage}
                            disabled={!paginationData.hasPrevPage}
                            className={`gap-2 h-11 px-5 rounded-2xl font-medium transition-all duration-300 ${
                                paginationData.hasPrevPage
                                    ? "hover:bg-gradient-to-r hover:from-blue-500 hover:to-cyan-500 hover:text-white hover:shadow-lg text-gray-700"
                                    : "text-gray-400 cursor-not-allowed opacity-50"
                            }`}
                        >
                            <ChevronLeft className="w-4 h-4" />
                            <span className="hidden sm:inline">Previous</span>
                        </Button>
                    </motion.div>
                    
                    {/* Page Numbers */}
                    <div className="flex items-center gap-1 sm:gap-2 mx-1 sm:mx-2">
                        {(() => {
                            const maxVisiblePages = 5;
                            const totalPages = paginationData.totalPages;
                            
                            if (totalPages <= maxVisiblePages) {
                                return Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                                    <motion.div
                                        key={page}
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                        className={page > 3 ? "hidden sm:block" : ""} // Hide some pages on mobile
                                    >
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => onPageChange(page)}
                                            className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                                currentPage === page
                                                    ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                    : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                            }`}
                                        >
                                            {page}
                                        </Button>
                                    </motion.div>
                                ));
                            }
                            
                            const pages = [];
                            
                            // First page
                            pages.push(
                                <motion.div
                                    key={1}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                >
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => onPageChange(1)}
                                        className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                            currentPage === 1
                                                ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                        }`}
                                    >
                                        1
                                    </Button>
                                </motion.div>
                            );
                            
                            // Left ellipsis
                            if (currentPage > 3) {
                                pages.push(
                                    <div key="ellipsis1" className="flex items-center justify-center w-10 h-10 sm:w-11 sm:h-11">
                                        <span className="text-gray-400 font-medium text-sm sm:text-lg">•••</span>
                                    </div>
                                );
                            }
                            
                            // Middle pages
                            const start = Math.max(2, currentPage - 1);
                            const end = Math.min(totalPages - 1, currentPage + 1);
                            
                            for (let i = start; i <= end; i++) {
                                if (i !== 1 && i !== totalPages) {
                                    pages.push(
                                        <motion.div
                                            key={i}
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.9 }}
                                        >
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => onPageChange(i)}
                                                className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                                    currentPage === i
                                                        ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                        : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                                }`}
                                            >
                                                {i}
                                            </Button>
                                        </motion.div>
                                    );
                                }
                            }
                            
                            // Right ellipsis
                            if (currentPage < totalPages - 2) {
                                pages.push(
                                    <div key="ellipsis2" className="flex items-center justify-center w-10 h-10 sm:w-11 sm:h-11">
                                        <span className="text-gray-400 font-medium text-sm sm:text-lg">•••</span>
                                    </div>
                                );
                            }
                            
                            // Last page
                            if (totalPages > 1) {
                                pages.push(
                                    <motion.div
                                        key={totalPages}
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.9 }}
                                    >
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => onPageChange(totalPages)}
                                            className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                                currentPage === totalPages
                                                    ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                    : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                            }`}
                                        >
                                            {totalPages}
                                        </Button>
                                    </motion.div>
                                );
                            }
                            
                            return pages;
                        })()}
                    </div>
                    
                    {/* Next Button */}
                    <motion.div
                        whileHover={{ scale: paginationData.hasNextPage ? 1.05 : 1 }}
                        whileTap={{ scale: paginationData.hasNextPage ? 0.95 : 1 }}
                    >
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onNextPage}
                            disabled={!paginationData.hasNextPage}
                            className={`gap-2 h-11 px-5 rounded-2xl font-medium transition-all duration-300 ${
                                paginationData.hasNextPage
                                    ? "hover:bg-gradient-to-r hover:from-blue-500 hover:to-cyan-500 hover:text-white hover:shadow-lg text-gray-700"
                                    : "text-gray-400 cursor-not-allowed opacity-50"
                            }`}
                        >
                            <span className="hidden sm:inline">Next</span>
                            <ChevronRight className="w-4 h-4" />
                        </Button>
                    </motion.div>
                </div>
            </div>
        </motion.div>
    );
}
