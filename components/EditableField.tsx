import React from "react";
import { Edit3 } from "lucide-react";

interface EditableFieldProps {
  field: string;
  value: string;
  className?: string;
  style?: React.CSSProperties;
  isTextarea?: boolean;
  placeholder?: string;
  editingField: string | null;
  onEdit: (field: string | null) => void;
  onChange: (field: string, value: string) => void;
  borderColor?: string;
}

const EditableField: React.FC<EditableFieldProps> = ({
  field,
  value,
  className = "",
  style,
  isTextarea = false,
  placeholder = "",
  editingField,
  onEdit,
  onChange,
  borderColor = "border-orange-500",
}) => {
  const isEditing = editingField === field;

  const handleBlur = () => {
    onEdit(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isTextarea && e.key === "Enter" && e.ctrlKey) {
      handleBlur();
    } else if (!isTextarea && e.key === "Enter") {
      handleBlur();
    }
  };

  return (
    <div className="relative group">
      {isEditing ? (
        isTextarea ? (
          <textarea
            value={value}
            onChange={(e) => onChange(field, e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className={`${className} border-2 ${borderColor} rounded p-2 w-full resize-none min-h-[100px] focus:outline-none`}
            style={{
              pageBreakInside: "avoid",
              breakInside: "avoid",
              ...style,
            }}
            placeholder={placeholder}
            autoFocus
          />
        ) : (
          <input
            type="text"
            value={value}
            onChange={(e) => onChange(field, e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            className={`${className} border-2 ${borderColor} rounded p-2 w-full focus:outline-none`}
            placeholder={placeholder}
            autoFocus
          />
        )
      ) : (
        <div
          className={`${className} cursor-pointer hover:bg-gray-100 rounded p-2 pr-8 transition-colors relative`}
          style={style}
          onClick={() => onEdit(field)}
        >
          {value || placeholder}
          <Edit3 className="w-4 h-4 text-gray-400 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      )}
    </div>
  );
};

export default EditableField;
