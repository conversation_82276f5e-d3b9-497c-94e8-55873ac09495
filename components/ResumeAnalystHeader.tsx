"use client";
import { motion } from "framer-motion";
import { Target } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface AnalysisHeaderProps {
  roleDescription: string;
}

export default function AnalysisHeader({
  roleDescription,
}: Readonly<AnalysisHeaderProps>) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/80 backdrop-blur-xl border-b border-slate-200 sticky top-0 z-10"
    >
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                <Target className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">
                  Resume Analysis Report
                </h1>
                <p className="text-sm text-slate-600">
                  Analyzing for: {roleDescription}
                </p>
              </div>
            </div>
          </div>
          <Badge variant="secondary" className="bg-purple-100 text-purple-700">
            Analysis Complete
          </Badge>
        </div>
      </div>
    </motion.div>
  );
}
