import React from "react";
import { LucideIcon } from "lucide-react";

interface DocumentPlaceholderProps {
  fileName: string;
  timestamp: number;
  icon: LucideIcon;
  colorFrom: string;
  colorTo: string;
}

const DocumentPlaceholder: React.FC<DocumentPlaceholderProps> = ({
  fileName,
  timestamp,
  icon: Icon,
  colorFrom,
  colorTo,
}) => {
  return (
    <div className="aspect-[3/4] flex flex-col items-center justify-center p-8 bg-gradient-to-br from-slate-50 to-slate-100 rounded-xl">
      <div
        className={`w-20 h-20 rounded-full bg-gradient-to-br ${colorFrom} ${colorTo} flex items-center justify-center mb-6 shadow-lg`}
      >
        <Icon className="w-10 h-10 text-white" />
      </div>
      <h4 className="text-lg font-semibold text-slate-800 mb-2 text-center">
        {fileName}
      </h4>
      <p className="text-sm text-slate-600 text-center max-w-xs">
        Document uploaded on {new Date(timestamp).toLocaleDateString()}
      </p>
      <p className="text-xs text-red-500 mt-2">File URL not available</p>
    </div>
  );
};

export default DocumentPlaceholder;
