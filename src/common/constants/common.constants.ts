import { Ionicons } from '@expo/vector-icons';

export const HTTP_CODES = {
  OK: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  INTERNAL_SERVER_ERROR: 500,
  NETWORK_ERROR: 0,
  PRECONDITION_ERROR: 412,
  SECTION_INVALID: 406,
};

import { HomeStackParamList } from '../types/rootParamList';

export interface Menu {
  id: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconBgColor?: string;
  navigate: keyof HomeStackParamList;
}

export const MENU: Menu[] = [
  {
    id: 'home',
    title: 'Trang chủ',
    icon: 'home-outline',
    iconBgColor: '#6B7280',
    navigate: 'Home',
  },

  {
    id: 'mock-interview',
    title: 'Mock Interview',
    icon: 'mic',
    iconBgColor: '#6B7280',
    navigate: 'MockInterview',
  },
  {
    id: 'ai-chat',
    title: 'AI Chat',
    icon: 'chatbubbles',
    iconBgColor: '#6B7280',
    navigate: 'AIChat',
  },
  {
    id: 'interviews',
    title: 'AI Resume Analyzer',
    icon: 'document-text',
    iconBgColor: '#6B7280',
    navigate: 'Analysis',
  },
  {
    id: 'career-roadmap',
    title: 'AI Roadmap',
    icon: 'map',
    iconBgColor: '#6B7280',
    navigate: 'CareerRoadmap',
  },
];

export const SITTING = [
  {
    id: 'settings',
    title: 'Cài đặt',
    icon: 'settings-outline',
  },
  {
    id: 'help',
    title: 'Trợ giúp',
    icon: 'help-circle-outline',
  },
  {
    id: 'logout',
    title: 'Đăng xuất',
    icon: 'log-out-outline',
  },
];
