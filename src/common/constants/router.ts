import { ENV } from '../config/env';

export const API_CONFIG = {
  BASE_URL: ENV.API.BASE_URL,
  TIMEOUT: ENV.API.TIMEOUT,
} as const;

// API Routes
export const API_ROUTES = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    REFRESH_TOKEN: '/auth/refresh-token',
    CREATE_OTP: '/auth/create-otp',
    VERIFY_OTP: '/auth/verify-otp',
    FORGOT_PASSWORD: '/auth/forgot-password',
  },
  USER: {
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/profiles',
    UPLOAD_AVATAR: '/profiles/upload-avatar',
  },
  ANALYST: {
    ANALYST_ROLE: '/analyst/analyst-by-role-description',
    ANALYST_JD: '/analyst/analyst-by-jd-description',
    ANALYST_BY_ID: '/analyst/analyst/:id',
    MY_ANALYST: '/analyst/my-analyst',
    DELETE_ANALYST: '/analyst/:id',
  },
  INTERVIEW_QUESTION: {
    CREATE: '/interview/generate-question-interview',
    GET_MY_INTERVIEW_QUESTION: '/interview/get-my-question-interview',
    GET_INTERVIEW_QUESTION_BY_ID: '/interview/get-question-interview-by-id/:id',
    DELETE: '/interview/:id',
  },
  AI_CHAT: {
    SESSIONS: '/chat-ai/get-my-sessions',
    SESSION_BY_ID: (sessionId: string) => `/chat-ai/chat-session/${sessionId}`,
    DELETE_SESSION: (sessionId: string) => `/chat-ai/chat-session/${sessionId}`,
    SEND_MESSAGE: (sessionId: string) => `/chat-ai/chat-session/${sessionId}`,
  },
  CONVERSATION: {
    GET_MY_CONVERSATION: '/conversation/user',
    GET_CONVERSATION_BY_ID: (conversationId: string) =>
      `/conversation/${conversationId}`,
    CREATE_CONVERSATION: '/conversation',
    DELETE_CONVERSATION: (conversationId: string) =>
      `/conversation/${conversationId}`,
  },
} as const;

export const TOKEN_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
};

export const USER_KEYS = {
  USER_PROFILE: 'user_profile',
};

export const APP_KEYS = {
  IS_FIRST_LAUNCH: 'is_first_launch',
};
