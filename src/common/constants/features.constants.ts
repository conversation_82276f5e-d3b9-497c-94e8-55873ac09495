import { Ionicons } from '@expo/vector-icons';
import { HomeStackParamList } from '../types/rootParamList';
export type FeatureType =
  | 'mock-interview'
  | 'ai-chat'
  | 'interviews'
  | 'career-roadmap';

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconBgColor: string;
  type: FeatureType;
  navigate: keyof HomeStackParamList;
}

export const FEATURES: Feature[] = [
  {
    id: 'mock-interview',
    title: 'Mock Interview',
    description: 'Thực hành với AI',
    icon: 'mic',
    iconBgColor: 'bg-purple-500',
    type: 'mock-interview',
    navigate: 'MockInterview',
  },
  {
    id: 'ai-chat',
    title: 'AI Chat',
    description: 'Tư vấn trực tiếp',
    icon: 'chatbubbles',
    iconBgColor: 'bg-green-500',
    type: 'ai-chat',
    navigate: 'AIChat',
  },
  {
    id: 'interviews',
    title: 'AI Resume Analyzer',
    description: '<PERSON><PERSON><PERSON> gi<PERSON> hồ sơ',
    icon: 'document-text',
    iconBgColor: 'bg-orange-500',
    type: 'interviews',
    navigate: 'Analysis',
  },
  {
    id: 'career-roadmap',
    title: 'AI Roadmap',
    description: 'Lộ trình phát triển',
    icon: 'map',
    iconBgColor: 'bg-blue-500',
    type: 'career-roadmap',
    navigate: 'CareerRoadmap',
  },
];
