import {
  FileText,
  Users,
  Award,
  Brain,
  Bar<PERSON><PERSON>3,
  Target,
} from 'lucide-react-native';

export interface StatsItem {
  id: number;
  title: string;
  value: string;
  icon: any;
  color: string;
}

export interface RecentAnalysis {
  id: number;
  name: string;
  role: string;
  score: number;
  type: string;
  avatar: string;
}

export interface QuickTip {
  title: string;
  content: string;
}

export interface Feature {
  icon: any;
  title: string;
  description: string;
}

// MOCK DATA
export const STATS_DATA: StatsItem[] = [
  {
    id: 1,
    title: 'Total Analyses',
    value: '247',
    icon: FileText,
    color: '#6366f1',
  },
  {
    id: 2,
    title: 'Average Score',
    value: '1,234',
    icon: Users,
    color: '#10b981',
  },
  {
    id: 3,
    title: 'Success Rate',
    value: '94%',
    icon: Award,
    color: '#f59e0b',
  },
];

export const RECENT_ANALYSES: RecentAnalysis[] = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>hắng',
    role: 'FULL STACK DEVELOPER',
    score: 78,
    type: 'Role Analysis',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: 2,
    name: 'Sarah <PERSON>',
    role: 'UI/UX DESIGNER',
    score: 85,
    type: 'JD Analysis',
    avatar:
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  },
  {
    id: 3,
    name: 'Michael Chen',
    role: 'PRODUCT MANAGER',
    score: 92,
    type: 'Role Analysis',
    avatar:
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: 4,
    name: 'Emily Davis',
    role: 'DATA ANALYST',
    score: 71,
    type: 'JD Analysis',
    avatar:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
  },
];

export const QUICK_TIPS_DATA: QuickTip[] = [
  {
    title: 'Resume Format',
    content:
      "Use a clean, professional format with consistent fonts and spacing. Avoid fancy graphics unless you're in a creative field.",
  },
  {
    title: 'Keywords Optimization',
    content:
      'Include relevant keywords from the job description to pass through Applicant Tracking Systems (ATS).',
  },
  {
    title: 'Quantify Achievements',
    content:
      'Use numbers and metrics to demonstrate your impact. For example: "Increased sales by 25%" instead of "Improved sales".',
  },
];

export const FEATURES: Feature[] = [
  {
    icon: Brain,
    title: 'AI-Powered Analysis',
    description: 'Advanced resume scanning technology',
  },
  {
    icon: BarChart3,
    title: 'Detailed Scoring',
    description: 'Comprehensive performance metrics',
  },
  {
    icon: Target,
    title: 'Smart Recommendations',
    description: 'Personalized improvement suggestions',
  },
];


export const mockData = {
  overallScore: 15,
  
  sections: [
    {
      title: 'Overall Feedback',
      content:
        'Your resume demonstrates strong technical competence and relevant experience, but needs improvement in key areas. Focus on quantifying achievements and aligning your skills to the job requirements.',
      type: 'feedback',
    },
    {
      title: 'Summary',
      content:
        'The comparison shows partial alignment between your resume and the job description. Additional experience in key areas would strengthen your candidacy.',
      type: 'summary',
    },
  ],

  skills: [
    {
      name: 'Matching Points',
      items: [
        'Basic knowledge of HTML/CSS',
        'Experience with React',
        'Team collaboration skills',
      ],
      status: 'good',
    },
    {
      name: 'Missing Skills',
      items: ['Node.js', 'TypeScript', 'AWS', 'Docker'],
      status: 'bad',
    },
    {
      name: 'Missing Experience',
      items: [
        '5+ years of full-stack development',
        'Leading team of 5+ developers',
        'Agile/Scrum methodology',
      ],
      status: 'warning',
    },
  ],

  recommendations: [
    'Add specific metrics to your achievements',
    'Highlight relevant certifications',
    'Quantify your experience in years',
  ],

  contactInfo: [
    {
      label: "What's Good",
      items: ['Well-formatted contact information'],
      status: 'good',
    },
    {
      label: 'Needs Improvement',
      items: ['Missing LinkedIn profile'],
      status: 'bad',
    },
  ],

  experience: [
    {
      label: "What's Good",
      items: ['Well-formatted contact information'],
      status: 'good',
    },
    {
      label: 'Needs Improvement',
      items: ['Missing LinkedIn profile'],
      status: 'bad',
    },
  ],

  education: [
    {
      label: "What's Good",
      items: ['Well-formatted contact information'],
      status: 'good',
    },
    {
      label: 'Needs Improvement',
      items: ['Missing LinkedIn profile'],
      status: 'bad',
    },
  ],

  jobSkills: [
    {
      label: "What's Good",
      items: ['Well-formatted contact information'],
      status: 'good',
    },
    {
      label: 'Needs Improvement',
      items: ['Missing LinkedIn profile'],
      status: 'bad',
    },
  ],
};