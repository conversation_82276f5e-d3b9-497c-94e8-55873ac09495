export interface ApiSuccessResponse<T = any> {
  statusCode: number;
  message: string;
  data: T;
}

export interface ApiErrorResponse {
  statusCode: number;
  message: string | string[];
  error: string;
  timestamp: string;
  errorCode?: string;
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

// Type guards
export const isApiSuccess = <T = any>(
  response: ApiResponse<T>
): response is ApiSuccessResponse<T> => {
  return 'data' in response && response.statusCode < 400;
};

export const isApiError = (
  response: ApiResponse
): response is ApiErrorResponse => {
  return 'error' in response && response.statusCode >= 400;
};
