export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  EmailVerification: undefined;
  ResetPassword: undefined;
};

export type HomeStackParamList = {
  HomeMain: undefined;
  MockInterview: undefined;
  AIChat: undefined;
  CareerRoadmap: undefined;
  Analysis: undefined;
  Home: undefined;
  RoadmapDetail: undefined;
};

export type CareerRoadmapStackParamList = {
  CareerRoadmapMain: undefined;
  RoadmapDetail: { id: string };
  Home: undefined;
};

export type AnalysisStackParamList = {
  AnalystMain: undefined;
  UploadResume: undefined;
  UploadCVRole: undefined;
  UploadCVJD: undefined;
  AnalystRole: { analysisId: string };
  AnalystJD: { analystId: string };
  History: undefined;
  Home: undefined;
  Interviews: undefined;
};

export type MockInterviewStackParamList = {
  InterviewQuestion: undefined;
  CreateQuestion: undefined;
  InterviewQuestionDetail: { interviewId: string };
  StartInterview: { interviewId: string };
  FeedbackDetail: { conversationId: string };
};

export type RootStackParamList = {
  Auth: undefined;
  Home: undefined;
  Analysis: undefined;
  MockInterview: undefined;
  CareerRoadmap: undefined;
  Profile: undefined;
  LandingPage: undefined;
};
