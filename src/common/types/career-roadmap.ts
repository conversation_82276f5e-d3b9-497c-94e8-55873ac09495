export interface RoadmapNode {
  id: string;
  type: string;
  position: {
    x: number;
    y: number;
  };
  data: {
    title: string;
    description: string;
    link?: string;
    estimatedTime?: string;
  };
}

export interface RoadmapEdge {
  id: string;
  source: string;
  target: string;
}

export interface RoadmapContent {
  summary: string;
  steps?: string[];
  skills?: string[];
  analysis: string;
  roadmapTitle: string;
  description: string;
  duration: string;
  initialNodes?: RoadmapNode[];
  initialEdges?: RoadmapEdge[];
}

export interface ResponseRoadmapAiDto {
  _id: string;
  userId: string;
  content: RoadmapContent;
  agentType: string;
  cvFileUrl?: string;
  cvText?: string;
  jdFileUrl?: string;
  jdText?: string;
  roleDescription?: string;
  jobPosition?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateRoadmapByRoleRequest {
  roleDescription: string;
  cvFile: any;
}

export interface CreateRoadmapByJdRequest {
  cvFile: any;
  jdFile: any;
}

export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data: T;
}
