interface EnvConfig {
  API: {
    PROTOCOL: string;
    DOMAIN: string;
    PATH: string;
    TIMEOUT: number;
    BASE_URL: string;
  };
  APP: {
    NAME: string;
    VERSION: string;
  };
}

// Environment variables with fallbacks
const createEnvConfig = (): EnvConfig => {
  const protocol = process.env.EXPO_PUBLIC_API_PROTOCOL || '';
  const domain = process.env.EXPO_PUBLIC_API_DOMAIN || '';
  const path = process.env.EXPO_PUBLIC_API_PATH || '';
  const timeout = Number(process.env.EXPO_PUBLIC_API_TIMEOUT);

  return {
    API: {
      PROTOCOL: protocol,
      DOMAIN: domain,
      PATH: path,
      TIMEOUT: timeout,
      BASE_URL: `${protocol}://${domain}${path}`,
    },
    APP: {
      NAME: process.env.APP_NAME || 'Learn Vox Mobile',
      VERSION: process.env.APP_VERSION || '1.0.0',
    },
  };
};

export const ENV = createEnvConfig();

if (__DEV__) {
  console.log('Environment Config:', {
    baseUrl: ENV.API.BASE_URL,
    timeout: ENV.API.TIMEOUT,
  });
}
