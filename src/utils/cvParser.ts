export interface CVInfo {
  name: string;
  role: string;
}

export const extractCVInfo = (cvText: string): CVInfo => {
  if (!cvText) {
    return {
      name: 'Unknown',
      role: 'Unknown Role',
    };
  }

  const lines = cvText
    .trim()
    .split('\n')
    .filter((line) => line.trim() !== '');

  let name = 'Unknown';
  let role = 'Unknown Role';

  if (lines.length > 0) {
    const firstLine = lines[0].trim();
    if (firstLine && !firstLine.includes('@') && !firstLine.includes('http')) {
      name = firstLine;
    }
  }

  for (let i = 1; i < Math.min(lines.length, 5); i++) {
    const line = lines[i].trim();

    if (
      line.includes('@') ||
      line.includes('http') ||
      line.includes('github') ||
      line.includes('linkedin') ||
      /^\d/.test(line)
    ) {
      continue;
    }

    if (line.length > 0 && line.length < 100) {
      const upperLine = line.toUpperCase();
      const jobKeywords = [
        'DEVELOPER',
        'ENGINEER',
        'ANALYST',
        'MANAGER',
        'DESIGNER',
        'INTERNSHIP',
        'SENIOR',
        'JUNIOR',
        'LEAD',
        'SPECIALIST',
        'FRONT-END',
        'BACK-END',
        'FULL STACK',
        'FULL-STACK',
        'SOFTWARE',
        'WEB',
        'MOBILE',
        'DATA',
        'DEVOPS',
      ];

      const hasJobKeyword = jobKeywords.some((keyword) =>
        upperLine.includes(keyword),
      );

      if (hasJobKeyword || upperLine === line) {
        role = line;
        break;
      }
    }
  }

  return {
    name: name.replace(/[^\w\s\u00C0-\u024F\u1E00-\u1EFF]/g, '').trim(), // Loại bỏ ký tự đặc biệt, giữ lại tiếng Việt
    role: role.trim(),
  };
};

export const formatRole = (role: string): string => {
  if (!role || role === 'Unknown Role') {
    return 'Unknown Role';
  }

  if (role === role.toUpperCase()) {
    return role
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  return role;
};
