import { ConversationResponse } from '@/services/types/conversation';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';

export interface CalculatedInterviewStats {
  totalQuestions: number;
  totalFeedback: number;
  averageRating: number;
  averageRatingText: string;
}

export const formatLargeNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

export const getRatingLevel = (averageRating: number) => {
  if (averageRating >= 8.5) {
    return { level: 'Excellent', color: '#10b981', bgColor: '#dcfce7' };
  } else if (averageRating >= 7.0) {
    return { level: 'Good', color: '#3b82f6', bgColor: '#dbeafe' };
  } else if (averageRating >= 5.5) {
    return { level: 'Average', color: '#f59e0b', bgColor: '#fef3c7' };
  } else if (averageRating >= 3.0) {
    return { level: 'Needs Improvement', color: '#ef4444', bgColor: '#fee2e2' };
  } else {
    return { level: 'Poor', color: '#dc2626', bgColor: '#fecaca' };
  }
};

export const calculateInterviewQuestionHistory = (
  myInterviewQuestions: InterviewQuestionSessionResponse[],
  myConversations: ConversationResponse[],
): CalculatedInterviewStats => {
  if (myInterviewQuestions.length === 0) {
    return {
      totalQuestions: 0,
      totalFeedback: 0,
      averageRating: 0,
      averageRatingText: '0/10',
    };
  }
  const totalQuestions = myInterviewQuestions.reduce(
    (acc, question) => acc + question.totalQuestion,
    0,
  );

  const totalFeedback = myConversations.length;

  let averageRating = myConversations.reduce(
    (acc, conversation) =>
      acc +
      conversation.feedback.perQuestionFeedback.reduce(
        (acc, question) => acc + question.rating,
        0,
      ) /
        conversation.feedback.perQuestionFeedback.length,
    0,
  );
  averageRating = Math.round(averageRating);

  const averageRatingText = `${averageRating}/10`;

  return { totalQuestions, totalFeedback, averageRating, averageRatingText };
};
