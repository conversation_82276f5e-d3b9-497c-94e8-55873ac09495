import { activateKeepAwakeAsync, deactivateKeepAwake } from 'expo-keep-awake';

// Comprehensive Daily SDK Patch
console.log('🔧 Applying Daily SDK patches...');
// Method 1: Patch via module.exports override
try {
  const DailyModule = require('@daily-co/react-native-daily-js');

  if (DailyModule && DailyModule.default) {
    const OriginalDaily = DailyModule.default;

    // Override createCallObject static method
    if (OriginalDaily.createCallObject) {
      const originalCreateCallObject = OriginalDaily.createCallObject;
      OriginalDaily.createCallObject = function (options?: any) {
        console.log('🔧 Daily.createCallObject patched');
        const callObject = originalCreateCallObject.call(this, options);

        if (callObject && typeof callObject === 'object') {
          // Patch nativeUtils method
          const originalNativeUtils = callObject.nativeUtils;
          callObject.nativeUtils = function () {
            const utils = originalNativeUtils
              ? originalNativeUtils.call(this)
              : {};
            return {
              ...utils,
              setKeepDeviceAwake: (keep: boolean) => {
                console.log(
                  `📱 Daily nativeUtils.setKeepDeviceAwake patched: ${keep}`,
                );
                if (keep) {
                  activateKeepAwakeAsync().catch((err) =>
                    console.warn('⚠️ activateKeepAwakeAsync failed:', err),
                  );
                } else {
                  deactivateKeepAwake();
                }
              },
              setAudioMode: (mode: any) => {
                console.log('📱 Daily nativeUtils.setAudioMode patched:', mode);
                // Mock implementation - do nothing
              },
            };
          };
        }

        return callObject;
      };
    }

    // Patch constructor prototype
    if (OriginalDaily.prototype) {
      const originalNativeUtils = OriginalDaily.prototype.nativeUtils;
      OriginalDaily.prototype.nativeUtils = function () {
        const utils = originalNativeUtils ? originalNativeUtils.call(this) : {};
        return {
          ...utils,
          setKeepDeviceAwake: (keep: boolean) => {
            console.log(
              `📱 Daily prototype.nativeUtils.setKeepDeviceAwake patched: ${keep}`,
            );
            if (keep) {
              activateKeepAwakeAsync().catch((err) =>
                console.warn('⚠️ activateKeepAwakeAsync failed:', err),
              );
            } else {
              deactivateKeepAwake();
            }
          },
          setAudioMode: (mode: any) => {
            console.log(
              '📱 Daily prototype.nativeUtils.setAudioMode patched:',
              mode,
            );
            // Mock implementation - do nothing
          },
        };
      };
    }
  }
} catch (error) {
  console.warn('⚠️ Daily module patch failed:', error);
}

// Method 2: Global object patching
if (typeof global !== 'undefined') {
  const globalObj = global as any;

  // Mock Daily if not exists
  if (!globalObj.Daily) {
    globalObj.Daily = {
      createCallObject: () => ({
        nativeUtils: () => ({
          setKeepDeviceAwake: (keep: boolean) => {
            console.log(`📱 Mock Daily.setKeepDeviceAwake: ${keep}`);
            if (keep) {
              activateKeepAwakeAsync().catch(console.warn);
            } else {
              deactivateKeepAwake();
            }
          },
          setAudioMode: (mode: any) => {
            console.log('📱 Mock Daily.setAudioMode:', mode);
            // Mock implementation - do nothing
          },
        }),
        join: () => Promise.resolve(),
        leave: () => Promise.resolve(),
        destroy: () => Promise.resolve(),
        on: () => {},
        off: () => {},
      }),
    };
  }
}

// Method 3: Error handler override
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args: any[]) => {
  const message = args.join(' ');
  if (
    message.includes('setKeepDeviceAwake is not a function') ||
    message.includes('setAudioMode is not a function') ||
    message.includes('daily-js version') ||
    message.includes('is no longer supported')
  ) {
    console.warn('⚠️ Daily SDK function error suppressed:', message);
    return;
  }
  originalConsoleError.apply(console, args);
};

console.warn = (...args: any[]) => {
  const message = args.join(' ');
  if (
    message.includes('daily-js version') ||
    message.includes('is no longer supported') ||
    message.includes('[Reanimated]') ||
    message.includes('Writing to `value` during component render')
  ) {
    // Suppress these warnings
    return;
  }
  originalConsoleWarn.apply(console, args);
};

console.log('✅ Daily SDK patches applied successfully');

export {};
