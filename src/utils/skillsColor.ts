export const getItemContainerStyles = (status: any) => {
  switch (status) {
    case 'good':
      return {
        backgroundColor: '#F0FDF4',
        borderColor: '#10B981',
        borderWidth: 1,
        borderRadius: 20,
      };
    case 'bad':
      return {
        backgroundColor: '#FEF2F2',
        borderColor: '#EF4444',
        borderWidth: 1,
        borderRadius: 20,
      };
    case 'warning':
      return {
        backgroundColor: '#FEFCE8',
        borderColor: '#F59E0B',
        borderWidth: 1,
        borderRadius: 20,
      };
    default:
      return {
        backgroundColor: '#F9FAFB',
        borderColor: '#D1D5DB',
        borderWidth: 1,
        borderRadius: 20,
      };
  }
};

export const getTagStyles = (status: any) => {
  switch (status) {
    case 'bad':
      return {
        backgroundColor: '#FEE2E2',
        borderColor: '#EF4444',
        borderWidth: 1,
        borderRadius: 20,
      };
    default:
      return {
        backgroundColor: '#F3F4F6',
        borderColor: '#D1D5DB',
        borderWidth: 1,
        borderRadius: 20,
      };
  }
};
