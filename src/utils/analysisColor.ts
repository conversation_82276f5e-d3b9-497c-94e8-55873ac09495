// This function now returns simple identifiers that components can use with theme colors
export const getTypeColor = (type: any) => {
  switch (type) {
    case 'Role Analysis':
      return {
        bg: 'blue',
        text: 'blue',
        border: 'blue',
      };
    case 'JD Analysis':
      return {
        bg: 'green',
        text: 'green',
        border: 'green',
      };
    default:
      return {
        bg: 'gray',
        text: 'gray',
        border: 'gray',
      };
  }
};
