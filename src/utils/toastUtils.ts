import { Toast } from 'toastify-react-native';

interface ToastOptions {
  text1: string;
  text2?: string;
  visibilityTime?: number;
  position?: 'top' | 'bottom';
  autoHide?: boolean;
  showProgressBar?: boolean;
  topOffset?: number;
  theme?: 'dark' | 'light';
  onPress?: () => void;
  onShow?: () => void;
  onHide?: () => void;
}

const defaultOptions: Partial<ToastOptions> = {
  position: 'top',
  visibilityTime: 2000,
  autoHide: true,
  topOffset: 50,
  showProgressBar: true,
  theme: 'dark',
};

export const showToast = {
  success: (options: ToastOptions) => {
    Toast.show({
      type: 'success',
      ...defaultOptions,
      ...options,
    });
  },

  error: (options: ToastOptions) => {
    Toast.show({
      type: 'error',
      ...defaultOptions,
      ...options,
    });
  },

  warn: (options: ToastOptions) => {
    Toast.show({
      type: 'warn',
      ...defaultOptions,
      ...options,
    });
  },

  info: (options: ToastOptions) => {
    Toast.show({
      type: 'info',
      ...defaultOptions,
      ...options,
    });
  },
};

// Utility functions
export const showAuthToast = {
  success: (message: string) => showToast.success({ text1: message }),
  error: (message: string) => showToast.error({ text1: message }),
  warn: (message: string) => showToast.warn({ text1: message }),
  info: (message: string) => showToast.info({ text1: message }),
};
