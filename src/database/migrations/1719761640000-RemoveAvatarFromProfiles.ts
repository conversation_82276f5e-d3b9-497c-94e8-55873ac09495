import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveAvatarFromProfiles1719761640000
  implements MigrationInterface
{
  name = 'RemoveAvatarFromProfiles1719761640000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if avatar column exists before dropping it
    const avatarColumnExists = await queryRunner.hasColumn(
      'profiles',
      'avatar',
    );
    if (avatarColumnExists) {
      await queryRunner.query(`ALTER TABLE "profiles" DROP COLUMN "avatar"`);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if avatar column doesn't exist before adding it back
    const avatarColumnExists = await queryRunner.hasColumn(
      'profiles',
      'avatar',
    );
    if (!avatarColumnExists) {
      await queryRunner.query(
        `ALTER TABLE "profiles" ADD "avatar" character varying`,
      );
    }
  }
}
