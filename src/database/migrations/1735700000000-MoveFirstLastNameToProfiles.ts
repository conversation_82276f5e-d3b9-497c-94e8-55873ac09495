import { MigrationInterface, QueryRunner } from 'typeorm';

export class MoveFirstLastNameToProfiles1735700000000
  implements MigrationInterface
{
  name = 'MoveFirstLastNameToProfiles1735700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if columns already exist before adding them
    const firstNameExists = await queryRunner.hasColumn(
      'profiles',
      'firstName',
    );
    const lastNameExists = await queryRunner.hasColumn('profiles', 'lastName');

    // Add firstName and lastName columns to profiles table if they don't exist
    if (!firstNameExists) {
      await queryRunner.query(
        `ALTER TABLE "profiles" ADD "firstName" character varying`,
      );
    }
    if (!lastNameExists) {
      await queryRunner.query(
        `ALTER TABLE "profiles" ADD "lastName" character varying`,
      );
    }

    // Check if users table still has firstName/lastName columns for migration
    const usersFirstNameExists = await queryRunner.hasColumn(
      'users',
      'firstName',
    );
    const usersLastNameExists = await queryRunner.hasColumn(
      'users',
      'lastName',
    );

    // Only migrate data if users table still has the columns
    if (usersFirstNameExists || usersLastNameExists) {
      // Migrate existing data from users to profiles
      // First, ensure all users have a profile
      await queryRunner.query(`
        INSERT INTO "profiles" ("userId", "firstName", "lastName")
        SELECT u."id", 
               ${usersFirstNameExists ? 'u."firstName"' : 'NULL'}, 
               ${usersLastNameExists ? 'u."lastName"' : 'NULL'}
        FROM "users" u
        LEFT JOIN "profiles" p ON p."userId" = u."id"
        WHERE p."id" IS NULL
          AND (${usersFirstNameExists ? 'u."firstName" IS NOT NULL' : 'FALSE'} 
               OR ${usersLastNameExists ? 'u."lastName" IS NOT NULL' : 'FALSE'})
      `);

      // Update existing profiles with firstName and lastName from users
      await queryRunner.query(`
        UPDATE "profiles" 
        SET "firstName" = COALESCE(p."firstName", ${usersFirstNameExists ? 'u."firstName"' : 'NULL'}), 
            "lastName" = COALESCE(p."lastName", ${usersLastNameExists ? 'u."lastName"' : 'NULL'})
        FROM "users" u
        WHERE "profiles"."userId" = u."id"
          AND (${usersFirstNameExists ? 'u."firstName" IS NOT NULL' : 'FALSE'} 
               OR ${usersLastNameExists ? 'u."lastName" IS NOT NULL' : 'FALSE'})
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Move data back from profiles to users
    await queryRunner.query(`
      UPDATE "users" 
      SET "firstName" = p."firstName", "lastName" = p."lastName"
      FROM "profiles" p
      WHERE "users"."id" = p."userId"
        AND p."firstName" IS NOT NULL 
        AND p."lastName" IS NOT NULL
    `);

    // Remove firstName and lastName columns from profiles table
    await queryRunner.query(`ALTER TABLE "profiles" DROP COLUMN "lastName"`);
    await queryRunner.query(`ALTER TABLE "profiles" DROP COLUMN "firstName"`);
  }
}
