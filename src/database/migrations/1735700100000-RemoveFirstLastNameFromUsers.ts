import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveFirstLastNameFromUsers1735700100000
  implements MigrationInterface
{
  name = 'RemoveFirstLastNameFromUsers1735700100000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove firstName and lastName columns from users table since they're now in profiles
    const firstNameExists = await queryRunner.hasColumn('users', 'firstName');
    const lastNameExists = await queryRunner.hasColumn('users', 'lastName');

    if (firstNameExists) {
      await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "firstName"`);
    }

    if (lastNameExists) {
      await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "lastName"`);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back firstName and lastName columns to users table
    const firstNameExists = await queryRunner.hasColumn('users', 'firstName');
    const lastNameExists = await queryRunner.hasColumn('users', 'lastName');

    if (!firstNameExists) {
      await queryRunner.query(
        `ALTER TABLE "users" ADD "firstName" character varying`,
      );
    }

    if (!lastNameExists) {
      await queryRunner.query(
        `ALTER TABLE "users" ADD "lastName" character varying`,
      );
    }

    // Migrate data back from profiles to users
    await queryRunner.query(`
      UPDATE "users" 
      SET "firstName" = p."firstName", "lastName" = p."lastName"
      FROM "profiles" p
      WHERE "users"."id" = p."userId"
        AND p."firstName" IS NOT NULL 
        AND p."lastName" IS NOT NULL
    `);
  }
}
