import React from 'react';
import { View, Text, TouchableOpacity, Platform, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card } from '@/components/ui';
import * as DocumentPicker from 'expo-document-picker';
import { FieldError } from 'react-hook-form';
import { useThemeColors } from '@/hooks/useThemeColors';

interface FormFileUploadProps {
  label: string;
  description?: string;
  value?: DocumentPicker.DocumentPickerAsset | null;
  onChange: (file: DocumentPicker.DocumentPickerAsset | null) => void;
  error?: FieldError;
  maxSize?: number; // in MB
  accept?: string[];
  title?: string;
  fileDescription?: string;
  disabled?: boolean;
}

const FormFileUpload: React.FC<FormFileUploadProps> = ({
  label,
  description,
  value,
  onChange,
  error,
  maxSize = 5,
  accept = ['application/pdf'],
  title = Platform.OS === 'ios'
    ? 'Tap to select a file'
    : 'Select file from device',
  fileDescription = 'PDF, DOC, DOCX (Maximum 5MB)',
  disabled = false,
}) => {
  const themeColors = useThemeColors();
  const pickDocument = async () => {
    try {
      // Platform-specific document picker config
      const pickerConfig: DocumentPicker.DocumentPickerOptions = {
        type: accept,
        copyToCacheDirectory: true,
        multiple: false,
      };

      const result = await DocumentPicker.getDocumentAsync(pickerConfig);

      if (!result.canceled && result.assets[0]) {
        const file = result.assets[0];

        // Enhanced file validation
        if (file.size && file.size > maxSize * 1024 * 1024) {
          Alert.alert(
            'File Too Large',
            `Please select a file smaller than ${maxSize}MB. Current file: ${formatFileSize(file.size)}`,
            [{ text: 'OK', style: 'default' }],
          );
          return;
        }

        // Validate file type on Android (iOS handles this automatically)
        if (
          Platform.OS === 'android' &&
          accept.length > 0 &&
          file.mimeType &&
          !accept.includes(file.mimeType)
        ) {
          Alert.alert(
            'Invalid File Type',
            `Please select a valid file type: ${accept.join(', ')}`,
            [{ text: 'OK', style: 'default' }],
          );
          return;
        }

        onChange(file);
      }
    } catch (error) {
      console.error('Failed to pick document:', error);
      Alert.alert('Error', 'Failed to select file. Please try again.', [
        { text: 'OK', style: 'default' },
      ]);
    }
  };

  const formatFileSize = (bytes: number): string => {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  };

  const hasFile = !!value;

  return (
    <View className="mb-4">
      <Text
        className="font-medium mb-2"
        style={{ color: themeColors.text }}
      >
        {label}
        <Text className="text-red-500 ml-1">*</Text>
      </Text>

      {description && (
        <Text
          className="text-sm mb-3"
          style={{ color: themeColors.textSecondary }}
        >
          {description}
        </Text>
      )}

      <Card
        style={{
          borderWidth: 2,
          borderStyle: 'dashed',
          borderColor: error ? '#EF4444' : hasFile ? '#10B981' : '#93C5FD',
          backgroundColor: error ? '#FEF2F2' : hasFile ? '#F0FDF4' : themeColors.surface,
          padding: 24,
          borderRadius: 15,
          // Platform-specific shadow
          ...(Platform.OS === 'ios' && {
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
          }),
          ...(Platform.OS === 'android' && {
            elevation: 3,
          }),
        }}
      >
        <View className="items-center">
          <View className="mb-4">
            <View
              className={`w-16 h-16 ${error ? 'bg-red-100' : hasFile ? 'bg-green-100' : 'bg-blue-100'
                } rounded-full items-center justify-center`}
            >
              <Ionicons
                name={
                  error
                    ? 'alert-circle-outline'
                    : hasFile
                      ? 'checkmark-circle'
                      : 'cloud-upload-outline'
                }
                size={32}
                color={error ? '#EF4444' : hasFile ? '#10B981' : '#3B82F6'}
              />
            </View>
          </View>

          {hasFile && value ? (
            <View className="items-center">
              <Text className="text-green-700 font-semibold text-center mb-2 text-base">
                {value.name}
              </Text>
              <Text className="text-green-600 text-sm text-center mb-4">
                {value.size ? formatFileSize(value.size) : 'File selected'}
              </Text>
            </View>
          ) : (
            <>
              <Text
                className="font-semibold text-center mb-2 text-base"
                style={{ color: themeColors.text }}
              >
                {title}
              </Text>
              <Text
                className="text-base text-center mb-4"
                style={{ color: themeColors.textMuted }}
              >
                {fileDescription}
              </Text>
            </>
          )}

          <TouchableOpacity
            onPress={pickDocument}
            disabled={disabled}
            style={{
              backgroundColor: hasFile ? '#10B981' : themeColors.surface,
              borderWidth: 1,
              borderColor: error ? '#EF4444' : hasFile ? '#10B981' : '#93C5FD',
              paddingHorizontal: 24,
              paddingVertical: Platform.OS === 'android' ? 12 : 8, // Larger touch target on Android
              borderRadius: 8,
              opacity: disabled ? 0.5 : 1,
              // Improved touch feedback
              ...(Platform.OS === 'android' && {
                elevation: hasFile ? 2 : 0,
              }),
            }}
            activeOpacity={0.7}
          >
            <Text
              className="font-medium"
              style={{
                color: hasFile
                  ? 'white'
                  : error
                    ? '#EF4444'
                    : '#3B82F6'
              }}
            >
              {hasFile ? 'Change file' : 'Select file'}
            </Text>
          </TouchableOpacity>
        </View>
      </Card>

      {error && (
        <Text className="text-red-500 text-sm mt-2">{error.message}</Text>
      )}
    </View>
  );
};

export default FormFileUpload;
