import React from 'react';
import { View, Text } from 'react-native';
import { Button } from './Button';

interface ActionButtonsProps {
  onCancel: () => void;
  onSubmit: () => void;
  isLoading?: boolean;
  cancelText?: string;
  submitText?: string;
  loadingText?: string;
  disabled?: boolean;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  onCancel,
  onSubmit,
  isLoading = false,
  cancelText = 'Cancel',
  submitText = 'Continue',
  loadingText = 'Processing...',
  disabled = false,
}) => {
  return (
    <View className="flex-row space-x-3 mt-auto mb-4">
      <Button
        onPress={onCancel}
        className="flex-1 bg-white py-3 rounded-lg"
        style={{ borderWidth: 1, borderColor: '#93C5FD' }}
        disabled={isLoading || disabled}
      >
        <Text className="text-blue-700 font-medium text-center">
          {cancelText}
        </Text>
      </Button>
      <Button
        onPress={onSubmit}
        className="flex-1 py-3 rounded-lg ml-4"
        disabled={isLoading || disabled}
        style={{
          backgroundColor: isLoading || disabled ? '#9CA3AF' : '#2563EB',
        }}
      >
        <Text className="text-white font-medium text-center">
          {isLoading ? loadingText : submitText}
        </Text>
      </Button>
    </View>
  );
};
