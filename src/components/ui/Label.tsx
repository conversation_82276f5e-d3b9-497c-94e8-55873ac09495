import { ComponentPropsWithoutRef, ElementRef, forwardRef } from 'react';
import * as LabelPrimitive from '@rn-primitives/label';
import { cn } from '@/utils/utils';
import { useThemeColors } from '@/hooks/useThemeColors';

const Label = forwardRef<
  ElementRef<typeof LabelPrimitive.Text>,
  ComponentPropsWithoutRef<typeof LabelPrimitive.Text>
>(
  (
    { className, onPress, onLongPress, onPressIn, onPressOut, style, ...props },
    ref,
  ) => {
    const colors = useThemeColors();

    return (
      <LabelPrimitive.Root
        className="web:cursor-default"
        onPress={onPress}
        onLongPress={onLongPress}
        onPressIn={onPressIn}
        onPressOut={onPressOut}
      >
        <LabelPrimitive.Text
          ref={ref}
          className={cn(
            'text-sm native:text-base font-medium leading-none web:peer-disabled:cursor-not-allowed web:peer-disabled:opacity-70',
            className,
          )}
          style={[{ color: colors.text }, style]}
          {...props}
        />
      </LabelPrimitive.Root>
    );
  },
);
Label.displayName = LabelPrimitive.Root.displayName;

export { Label };
