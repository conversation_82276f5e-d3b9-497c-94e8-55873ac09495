import React from 'react';
import { View, Text, TouchableOpacity, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft } from 'lucide-react-native';

interface GradientHeaderProps {
  title: string;
  onBackPress: () => void;
  colors?: [string, string, ...string[]];
  height?: number;
}

export const GradientHeader: React.FC<GradientHeaderProps> = ({
  title,
  onBackPress,
  colors = ['#8B5CF6', '#A855F7', '#C084FC'],
  height = 130,
}) => {
  return (
    <LinearGradient
      colors={colors}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      className="px-4 py-10"
      style={{ height }}
    >
      <View className="flex-row items-center justify-between h-full">
        <TouchableOpacity
          onPress={onBackPress}
          className="absolute top-3 left-3 bg-white/20 rounded-full p-3 items-center justify-center z-10"
          activeOpacity={0.7}
          style={Platform.OS === 'ios' ? { top: 40 } : { top: 10 }}
          hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
        >
          <View pointerEvents="none">
            <ArrowLeft size={20} color="white" />
          </View>
        </TouchableOpacity>
        <Text className="flex-1 text-white text-center ml-6 text-2xl font-bold">
          {title}
        </Text>
      </View>
    </LinearGradient>
  );
};
