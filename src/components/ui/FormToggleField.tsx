import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Code2, Users, Briefcase, Puzzle, Crown } from 'lucide-react-native';
import { FieldError } from 'react-hook-form';
import { useThemeColors } from '@/hooks/useThemeColors';

interface Option {
  value: string;
  label: string;
  icon: string;
  color: string;
}

interface FormMultiSelectProps {
  label: string;
  description?: string;
  options: Option[];
  selectedValues: string[];
  onSelect: (values: string[]) => void;
  error?: FieldError;
  required?: boolean;
  disabled?: boolean;
}

const iconMap = {
  code: Code2,
  users: Users,
  briefcase: Briefcase,
  puzzle: Puzzle,
  crown: Crown,
};

const FormToggleField: React.FC<FormMultiSelectProps> = ({
  label,
  description,
  options,
  selectedValues,
  onSelect,
  error,
  required = false,
  disabled = false,
}) => {
  const themeColors = useThemeColors();

  const toggleSelection = (value: string) => {
    const newSelection = selectedValues.includes(value)
      ? selectedValues.filter((v) => v !== value)
      : [...selectedValues, value];
    onSelect(newSelection);
  };

  const renderIcon = (iconName: string, isSelected: boolean) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap];
    if (!IconComponent) return null;

    return (
      <IconComponent
        size={24}
        color={isSelected ? '#667eea' : themeColors.textMuted}
      />
    );
  };

  return (
    <View className="mb-4">
      <Text
        className="font-medium mb-2"
        style={{ color: themeColors.text }}
      >
        {label}
        {required && <Text className="text-red-500 ml-1">*</Text>}
      </Text>

      {description && (
        <Text
          className="text-sm mb-3"
          style={{ color: themeColors.textSecondary }}
        >
          {description}
        </Text>
      )}

      <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 10 }}>
        {options.map((option) => {
          const isSelected = selectedValues.includes(option.value);

          return (
            <TouchableOpacity
              key={option.value}
              onPress={() => toggleSelection(option.value)}
              disabled={disabled}
            >
              <View
                className="border-2 rounded-xl p-4 items-center"
                style={{
                  borderColor: isSelected ? '#667eea' : themeColors.border,
                  backgroundColor: isSelected ? '#DBE9FE' : themeColors.surface,
                }}
              >
                {renderIcon(option.icon, isSelected)}
                <Text
                  className="font-medium mt-2 text-center"
                  style={{
                    color: isSelected ? '#667eea' : themeColors.textMuted
                  }}
                >
                  {option.label}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>

      {error && (
        <Text className="text-red-500 text-sm mt-2">{error.message}</Text>
      )}
    </View>
  );
};

export default FormToggleField;
