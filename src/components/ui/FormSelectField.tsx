import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Pressable,
} from 'react-native';
import { ChevronDown, Check } from 'lucide-react-native';
import { FieldError } from 'react-hook-form';
import { useThemeColors } from '@/hooks/useThemeColors';

interface Option {
  value: string;
  label: string;
}

interface FormSelectProps {
  label: string;
  placeholder: string;
  options: Option[];
  value: string;
  onSelect: (value: string) => void;
  error?: FieldError;
  required?: boolean;
  disabled?: boolean;
}

const FormSelect: React.FC<FormSelectProps> = ({
  label,
  placeholder,
  options,
  value,
  onSelect,
  error,
  required = false,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const themeColors = useThemeColors();

  const selectedOption = options.find((opt) => opt.value === value);

  return (
    <View className="mb-4 relative z-10">
      <Text
        className="font-medium mb-2"
        style={{ color: themeColors.text }}
      >
        {label}
        {required && <Text className="text-red-500 ml-1">*</Text>}
      </Text>

      <TouchableOpacity
        onPress={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className="border rounded-xl px-4 py-3 flex-row justify-between items-center"
        style={{
          borderColor: error ? '#EF4444' : themeColors.border,
          backgroundColor: error ? '#FEF2F2' : themeColors.surface,
          opacity: disabled ? 0.5 : 1
        }}
      >
        <Text
          style={{
            color: selectedOption ? themeColors.text : themeColors.textMuted
          }}
        >
          {selectedOption ? selectedOption.label : placeholder}
        </Text>
        <ChevronDown
          size={20}
          color={themeColors.textMuted}
          style={{
            transform: [{ rotate: isOpen ? '180deg' : '0deg' }],
          }}
        />
      </TouchableOpacity>

      {error && (
        <Text className="text-red-500 text-sm mt-1">{error.message}</Text>
      )}

      {isOpen && (
        <>
          {/* Overlay để đóng dropdown khi tap bên ngoài */}
          <Pressable
            className="absolute top-0 left-0 right-0 bottom-0 z-0"
            onPress={() => setIsOpen(false)}
            style={{
              position: 'absolute',
              top: -1000,
              left: -1000,
              right: -1000,
              bottom: -1000,
            }}
          />

          {/* Dropdown menu */}
          <View
            className="absolute top-full left-0 right-0 z-20 rounded-xl shadow-lg mt-1 max-h-60"
            style={{
              backgroundColor: themeColors.surface,
              borderColor: themeColors.border,
              borderWidth: 1
            }}
          >
            <ScrollView
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
            >
              {options.map((item, index) => (
                <TouchableOpacity
                  key={item.value}
                  onPress={() => {
                    onSelect(item.value);
                    setIsOpen(false);
                  }}
                  className="px-4 py-3 flex-row justify-between items-center"
                  style={{
                    borderBottomWidth: index === options.length - 1 ? 0 : 1,
                    borderBottomColor: themeColors.borderLight,
                    borderTopLeftRadius: index === 0 ? 12 : 0,
                    borderTopRightRadius: index === 0 ? 12 : 0,
                    borderBottomLeftRadius: index === options.length - 1 ? 12 : 0,
                    borderBottomRightRadius: index === options.length - 1 ? 12 : 0,
                  }}
                >
                  <Text style={{ color: themeColors.text }}>{item.label}</Text>
                  {value === item.value && <Check size={20} color="#3B82F6" />}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </>
      )}
    </View>
  );
};

export default FormSelect;
