import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AntDesign, MaterialIcons } from '@expo/vector-icons';

interface ToastProps {
  text1?: string;
  text2?: string;
  onPress?: () => void;
}

export const CustomToast = {
  success: (props: ToastProps) => (
    <TouchableOpacity
      style={[styles.container, styles.successContainer]}
      onPress={props.onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, styles.successIconContainer]}>
        <AntDesign name="checkcircle" size={20} color="white" />
      </View>
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.whiteText]}>{props.text1}</Text>
        {props.text2 && (
          <Text style={[styles.text2, styles.whiteText]}>{props.text2}</Text>
        )}
      </View>
      <TouchableOpacity style={styles.closeButton}>
        <AntDesign name="close" size={16} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  ),

  error: (props: ToastProps) => (
    <TouchableOpacity
      style={[styles.container, styles.errorContainer]}
      onPress={props.onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, styles.errorIconContainer]}>
        <MaterialIcons name="error" size={20} color="white" />
      </View>
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.whiteText]}>{props.text1}</Text>
        {props.text2 && (
          <Text style={[styles.text2, styles.whiteText]}>{props.text2}</Text>
        )}
      </View>
      <TouchableOpacity style={styles.closeButton}>
        <AntDesign name="close" size={16} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  ),

  warn: (props: ToastProps) => (
    <TouchableOpacity
      style={[styles.container, styles.warningContainer]}
      onPress={props.onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, styles.warningIconContainer]}>
        <AntDesign name="warning" size={20} color="white" />
      </View>
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.whiteText]}>{props.text1}</Text>
        {props.text2 && (
          <Text style={[styles.text2, styles.whiteText]}>{props.text2}</Text>
        )}
      </View>
      <TouchableOpacity style={styles.closeButton}>
        <AntDesign name="close" size={16} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  ),

  info: (props: ToastProps) => (
    <TouchableOpacity
      style={[styles.container, styles.infoContainer]}
      onPress={props.onPress}
      activeOpacity={0.8}
    >
      <View style={[styles.iconContainer, styles.infoIconContainer]}>
        <AntDesign name="infocirlce" size={20} color="white" />
      </View>
      <View style={styles.textContainer}>
        <Text style={[styles.text1, styles.whiteText]}>{props.text1}</Text>
        {props.text2 && (
          <Text style={[styles.text2, styles.whiteText]}>{props.text2}</Text>
        )}
      </View>
      <TouchableOpacity style={styles.closeButton}>
        <AntDesign name="close" size={16} color="white" />
      </TouchableOpacity>
    </TouchableOpacity>
  ),
};

const styles = StyleSheet.create({
  progressBar: {
    height: 4,
    backgroundColor: 'white',
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    marginHorizontal: 16,
    marginTop: 10,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 7,
    minHeight: 60,
  },

  successContainer: {
    backgroundColor: '#4CAF50',
  },

  errorContainer: {
    backgroundColor: '#F44336',
  },

  warningContainer: {
    backgroundColor: '#FF9800',
  },

  infoContainer: {
    backgroundColor: '#2196F3',
  },

  iconContainer: {
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    width: 32,
    height: 32,
    borderRadius: 16,
  },

  successIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },

  errorIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },

  warningIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },

  infoIconContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },

  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },

  text1: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 20,
  },

  text2: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 2,
    lineHeight: 18,
    opacity: 0.9,
  },

  whiteText: {
    color: 'white',
  },

  closeButton: {
    padding: 4,
    marginLeft: 8,
    opacity: 0.8,
  },
});

export const toastConfig = {
  success: CustomToast.success,
  error: CustomToast.error,
  warn: CustomToast.warn,
  info: CustomToast.info,
};
