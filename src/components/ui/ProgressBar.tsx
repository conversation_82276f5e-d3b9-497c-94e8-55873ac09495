import React from 'react';
import { View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface ProgressBarProps {
  percentage: number;
  color: string;
}

const getGradientColors = (color: string): [string, string] => {
  switch (color) {
    case 'bg-green-500':
      return ['#10B981', '#059669'];
    case 'bg-blue-500':
      return ['#3B82F6', '#2563EB'];
    case 'bg-purple-500':
      return ['#8B5CF6', '#7C3AED'];
    case 'bg-red-500':
      return ['#EF4444', '#DC2626'];
    case 'bg-orange-500':
      return ['#F59E0B', '#D97706'];
    default:
      return ['#6B7280', '#4B5563'];
  }
};

export const ProgressBar: React.FC<ProgressBarProps> = ({
  percentage,
  color,
}) => (
  <View className="w-full bg-gray-100 rounded-full h-3 shadow-inner">
    <LinearGradient
      colors={getGradientColors(color)}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      className="h-3 rounded-full shadow-sm"
      style={{ width: `${percentage}%` }}
    />
  </View>
);
