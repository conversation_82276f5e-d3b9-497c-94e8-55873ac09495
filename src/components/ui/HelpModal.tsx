import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, Linking } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const HelpModal = ({ visible, onClose, navigation }: any) => {
  const [activeTab, setActiveTab] = useState('guidelines');

  const openSupportLink = () => {
    Linking.openURL('https://x.ai/help');
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View className="flex-1 justify-center items-center bg-black/50 px-4">
        <View className="bg-white rounded-2xl shadow-lg w-full max-w-sm p-6">
          {/* Header */}
          <View className="flex-row justify-between items-center mb-6">
            <Text className="text-xl font-bold text-gray-800">Trợ giúp</Text>
            <TouchableOpacity
              onPress={onClose}
              className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center"
            >
              <Ionicons name="close" size={16} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* Tabs */}
          <View className="flex-row justify-around mb-6 bg-gray-50 rounded-xl p-1">
            <TouchableOpacity
              onPress={() => setActiveTab('guidelines')}
              className="flex-1 py-3 px-3 rounded-lg items-center"
              style={{
                backgroundColor:
                  activeTab === 'guidelines' ? '#ffffff' : 'transparent',
                shadowColor:
                  activeTab === 'guidelines' ? '#000' : 'transparent',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: activeTab === 'guidelines' ? 0.1 : 0,
                shadowRadius: 2,
                elevation: activeTab === 'guidelines' ? 2 : 0,
              }}
            >
              <Ionicons
                name="document-text-outline"
                size={16}
                color={activeTab === 'guidelines' ? '#3B82F6' : '#9CA3AF'}
              />
              <Text
                className="text-sm font-medium mt-1"
                style={{
                  color: activeTab === 'guidelines' ? '#3B82F6' : '#9CA3AF',
                }}
              >
                Hướng dẫn
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setActiveTab('tips')}
              className="flex-1 py-3 px-3 rounded-lg items-center"
              style={{
                backgroundColor:
                  activeTab === 'tips' ? '#ffffff' : 'transparent',
                shadowColor: activeTab === 'tips' ? '#000' : 'transparent',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: activeTab === 'tips' ? 0.1 : 0,
                shadowRadius: 2,
                elevation: activeTab === 'tips' ? 2 : 0,
              }}
            >
              <Ionicons
                name="bulb-outline"
                size={16}
                color={activeTab === 'tips' ? '#3B82F6' : '#9CA3AF'}
              />
              <Text
                className="text-sm font-medium mt-1"
                style={{ color: activeTab === 'tips' ? '#3B82F6' : '#9CA3AF' }}
              >
                Mẹo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setActiveTab('support')}
              className="flex-1 py-3 px-3 rounded-lg items-center"
              style={{
                backgroundColor:
                  activeTab === 'support' ? '#ffffff' : 'transparent',
                shadowColor: activeTab === 'support' ? '#000' : 'transparent',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: activeTab === 'support' ? 0.1 : 0,
                shadowRadius: 2,
                elevation: activeTab === 'support' ? 2 : 0,
              }}
            >
              <Ionicons
                name="help-circle-outline"
                size={16}
                color={activeTab === 'support' ? '#3B82F6' : '#9CA3AF'}
              />
              <Text
                className="text-sm font-medium mt-1"
                style={{
                  color: activeTab === 'support' ? '#3B82F6' : '#9CA3AF',
                }}
              >
                Hỗ trợ
              </Text>
            </TouchableOpacity>
          </View>

          {/* Content */}
          {activeTab === 'guidelines' && (
            <View>
              <View className="bg-blue-50 rounded-xl p-4 mb-4">
                <View className="flex-row items-center mb-3">
                  <View className="w-8 h-8 bg-blue-100 rounded-full mt-2 items-center justify-center mr-3">
                    <Ionicons
                      name="cloud-upload-outline"
                      size={16}
                      color="#3B82F6"
                    />
                  </View>
                  <Text className="text-lg font-bold text-gray-800 mt-2">
                    Hướng dẫn tải lên CV/JD
                  </Text>
                </View>
              </View>

              <View className="space-y-3">
                <View className="bg-gray-50 rounded-lg p-3">
                  <View className="flex-row items-start">
                    <View className="w-5 h-5 bg-green-100 rounded-full items-center justify-center mr-3 mt-0.5">
                      <Ionicons name="checkmark" size={12} color="#059669" />
                    </View>
                    <Text className="text-gray-700 flex-1 text-sm leading-5">
                      Đảm bảo file có định dạng PDF, DOC, hoặc DOCX và dung
                      lượng không quá 10MB.
                    </Text>
                  </View>
                </View>

                <View className="bg-gray-50 rounded-lg p-3">
                  <View className="flex-row items-start">
                    <View className="w-5 h-5 bg-blue-100 rounded-full items-center justify-center mr-3 mt-0.5">
                      <Ionicons name="arrow-up" size={12} color="#3B82F6" />
                    </View>
                    <Text className="text-gray-700 flex-1 text-sm leading-5">
                      Kéo thả file hoặc nhấn 'Chọn file' để tải lên.
                    </Text>
                  </View>
                </View>

                <View className="bg-gray-50 rounded-lg p-3">
                  <View className="flex-row items-start">
                    <View className="w-5 h-5 bg-amber-100 rounded-full items-center justify-center mr-3 mt-0.5">
                      <Ionicons name="information" size={12} color="#D97706" />
                    </View>
                    <Text className="text-gray-700 flex-1 text-sm leading-5">
                      Nếu gặp khó khăn, hãy kiểm tra định dạng file hoặc liên hệ
                      hỗ trợ.
                    </Text>
                  </View>
                </View>
              </View>

              <TouchableOpacity
                onPress={onClose}
                className="bg-blue-500 py-3 px-6 rounded-lg self-center mt-6"
              >
                <Text className="text-white font-medium">Đóng</Text>
              </TouchableOpacity>
            </View>
          )}

          {activeTab === 'tips' && (
            <View>
              <View className="bg-amber-50 rounded-xl p-4 mb-4">
                <View className="flex-row items-center mb-3">
                  <View className="w-8 h-8 mt-2  bg-amber-100 rounded-full items-center justify-center mr-3">
                    <Ionicons name="star-outline" size={16} color="#D97706" />
                  </View>
                  <Text className="text-lg mt-2 font-bold text-gray-800">
                    Mẹo để tải lên thành công
                  </Text>
                </View>
              </View>

              <View className="space-y-3">
                <View className="bg-gray-50 rounded-lg p-3">
                  <View className="flex-row items-start">
                    <View className="w-5 h-5 bg-purple-100 rounded-full items-center justify-center mr-3 mt-0.5">
                      <Text className="text-purple-600 text-xs font-bold">
                        1
                      </Text>
                    </View>
                    <Text className="text-gray-700 flex-1 text-sm leading-5">
                      Bao gồm thông tin cá nhân và kỹ năng chính trong CV.
                    </Text>
                  </View>
                </View>

                <View className="bg-gray-50 rounded-lg p-3">
                  <View className="flex-row items-start">
                    <View className="w-5 h-5 bg-purple-100 rounded-full items-center justify-center mr-3 mt-0.5">
                      <Text className="text-purple-600 text-xs font-bold">
                        2
                      </Text>
                    </View>
                    <Text className="text-gray-700 flex-1 text-sm leading-5">
                      Đảm bảo JD mô tả rõ yêu cầu công việc.
                    </Text>
                  </View>
                </View>

                <View className="bg-gray-50 rounded-lg p-3">
                  <View className="flex-row items-start">
                    <View className="w-5 h-5 bg-purple-100 rounded-full items-center justify-center mr-3 mt-0.5">
                      <Text className="text-purple-600 text-xs font-bold">
                        3
                      </Text>
                    </View>
                    <Text className="text-gray-700 flex-1 text-sm leading-5">
                      Kiểm tra lại file trước khi phân tích.
                    </Text>
                  </View>
                </View>
              </View>

              <View className="flex-row justify-between mt-6">
                <TouchableOpacity
                  onPress={onClose}
                  className="bg-blue-500 py-3 px-6 rounded-lg"
                >
                  <Text className="text-white font-medium">OK</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    onClose();
                    navigation.navigate('SampleScreen');
                  }}
                  className="bg-gray-200 py-3 px-4 rounded-lg flex-row items-center"
                >
                  <Ionicons name="eye-outline" size={16} color="#6B7280" />
                  <Text className="text-gray-700 font-medium ml-2">
                    Xem mẫu
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {activeTab === 'support' && (
            <View>
              <View className="bg-green-50 rounded-xl p-4 mb-4">
                <View className="flex-row items-center mb-3">
                  <View className="w-8 h-8 mt-2 bg-green-100 rounded-full items-center justify-center mr-3">
                    <Ionicons
                      name="headset-outline"
                      size={16}
                      color="#059669"
                    />
                  </View>
                  <Text className="text-lg mt-2 font-bold text-gray-800">
                    Cần trợ giúp?
                  </Text>
                </View>
              </View>

              <View className="space-y-3">
                <View className="bg-gray-50 rounded-lg p-4">
                  <View className="flex-row items-center">
                    <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-3">
                      <Ionicons name="mail-outline" size={16} color="#3B82F6" />
                    </View>
                    <View className="flex-1">
                      <Text className="text-gray-800 font-medium text-sm">
                        Email hỗ trợ
                      </Text>
                      <Text className="text-blue-600 text-xs mt-1">
                        learnvox@.ai
                      </Text>
                    </View>
                  </View>
                </View>

                <TouchableOpacity
                  onPress={openSupportLink}
                  className="bg-gray-50 rounded-lg p-4"
                >
                  <View className="flex-row items-center">
                    <View className="w-8 h-8 bg-green-100 rounded-full items-center justify-center mr-3">
                      <Ionicons
                        name="globe-outline"
                        size={16}
                        color="#059669"
                      />
                    </View>
                    <View className="flex-1">
                      <Text className="text-gray-800 font-medium text-sm">
                        Trang trợ giúp
                      </Text>
                      <Text className="text-green-600 text-xs mt-1">
                        https://learnvox-ai/help
                      </Text>
                    </View>
                    <Ionicons name="open-outline" size={16} color="#9CA3AF" />
                  </View>
                </TouchableOpacity>
              </View>

              <View className="flex-row justify-between mt-6">
                <TouchableOpacity
                  onPress={onClose}
                  className="bg-blue-500 py-3 px-6 rounded-lg"
                >
                  <Text className="text-white font-medium">Đóng</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={openSupportLink}
                  className="bg-gray-200 py-3 px-4 rounded-lg flex-row items-center"
                >
                  <Ionicons name="open-outline" size={16} color="#6B7280" />
                  <Text className="text-gray-700 font-medium ml-2">
                    Mở trang hỗ trợ
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default HelpModal;
