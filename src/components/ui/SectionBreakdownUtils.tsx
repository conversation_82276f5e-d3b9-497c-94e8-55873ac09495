import { Ionicons } from '@expo/vector-icons';
import { sectionBreakdownStyles } from '@/styles/SectionBreakdown.styles';

export const getIconContainerStyle = (sectionType: string) => {
  switch (sectionType) {
    case 'contact':
      return sectionBreakdownStyles.contactIconContainer;
    case 'experience':
      return sectionBreakdownStyles.experienceIconContainer;
    case 'education':
      return sectionBreakdownStyles.educationIconContainer;
    case 'skills':
      return sectionBreakdownStyles.skillsIconContainer;
    default:
      return sectionBreakdownStyles.contactIconContainer;
  }
};

export const getProgressBarStyle = (sectionType: string) => {
  switch (sectionType) {
    case 'contact':
      return sectionBreakdownStyles.contactProgressBar;
    case 'experience':
      return sectionBreakdownStyles.experienceProgressBar;
    case 'education':
      return sectionBreakdownStyles.educationProgressBar;
    case 'skills':
      return sectionBreakdownStyles.skillsProgressBar;
    default:
      return sectionBreakdownStyles.contactProgressBar;
  }
};

export const getIcon = (sectionType: string) => {
  const iconColor = '#FFFFFF';
  const iconSize = 18;

  switch (sectionType) {
    case 'contact':
      return <Ionicons name="person" size={iconSize} color={iconColor} />;
    case 'experience':
      return <Ionicons name="briefcase" size={iconSize} color={iconColor} />;
    case 'education':
      return <Ionicons name="school" size={iconSize} color={iconColor} />;
    case 'skills':
      return <Ionicons name="flash" size={iconSize} color={iconColor} />;
    default:
      return <Ionicons name="person" size={iconSize} color={iconColor} />;
  }
};
