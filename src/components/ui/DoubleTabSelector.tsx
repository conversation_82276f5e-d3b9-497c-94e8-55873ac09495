import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { FileText, MessageSquareText } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface DoubleTabSelectorProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  countItem1: number;
  countItem2: number;
  labelItem1: string;
  labelItem2: string;
  listType: string[];
}

const DoubleTabSelector: React.FC<DoubleTabSelectorProps> = ({
  activeTab,
  onTabChange,
  countItem1,
  countItem2,
  labelItem1,
  labelItem2,
  listType,
}) => {
  const colors = useThemeColors();

  return (
    <View style={{ backgroundColor: colors.background, borderBottomColor: colors.border }} className="border-b px-6 py-4">
      <View style={{ backgroundColor: colors.cardBackground }} className="flex-row rounded-xl p-1">
        {/* Interviews Tab */}
        <TouchableOpacity
          onPress={() => onTabChange(listType[0])}
          style={{
            backgroundColor: activeTab === listType[0] ? colors.background : 'transparent'
          }}
          className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-lg ${activeTab === listType[0] ? 'shadow-sm' : ''
            }`}
        >
          <FileText
            size={18}
            color={activeTab === listType[0] ? '#6366F1' : '#6B7280'}
          />
          <Text
            style={{
              color: activeTab === listType[0] ? '#6366F1' : colors.text
            }}
            className="ml-2 font-semibold"
          >
            {labelItem1}
          </Text>
          <View
            style={{
              backgroundColor: activeTab === listType[0] ? '#E0E7FF' : colors.surfaceSecondary
            }}
            className="ml-2 px-2 py-0.5 rounded-full"
          >
            <Text
              style={{
                color: activeTab === listType[0] ? '#6366F1' : colors.text
              }}
              className="text-xs font-bold"
            >
              {countItem1}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Feedbacks Tab */}
        <TouchableOpacity
          onPress={() => onTabChange(listType[1])}
          style={{
            backgroundColor: activeTab === listType[1] ? colors.background : 'transparent'
          }}
          className={`flex-1 flex-row items-center justify-center py-3 px-4 rounded-lg ${activeTab === listType[1] ? 'shadow-sm' : ''
            }`}
        >
          <MessageSquareText
            size={18}
            color={activeTab === listType[1] ? '#9333EA' : '#6B7280'}
          />
          <Text
            style={{
              color: activeTab === listType[1] ? '#9333EA' : colors.text
            }}
            className="ml-2 font-semibold"
          >
            {labelItem2}
          </Text>
          <View
            style={{
              backgroundColor: activeTab === listType[1] ? '#F3E8FF' : colors.surfaceSecondary
            }}
            className="ml-2 px-2 py-0.5 rounded-full"
          >
            <Text
              style={{
                color: activeTab === listType[1] ? '#9333EA' : colors.text
              }}
              className="text-xs font-bold"
            >
              {countItem2}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default DoubleTabSelector;
