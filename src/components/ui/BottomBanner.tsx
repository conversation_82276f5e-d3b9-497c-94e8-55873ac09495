import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card } from './Card';
import { Button } from './Button';
import { LinearGradient } from 'expo-linear-gradient';

interface BottomBannerProps {
  title: string;
  subtitle: string;
  buttonText: string;
  onButtonPress?: () => void;
  iconName?: keyof typeof Ionicons.glyphMap;
  gradientColors?: [string, string, ...string[]];
}

const BottomBanner: React.FC<BottomBannerProps> = ({
  title,
  subtitle,
  buttonText,
  onButtonPress,
  iconName = 'sparkles',
  gradientColors = ['#1E40AF', '#3B82F6', '#60A5FA'],
}) => {
  return (
    <Card
      className="mt-6 mb-8 rounded-2xl overflow-hidden border-0"
      style={{
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.15,
        shadowRadius: 20,
        elevation: 12,
      }}
    >
      <LinearGradient
        colors={gradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        className="p-8"
      >
        <View className="items-center">
          <View className="bg-white/20 p-4 rounded-full mb-4">
            <Ionicons name={iconName} size={32} color="white" />
          </View>
          <Text className="text-white text-2xl font-bold text-center mb-2">
            {title}
          </Text>
          <Text className="text-white opacity-80 text-center mt-1">
            {subtitle}
          </Text>
          <Button
            className="mt-4 bg-white px-6 py-3 rounded-lg w-full"
            onPress={onButtonPress}
          >
            <Text className="text-blue-600 font-semibold">{buttonText}</Text>
          </Button>
        </View>
      </LinearGradient>
    </Card>
  );
};

export default BottomBanner;
