import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { ChevronLeft, ChevronRight } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  disabled?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  disabled = false,
}) => {
  const colors = useThemeColors();
  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  if (totalPages <= 1) return null;

  const visiblePages = getVisiblePages();

  return (
    <View className="flex-row justify-center items-center py-4" style={{ backgroundColor: colors.surface }}>
      {/* Previous button */}
      <TouchableOpacity
        onPress={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1 || disabled}
        className="w-10 h-10 rounded-lg flex-row justify-center items-center mx-1"
        style={{
          backgroundColor: currentPage === 1 || disabled ? colors.surfaceSecondary : colors.surface,
          borderWidth: currentPage === 1 || disabled ? 0 : 1,
          borderColor: colors.border,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: currentPage === 1 || disabled ? 0 : 0.05,
          shadowRadius: 2,
          elevation: currentPage === 1 || disabled ? 0 : 1,
        }}
      >
        <ChevronLeft
          size={16}
          color={currentPage === 1 || disabled ? colors.textSecondary : colors.text}
        />
      </TouchableOpacity>

      {/* Page numbers */}
      {visiblePages.map((page, index) => {
        if (page === '...') {
          return (
            <View
              key={`dots-${index}`}
              className="w-10 h-10 justify-center items-center"
            >
              <Text style={{ color: colors.textSecondary }}>...</Text>
            </View>
          );
        }

        const pageNumber = page as number;
        const isActive = pageNumber === currentPage;

        return (
          <TouchableOpacity
            key={pageNumber}
            onPress={() => onPageChange(pageNumber)}
            disabled={disabled}
            className="w-10 h-10 rounded-lg flex-row justify-center items-center mx-1"
            style={{
              backgroundColor: isActive
                ? colors.primary
                : disabled
                  ? colors.surfaceSecondary
                  : colors.surface,
              borderWidth: isActive || disabled ? 0 : 1,
              borderColor: colors.border,
              shadowColor: colors.shadow,
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: isActive || disabled ? 0 : 0.05,
              shadowRadius: 2,
              elevation: isActive || disabled ? 0 : 1,
            }}
          >
            <Text
              className="text-sm font-medium"
              style={{
                color: isActive
                  ? 'white'
                  : disabled
                    ? colors.textSecondary
                    : colors.text
              }}
            >
              {pageNumber}
            </Text>
          </TouchableOpacity>
        );
      })}

      {/* Next button */}
      <TouchableOpacity
        onPress={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages || disabled}
        className="w-10 h-10 rounded-lg flex-row justify-center items-center mx-1"
        style={{
          backgroundColor: currentPage === totalPages || disabled ? colors.surfaceSecondary : colors.surface,
          borderWidth: currentPage === totalPages || disabled ? 0 : 1,
          borderColor: colors.border,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: currentPage === totalPages || disabled ? 0 : 0.05,
          shadowRadius: 2,
          elevation: currentPage === totalPages || disabled ? 0 : 1,
        }}
      >
        <ChevronRight
          size={16}
          color={currentPage === totalPages || disabled ? colors.textSecondary : colors.text}
        />
      </TouchableOpacity>
    </View>
  );
};

export default Pagination;
