import React from 'react';
import { Mo<PERSON>, ScrollView, View, Text, TouchableOpacity } from 'react-native';
import { H1, P } from '@/components/ui';
import { FontAwesome6 } from '@expo/vector-icons';
interface PrivacyPolicyModalProps {
  visible: boolean;
  onClose: () => void;
}

const PrivacyPolicyModal = ({ visible, onClose }: PrivacyPolicyModalProps) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View className="flex-1 bg-white">
        {/* Header */}
        <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
          <H1 className="flex-1 text-center">Chính sách bảo mật</H1>
          <TouchableOpacity onPress={onClose} className="p-2">
            <FontAwesome6 name="xmark" size={20} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView className="flex-1 p-4">
          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">
              1. Thông tin chúng tôi thu thập
            </Text>
            <P className="mb-4 leading-6">
              Chúng tôi thu thập các loại thông tin sau để cung cấp và cải thiện
              dịch vụ của mình:
            </P>
            <View className="ml-4 mb-4">
              <P className="mb-2">
                • <Text className="font-semibold">Thông tin cá nhân:</Text> Họ
                tên, địa chỉ email, số điện thoại
              </P>
              <P className="mb-2">
                • <Text className="font-semibold">Thông tin tài khoản:</Text>
                Tên đăng nhập, mật khẩu (được mã hóa)
              </P>
              <P className="mb-2">
                • <Text className="font-semibold">Thông tin sử dụng:</Text> Cách
                bạn tương tác với ứng dụng
              </P>
              <P className="mb-2">
                • <Text className="font-semibold">Thông tin thiết bị:</Text>
                Loại thiết bị, hệ điều hành, địa chỉ IP
              </P>
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">
              2. Cách chúng tôi sử dụng thông tin
            </Text>
            <P className="mb-4 leading-6">
              Chúng tôi sử dụng thông tin thu thập được để:
            </P>
            <View className="ml-4 mb-4">
              <P className="mb-2">
                • Cung cấp và duy trì dịch vụ của chúng tôi
              </P>
              <P className="mb-2">
                • Cải thiện và phát triển các tính năng mới
              </P>
              <P className="mb-2">• Liên lạc với bạn về dịch vụ và cập nhật</P>
              <P className="mb-2">• Đảm bảo an toàn và bảo mật</P>
              <P className="mb-2">• Tuân thủ các yêu cầu pháp lý</P>
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">
              3. Chia sẻ thông tin
            </Text>
            <P className="mb-4 leading-6">
              Chúng tôi không bán, cho thuê hoặc chia sẻ thông tin cá nhân của
              bạn với bên thứ ba, trừ trong các trường hợp sau:
            </P>
            <View className="ml-4 mb-4">
              <P className="mb-2">• Khi có sự đồng ý rõ ràng từ bạn</P>
              <P className="mb-2">
                • Để tuân thủ yêu cầu pháp lý hoặc quy trình pháp lý
              </P>
              <P className="mb-2">
                • Để bảo vệ quyền lợi, tài sản hoặc an toàn của chúng tôi
              </P>
              <P className="mb-2">
                • Với các nhà cung cấp dịch vụ đáng tin cậy (dưới hợp đồng bảo
                mật)
              </P>
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">
              4. Bảo mật dữ liệu
            </Text>
            <P className="mb-4 leading-6">
              Chúng tôi thực hiện các biện pháp bảo mật kỹ thuật và tổ chức phù
              hợp để bảo vệ thông tin cá nhân của bạn khỏi truy cập trái phép,
              thay đổi, tiết lộ hoặc phá hủy.
            </P>
            <View className="ml-4 mb-4">
              <P className="mb-2">
                • Mã hóa dữ liệu trong quá trình truyền tải
              </P>
              <P className="mb-2">
                • Lưu trữ an toàn trên máy chủ được bảo mật
              </P>
              <P className="mb-2">• Kiểm soát truy cập nghiêm ngặt</P>
              <P className="mb-2">
                • Giám sát và kiểm tra bảo mật thường xuyên
              </P>
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">5. Quyền của bạn</Text>
            <P className="mb-4 leading-6">
              Bạn có các quyền sau đối với thông tin cá nhân của mình:
            </P>
            <View className="ml-4 mb-4">
              <P className="mb-2">• Quyền truy cập và xem thông tin cá nhân</P>
              <P className="mb-2">• Quyền chỉnh sửa hoặc cập nhật thông tin</P>
              <P className="mb-2">• Quyền xóa tài khoản và dữ liệu</P>
              <P className="mb-2">• Quyền từ chối nhận thông tin quảng cáo</P>
              <P className="mb-2">• Quyền khiếu nại về việc xử lý dữ liệu</P>
            </View>
          </View>

          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">
              6. Cookies và Công nghệ theo dõi
            </Text>
            <P className="mb-4 leading-6">
              Chúng tôi có thể sử dụng cookies và các công nghệ tương tự để cải
              thiện trải nghiệm người dùng, phân tích cách sử dụng dịch vụ và
              cung cấp nội dung phù hợp.
            </P>
          </View>

          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">
              7. Thay đổi Chính sách
            </Text>
            <P className="mb-4 leading-6">
              Chúng tôi có thể cập nhật Chính sách Bảo mật này theo thời gian.
              Chúng tôi sẽ thông báo cho bạn về bất kỳ thay đổi quan trọng nào
              bằng cách đăng chính sách mới trên ứng dụng.
            </P>
          </View>

          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3">8. Liên hệ</Text>
            <P className="mb-4 leading-6">
              Nếu bạn có bất kỳ câu hỏi nào về Chính sách Bảo mật này hoặc muốn
              thực hiện các quyền của mình, vui lòng liên hệ:
            </P>
            <View className="ml-4 mb-4">
              <P className="mb-2">Email: <EMAIL></P>
              <P className="mb-2">Điện thoại: 1900-xxxx</P>
            </View>
          </View>

          <View className="mb-8">
            <P className="text-gray-600 text-sm">
              Chính sách này có hiệu lực từ ngày 18/06/2025
            </P>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

export default PrivacyPolicyModal;
