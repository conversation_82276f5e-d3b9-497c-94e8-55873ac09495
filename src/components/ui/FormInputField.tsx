import React from 'react';
import { View, Text, TextInput, TextInputProps } from 'react-native';
import { FieldError } from 'react-hook-form';
import { useThemeColors } from '@/hooks/useThemeColors';

interface FormInputProps extends TextInputProps {
  label: string;
  error?: FieldError;
  required?: boolean;
  disabled?: boolean;
}

const FormInput: React.FC<FormInputProps> = ({
  label,
  error,
  required = false,
  disabled = false,
  ...props
}) => {
  const themeColors = useThemeColors();

  return (
    <View className="mb-4">
      <Text
        className="font-medium mb-2"
        style={{ color: themeColors.text }}
      >
        {label}
        {required && <Text className="text-red-500 ml-1">*</Text>}
      </Text>

      <TextInput
        className="border rounded-xl px-4 py-3"
        style={{
          borderColor: error ? '#EF4444' : themeColors.border,
          backgroundColor: error ? '#FEF2F2' : themeColors.surface,
          color: themeColors.text,
          opacity: disabled ? 0.5 : 1
        }}
        placeholderTextColor={themeColors.textMuted}
        editable={!disabled}
        {...props}
      />

      {error && (
        <Text className="text-red-500 text-sm mt-1">{error.message}</Text>
      )}
    </View>
  );
};

export default FormInput;
