import React from 'react';
import { View, Text, TextInput, TouchableOpacity } from 'react-native';
import Animated, {
  FadeInDown,
  FadeIn,
  BounceIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import { Search, X } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
  resultCount?: number;
  resultLabel?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  searchQuery,
  onSearchChange,
  placeholder = 'Search...',
  resultCount,
  resultLabel = 'result',
}) => {
  const colors = useThemeColors();
  const searchFocusAnim = useSharedValue(0);

  const handleSearchFocus = () => {
    searchFocusAnim.value = withSpring(1, { damping: 15, stiffness: 150 });
  };

  const handleSearchBlur = () => {
    searchFocusAnim.value = withSpring(0, { damping: 15, stiffness: 150 });
  };

  const animatedSearchStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: interpolate(searchFocusAnim.value, [0, 1], [1, 1.02]) },
      ],
    };
  });

  const clearSearch = () => {
    onSearchChange('');
  };

  return (
    <Animated.View
      style={{ marginBottom: 24 }}
      entering={FadeInDown.delay(200).springify()}
    >
      <Animated.View entering={FadeInDown.delay(250).springify()}>
        <Animated.View
          style={[
            {
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: colors.surface,
              borderRadius: 25,
              paddingHorizontal: 16,
              paddingVertical: 12,
              borderWidth: 1,
              borderColor: colors.border,
            },
            animatedSearchStyle,
            {
              shadowColor: colors.shadow,
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.05,
              shadowRadius: 2,
              elevation: 0.6,
            },
          ]}
        >
          <Search size={18} color={colors.textMuted} style={{ marginRight: 12 }} />
          <TextInput
            style={{
              flex: 1,
              color: colors.text,
              fontSize: 16,
            }}
            placeholder={placeholder}
            placeholderTextColor={colors.textMuted}
            value={searchQuery}
            onChangeText={onSearchChange}
            onFocus={handleSearchFocus}
            onBlur={handleSearchBlur}
          />
          {searchQuery.length > 0 && (
            <AnimatedTouchableOpacity
              entering={BounceIn.duration(300)}
              onPress={clearSearch}
              style={{
                marginLeft: 8,
                padding: 4,
              }}
              activeOpacity={0.7}
            >
              <X size={16} color={colors.textMuted} />
            </AnimatedTouchableOpacity>
          )}
        </Animated.View>
      </Animated.View>

      {searchQuery.length > 0 && resultCount !== undefined && (
        <Animated.View
          style={{
            marginTop: 12,
            paddingHorizontal: 8
          }}
          entering={FadeIn.delay(100)}
        >
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}>
            <Animated.View
              style={{
                width: 8,
                height: 8,
                backgroundColor: colors.primary,
                borderRadius: 4,
                marginRight: 8,
              }}
              entering={BounceIn.delay(200)}
            />
            <Text style={{
              fontSize: 14,
              color: colors.textSecondary,
              fontWeight: '500',
            }}>
              {resultCount} {resultLabel}
              {resultCount !== 1 ? 's' : ''} found
              {searchQuery.length > 0 && (
                <Text style={{
                  color: colors.primary,
                  fontWeight: '600',
                }}>
                  {` for "${searchQuery}"`}
                </Text>
              )}
            </Text>
          </View>
        </Animated.View>
      )}
    </Animated.View>
  );
};
