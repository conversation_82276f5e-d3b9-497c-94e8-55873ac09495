import React, { useEffect } from 'react';
import { View, Image, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

interface SplashScreenProps {
  onFinish: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onFinish }) => {
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.8)).current;
  const sparkleRotate = React.useRef(new Animated.Value(0)).current;
  const starScale = React.useRef(new Animated.Value(0.5)).current;

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Continuous sparkle rotation
    Animated.loop(
      Animated.timing(sparkleRotate, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: true,
      }),
    ).start();

    // Continuous star pulsing
    Animated.loop(
      Animated.sequence([
        Animated.timing(starScale, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(starScale, {
          toValue: 0.5,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
    ).start();

    // Auto finish after 2.5 seconds
    const timer = setTimeout(() => {
      onFinish();
    }, 2500);

    return () => clearTimeout(timer);
  }, [fadeAnim, scaleAnim, sparkleRotate, starScale, onFinish]);

  const sparkleRotateInterpolate = sparkleRotate.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <SafeAreaView style={{ flex: 1 }} edges={['left', 'right']}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
      >
        {/* Floating decorative elements */}
        <Animated.View
          style={{
            position: 'absolute',
            top: 100,
            right: 40,
            opacity: 0.3,
            transform: [{ rotate: sparkleRotateInterpolate }],
          }}
        >
          <Ionicons name="sparkles" size={40} color="white" />
        </Animated.View>

        <Animated.View
          style={{
            position: 'absolute',
            top: 150,
            left: 40,
            opacity: 0.3,
            transform: [{ scale: starScale }],
          }}
        >
          <Ionicons name="star" size={32} color="white" />
        </Animated.View>

        {/* Main logo */}
        <Animated.View
          style={{
            alignItems: 'center',
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          }}
        >
          <View
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 80,
              padding: 20,
              shadowColor: '#fff',
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.5,
              shadowRadius: 20,
              elevation: 10,
            }}
          >
            <Image
              source={require('../../../assets/images/logo2.png')}
              style={{ width: 80, height: 80 }}
              resizeMode="contain"
            />
          </View>
        </Animated.View>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default SplashScreen;
