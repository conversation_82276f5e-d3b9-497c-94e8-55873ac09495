import { TouchableOpacity, Text, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '@/hooks/useThemeColors';

const ThemeToggleButton = () => {
  const { theme, mode, setTheme, isLoaded, ...colors } = useThemeColors();

  // No need for local state management, use ThemeProvider



  if (!isLoaded) {
    return (
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 16,
        paddingHorizontal: 24,
        opacity: 0.5,
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <Ionicons name="color-palette-outline" size={24} color={colors.textSecondary} />
          <Text style={{
            marginLeft: 12,
            fontSize: 16,
            color: colors.textSecondary,
          }}>
            Theme
          </Text>
        </View>
        <Text style={{
          fontSize: 14,
          color: colors.textMuted,
        }}>
          Loading...
        </Text>
      </View>
    );
  }

  const getThemeIcon = () => {
    switch (mode) {
      case 'light':
        return 'sunny-outline';
      case 'dark':
        return 'moon-outline';
      case 'system':
        return 'phone-portrait-outline';
      default:
        return 'color-palette-outline';
    }
  };

  const getThemeLabel = () => {
    switch (mode) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return `Auto (${theme})`;
      default:
        return 'Theme';
    }
  };

  const cycleTheme = () => {
    const modes: Array<'light' | 'dark' | 'system'> = [
      'light',
      'dark',
      'system',
    ];
    const currentIndex = modes.indexOf(mode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setTheme(modes[nextIndex]);
  };

  return (
    <TouchableOpacity
      onPress={cycleTheme}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 16,
        paddingHorizontal: 24,
        borderRadius: 16,
      }}
      activeOpacity={0.7}
    >
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <Ionicons
          name={getThemeIcon()}
          size={24}
          color={colors.text}
        />
        <Text style={{
          marginLeft: 12,
          fontSize: 16,
          fontWeight: '500',
          color: colors.text,
        }}>
          Theme
        </Text>
      </View>

      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <Text style={{
          fontSize: 14,
          marginRight: 8,
          color: colors.textSecondary,
        }}>
          {getThemeLabel()}
        </Text>
        <Ionicons
          name="chevron-forward-outline"
          size={16}
          color={colors.textSecondary}
        />
      </View>
    </TouchableOpacity>
  );
};

export default ThemeToggleButton;
