import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from './Accordion';
import { QUICK_TIPS_DATA } from '@/common/constants/analyst.constant';
import { useThemeColors } from '@/hooks/useThemeColors';

const QuickTipsSection: React.FC = () => {
  const colors = useThemeColors();

  return (
    <View style={{
      paddingHorizontal: 24,
      paddingVertical: 24,
      backgroundColor: colors.surface
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
        <Ionicons name="bulb-outline" size={24} color={colors.warning} />
        <Text style={{
          marginLeft: 8,
          fontSize: 24,
          fontWeight: 'bold',
          color: colors.text
        }}>
          Quick Tips
        </Text>
      </View>

      <Text style={{
        color: colors.textSecondary,
        marginBottom: 16,
        fontSize: 18
      }}>
        Resume Best Practices
      </Text>

      <Accordion type="single" collapsible style={{ width: '100%' }}>
        <AccordionItem
          value="tips-guide"
          style={{
            borderWidth: 1,
            borderColor: colors.border,
            borderRadius: 12
          }}
        >
          <AccordionTrigger style={{ paddingHorizontal: 16, paddingVertical: 16 }}>
            <Text style={{
              color: colors.primary,
              fontWeight: '600',
              fontSize: 18
            }}>
              Read our complete guide
            </Text>
          </AccordionTrigger>
          <AccordionContent style={{ paddingHorizontal: 16, paddingBottom: 16 }}>
            {QUICK_TIPS_DATA.map((tip, index) => (
              <View
                key={index}
                style={{
                  paddingVertical: 16,
                  borderBottomWidth: index !== QUICK_TIPS_DATA.length - 1 ? 1 : 0,
                  borderBottomColor: colors.border,
                }}
              >
                <Text style={{
                  fontWeight: '600',
                  color: colors.text,
                  marginBottom: 8,
                  fontSize: 18
                }}>
                  {tip.title}
                </Text>
                <Text style={{
                  color: colors.textSecondary,
                  lineHeight: 20
                }}>
                  {tip.content}
                </Text>
              </View>
            ))}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </View>
  );
};

export default QuickTipsSection;
