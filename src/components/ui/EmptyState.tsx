import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Animated, {
  FadeInUp,
  BounceIn,
  FadeIn,
  SlideInRight,
} from 'react-native-reanimated';
import { useThemeColors } from '@/hooks/useThemeColors';

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

interface EmptyStateProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  buttonText?: string;
  onButtonPress?: () => void;
  delay?: number;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  buttonText,
  onButtonPress,
  delay = 400,
}) => {
  const colors = useThemeColors();

  return (
    <Animated.View
      style={{
        backgroundColor: colors.surface,
        borderRadius: 24,
        padding: 40,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.border,
        shadowColor: colors.text,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.06,
        shadowRadius: 20,
        elevation: 10,
      }}
      entering={FadeInUp.delay(delay).springify()}
    >
      <Animated.View
        style={{
          width: 80,
          height: 80,
          backgroundColor: colors.primaryLight,
          borderRadius: 16,
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 24,
        }}
        entering={BounceIn.delay(delay + 200)}
      >
        {icon}
      </Animated.View>
      <Animated.Text
        style={{
          color: colors.text,
          fontWeight: 'bold',
          fontSize: 20,
          marginBottom: 12,
        }}
        entering={FadeIn.delay(delay + 300)}
      >
        {title}
      </Animated.Text>
      <Animated.Text
        style={{
          color: colors.textSecondary,
          textAlign: 'center',
          lineHeight: 24,
          marginBottom: 24,
          maxWidth: 280,
        }}
        entering={FadeIn.delay(delay + 400)}
      >
        {description}
      </Animated.Text>
      {buttonText && onButtonPress && (
        <AnimatedTouchableOpacity
          entering={SlideInRight.delay(delay + 500).springify()}
          onPress={onButtonPress}
          style={{
            backgroundColor: colors.primary,
            borderRadius: 16,
            paddingVertical: 16,
            paddingHorizontal: 32,
            shadowColor: colors.primary,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 6,
          }}
        >
          <Text style={{
            color: 'white',
            fontWeight: 'bold',
            fontSize: 16,
          }}>
            {buttonText}
          </Text>
        </AnimatedTouchableOpacity>
      )}
    </Animated.View>
  );
};
