import React, { useState } from 'react';
import { View, TextInput, Text, TouchableOpacity } from 'react-native';
import { Eye, EyeOff } from 'lucide-react-native';
import { cn } from '@/utils/utils';
import { ComponentPropsWithoutRef, ElementRef, forwardRef } from 'react';
import { Label } from './Label';

interface InputProps extends ComponentPropsWithoutRef<typeof TextInput> {
  label?: string;
  error?: string;
  type?: 'text' | 'password';
  placeholderClassName?: string;
  labelClassName?: string;
}

const Input = forwardRef<ElementRef<typeof TextInput>, InputProps>(
  (
    {
      className,
      placeholderClassName,
      labelClassName,
      label,
      error,
      type = 'text',
      ...props
    },
    ref,
  ) => {
    const [secureTextEntry, setSecureTextEntry] = useState<boolean>(true);
    const isPassword = type === 'password';

    return (
      <View className="mb-4">
        {label && <Label className={labelClassName}>{label}</Label>}

        <View className="relative ">
          <TextInput
            ref={ref}
            className={cn(
              'border border-gray-300 rounded-xl px-4 py-2 text-base',
              isPassword && 'pr-10',
              props.editable === false && 'opacity-50',
              className,
            )}
            placeholderClassName={cn(
              'text-muted-foreground',
              placeholderClassName,
            )}
            secureTextEntry={isPassword ? secureTextEntry : false}
            {...props}
          />
          {isPassword && (
            <TouchableOpacity
              onPress={() => setSecureTextEntry(!secureTextEntry)}
              className="absolute right-3 top-1/2 -translate-y-1/2"
            >
              {secureTextEntry ? (
                <EyeOff size={20} color="#6b7280" />
              ) : (
                <Eye size={20} color="#6b7280" />
              )}
            </TouchableOpacity>
          )}
        </View>

        {error && <Text className="text-red-500 mt-1 text-sm">{error}</Text>}
      </View>
    );
  },
);

Input.displayName = 'Input';

export { Input };
export type { InputProps };
