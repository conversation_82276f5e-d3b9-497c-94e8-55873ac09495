import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { WebView } from 'react-native-webview';

interface PDFPreviewProps {
  cvFileUrl?: string;
}

export const PDFPreview: React.FC<PDFPreviewProps> = ({ cvFileUrl }) => {
  const [pdfError, setPdfError] = useState(false);

  const handleOpenPDF = async () => {
    if (cvFileUrl) {
      try {
        const supported = await Linking.canOpenURL(cvFileUrl);
        if (supported) {
          await Linking.openURL(cvFileUrl);
        } else {
          console.log("Don't know how to open URI: " + cvFileUrl);
        }
      } catch (error) {
        console.error('Error opening PDF:', error);
      }
    }
  };

  if (!cvFileUrl) {
    return (
      <View className="p-12 items-center bg-gray-50 mx-4 mb-4 rounded-2xl border border-gray-200">
        <View className="bg-gray-200 p-6 rounded-full mb-4">
          <Ionicons name="document-outline" size={48} color="#9CA3AF" />
        </View>
        <Text className="text-gray-500 text-lg font-medium">
          No CV file available
        </Text>
        <Text className="text-gray-400 text-sm mt-1">
          Upload a resume to view preview
        </Text>
      </View>
    );
  }

  if (pdfError) {
    return (
      <View className="p-8 items-center bg-red-50 mx-4 mb-4 rounded-2xl border border-red-200">
        <View className="bg-red-100 p-6 rounded-full mb-4">
          <Ionicons name="alert-circle" size={48} color="#EF4444" />
        </View>
        <Text className="text-red-700 font-semibold text-lg mb-2">
          Unable to preview PDF
        </Text>
        <Text className="text-red-600 text-center text-sm mb-4">
          The PDF preview could not be loaded. You can still open the file
          directly.
        </Text>
        <TouchableOpacity
          onPress={handleOpenPDF}
          className="px-6 py-3 rounded-xl"
          style={{
            backgroundColor: '#EF4444',
            shadowColor: '#EF4444',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.25,
            shadowRadius: 4,
            elevation: 5,
          }}
        >
          <Text className="text-white font-bold">Open PDF</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const googleDocsUrl = `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(cvFileUrl)}`;

  return (
    <View
      className="mx-4 mb-4 rounded-2xl overflow-hidden border border-gray-200"
      style={{
        height: 350,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 6,
      }}
    >
      <LinearGradient
        colors={['#3B82F6', '#1D4ED8']}
        className="flex-row items-center justify-between p-4"
      >
        <View className="flex-row items-center">
          <Ionicons name="document-text" size={20} color="white" />
          <Text className="text-white font-semibold ml-2">PDF Preview</Text>
        </View>
        <TouchableOpacity
          onPress={handleOpenPDF}
          className="bg-white/20 px-4 py-2 rounded-lg flex-row items-center"
          activeOpacity={0.7}
        >
          <Ionicons name="open-outline" size={16} color="white" />
          <Text className="text-white text-sm ml-1 font-medium">Open</Text>
        </TouchableOpacity>
      </LinearGradient>

      <WebView
        source={{ uri: googleDocsUrl }}
        style={{ flex: 1, backgroundColor: '#F9FAFB' }}
        onError={() => setPdfError(true)}
        onHttpError={() => setPdfError(true)}
        startInLoadingState={true}
        renderLoading={() => (
          <View className="flex-1 justify-center items-center bg-gray-50">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-500 mt-3 font-medium">
              Loading PDF preview...
            </Text>
          </View>
        )}
        scalesPageToFit={true}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};
