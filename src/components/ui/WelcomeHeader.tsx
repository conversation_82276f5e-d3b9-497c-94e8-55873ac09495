import { View } from 'react-native';
import { H1 } from './Typography';
import { useThemeColors } from '@/hooks/useThemeColors';
import { userService } from '@/services/tokenService';
import { useCallback, useEffect, useState } from 'react';
import { User } from '@/redux/types/userType';
import { useFocusEffect } from '@react-navigation/native';

const WelcomeHeader = () => {
  const colors = useThemeColors();
  const [profile, setProfile] = useState<User | null>(null);

  useFocusEffect(
    useCallback(() => {
      const fetchProfile = async () => {
        const profile = await userService.getUserProfile();
        setProfile(profile);
      };
      fetchProfile();
    }, []),
  );

  return (
    <View className="mb-4" style={{ marginTop: 80 }}>
      <H1
        style={{
          color: colors.text,
          marginBottom: 4,
          fontWeight: 'normal',
        }}
      >
        Ch<PERSON><PERSON> mừng trở lại,
      </H1>
      <H1
        style={{
          color: colors.text,
          fontWeight: 'bold',
        }}
      >
        {profile?.firstName + ' ' + profile?.lastName}
      </H1>
    </View>
  );
};

export default WelcomeHeader;
