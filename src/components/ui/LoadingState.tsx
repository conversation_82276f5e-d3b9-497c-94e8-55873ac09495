import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import Animated, { FadeIn, BounceIn } from 'react-native-reanimated';
import { useThemeColors } from '@/hooks/useThemeColors';

interface LoadingStateProps {
  title?: string;
  description?: string;
  delay?: number;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  title = 'Loading...',
  description = 'Please wait while we fetch your data',
  delay = 300,
}) => {
  const colors = useThemeColors();

  return (
    <Animated.View
      style={{
        paddingVertical: 64,
        alignItems: 'center',
      }}
      entering={FadeIn.delay(delay)}
    >
      <View
        style={{
          backgroundColor: colors.surface,
          borderRadius: 24,
          padding: 32,
          alignItems: 'center',
          borderWidth: 1,
          borderColor: colors.border,
          shadowColor: colors.text,
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.06,
          shadowRadius: 20,
          elevation: 10,
        }}
      >
        <Animated.View
          style={{
            width: 64,
            height: 64,
            backgroundColor: colors.primaryLight,
            borderRadius: 16,
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 16,
          }}
          entering={BounceIn.delay(delay + 100)}
        >
          <ActivityIndicator size="large" color={colors.primary} />
        </Animated.View>
        <Text style={{
          color: colors.text,
          fontWeight: '600',
          fontSize: 18,
        }}>
          {title}
        </Text>
        <Text style={{
          color: colors.textSecondary,
          textAlign: 'center',
          marginTop: 8,
        }}>
          {description}
        </Text>
      </View>
    </Animated.View>
  );
};
