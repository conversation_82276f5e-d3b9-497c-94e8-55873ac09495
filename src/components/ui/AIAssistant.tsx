import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MessageCircle } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

const AIAssistantSection: React.FC = () => {
  const colors = useThemeColors();

  return (
    <View style={{
      paddingHorizontal: 24,
      paddingVertical: 24,
      backgroundColor: colors.background
    }}>
      <LinearGradient
        colors={[colors.primaryLight, colors.primaryLight + '80']}
        style={{ borderRadius: 12, padding: 24 }}
      >
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 16
        }}>
          <MessageCircle size={24} color={colors.primary} />
          <Text style={{
            color: colors.primary,
            fontWeight: 'bold',
            marginLeft: 8,
            fontSize: 20
          }}>
            AI Assistant
          </Text>
        </View>
        <Text style={{
          color: colors.textSecondary,
          marginBottom: 24,
          fontSize: 18
        }}>
          Need help improving your resume? Ask our AI assistant for
          personalized advice.
        </Text>
        <TouchableOpacity
          style={{
            backgroundColor: colors.primary,
            borderRadius: 12,
            paddingVertical: 16,
            paddingHorizontal: 24,
            alignSelf: 'flex-start',
            shadowColor: colors.primary,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 8,
          }}
          activeOpacity={0.9}
        >
          <Text style={{
            color: colors.primaryText,
            fontWeight: '600',
            fontSize: 18
          }}>
            Chat Now
          </Text>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );
};

export default AIAssistantSection;