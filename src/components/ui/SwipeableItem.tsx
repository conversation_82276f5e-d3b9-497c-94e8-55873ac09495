import React from 'react';
import {
  View,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolateColor,
  useAnimatedGestureHandler,
  runOnJS,
  withTiming,
} from 'react-native-reanimated';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import { Trash2 } from 'lucide-react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const SWIPE_THRESHOLD = -80;
const DELETE_THRESHOLD = -120;

interface SwipeableItemProps {
  children: React.ReactNode;
  onDelete: () => void;
  isDeleting: boolean;
  itemId: string;
  deleteTitle?: string;
  deleteMessage?: string;
}

export const SwipeableItem: React.FC<SwipeableItemProps> = ({
  children,
  onDelete,
  isDeleting,
  itemId,
  deleteTitle = 'Delete Item',
  deleteMessage = 'Are you sure you want to delete this item? This action cannot be undone.',
}) => {
  const translateX = useSharedValue(0);
  const itemHeight = useSharedValue(1);
  const itemOpacity = useSharedValue(1);
  const deleteButtonScale = useSharedValue(0);

  const handleDelete = () => {
    Alert.alert(deleteTitle, deleteMessage, [
      {
        text: 'Cancel',
        style: 'cancel',
        onPress: () => {
          translateX.value = withSpring(0);
          deleteButtonScale.value = withSpring(0);
        },
      },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: () => {
          itemHeight.value = withTiming(0, { duration: 300 });
          itemOpacity.value = withTiming(0, { duration: 300 });
          translateX.value = withTiming(-SCREEN_WIDTH, { duration: 300 });

          setTimeout(() => {
            onDelete();
          }, 300);
        },
      },
    ]);
  };

  const gestureHandler = useAnimatedGestureHandler<
    PanGestureHandlerGestureEvent,
    { startX: number; isHorizontalSwipe: boolean }
  >({
    onStart: (_, context) => {
      context.startX = translateX.value;
      context.isHorizontalSwipe = false;
    },
    onActive: (event, context) => {
      if (!context.isHorizontalSwipe) {
        const absX = Math.abs(event.translationX);
        const absY = Math.abs(event.translationY);

        if (absX > absY && absX > 10) {
          context.isHorizontalSwipe = true;
        } else if (absY > absX && absY > 10) {
          return;
        }
      }

      if (context.isHorizontalSwipe) {
        const newTranslateX = context.startX + event.translationX;

        if (newTranslateX <= 0) {
          translateX.value = newTranslateX;

          if (newTranslateX < SWIPE_THRESHOLD) {
            deleteButtonScale.value = withSpring(1);
          } else {
            deleteButtonScale.value = withSpring(0);
          }
        }
      }
    },
    onEnd: (event, context) => {
      if (context.isHorizontalSwipe) {
        const shouldDelete = translateX.value < DELETE_THRESHOLD;

        if (shouldDelete) {
          runOnJS(handleDelete)();
        } else if (translateX.value < SWIPE_THRESHOLD) {
          translateX.value = withSpring(SWIPE_THRESHOLD);
          deleteButtonScale.value = withSpring(1);
        } else {
          translateX.value = withSpring(0);
          deleteButtonScale.value = withSpring(0);
        }
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { scale: itemHeight.value },
      ],
      opacity: itemOpacity.value,
    };
  });

  const deleteButtonAnimatedStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      deleteButtonScale.value,
      [0, 1],
      ['rgba(239, 68, 68, 0.1)', 'rgba(239, 68, 68, 1)'],
    );

    return {
      transform: [{ scale: deleteButtonScale.value }],
      backgroundColor,
    };
  });

  const deleteButtonContainerStyle = useAnimatedStyle(() => {
    return {
      opacity: deleteButtonScale.value,
    };
  });

  return (
    <View className="relative">
      <Animated.View
        className="absolute right-0 top-0 bottom-0 flex-row items-center justify-center"
        style={[
          deleteButtonContainerStyle,
          {
            width: Math.abs(SWIPE_THRESHOLD) + 20,
            marginBottom: 16,
          },
        ]}
      >
        <Animated.View
          className="w-16 h-16 rounded-full items-center justify-center"
          style={deleteButtonAnimatedStyle}
        >
          {isDeleting ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Trash2 size={24} color="white" />
          )}
        </Animated.View>
      </Animated.View>

      <PanGestureHandler
        onGestureEvent={gestureHandler}
        activeOffsetX={[-10, 10]}
        failOffsetY={[-15, 15]}
        shouldCancelWhenOutside={true}
      >
        <Animated.View style={animatedStyle}>{children}</Animated.View>
      </PanGestureHandler>
    </View>
  );
};
