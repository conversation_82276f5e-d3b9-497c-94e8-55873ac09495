import { ComponentPropsWithoutRef, useEffect } from "react";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from "react-native-reanimated";
import { cn } from '@/utils/utils';
import { useThemeColors } from '@/hooks/useThemeColors';

const duration = 1000;

function Skeleton({
  className,
  style: customStyle,
  ...props
}: Omit<ComponentPropsWithoutRef<typeof Animated.View>, "style"> & { style?: any }) {
  const sv = useSharedValue(1);
  const colors = useThemeColors();

  useEffect(() => {
    sv.value = withRepeat(
      withSequence(withTiming(0.5, { duration }), withTiming(1, { duration })),
      -1
    );
  }, [sv]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: sv.value,
  }));

  const skeletonStyle = [
    {
      borderRadius: 6,
      backgroundColor: colors.backgroundSecondary,
    },
    animatedStyle,
    customStyle,
  ];

  return (
    <Animated.View
      style={skeletonStyle}
      className={className}
      {...props}
    />
  );
}

export { Skeleton };
