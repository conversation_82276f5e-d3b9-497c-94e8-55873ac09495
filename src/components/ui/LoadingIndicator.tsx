import { View, Animated } from 'react-native';
import Avatar from '@/components/ui/Avatar';
import { useEffect, useRef } from 'react';

export default function LoadingIndicator() {
  const opacity1 = useRef(new Animated.Value(0.3)).current;
  const opacity2 = useRef(new Animated.Value(0.3)).current;
  const opacity3 = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animate = () => {
      const animation = Animated.sequence([
        Animated.timing(opacity1, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(opacity2, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(opacity3, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(opacity1, {
          toValue: 0.3,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(opacity2, {
          toValue: 0.3,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(opacity3, {
          toValue: 0.3,
          duration: 400,
          useNativeDriver: true,
        }),
      ]);

      Animated.loop(animation).start();
    };

    animate();
  }, [opacity1, opacity2, opacity3]);
  return (
    <View className="mb-6">
      <View className="flex-row justify-start">
        <View className="mr-3">
          <Avatar size={36} />
        </View>
        <View className="max-w-[75%] items-start">
          <View
            style={{
              backgroundColor: '#F3F4F6',
              borderRadius: 16,
              borderBottomLeftRadius: 6,
              paddingHorizontal: 20,
              paddingVertical: 16,
            }}
          >
            <View className="flex-row items-center space-x-1">
              <Animated.View
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: '#6B7280',
                  marginRight: 4,
                  opacity: opacity1,
                }}
              />
              <Animated.View
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: '#6B7280',
                  marginRight: 4,
                  opacity: opacity2,
                }}
              />
              <Animated.View
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: '#6B7280',
                  opacity: opacity3,
                }}
              />
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
