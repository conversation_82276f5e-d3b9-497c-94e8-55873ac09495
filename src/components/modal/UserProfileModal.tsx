import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Modal,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '@/hooks/useThemeColors';
import { User } from '@/redux/types/userType';

interface UserProfileModalProps {
  visible: boolean;
  onClose: () => void;
  onLogout: () => void;
  onSettings: () => void;
  profile: User | null;
}

const UserProfileModal = ({
  visible,
  onClose,
  onLogout,
  onSettings,
  profile,
}: UserProfileModalProps) => {
  const colors = useThemeColors();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <View
          style={[
            styles.modalContainer,
            {
              backgroundColor: colors.surface,
              borderRadius: 24,
              padding: 24,
              width: '90%',
              maxWidth: 420,
            },
          ]}
        >
          {/* Close button */}
          <TouchableOpacity
            style={[
              styles.closeButton,
              {
                position: 'absolute',
                top: 20,
                right: 20,
                padding: 8,
              },
            ]}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color={colors.textMuted} />
          </TouchableOpacity>
          {/* User info */}
          <View
            style={{
              alignItems: 'center',
              marginBottom: 32,
            }}
          >
            <Image
              source={{ uri: profile?.avatar || '' }}
              style={{
                width: 96,
                height: 96,
                borderRadius: 48,
                marginBottom: 12,
              }}
            />
            <Text
              style={{
                color: colors.text,
                fontWeight: '600',
                fontSize: 20,
              }}
            >
              {profile?.firstName + ' ' + profile?.lastName}
            </Text>
            <Text
              style={{
                color: colors.textSecondary,
                fontSize: 16,
              }}
            >
              {profile?.email}
            </Text>
          </View>
          {/* Stats */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              marginBottom: 32,
            }}
          >
            <View
              style={{
                alignItems: 'center',
                flex: 1,
              }}
            >
              <Text
                style={{
                  color: colors.primary,
                  fontWeight: '600',
                  fontSize: 20,
                }}
              >
                {/* TODO: fix later change to user stats */}
                24
              </Text>
              <Text
                style={{
                  color: colors.textSecondary,
                }}
              >
                Phỏng vấn
              </Text>
            </View>
            <View
              style={{
                alignItems: 'center',
                flex: 1,
              }}
            >
              <Text
                style={{
                  color: colors.primary,
                  fontWeight: '600',
                  fontSize: 20,
                }}
              >
                {/* TODO: fix later change to user stats */}
                48h
              </Text>
              <Text
                style={{
                  color: colors.textSecondary,
                }}
              >
                Tổng thời gian
              </Text>
            </View>
            <View
              style={{
                alignItems: 'center',
                flex: 1,
              }}
            >
              <Text
                style={{
                  color: colors.primary,
                  fontWeight: '600',
                  fontSize: 20,
                }}
              >
                {/* TODO: fix later change to user stats */}7
              </Text>
              <Text
                style={{
                  color: colors.textSecondary,
                }}
              >
                Ngày liên tiếp
              </Text>
            </View>
          </View>
          {/* Settings button */}
          <TouchableOpacity
            onPress={onSettings}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 12,
              marginBottom: 12,
              backgroundColor: colors.backgroundSecondary,
            }}
          >
            <Ionicons name="settings-outline" size={20} color={colors.text} />
            <Text
              style={{
                marginLeft: 8,
                color: colors.text,
                fontWeight: '500',
              }}
            >
              Cài đặt tài khoản
            </Text>
          </TouchableOpacity>
          {/* Logout button */}
          <TouchableOpacity
            onPress={onLogout}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 12,
              paddingHorizontal: 16,
              borderRadius: 12,
              backgroundColor: colors.backgroundSecondary,
            }}
          >
            <Ionicons name="log-out-outline" size={20} color="#EF4444" />
            <Text
              style={{
                marginLeft: 8,
                color: '#EF4444',
                fontWeight: '500',
              }}
            >
              Đăng xuất
            </Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  closeButton: {
    zIndex: 1,
  },
});

export default UserProfileModal;
