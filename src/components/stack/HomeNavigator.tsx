import React from 'react';
import { HomeScreen, AIChatPage } from '@/screens';
import Header from '@/components/layouts/Header';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import CustomDrawerContent from '../drawer/CustomDrawerContent';
import { HomeStackParamList } from '@/common/types/rootParamList';
import AIChatDrawer from './AIChatNavigator';
const Drawer = createDrawerNavigator();

const Stack = createNativeStackNavigator<HomeStackParamList>();

const HomeStackNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={({ navigation, route }) => ({
        header: ({ back }) => (
          <Header navigation={navigation} route={route} back={back} />
        ),
      })}
    >
      <Stack.Screen name="HomeMain" component={HomeScreen} />
      <Stack.Screen
        options={{ headerShown: false }}
        name="AIChat"
        component={AIChatDrawer}
      />
    </Stack.Navigator>
  );
};

const HomeNavigator = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: true,
      }}
      drawerContent={(props) => {
        return <CustomDrawerContent {...props} />;
      }}
    >
      <Drawer.Screen
        name="Home"
        component={HomeStackNavigator}
        options={{
          headerShown: false,
        }}
      />
    </Drawer.Navigator>
  );
};

export default HomeNavigator;
