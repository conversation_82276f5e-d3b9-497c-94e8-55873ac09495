import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import InterviewQuestionScreen from '@/screens/mock-interview/InterviewQuestionScreen';
import CreateQuestionScreen from '@/screens/mock-interview/CreateQuestionScreen';
import InterviewQuestionDetail from '@/screens/mock-interview/InterviewQuestionDetail';
import StartInterviewScreen from '@/screens/start-interview/StartInterviewScreen';
import FeedbackDetailScreen from '@/screens/feedback/FeedbackDetailScreen';

const Stack = createNativeStackNavigator<MockInterviewStackParamList>();

const InterviewNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false }}
      initialRouteName="InterviewQuestion"
    >
      <Stack.Screen
        name="InterviewQuestion"
        component={InterviewQuestionScreen}
      />
      <Stack.Screen name="CreateQuestion" component={CreateQuestionScreen} />
      <Stack.Screen
        name="InterviewQuestionDetail"
        component={InterviewQuestionDetail}
      />
      <Stack.Screen name="StartInterview" component={StartInterviewScreen} />
      <Stack.Screen name="FeedbackDetail" component={FeedbackDetailScreen} />
    </Stack.Navigator>
  );
};

export default InterviewNavigator;
