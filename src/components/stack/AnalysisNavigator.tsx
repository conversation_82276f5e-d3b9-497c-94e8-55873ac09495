import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AnalystScreen from '@/screens/analyst/AnalystScreen';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import UploadResume from '@/screens/analyst/UploadResume';
import Upload<PERSON>VRole from '@/screens/analyst/UploadCVRole';
import UploadCVJD from '@/screens/analyst/UploadCVJD';
import AnalystRole from '@/screens/analyst/AnalystRole';
import AnalystJD from '@/screens/analyst/AnalystJD';

const Stack = createNativeStackNavigator<AnalysisStackParamList>();

const AnalysisNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="AnalystMain" component={AnalystScreen} />
      <Stack.Screen name="UploadResume" component={UploadResume} />
      <Stack.Screen name="UploadCVRole" component={UploadCVRole} />
      <Stack.Screen name="UploadCVJD" component={UploadCVJD} />
      <Stack.Screen name="AnalystRole" component={AnalystRole} />
      <Stack.Screen name="AnalystJD" component={AnalystJD} />
    </Stack.Navigator>
  );
};

export default AnalysisNavigator;
