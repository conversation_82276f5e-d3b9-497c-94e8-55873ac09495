import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import AIChatDrawer from '../drawer/aiChatDrawer/AIChatDrawer';
import { AIChatPage } from '@/screens';

const Drawer = createDrawerNavigator();

const AIChatNavigator = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerPosition: 'right',
      }}
      drawerContent={(props) => {
        return <AIChatDrawer {...props} />;
      }}
    >
      <Drawer.Screen
        name="AIChatPage"
        component={AIChatPage}
        options={{
          headerShown: false,
        }}
      />
    </Drawer.Navigator>
  );
};

export default AIChatNavigator;
