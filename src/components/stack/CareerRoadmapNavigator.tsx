import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { CareerRoadmapScreen, RoadmapDetailScreen } from '@/screens';
import { CareerRoadmapStackParamList } from '@/common/types/rootParamList';

const Stack = createNativeStackNavigator<CareerRoadmapStackParamList>();

const CareerRoadmapNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="CareerRoadmapMain" component={CareerRoadmapScreen} />
      <Stack.Screen name="RoadmapDetail" component={RoadmapDetailScreen} />
    </Stack.Navigator>
  );
};

export default CareerRoadmapNavigator;
