import React, { useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AuthNavigator from './AuthNavigator';
import HomeNavigator from './HomeNavigator';
import { useAuthContext } from '@/providers/AuthProvider';
import { useThemeColors } from '@/hooks/useThemeColors';
import { View, ActivityIndicator, StatusBar, Platform } from 'react-native';
import { RootStackParamList } from '@/common/types/rootParamList';
import AnalysisNavigator from './AnalysisNavigator';
import InterviewNavigator from './InterviewNavigator';
import CareerRoadmapNavigator from './CareerRoadmapNavigator';
import ProfilePage from '@/screens/profile/ProfilePage';
import { SplashScreen } from '../ui';
import { LandingPage } from '@/screens';
import { useFirstLaunch } from '@/hooks/useFirstLaunch';
import { navigationRef } from '@/services/navigationService';

const Stack = createNativeStackNavigator<RootStackParamList>();

// Component wrapper để apply theme toàn bộ app
const ThemeWrapper = ({ children }: { children: React.ReactNode }) => {
  const colors = useThemeColors();

  return (
    <>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent={true}
        {...(Platform.OS === 'android' && {
          navigationBarColor: colors.background,
          navigationBarStyle: 'dark-content',
        })}
      />
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        {children}
      </View>
    </>
  );
};

const RootNavigator = () => {
  const { isAuthenticated } = useAuthContext();
  const colors = useThemeColors();
  const [showSplash, setShowSplash] = useState(true);
  const { isFirstLaunch } = useFirstLaunch();
  // Show splash screen on app start
  if (showSplash) {
    return <SplashScreen onFinish={() => setShowSplash(false)} />;
  }

  // Show loading while checking authentication
  if (isAuthenticated === null) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.background,
        }}
      >
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <NavigationContainer ref={navigationRef}>
      <ThemeWrapper>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
          }}
        >
          {isAuthenticated ? (
            <>
              <Stack.Screen name="Home" component={HomeNavigator} />
              <Stack.Screen name="Analysis" component={AnalysisNavigator} />
              <Stack.Screen
                name="MockInterview"
                component={InterviewNavigator}
              />
              <Stack.Screen
                name="CareerRoadmap"
                component={CareerRoadmapNavigator}
              />
              <Stack.Screen name="Profile" component={ProfilePage} />
            </>
          ) : (
            <>
              {isFirstLaunch && (
                <Stack.Screen name="LandingPage" component={LandingPage} />
              )}
              <Stack.Screen name="Auth" component={AuthNavigator} />
            </>
          )}
        </Stack.Navigator>
      </ThemeWrapper>
    </NavigationContainer>
  );
};

export default RootNavigator;
