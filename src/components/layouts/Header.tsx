import { DrawerActions, useFocusEffect } from '@react-navigation/native';
import { View, TouchableOpacity } from 'react-native';
import { useCallback, useEffect, useState } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import { Avatar, H4 } from '@/components/ui';
import { UserProfileModal } from '@/components/modal';
import { useThemeColors } from '@/hooks/useThemeColors';
import { tokenService, userService } from '@/services';
import { useAuthContext } from '@/providers/AuthProvider';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { User } from '@/redux/types/userType';

interface HeaderProps {
  navigation: any;
  route?: any;
  back?: any;
  title?: string;
}

const Header = ({ navigation, route, back, title }: HeaderProps) => {
  const [isProfileModalVisible, setProfileModalVisible] = useState(false);
  const colors = useThemeColors();
  const { checkAuth } = useAuthContext();
  const insets = useSafeAreaInsets();

  const isRoot = !back || route?.name === 'Home';
  const displayTitle = title || getScreenTitle(route?.name);
  const shouldShowBackButton = !isRoot;

  const [profile, setProfile] = useState<User | null>(null);

  useFocusEffect(
    useCallback(() => {
      const fetchProfile = async () => {
        const profile = await userService.getUserProfile();
        setProfile(profile);
      };
      fetchProfile();
    }, []),
  );

  function getScreenTitle(routeName?: string) {
    switch (routeName) {
      case 'HomeMain':
        return 'AI Interview Pro';
      case 'MockInterview':
        return 'Mock Interview';
      case 'AIChat':
        return 'AI Chat';
      case 'CareerRoadmap':
        return 'Career Roadmap';
      case 'RoadmapDetail':
        return 'Roadmap Detail';
      default:
        return 'AI Interview Pro';
    }
  }

  const toggleDrawer = () => {
    setTimeout(() => {
      const parent = navigation.getParent();
      if (parent) {
        navigation.dispatch(DrawerActions.openDrawer());
      } else {
        console.warn('Drawer is not available on this screen.');
      }
    }, 100);
  };

  const handleBackPress = () => {
    setTimeout(() => {
      navigation.goBack();
    }, 100);
  };

  const handleLogout = async () => {
    await tokenService.clearTokens();
    await checkAuth();
  };

  const handleSettings = () => {
    navigation.navigate('Profile' as never);
    setProfileModalVisible(false);
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: colors.background,
      }}
      edges={['top', 'left', 'right']}
    >
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingHorizontal: 20,
          paddingVertical: 10,
          height: 100,
          paddingTop: insets.top,
          marginTop: -insets.top,
          backgroundColor: colors.headerBackground,
          backdropFilter: 'blur(10px)',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 4,
          borderBottomWidth: 1,
          borderBottomColor: colors.borderLight,
        }}
      >
        {/* Left side */}
        <View className="flex-row items-center flex-1">
          <TouchableOpacity
            onPress={shouldShowBackButton ? handleBackPress : toggleDrawer}
            style={{
              width: 44,
              height: 44,
              marginRight: 16,
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 8,
            }}
            activeOpacity={0.7}
            delayPressIn={0}
          >
            <Ionicons
              name={shouldShowBackButton ? 'arrow-back' : 'menu'}
              size={26}
              color={colors.text}
            />
          </TouchableOpacity>

          <View className="flex-1">
            <H4
              style={{
                fontWeight: '600',
                fontSize: 20,
                color: colors.text,
              }}
            >
              {displayTitle}
            </H4>
          </View>
        </View>
        <View className="flex-row items-center">
          <Avatar
            size={42}
            imageUrl={profile?.avatar}
            onPress={() => setProfileModalVisible(true)}
            borderColor={colors.border}
          />
        </View>
        {/* User Profile Modal */}
        <UserProfileModal
          visible={isProfileModalVisible}
          onClose={() => setProfileModalVisible(false)}
          onLogout={() => handleLogout()}
          onSettings={handleSettings}
          profile={profile as User | null}
        />
      </View>
    </SafeAreaView>
  );
};

export default Header;
