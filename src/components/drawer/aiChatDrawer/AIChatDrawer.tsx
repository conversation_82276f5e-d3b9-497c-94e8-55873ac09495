import { DrawerContentComponentProps } from '@react-navigation/drawer';
import { View, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import { H4, P, Avatar } from '@/components/ui';
import { CustomDrawerItem } from '../CustomDrawerItems';
import { useThemeColors } from '@/hooks/useThemeColors';
import { selectUserProfile } from '@/selectors/userSelector';
import { selectAiChatSessions } from '@/selectors/aiChatSelector';
import { AppDispatch } from '@/redux/store';
import {
  fetchChatSessionById,
  deleteChatSession,
  fetchChatSessions,
} from '@/redux/thunks/aiChatThunks';
import {
  clearCurrentSession,
  setCurrentSession,
} from '@/redux/slices/aiChatSlice';
import * as Crypto from 'expo-crypto';
import { useEffect } from 'react';

const AIChatDrawer = (props: DrawerContentComponentProps) => {
  const colors = useThemeColors();
  const theme = 'light'; // Tạm thời fix theme
  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector(selectUserProfile);
  const chatSessions = useSelector(selectAiChatSessions);

  // Refresh sessions when drawer opens
  useEffect(() => {
    dispatch(fetchChatSessions());
  }, [dispatch]);

  const handleSelectChatSession = (sessionId: string) => {
    dispatch(fetchChatSessionById(sessionId));
    props.navigation.closeDrawer();
  };

  const handleDeleteChatSession = (sessionId: string, sessionName: string) => {
    Alert.alert(
      'Xóa lịch sử trò chuyện',
      `Bạn có chắc chắn muốn xóa "${sessionName}"?`,
      [
        {
          text: 'Hủy',
          style: 'cancel',
        },
        {
          text: 'Xóa',
          style: 'destructive',
          onPress: () => {
            dispatch(deleteChatSession(sessionId));
          },
        },
      ],
    );
  };
  const handleNewChat = () => {
    dispatch(clearCurrentSession());

    const newSessionId = Crypto.randomUUID();
    dispatch(
      setCurrentSession({
        _id: newSessionId,
        sessionId: newSessionId,
        content: [],
      }),
    );
    props.navigation.closeDrawer();
  };

  const handleGoHome = () => {
    const parentNavigator = props.navigation.getParent();
    if (parentNavigator) {
      parentNavigator.navigate('Home', { screen: 'HomeMain' });
    } else {
      // Fallback navigation
      props.navigation.navigate('Home' as any);
    }
    props.navigation.closeDrawer();
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: theme === 'dark' ? '#111827' : '#FFFFFF',
      }}
      className="px-5"
    >
      {/* Fixed Header */}
      <SafeAreaView
        edges={['top']}
        style={{ backgroundColor: theme === 'dark' ? '#111827' : '#FFFFFF' }}
      >
        <View className="pt-4 pb-2">
          {/* Navigation Section */}
          <View className="mb-4">
            <H4
              className={`${colors.textSecondary} uppercase tracking-wide font-medium text-xs mb-3`}
            >
              NAVIGATION
            </H4>

            <CustomDrawerItem
              item={{
                id: 'home',
                title: 'Trang chủ',
                icon: 'home-outline',
              }}
              onPress={handleGoHome}
              className="mb-2"
            />

            <CustomDrawerItem
              item={{
                id: 'new-chat',
                title: 'Cuộc trò chuyện mới',
                icon: 'add-circle-outline',
              }}
              onPress={handleNewChat}
              className="mb-2"
            />
          </View>
          <View className={`border ${colors.border} mb-4`} />
          <H4
            className={`${colors.textSecondary} uppercase tracking-wide font-medium text-xs mb-3`}
          >
            LỊCH SỬ TRÒ CHUYỆN
          </H4>
        </View>
      </SafeAreaView>

      {/* Scrollable Content */}
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        {chatSessions.length > 0 ? (
          chatSessions.map((session) => (
            <CustomDrawerItem
              key={session.sessionId}
              item={{
                id: session.sessionId,
                title: session.name || 'Cuộc trò chuyện mới',
                icon: 'chatbubble-ellipses-outline',
              }}
              onPress={() => handleSelectChatSession(session.sessionId)}
              rightIcon={{
                icon: 'trash-outline',
                size: 18,
                color: '#6B7280',
              }}
              onRightIconPress={() =>
                handleDeleteChatSession(
                  session.sessionId,
                  session.name || 'Cuộc trò chuyện',
                )
              }
              className="mb-1"
            />
          ))
        ) : (
          <View className="px-4 py-8">
            <View className={`${colors.surface} rounded-xl p-4`}>
              <P className={`${colors.textSecondary} text-center text-sm`}>
                Chưa có lịch sử trò chuyện
              </P>
              <P className={`${colors.textSecondary} text-center text-xs mt-1`}>
                Bắt đầu cuộc trò chuyện đầu tiên!
              </P>
            </View>
          </View>
        )}
      </ScrollView>

      <SafeAreaView
        edges={['bottom']}
        style={{ backgroundColor: theme === 'dark' ? '#111827' : '#FFFFFF' }}
      >
        <View className={`border-t ${colors.border} px-4 py-4`}>
          <View className={`${colors.surface} rounded-xl p-4`}>
            <View className="flex-row items-center">
              <Avatar size={48} />
              <View className="ml-3 flex-1">
                <H4 className={`${colors.text} text-base font-medium`}>
                  {user.firstName + ' ' + user.lastName}
                </H4>
                <P className={`${colors.textSecondary} text-sm mt-0.5`}>
                  {user.email}
                </P>
              </View>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default AIChatDrawer;
