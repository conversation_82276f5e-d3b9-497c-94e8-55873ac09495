import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useThemeColors } from '@/hooks/useThemeColors';
import { H4 } from '@/components/ui';
import { DrawerItemProps } from '@/common/types/drawer-item';

export const CustomDrawerItem = React.memo(
  ({
    item,
    onPress,
    isLogout = false,
    rightIcon,
    onRightIconPress,
  }: DrawerItemProps) => {
    const colors = useThemeColors();

    const hasRightIcon = rightIcon && onRightIconPress;

    return (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 12,
          paddingVertical: 12,
          borderRadius: 16,
          marginBottom: 8,
          backgroundColor: colors.surface,
        }}
        onPress={onPress}
      >
        <View
          style={{
            width: 32,
            height: 32,
            borderRadius: 16,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 12,
            backgroundColor: colors.backgroundSecondary,
          }}
        >
          <Ionicons
            name={item.icon as any}
            size={20}
            color={colors.text}
          />
        </View>
        {/* Title */}
        <H4
          style={{
            fontWeight: '500',
            fontSize: 16,
            flex: 1,
            color: isLogout ? '#EF4444' : colors.text,
          }}
        >
          {item.title}
        </H4>

        {/* Right icon */}
        {hasRightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            style={{
              padding: 8,
              marginLeft: 8,
            }}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons
              name={rightIcon.icon as any}
              size={rightIcon.size || 18}
              color={rightIcon.color || colors.textMuted}
            />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  },
);
