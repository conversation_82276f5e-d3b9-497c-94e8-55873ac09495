import { DrawerContentComponentProps } from '@react-navigation/drawer';
import { View, ScrollView, Alert } from 'react-native';
import { Avatar, ThemeToggleButton } from '@/components/ui';
import { MENU, SITTING } from '@/constants/common.constants';
import { useThemeColors } from '@/hooks/useThemeColors';
import { H4, P } from '@/components/ui';
import { CustomDrawerItem } from './CustomDrawerItems';
import { tokenService, userService } from '@/services';
import { useAuthContext } from '@/providers/AuthProvider';
import { selectUserProfile } from '@/redux/selectors/userSelector';
import { useSelector } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useState } from 'react';
import { User } from '@/redux/types/userType';

const CustomDrawerContent = (props: DrawerContentComponentProps) => {
  const colors = useThemeColors();
  const { checkAuth } = useAuthContext();
  const [profile, setProfile] = useState<User | null>(null);

  useFocusEffect(
    useCallback(() => {
      const fetchProfile = async () => {
        const profile = await userService.getUserProfile();
        setProfile(profile);
      };
      fetchProfile();
    }, []),
  );

  const handleLogout = async () => {
    await tokenService.clearTokens();
    await checkAuth();
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: colors.background,
        padding: 10,
      }}
    >
      <View className="flex-1">
        <View className="px-4 pt-14  pb-4">
          <View
            style={{
              borderRadius: 16,
              padding: 16,
              borderWidth: 1,
              borderColor: colors.border,
              backgroundColor: colors.surface,
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Avatar size={48} imageUrl={profile?.avatar} />
              <View
                style={{
                  marginLeft: 12,
                  flex: 1,
                }}
              >
                <H4
                  style={{
                    color: colors.text,
                    fontSize: 18,
                    fontWeight: '600',
                  }}
                >
                  {profile?.firstName + ' ' + profile?.lastName}
                </H4>
                <P
                  style={{
                    color: colors.textSecondary,
                    fontSize: 14,
                    marginTop: 2,
                  }}
                >
                  {profile?.email}
                </P>
              </View>
            </View>
          </View>
        </View>
        {/* Scrollable Content */}
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {/* Main Menu Items */}
          <View
            style={{
              paddingHorizontal: 4,
              paddingTop: 8,
            }}
          >
            <H4
              style={{
                color: colors.textMuted,
                textTransform: 'uppercase',
                letterSpacing: 1,
                fontWeight: '500',
                fontSize: 12,
                paddingHorizontal: 12,
                marginBottom: 8,
              }}
            >
              MENU CHÍNH
            </H4>
            {/* Features */}
            {MENU.map((menu) => (
              <CustomDrawerItem
                key={menu.id}
                item={menu}
                onPress={() => {
                  props.navigation.closeDrawer();
                  if (menu.navigate === 'MockInterview') {
                    props.navigation.navigate('Home', {
                      screen: 'MockInterview',
                    });
                  } else if (menu.navigate === 'AIChat') {
                    props.navigation.navigate('Home', {
                      screen: 'AIChat',
                    });
                  } else if (menu.navigate === 'CareerRoadmap') {
                    props.navigation.navigate('Home', {
                      screen: 'CareerRoadmap',
                    });
                  } else {
                    props.navigation.navigate(menu.navigate as never);
                  }
                }}
              />
            ))}
          </View>
          {/* Divider */}
          <View
            style={{
              marginHorizontal: 12,
              marginVertical: 12,
              height: 1,
              backgroundColor: colors.border,
            }}
          />
          {/* Settings Section */}
          <View
            style={{
              paddingHorizontal: 4,
              flex: 1,
            }}
          >
            <H4
              style={{
                color: colors.textMuted,
                textTransform: 'uppercase',
                letterSpacing: 1,
                fontWeight: '500',
                fontSize: 12,
                paddingHorizontal: 12,
                marginBottom: 8,
              }}
            >
              CÀI ĐẶT
            </H4>
            {SITTING.map((sitting) => (
              <CustomDrawerItem
                key={sitting.id}
                item={sitting}
                onPress={() => {
                  if (sitting.id === 'logout') {
                    handleLogout();
                  }
                  if (sitting.id === 'settings') {
                    props.navigation.navigate('Profile' as never);
                  }
                }}
                isLogout={sitting.id === 'logout'}
              />
            ))}
            {/* Theme Toggle Button */}
            <ThemeToggleButton />
          </View>
        </ScrollView>
      </View>

      {/* Fixed Footer */}
      <View
        style={{
          paddingHorizontal: 12,
          paddingVertical: 16,
          borderTopWidth: 1,
          borderTopColor: colors.border,
        }}
      >
        <View
          style={{
            backgroundColor: colors.surface,
            borderRadius: 12,
            padding: 12,
          }}
        >
          <H4
            style={{
              color: colors.textSecondary,
              textAlign: 'center',
              fontSize: 12,
            }}
          >
            LearnVox AI v1.0.0
          </H4>
          <H4
            style={{
              color: colors.textSecondary,
              textAlign: 'center',
              fontSize: 12,
              marginTop: 4,
            }}
          >
            © 2025 All rights reserved
          </H4>
        </View>
      </View>
    </View>
  );
};

export default CustomDrawerContent;
