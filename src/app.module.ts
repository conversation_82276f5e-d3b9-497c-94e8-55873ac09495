import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import appConfig from '@configs/app.config';
import postgreConfig from '@database/config/postgre.config';
import { UsersModule } from '@modules/users/users.module';
import { ProfilesModule } from '@modules/profiles/profiles.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TypeOrmConfigService } from '@database/config/type-orm.config';
import mongoConfig from '@database/config/mongo.config';
import { MongooseModule } from '@nestjs/mongoose';
import redisConfig from '@database/config/redis.config';
import { RedisModule } from '@nestjs-modules/ioredis';
import * as redisStore from 'cache-manager-ioredis';
import { CacheModule } from '@nestjs/cache-manager';
import googleConfig from '@modules/auth/configs/google-oauth.config';
import { AuthModule } from '@modules/auth/auth.module';
import jwtConfig from '@modules/auth/configs/jwt.config';
import refreshJwtConfig from '@modules/auth/configs/refresh-jwt.config';
import authConfig from '@modules/auth/configs/auth.config';
import mailConfig from '@modules/mail/configs/mail.config';
import { MaillerModule } from '@modules/mail/mail.module';
import { apiDocs_config } from '@configs/api-docs.config';
import cloudinaryConfig from '@configs/cloudinary.config';
import { CloudinaryModule } from '@modules/cloudinary/cloudinary.module';
import { AiModule } from '@modules/ai/ai.module';
import { InterviewModule } from '@modules/interview/interview.module';
import { AnalystModule } from '@modules/analyst/analyst.module';
import { ChatAiModule } from '@modules/chat-ai/chat-ai.module';
import { RoadmapAiModule } from '@modules/roadmap-ai/roadmap-ai.module';
import imagekitConfig from '@modules/imagekit/configs/imagekit.config';
import { ImagekitModule } from '@modules/imagekit/imagekit.module';
import { ConversationModule } from '@modules/conversation/conversation.module';
import { CoverLetterModule } from '@modules/cover-letter/cover-letter.module';
import defaultAvatarConfig from '@configs/default-avatar.config';
import { ParsePdfModule } from '@modules/parse-pdf/parse-pdf.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        appConfig,
        postgreConfig,
        mongoConfig,
        redisConfig,
        jwtConfig,
        refreshJwtConfig,
        authConfig,
        googleConfig,
        mailConfig,
        apiDocs_config,
        cloudinaryConfig,
        imagekitConfig,
        defaultAvatarConfig,
      ],
      cache: true,
      envFilePath: `.env.${process.env.NODE_ENV || 'dev'}`,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useClass: TypeOrmConfigService,
      inject: [ConfigService],
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get<string>('mongo.uri'),
      }),
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          type: 'single',
          options: {
            host: configService.get<string>('redis.host'),
            port: configService.get<number>('redis.port'),
            password: configService.get<string>('redis.password'),
            tls: configService.get<boolean>('redis.tls') ? {} : undefined,
          },
        };
      },
    }),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      isGlobal: true,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const redisConfig = configService.get('redis');
        return {
          store: redisStore,
          host: redisConfig.host,
          port: redisConfig.port,
          password: redisConfig.password,
          tls: redisConfig.tls,
          ttl: 300,
        };
      },
    }),
    AuthModule,
    UsersModule,
    ProfilesModule,
    MaillerModule,
    CloudinaryModule,
    ImagekitModule,
    InterviewModule,
    ConversationModule,
    AnalystModule,
    ChatAiModule,
    AiModule,
    RoadmapAiModule,
    CoverLetterModule,
    ParsePdfModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
