import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '../store';

export const selectAuth = (state: RootState) => state.auth;

export const selectIsAuthenticated = createSelector(
  [selectAuth],
  (auth) => auth.isAuthenticated,
);

export const selectAuthLoading = createSelector(
  [selectAuth],
  (auth) => auth.loading,
);

export const selectAuthError = createSelector(
  [selectAuth],
  (auth) => auth.error,
);

export const selectCurrentUser = createSelector(
  [selectAuth],
  (auth) => auth.data,
);

export const selectOtpVerified = createSelector(
  [selectAuth],
  (auth) => auth.otpVerified,
);
export const selectResetEmail = createSelector(
  [selectAuth],
  (auth) => auth.resetEmail,
);

export const selectVerifiedOtp = createSelector(
  [selectAuth],
  (auth) => auth.verifiedOtp,
);
