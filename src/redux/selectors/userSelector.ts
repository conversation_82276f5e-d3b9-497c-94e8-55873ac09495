import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '../store';

export const selectUser = (state: RootState) => state.user;

export const selectUserLoading = createSelector(
  [selectUser],
  (user) => user.loading,
);

export const selectUserError = createSelector(
  [selectUser],
  (user) => user.error,
);

export const selectUserMessage = createSelector(
  [selectUser],
  (user) => user.message,
);

export const selectUserProfile = createSelector(
  [selectUser],
  (user) => user.user,
);

export const selectIsUpdatingProfile = createSelector(
  [selectUser],
  (user) => user.isUpdating,
);

export const selectIsUploadingAvatar = createSelector(
  [selectUser],
  (user) => user.isUploadingAvatar,
);
