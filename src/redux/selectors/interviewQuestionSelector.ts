import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectInterviewQuestion = (state: RootState) =>
  state.interviewQuestion;

export const selectInterviewQuestionLoading = createSelector(
  [selectInterviewQuestion],
  (interviewQuestion) => interviewQuestion.loading,
);

export const selectInterviewQuestionError = createSelector(
  [selectInterviewQuestion],
  (interviewQuestion) => interviewQuestion.message,
);

export const selectInterviewQuestionLastUpdated = createSelector(
  [selectInterviewQuestion],
  (interviewQuestion) => interviewQuestion.lastUpdated,
);

export const selectMyInterviewQuestion = createSelector(
  [selectInterviewQuestion],
  (interviewQuestion) => interviewQuestion.myInterviewQuestions,
);

export const selectCurrentInterviewQuestion = createSelector(
  [selectInterviewQuestion],
  (interviewQuestion) => interviewQuestion.currentInterviewQuestion,
);
