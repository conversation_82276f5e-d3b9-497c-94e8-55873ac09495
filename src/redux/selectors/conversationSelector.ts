import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectConversation = (state: RootState) => state.conversation;

export const selectMyConversations = createSelector(
  [selectConversation],
  (conversation) => conversation.myConversations,
);

export const selectCurrentConversation = createSelector(
  [selectConversation],
  (conversation) => conversation.currentConversation,
);

export const selectConversationLoading = createSelector(
  [selectConversation],
  (conversation) => conversation.loading,
);

export const selectConversationError = createSelector(
  [selectConversation],
  (conversation) => conversation.error,
);

export const selectConversationLastUpdated = createSelector(
  [selectConversation],
  (conversation) => conversation.lastUpdated,
);
