import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '../store';

export const selectAnalyst = (state: RootState) => state.analyst;

export const selectAnalystLoading = createSelector(
  [selectAnalyst],
  (analyst) => analyst.loading,
);

// Chỉ return message khi đang loading false và có error
export const selectAnalystError = createSelector([selectAnalyst], (analyst) =>
  !analyst.loading && analyst.message ? analyst.message : null,
);

export const selectCurrentAnalyst = createSelector(
  [selectAnalyst],
  (analyst) => analyst.currentAnalyst,
);

export const selectMyAnalysts = createSelector(
  [selectAnalyst],
  (analyst) => analyst.myAnalysts,
);

export const selectAnalystsByType = createSelector(
  [selectMyAnalysts],
  (analysts) => ({
    roleAnalysts: analysts.filter(
      (a) => a.agentType === 'AI_RESUME_ANALYSIS_ROLE',
    ),
    jdAnalysts: analysts.filter((a) => a.agentType === 'AI_RESUME_ANALYSIS_JD'),
  }),
);

// Thêm selector để check có data hay không
export const selectHasAnalystData = createSelector(
  [selectCurrentAnalyst, selectAnalystLoading, selectAnalystError],
  (currentAnalyst, loading, error) => {
    return !loading && !error && currentAnalyst !== null;
  },
);
