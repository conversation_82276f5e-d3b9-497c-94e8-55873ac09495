import { AsyncState } from './baseType';

export interface User {
  id: string | '';
  firstName: string | '';
  lastName: string | '';
  email: string | '';
  avatar?: string | '';
  bio?: string | '';
  phone?: string | '';
  address?: string | '';
  dateOfBirth?: string | '';
}

export interface UserState extends AsyncState {
  user: User;
  message: string | null;
  isUpdating: boolean;
  isUploadingAvatar: boolean;
}
