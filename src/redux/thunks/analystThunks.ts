import { createAsyncThunk } from '@reduxjs/toolkit';
import { analystApi } from '@/services/analystApi';
import { ApiErrorResponse } from '@/common/types/api';
import {
  AnalystRoleCredentials,
  AnalystJDCredentials,
  AnalystResponse,
  ApiResponse,
} from '@/services/types/analyst';

export const createAnalystByRole = createAsyncThunk<
  ApiResponse<AnalystResponse>,
  AnalystRoleCredentials,
  { rejectValue: ApiErrorResponse }
>('analyst/createByRole', async (credentials, { rejectWithValue }) => {
  try {
    const result = await analystApi.createAnalystByRole(credentials);
    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const createAnalystByJD = createAsyncThunk<
  ApiResponse<AnalystResponse>,
  AnalystJDCredentials,
  { rejectValue: ApiErrorResponse }
>('analyst/createByJD', async (credentials, { rejectWithValue }) => {
  try {
    const result = await analystApi.createAnalystByJD(credentials);
    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const getAnalystById = createAsyncThunk<
  ApiResponse<AnalystResponse>,
  string,
  { rejectValue: ApiErrorResponse }
>('analyst/getById', async (id, { rejectWithValue }) => {
  try {
    const result = await analystApi.getAnalystById(id);
    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const getMyAnalysts = createAsyncThunk<
  ApiResponse<AnalystResponse[]>,
  void,
  { rejectValue: ApiErrorResponse }
>('analyst/getMyAnalysts', async (_, { rejectWithValue }) => {
  try {
    const result = await analystApi.getMyAnalysts();
    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const deleteAnalyst = createAsyncThunk<
  string,
  string,
  { rejectValue: ApiErrorResponse }
>('analyst/delete', async (id, { rejectWithValue }) => {
  try {
    await analystApi.deleteAnalyst(id);
    return id; 
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});
