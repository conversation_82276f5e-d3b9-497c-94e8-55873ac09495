import { createAsyncThunk } from '@reduxjs/toolkit';
import { aiChatApi } from '@/services';

export const fetchChatSessions = createAsyncThunk(
  'aiChat/fetchChatSessions',
  async (_, { rejectWithValue }) => {
    try {
      const response = await aiChatApi.getChatSessions();
      return response;
    } catch (error: any) {
      return rejectWithValue(error);
    }
  },
);

export const fetchChatSessionById = createAsyncThunk(
  'aiChat/fetchChatSessionById',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      const response = await aiChatApi.getChatSessionById(sessionId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error);
    }
  },
);

export const deleteChatSession = createAsyncThunk(
  'aiChat/deleteChatSession',
  async (sessionId: string, { rejectWithValue }) => {
    try {
      await aiChatApi.deleteChatSession(sessionId);
      return sessionId;
    } catch (error: any) {
      return rejectWithValue(error);
    }
  },
);

export const sendMessage = createAsyncThunk(
  'aiChat/sendMessage',
  async (
    { sessionId, message }: { sessionId: string; message: string },
    { rejectWithValue },
  ) => {
    try {
      const response = await aiChatApi.sendMessage(sessionId, message);

      return {
        sessionId,
        userMessage: message,
        assistantResponse: response,
      };
    } catch (error: any) {
      return rejectWithValue(error);
    }
  },
);
