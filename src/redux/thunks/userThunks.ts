import { ApiErrorResponse, isApiSuccess } from '@/common/types/api';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { User } from '../types/userType';
import {
  userApi,
  UpdateProfileRequest,
  UploadAvatarResponse,
} from '@/services/userApi';

export const getProfile = createAsyncThunk<
  User,
  void,
  { rejectValue: ApiErrorResponse }
>('user/getProfile', async (_, { rejectWithValue }) => {
  try {
    const response = await userApi.getProfile();
    return response as User;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const updateProfile = createAsyncThunk<
  User,
  UpdateProfileRequest,
  { rejectValue: ApiErrorResponse }
>('user/updateProfile', async (profileData, { rejectWithValue }) => {
  try {
    console.log('Updating profile with data:', profileData);

    const response = await userApi.updateProfile(profileData);
    if (isApiSuccess(response)) {
      return response.data;
    } else {
      return rejectWithValue(response);
    }
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const uploadAvatar = createAsyncThunk<
  UploadAvatarResponse,
  string,
  { rejectValue: ApiErrorResponse }
>('user/uploadAvatar', async (imageUri, { rejectWithValue }) => {
  try {
    const response = await userApi.uploadAvatar(imageUri);
    return response.data;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});
