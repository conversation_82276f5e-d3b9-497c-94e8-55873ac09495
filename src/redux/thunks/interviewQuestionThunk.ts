import { ApiErrorResponse, ApiResponse } from '@/common/types/api';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { interviewQuestionApi } from '@/services/interviewQuestion';

export const getMyInterviewQuestion = createAsyncThunk<
  ApiResponse<InterviewQuestionSessionResponse[]>,
  void,
  { rejectValue: ApiErrorResponse }
>(
  'interviewQuestion/getMyInterviewQuestion',
  async (_, { rejectWithValue }) => {
    try {
      const response = await interviewQuestionApi.getMyInterviewQuestion();
      return response;
    } catch (error) {
      return rejectWithValue(error as ApiErrorResponse);
    }
  },
);

export const getInterviewQuestionById = createAsyncThunk<
  ApiResponse<InterviewQuestionSessionResponse>,
  string,
  { rejectValue: ApiErrorResponse }
>(
  'interviewQuestion/getInterviewQuestionById',
  async (interviewId, { rejectWithValue }) => {
    try {
      const response =
        await interviewQuestionApi.getInterviewQuestionById(interviewId);
      return response;
    } catch (error) {
      return rejectWithValue(error as ApiErrorResponse);
    }
  },
);

export const createInterviewQuestionThunk = createAsyncThunk<
  ApiResponse<InterviewQuestionSessionResponse>,
  FormData,
  { rejectValue: ApiErrorResponse }
>(
  'interviewQuestion/createInterviewQuestionThunks',
  async (formData, { rejectWithValue }) => {
    try {
      const response =
        await interviewQuestionApi.createInterviewQuestion(formData);
      return response;
    } catch (error) {
      return rejectWithValue(error as ApiErrorResponse);
    }
  },
);

export const deleteInterviewQuestionThunk = createAsyncThunk<
  string,
  string,
  { rejectValue: ApiErrorResponse }
>(
  'interviewQuestion/deleteInterviewQuestionThunk',
  async (interviewId, { rejectWithValue }) => {
    try {
      await interviewQuestionApi.deleteInterviewQuestion(interviewId);
      return interviewId;
    } catch (error) {
      return rejectWithValue(error as ApiErrorResponse);
    }
  },
);
