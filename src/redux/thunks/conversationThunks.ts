import { ApiResponse } from '@/common/types/api';
import { ApiErrorResponse } from '@/common/types/api';
import { conversationApi } from '@/services/conversation';
import {
  ConversationRequest,
  ConversationResponse,
} from '@/services/types/conversation';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const getMyConversations = createAsyncThunk<
  ApiResponse<ConversationResponse[]>,
  void,
  { rejectValue: ApiErrorResponse }
>('conversation/getMyConversations', async (_, { rejectWithValue }) => {
  try {
    const response = await conversationApi.getMyConversations();
    return response;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const getConversationById = createAsyncThunk<
  ApiResponse<ConversationResponse>,
  string,
  { rejectValue: ApiErrorResponse }
>(
  'conversation/getConversationById',
  async (conversationId, { rejectWithValue }) => {
    try {
      const response =
        await conversationApi.getConversationById(conversationId);
      return response;
    } catch (error) {
      return rejectWithValue(error as ApiErrorResponse);
    }
  },
);

export const createConversation = createAsyncThunk<
  ApiResponse<ConversationResponse>,
  ConversationRequest,
  { rejectValue: ApiErrorResponse }
>('conversation/createConversation', async (body, { rejectWithValue }) => {
  try {
    const response = await conversationApi.createConversation(body);
    return response;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const deleteConversation = createAsyncThunk<
  string,
  string,
  { rejectValue: ApiErrorResponse }
>(
  'conversation/deleteConversation',
  async (conversationId, { rejectWithValue }) => {
    try {
      await conversationApi.deleteConversation(conversationId);
      return conversationId;
    } catch (error) {
      return rejectWithValue(error as ApiErrorResponse);
    }
  },
);
