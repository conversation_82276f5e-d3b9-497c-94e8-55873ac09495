import { createAsyncThunk } from '@reduxjs/toolkit';
import { authApi } from '@/services/authApi';
import { ApiErrorResponse, isApiSuccess } from '@/common/types/api';
import { tokenService } from '@/services/tokenService';
import {
  CreateOtpCredentials,
  ForgotPasswordCredentials,
  LoginCredentials,
  RegisterCredentials,
  VerifyOtpCredentials,
} from '@/services/types/auth';

export const loginUser = createAsyncThunk<
  any,
  LoginCredentials,
  { rejectValue: ApiErrorResponse }
>('auth/login', async (credentials, { rejectWithValue }) => {
  try {
    const result = await authApi.login(credentials);
    if (isApiSuccess(result)) {
      const { accessToken, refreshToken } = result.data;
      await tokenService.saveTokens(accessToken, refreshToken);
    }

    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const registerUser = createAsyncThunk<
  any,
  RegisterCredentials,
  { rejectValue: ApiErrorResponse }
>('auth/register', async (credentials, { rejectWithValue }) => {
  try {
    const result = await authApi.register(credentials);

    if (isApiSuccess(result)) {
      return result;
    }
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const createOtp = createAsyncThunk<
  any,
  CreateOtpCredentials,
  { rejectValue: ApiErrorResponse }
>('auth/createOtp', async (credentials, { rejectWithValue }) => {
  try {
    const result = await authApi.createOtp(credentials);
    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const verifyOtp = createAsyncThunk<
  any,
  VerifyOtpCredentials,
  { rejectValue: ApiErrorResponse }
>('auth/verifyOtp', async (credentials, { rejectWithValue }) => {
  try {
    const result = await authApi.verifyOtp(credentials);
    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const forgotPassword = createAsyncThunk<
  any,
  ForgotPasswordCredentials,
  { rejectValue: ApiErrorResponse }
>('auth/forgotPassword', async (credentials, { rejectWithValue }) => {
  try {
    const result = await authApi.forgotPassword(credentials);
    return result;
  } catch (error) {
    return rejectWithValue(error as ApiErrorResponse);
  }
});

export const logoutUser = createAsyncThunk('auth/logout', async () => {
  await tokenService.clearTokens();
  return true;
});
