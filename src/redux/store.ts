import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import analystSlice from './slices/analystSlice';
import { authSlice, aiChatSlice, userSliceReducer } from './slices';
import interviewQuestionSlice from './slices/interviewQuestionSlice';
import conversationSlice from './slices/conversationSlice';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Persist config for user
const userPersistConfig = {
  key: 'user',
  storage: AsyncStorage,
  whitelist: ['userProfile', 'isAuthenticated'],
};

// Persisted reducer 
const persistedUserReducer = persistReducer(
  userPersistConfig,
  userSliceReducer,
);

export const store = configureStore({
  reducer: {
    auth: authSlice,
    user: persistedUserReducer,
    analyst: analyst<PERSON>lice,
    aiChat: aiChatSlice,
    interviewQuestion: interviewQuestionSlice,
    conversation: conversationSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: __DEV__,
});

// refetch
setupListeners(store.dispatch);

// Persistor
export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
