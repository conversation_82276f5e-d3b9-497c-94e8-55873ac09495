import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatAiState } from '../types/aiChattingType';
import { ChatMessageContent, ChatSession } from '@/common/types/ai-chat.d';
import {
  fetchChatSessions,
  fetchChatSessionById,
  deleteChatSession,
  sendMessage,
} from '@/thunks/aiChatThunks';

const initialState: ChatAiState = {
  mySessions: [],
  currentSession: null,
};

const aiChatSlice = createSlice({
  name: 'aiChat',
  initialState,
  reducers: {
    createNewSession: (state, action: PayloadAction<ChatSession>) => {
      state.mySessions.unshift(action.payload);
      state.currentSession = action.payload;
    },
    setMySessions: (state, action: PayloadAction<ChatSession[]>) => {
      state.mySessions = action.payload;
    },
    setCurrentSession: (state, action: PayloadAction<ChatSession | null>) => {
      state.currentSession = action.payload;
    },
    clearCurrentSession: (state) => {
      state.currentSession = null;
    },
    setLoadingChatSession: (state, action: PayloadAction<boolean>) => {
      if (state.currentSession) {
        state.currentSession.loading = action.payload;
      }
    },
    setMessageChatSession: (
      state,
      action: PayloadAction<{ sessionId: string; message: ChatMessageContent }>,
    ) => {
      const { sessionId, message } = action.payload;
      // Only update currentSession, don't add to mySessions yet
      // Let the API response handle adding to mySessions
      if (state.currentSession?.sessionId === sessionId) {
        state.currentSession.content.push(message);
      } else {
        state.currentSession = {
          _id: sessionId,
          sessionId,
          content: [message],
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch chat sessions
      .addCase(fetchChatSessions.fulfilled, (state, action) => {
        if (action.payload && 'data' in action.payload) {
          state.mySessions = action.payload.data;
        }
      })
      // Fetch chat session by ID
      .addCase(fetchChatSessionById.fulfilled, (state, action) => {
        if (action.payload && 'data' in action.payload) {
          state.currentSession = action.payload.data;
        }
      }) // Delete chat session
      .addCase(deleteChatSession.fulfilled, (state, action) => {
        const sessionId = action.payload;
        state.mySessions = state.mySessions.filter(
          (session) => session.sessionId !== sessionId,
        );
        // If deleted session is current session, clear it
        if (state.currentSession?.sessionId === sessionId) {
          state.currentSession = null;
        }
      })
      // Send message
      .addCase(sendMessage.pending, (state) => {
        if (state.currentSession) {
          state.currentSession.loading = true;
        }
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        const { sessionId, userMessage, assistantResponse } = action.payload;

        // Create assistant message from response
        const assistantMessageObj: ChatMessageContent = {
          role: 'assistant',
          content: (assistantResponse as any).data?.content || '',
          timestamp:
            (assistantResponse as any).data?.timestamp ||
            new Date().toISOString(),
        };

        // Update current session
        if (state.currentSession?.sessionId === sessionId) {
          state.currentSession.content.push(assistantMessageObj);
          state.currentSession.loading = false;
        }

        // Update sessions list if session already exists
        const sessionIndex = state.mySessions.findIndex(
          (session) => session.sessionId === sessionId,
        );
        if (sessionIndex !== -1) {
          state.mySessions[sessionIndex].content.push(assistantMessageObj);
        }
        // For new sessions, don't add to mySessions here
        // Let fetchChatSessions handle it to get correct session name from backend
      })
      .addCase(sendMessage.rejected, (state) => {
        if (state.currentSession) {
          state.currentSession.loading = false;
        }
      });
  },
});

export const {
  setMySessions,
  setCurrentSession,
  clearCurrentSession,
  setLoadingChatSession,
  setMessageChatSession,
  createNewSession,
} = aiChatSlice.actions;
export default aiChatSlice.reducer;
