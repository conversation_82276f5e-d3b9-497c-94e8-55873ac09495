import { ConversationResponse } from '@/services/types/conversation';
import { createAsyncState } from '../types/baseType';
import { ConversationState } from '../types/conversation';
import {
  createConversation,
  deleteConversation,
  getConversationById,
  getMyConversations,
} from '../thunks/conversationThunks';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ApiResponse } from '@/common/types/api';

const initialState: ConversationState = {
  ...createAsyncState<ConversationResponse[]>(),
  currentConversation: null,
  myConversations: [],
};

const conversationSlice = createSlice({
  name: 'conversation',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getMyConversations.pending, (state) => {
        state.loading = true;
      })
      .addCase(getConversationById.pending, (state) => {
        state.loading = true;
      })
      .addCase(createConversation.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteConversation.pending, (state) => {
        state.loading = true;
      })
      .addCase(
        getMyConversations.fulfilled,
        (state, action: PayloadAction<ApiResponse<ConversationResponse[]>>) => {
          state.loading = false;
          state.myConversations =
            'data' in action.payload ? action.payload.data : [];
          state.lastUpdated = Date.now();
          state.message = null;
        },
      )
      .addCase(getMyConversations.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      })
      .addCase(getConversationById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentConversation =
          'data' in action.payload ? action.payload.data : null;
        state.lastUpdated = Date.now();
        state.message = null;
      })
      .addCase(getConversationById.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      })
      .addCase(createConversation.fulfilled, (state, action) => {
        state.loading = false;
        state.lastUpdated = Date.now();
        state.message = null;
        state.currentConversation =
          'data' in action.payload ? action.payload.data : null;
        if (state.myConversations && 'data' in action.payload) {
          state.myConversations.unshift(action.payload.data);
        }
      })
      .addCase(createConversation.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      })
      .addCase(deleteConversation.fulfilled, (state, action) => {
        state.loading = false;
        const deletedId = action.payload;

        state.myConversations = state.myConversations.filter(
          (conversation) => conversation._id !== deletedId,
        );
        state.lastUpdated = Date.now();
        state.message = null;
      })
      .addCase(deleteConversation.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      });
  },
});

export const {} = conversationSlice.actions;

export default conversationSlice.reducer;
