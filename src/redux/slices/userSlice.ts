import { createSlice } from '@reduxjs/toolkit';
import { createAsyncState } from '../types/baseType';
import { User, UserState } from '../types/userType';
import { getProfile, updateProfile, uploadAvatar } from '../thunks/userThunks';

const initialState: UserState = {
  ...createAsyncState<User>(),
  user: {
    id: '',
    firstName: '',
    lastName: '',
    email: '',
    avatar: '',
    phone: '',
    address: '',
    bio: '',
    dateOfBirth: '',
  },
  isUpdating: false,
  isUploadingAvatar: false,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get Profile
      .addCase(getProfile.pending, (state) => {
        state.loading = true;
        state.message = '';
      })
      .addCase(getProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
      })
      .addCase(getProfile.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) || 'Get profile failed';
      })

      // Update Profile
      .addCase(updateProfile.pending, (state) => {
        state.isUpdating = true;
        state.message = '';
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isUpdating = false;
        state.user = { ...state.user, ...action.payload };
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isUpdating = false;
        state.message =
          (action.payload?.message as string | null) || 'Update profile failed';
      })

      // Upload Avatar
      .addCase(uploadAvatar.pending, (state) => {
        state.isUploadingAvatar = true;
        state.message = '';
      })
      .addCase(uploadAvatar.fulfilled, (state, action) => {
        state.isUploadingAvatar = false;
        state.user.avatar = action.payload.avatarUrl;
      })
      .addCase(uploadAvatar.rejected, (state, action) => {
        state.isUploadingAvatar = false;
        state.message =
          (action.payload?.message as string | null) || 'Upload avatar failed';
      });
  },
});

export const userSliceReducer = userSlice.reducer;
