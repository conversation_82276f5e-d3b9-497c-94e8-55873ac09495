import { createSlice } from '@reduxjs/toolkit';
import {
  createOtp,
  forgotPassword,
  loginUser,
  logoutUser,
  registerUser,
  verifyOtp,
} from '../thunks/authThunks';
import { AuthState } from '../types/authType';
import { createAsyncState } from '../types/baseType';

const initialState: AuthState = {
  ...createAsyncState(),
  isAuthenticated: false,
  otpVerified: false,
  resetEmail: null,
  verifiedOtp: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.message = null;
    },
    clearOtpState: (state) => {
      state.otpVerified = false;
      state.resetEmail = null;
      state.verifiedOtp = null;
    },
    setResetEmail: (state, action) => {
      state.resetEmail = action.payload;
    },
    setVerifiedOtp: (state, action) => {
      state.verifiedOtp = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login user
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.lastUpdated = Date.now();
        state.message = action.payload?.message ?? '';
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ?? 'Login failed';
        state.isAuthenticated = false;
      })
      // Register user
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.lastUpdated = Date.now();
        state.message = action.payload?.message ?? 'Register successfully';
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ?? 'Register failed';
        state.isAuthenticated = false;
      })
      // Create OTP
      .addCase(createOtp.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(createOtp.fulfilled, (state, action) => {
        state.loading = false;
        state.lastUpdated = Date.now();
        state.message = action.payload?.message ?? 'OTP sent successfully';
      })
      .addCase(createOtp.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ?? 'Failed to send OTP';
      })
      // Verify OTP
      .addCase(verifyOtp.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(verifyOtp.fulfilled, (state, action) => {
        state.loading = false;
        state.otpVerified = true;
        state.verifiedOtp = action.meta.arg.otp;
        state.lastUpdated = Date.now();
        state.message = action.payload?.message ?? 'OTP verified successfully';
      })
      .addCase(verifyOtp.rejected, (state, action) => {
        state.loading = false;
        state.otpVerified = false;
        state.message =
          (action.payload?.message as string | null) ?? 'Invalid OTP';
      })
      // Forgot Password
      .addCase(forgotPassword.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(forgotPassword.fulfilled, (state, action) => {
        state.loading = false;
        state.otpVerified = false;
        state.verifiedOtp = action.meta.arg.otp;
        state.lastUpdated = Date.now();
        state.message =
          action.payload?.message ?? 'Password reset successfully';
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ??
          'Failed to reset password';
      })

      .addCase(logoutUser.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.data = null;
        state.message = '';
        state.lastUpdated = Date.now();
      });
  },
});

export const { clearError, clearOtpState, setResetEmail, setVerifiedOtp } =
  authSlice.actions;
export default authSlice.reducer;
