import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  createAnalystByRole,
  createAnalystByJD,
  getAnalystById,
  getMyAnalysts,
  deleteAnalyst,
} from '../thunks/analystThunks';
import { AnalystState } from '../types/analystType';
import { createAsyncState } from '../types/baseType';
import { AnalystResponse, ApiResponse } from '@/services/types/analyst';

const initialState: AnalystState = {
  ...createAsyncState<AnalystResponse[]>(),
  currentAnalyst: null,
  myAnalysts: [],
};

const analystSlice = createSlice({
  name: 'analyst',
  initialState,
  reducers: {
    clearError: (state) => {
      state.message = null;
    },
    clearCurrentAnalyst: (state) => {
      state.currentAnalyst = null;
    },
    setCurrentAnalyst: (state, action: PayloadAction<AnalystResponse>) => {
      state.currentAnalyst = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create Analyst by Role
      .addCase(createAnalystByRole.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(
        createAnalystByRole.fulfilled,
        (state, action: PayloadAction<ApiResponse<AnalystResponse>>) => {
          state.loading = false;
          state.currentAnalyst = action.payload.data;
          state.lastUpdated = Date.now();
          state.message = null; // Clear message on success
          // Add to myAnalysts if it exists
          if (state.myAnalysts) {
            state.myAnalysts.unshift(action.payload.data);
          }
        },
      )
      .addCase(createAnalystByRole.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ??
          'Failed to create analyst';
      })

      // Create Analyst by JD
      .addCase(createAnalystByJD.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(
        createAnalystByJD.fulfilled,
        (state, action: PayloadAction<ApiResponse<AnalystResponse>>) => {
          state.loading = false;
          state.currentAnalyst = action.payload.data;
          state.lastUpdated = Date.now();
          state.message = null; // Clear message on success
          // Add to myAnalysts if it exists
          if (state.myAnalysts) {
            state.myAnalysts.unshift(action.payload.data);
          }
        },
      )
      .addCase(createAnalystByJD.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ??
          'Failed to create analyst';
      })

      // Get Analyst by ID
      .addCase(getAnalystById.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(
        getAnalystById.fulfilled,
        (state, action: PayloadAction<ApiResponse<AnalystResponse>>) => {
          state.loading = false;
          state.currentAnalyst = action.payload.data;
          state.lastUpdated = Date.now();
          state.message = null; // Clear message on success
        },
      )
      .addCase(getAnalystById.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ??
          'Failed to fetch analyst';
      })

      // Get My Analysts
      .addCase(getMyAnalysts.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(
        getMyAnalysts.fulfilled,
        (state, action: PayloadAction<ApiResponse<AnalystResponse[]>>) => {
          state.loading = false;
          state.myAnalysts = action.payload.data;
          state.data = action.payload.data;
          state.lastUpdated = Date.now();
          state.message = null; // Clear message on success
        },
      )
      .addCase(getMyAnalysts.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ??
          'Failed to fetch analysts';
      })

      // Delete Analyst
      .addCase(deleteAnalyst.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(
        deleteAnalyst.fulfilled,
        (state, action: PayloadAction<string>) => {
          state.loading = false;
          const deletedId = action.payload;

          // Remove from myAnalysts
          state.myAnalysts = state.myAnalysts.filter(
            (analyst) => analyst._id !== deletedId,
          );

          // Remove from data
          if (state.data) {
            state.data = state.data.filter(
              (analyst) => analyst._id !== deletedId,
            );
          }

          // Clear currentAnalyst if it's the deleted one
          if (state.currentAnalyst?._id === deletedId) {
            state.currentAnalyst = null;
          }

          state.lastUpdated = Date.now();
          state.message = 'Analysis deleted successfully';
        },
      )
      .addCase(deleteAnalyst.rejected, (state, action) => {
        state.loading = false;
        state.message =
          (action.payload?.message as string | null) ??
          'Failed to delete analysis';
      });
  },
});

export const { clearError, clearCurrentAnalyst, setCurrentAnalyst } =
  analystSlice.actions;
export default analystSlice.reducer;
