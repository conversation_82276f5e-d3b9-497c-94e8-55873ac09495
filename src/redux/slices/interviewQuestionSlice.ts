import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { InterviewQuestionState } from '../types/interviewQuestionType';
import { createAsyncState } from '../types/baseType';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import {
  createInterviewQuestionThunk,
  deleteInterviewQuestionThunk,
  getInterviewQuestionById,
  getMyInterviewQuestion,
} from '../thunks/interviewQuestionThunk';
import { ApiResponse } from '@/common/types/api';

const initialState: InterviewQuestionState = {
  ...createAsyncState<InterviewQuestionSessionResponse[]>(),
  currentInterviewQuestion: null,
  myInterviewQuestions: [],
};

const interviewQuestionSlice = createSlice({
  name: 'interviewQuestion',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getMyInterviewQuestion.pending, (state) => {
        state.loading = true;
      })
      .addCase(
        getMyInterviewQuestion.fulfilled,
        (
          state,
          action: PayloadAction<
            ApiResponse<InterviewQuestionSessionResponse[]>
          >,
        ) => {
          state.loading = false;
          state.myInterviewQuestions =
            'data' in action.payload ? action.payload.data : [];
          state.lastUpdated = Date.now();
          state.message = null;
        },
      )
      .addCase(getMyInterviewQuestion.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      })
      .addCase(getInterviewQuestionById.pending, (state) => {
        state.loading = true;
      })
      .addCase(
        getInterviewQuestionById.fulfilled,
        (
          state,
          action: PayloadAction<ApiResponse<InterviewQuestionSessionResponse>>,
        ) => {
          state.loading = false;
          state.currentInterviewQuestion =
            'data' in action.payload ? action.payload.data : null;
          state.lastUpdated = Date.now();
          state.message = null;
        },
      )
      .addCase(getInterviewQuestionById.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      })
      .addCase(createInterviewQuestionThunk.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(
        createInterviewQuestionThunk.fulfilled,
        (
          state,
          action: PayloadAction<ApiResponse<InterviewQuestionSessionResponse>>,
        ) => {
          state.loading = false;
          state.message = null;
          state.lastUpdated = Date.now();
          state.currentInterviewQuestion =
            'data' in action.payload ? action.payload.data : null;
          if (state.myInterviewQuestions && 'data' in action.payload) {
            state.myInterviewQuestions.unshift(action.payload.data);
          }
        },
      )
      .addCase(createInterviewQuestionThunk.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      })
      .addCase(deleteInterviewQuestionThunk.pending, (state) => {
        state.loading = true;
        state.message = null;
      })
      .addCase(deleteInterviewQuestionThunk.fulfilled, (state, action) => {
        state.loading = false;
        const deletedId = action.payload;

        // Remove from myInterviewQuestions
        state.myInterviewQuestions = state.myInterviewQuestions.filter(
          (question) => question._id !== deletedId,
        );
      })
      .addCase(deleteInterviewQuestionThunk.rejected, (state, action) => {
        state.loading = false;
        state.message = action.payload?.message as string | null;
      });
  },
});

export const {} = interviewQuestionSlice.actions;
export default interviewQuestionSlice.reducer;
