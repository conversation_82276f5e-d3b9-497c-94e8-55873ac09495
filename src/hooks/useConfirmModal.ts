import { useState, useCallback } from 'react';
import { Alert } from 'react-native';

interface ConfirmModalOptions {
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  destructive?: boolean;
}

interface UseConfirmModalReturn {
  showConfirm: (
    options: ConfirmModalOptions,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void | Promise<void>,
  ) => void;
  isVisible: boolean;
}

export const useConfirmModal = (): UseConfirmModalReturn => {
  const [isVisible, setIsVisible] = useState(false);

  const showConfirm = useCallback(
    (
      options: ConfirmModalOptions,
      onConfirm: () => void | Promise<void>,
      onCancel?: () => void | Promise<void>,
    ) => {
      const {
        title = 'Xác nhận',
        message,
        confirmText = 'Có',
        cancelText = 'Hủy',
        destructive = false,
      } = options;

      setIsVisible(true);

      Alert.alert(
        title,
        message,
        [
          {
            text: cancelText,
            style: 'cancel',
            onPress: async () => {
              setIsVisible(false);
              if (onCancel) {
                await onCancel();
              }
            },
          },
          {
            text: confirmText,
            style: destructive ? 'destructive' : 'default',
            onPress: async () => {
              setIsVisible(false);
              await onConfirm();
            },
          },
        ],
        { cancelable: true },
      );
    },
    [],
  );

  return {
    showConfirm,
    isVisible,
  };
};
