import '../utils/dailyPatch';

import { useEffect, useRef, useState } from 'react';
import Vapi from '@vapi-ai/react-native';
import { Audio } from 'expo-av';
import Constants from 'expo-constants';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import { CreateAssistantDTO } from '@vapi-ai/react-native/dist/api';
import { activateKeepAwakeAsync, deactivateKeep<PERSON>wake } from 'expo-keep-awake';

export const useVapi = () => {
  const [assistantSpeech, setAssistantSpeech] = useState<string>('');
  const [userSpeech, setUserSpeech] = useState<string>('');
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [hasPermissions, setHasPermissions] = useState(false);

  const [conversationLog, setConversationLog] = useState<
    Array<{
      timestamp: string;
      role: 'assistant' | 'user';
      message: string;
      isComplete: boolean;
    }>
  >([]);
  const [conversationLatest, setConversationLatest] = useState<
    Array<{
      timestamp: string;
      role: 'assistant' | 'user' | 'system';
      message: string;
    }>
  >([]);

  const vapiRef = useRef<Vapi | null>(null);
  const currentSpeakerRef = useRef<'assistant' | 'user' | null>(null);

  // Request permissions
  const requestPermissions = async () => {
    try {
      // Request recording permissions
      const { status } = await Audio.requestPermissionsAsync();

      // Setup audio mode for voice calls
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false, // Use speaker instead of earpiece
      });

      console.log('🔊 Audio mode configured for voice calls');

      const granted = status === 'granted';
      setHasPermissions(granted);
      return granted;
    } catch (error) {
      console.error('❌ Error requesting permissions:', error);
      return false;
    }
  };

  // Initialize VAPI
  useEffect(() => {
    // Request permissions on mount
    requestPermissions();

    if (!vapiRef.current) {
      const apiKey =
        Constants.expoConfig?.extra?.VAPI_KEY ||
        process.env.EXPO_PUBLIC_VAPI_KEY ||
        '';
      vapiRef.current = new Vapi(apiKey);
    }

    const vapi = vapiRef.current;

    // Event handlers (same as web version)
    const handleCallStart = () => {
      console.log('🟢 VAPI Call Started');
      setIsCallActive(true);
      setConversationLog([]);
      currentSpeakerRef.current = null;
    };

    const handleCallEnd = () => {
      console.log('🔴 VAPI Call Ended');
      setIsCallActive(false);
      setIsMuted(false);
    };

    const handleTranscript = (transcript: any) => {
      console.log('📝 Transcript received:', transcript);

      const message = transcript.transcript || transcript.text || '';
      const role = transcript.role as 'assistant' | 'user';
      const isFinal = transcript.type === 'transcript';
      const conversationLatest = transcript?.conversation;

      if (conversationLatest) {
        setConversationLatest(conversationLatest);
      }

      if (!message || message.trim().length < 1) {
        return;
      }

      const timestamp = new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });

      // Update real-time speech indicators
      if (role === 'assistant') {
        setAssistantSpeech(message);
      } else {
        setUserSpeech(message);
      }

      setConversationLog((prev) => {
        const newLog = [...prev];
        const lastMessage = newLog[newLog.length - 1];
        const isSameSpeaker =
          lastMessage && lastMessage.role === role && !lastMessage.isComplete;

        if (isSameSpeaker) {
          newLog[newLog.length - 1] = {
            ...lastMessage,
            message: message.trim(),
            timestamp: isFinal ? timestamp : lastMessage.timestamp,
            isComplete: isFinal,
          };
        } else {
          const newMessage = {
            timestamp,
            role,
            message: message.trim(),
            isComplete: isFinal,
          };
          newLog.push(newMessage);
        }

        currentSpeakerRef.current = role;
        return newLog;
      });
    };

    const handleError = (error: any) => {
      console.error('❌ VAPI Error:', error);
      setIsCallActive(false);
    };

    // Add event listeners
    vapi.on('call-start', handleCallStart);
    vapi.on('call-end', handleCallEnd);
    vapi.on('message', handleTranscript);
    vapi.on('error', handleError);

    return () => {
      vapi.off('call-start', handleCallStart);
      vapi.off('call-end', handleCallEnd);
      vapi.off('message', handleTranscript);
      vapi.off('error', handleError);
    };
  }, []);

  const createAssistantOptions = (
    interviewQuestions: InterviewQuestionSessionResponse,
    carrier: string,
  ) => {
    let questionList = '';
    interviewQuestions.interviewQuestions.map((item) => {
      questionList = item.question + ',' + questionList;
    });

    console.log('💬 Question List:', questionList);
    let language = '';
    language = 'en';

    return {
      name: 'Learn Vox AI',
      firstMessage:
        language === 'vi'
          ? `Chào ${carrier}! Bạn đã sẵn sàng cho buổi phỏng vấn vị trí ${interviewQuestions.jobPosition} chưa?`
          : `Hi ${carrier}, how are you? Ready for your interview for ${interviewQuestions.jobPosition}?`,

      transcriber: {
        provider: 'google',
        model: 'gemini-2.0-flash',
        language: language === 'vi' ? 'Vietnamese' : 'English',
      },

      voice: {
        provider: language === 'vi' ? 'azure' : 'playht',
        voiceId: language === 'vi' ? 'hoaimy' : 'jennifer',
      },

      model: {
        provider: 'openai',
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `
              You are an AI voice assistant conducting a technical mock interview for the position of ${
                interviewQuestions.jobPosition
              }. Your job is to ask **only** the interview questions provided, in the **exact order given**, one at a time.
                        
              Questions:
              ${questionList}
                        
              Language: Speak and listen in ${
                language === 'vi' ? 'Vietnamese' : 'English'
              } only.
                        
              Instructions:
              - Start with a short and friendly greeting. Mention that this is a mock interview for the role of ${
                interviewQuestions.jobPosition
              }.
              - Ask one question at a time, following the exact order above.
              - **After asking a question, wait patiently for the candidate's response. Give them time to think and answer.**
              - **Wait at least 8 seconds before prompting again if there's no response.**
              - **If the candidate is silent for 8+ seconds, you can gently encourage them with phrases like "Take your time" or "Think about it carefully".**
              - **Do not ask any questions that are not on the list.**
              - If the candidate seems stuck or confused, gently offer a hint or rephrase **only that specific question** without changing its original meaning.
              - After the answer, give short and supportive feedback (e.g., "Good job", "That's a valid point", etc.).
              - Keep responses concise, friendly, and professional.
              - **Speak slowly and clearly to ensure the candidate can understand.**
              - **Pause naturally between sentences to give a conversational flow.**
                        
              Rules:
              - **Do not invent or add new questions.**
              - **Do not interrupt the candidate while they are speaking or thinking.**
              - **Give candidates adequate time to think and respond (minimum 8 seconds).**
              - **Be patient and encouraging, especially if candidates need time to think.**
              - **Speak only when it's your turn (e.g., greeting, question, feedback).**
              - Speak **slowly and clearly**.
              - Use **a slower, calm, and steady pace** to improve comprehension.
              - Emphasize key words and **pause between sentences**.
              - **Respond strictly in ${
                language === 'vi' ? 'Vietnamese' : 'English'
              } only.**
              - **Speak at a moderate, conversational pace - not too fast.**
              - **Use natural pauses and inflections to sound more human-like.**
                        
              Encouraging phrases for silence:
              ${
                language === 'vi'
                  ? '- "Bạn có thể suy nghĩ thêm một chút"\n- "Không vội, hãy cứ từ từ"\n- "Bạn có muốn mình nhắc lại câu hỏi không?"'
                  : '- "Take your time to think about it"\n- "No rush, think it through"\n- "Would you like me to repeat the question?"'
              }
                        
              End the interview by summarizing the session positively and thanking the candidate.
              Closing example:
              "${
                language === 'vi'
                  ? 'Cảm ơn bạn đã tham gia buổi phỏng vấn thử. Chúc bạn học tốt và thành công trong sự nghiệp!'
                  : 'Thank you for participating in this mock interview. Keep learning and best of luck in your career!'
              }"
            `.trim(),
          },
        ],
      },
    };
  };

  const startCall = async (
    interviewQuestions: InterviewQuestionSessionResponse,
    carrier: string,
  ) => {
    if (!interviewQuestions || !vapiRef.current) return;

    // Check permissions first
    if (!hasPermissions) {
      const granted = await requestPermissions();
      if (!granted) {
        console.error('❌ Audio permission required');
        return;
      }
    }

    try {
      console.log('🚀 Starting VAPI call...');

      // Setup audio mode again before call
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
      console.log('🔊 Audio mode re-configured for call');

      await activateKeepAwakeAsync();
      const assistantOptions = createAssistantOptions(
        interviewQuestions,
        carrier,
      );
      await vapiRef.current.start(assistantOptions as CreateAssistantDTO);
    } catch (error) {
      console.error('❌ Error starting call:', error);
      deactivateKeepAwake();
      // Bypass keep awake error specifically
      if (
        error instanceof Error &&
        error.message?.includes('setKeepDeviceAwake')
      ) {
        console.warn('⚠️ Keep awake not supported, continuing without it');
        // You might want to implement alternative logic here
      }
      setIsCallActive(false);
    }
  };

  // End call
  const endCall = async () => {
    if (!vapiRef.current) return;
    try {
      vapiRef.current.stop();
      setIsCallActive(false);
    } catch (error) {
      console.error('❌ Error ending call:', error);
      setIsCallActive(false);
    }
    deactivateKeepAwake();
  };

  const toggleMute = () => {
    if (!vapiRef.current) return;
    try {
      if (isMuted) {
        vapiRef.current.setMuted(false);
        setIsMuted(false);
      } else {
        vapiRef.current.setMuted(true);
        setIsMuted(true);
      }
    } catch (error) {
      console.error('❌ Error toggling mute:', error);
    }
  };

  return {
    assistantSpeech,
    userSpeech,
    isCallActive,
    isMuted,
    hasPermissions,
    conversationLog,
    conversationLatest,
    startCall,
    endCall,
    toggleMute,
    requestPermissions,
    setAssistantSpeech,
    setUserSpeech,
  };
};
