import { useState, useEffect } from 'react';
import { appService } from '@/services';

export const useFirstLaunch = () => {
  const [isFirstLaunch, setIsFirstLaunch] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkFirstLaunch();
  }, []);

  const checkFirstLaunch = async () => {
    try {
      const isFirstLaunch = await appService.getIsFirstLaunch();
      setIsFirstLaunch(isFirstLaunch);
    } finally {
      setIsLoading(false);
    }
  };

  const markAsLaunched = async () => {
    try {
      await appService.setIsFirstLaunch('false');
      setIsFirstLaunch(false);
    } catch (error) {
      console.error('Error marking app as launched:', error);
      setIsFirstLaunch(false);
    }
  };

  const resetFirstLaunch = async () => {
    try {
      await appService.setIsFirstLaunch('true');
      setIsFirstLaunch(true);
    } catch (error) {
      console.error('Error resetting first launch:', error);
      setIsFirstLaunch(true);
    }
  };

  return {
    isFirstLaunch,
    isLoading,
    markAsLaunched,
    resetFirstLaunch,
  };
};
