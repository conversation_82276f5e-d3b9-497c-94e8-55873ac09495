import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Feature } from '@/common/constants/features.constants';
import { HomeStackParamList } from '@/common/types/rootParamList';

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

export const useFeatureNavigation = () => {
  const navigation = useNavigation<NavigationProp>();

  const navigateToFeature = (feature: Feature) => {
    switch (feature.type) {
      case 'mock-interview':
        navigation.navigate('MockInterview');
        break;
      case 'ai-chat':
        navigation.navigate('AIChat');
        break;
      case 'career-roadmap':
        navigation.navigate('CareerRoadmap');
        break;
      case 'interviews':
        navigation.navigate('Analysis');
        break;
      default:
        console.log('Unknown feature type:', feature.type);
    }
  };

  return { navigateToFeature };
};
