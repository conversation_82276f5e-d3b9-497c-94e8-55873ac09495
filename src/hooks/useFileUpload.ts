import { useState } from 'react';
import { Alert } from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import { showAuthToast } from '@/utils/toastUtils';

export interface FileUploadOptions {
  maxSizeInMB?: number;
  allowedTypes?: string[];
  successMessage?: string;
  errorMessage?: string;
}

export const useFileUpload = (options: FileUploadOptions = {}) => {
  const {
    maxSizeInMB = 10,
    allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
    successMessage = 'File selected successfully',
    errorMessage = 'Unable to select file',
  } = options;

  const [isUploading, setIsUploading] = useState(false);

  const uploadFile =
    async (): Promise<DocumentPicker.DocumentPickerResult | null> => {
      try {
        setIsUploading(true);

        const result = await DocumentPicker.getDocumentAsync({
          type: allowedTypes,
          copyToCacheDirectory: true,
          multiple: false,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const file = result.assets[0];

          // Check file size
          if (file.size && file.size > maxSizeInMB * 1024 * 1024) {
            Alert.alert(
              'Error',
              `File size must be less than ${maxSizeInMB}MB`,
            );
            return null;
          }

          showAuthToast.success(successMessage);
          return result;
        }

        return null;
      } catch (error) {
        console.error('Error selecting file:', error);
        showAuthToast.error(errorMessage);
        return null;
      } finally {
        setIsUploading(false);
      }
    };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return {
    uploadFile,
    formatFileSize,
    isUploading,
  };
};
