import { useTheme } from '@/providers/ThemeProvider';

export const useThemeColors = () => {
  const { theme, mode, setTheme, toggleTheme, isLoaded, systemTheme, isDark } =
    useTheme();
  const safeTheme = theme || 'light';

  return {
    // Background colors
    background: safeTheme === 'dark' ? '#111827' : '#ffffff', // gray-900 : white
    backgroundSecondary: safeTheme === 'dark' ? '#1F2937' : '#F9FAFB', // gray-800 : gray-50
    surface: safeTheme === 'dark' ? '#1F2937' : '#ffffff', // gray-800 : white
    surfaceSecondary: safeTheme === 'dark' ? '#374151' : '#F3F4F6', // gray-700 : gray-100

    // Text colors
    text: safeTheme === 'dark' ? '#ffffff' : '#111827', // white : gray-900
    textSecondary: safeTheme === 'dark' ? '#D1D5DB' : '#6B7280', // gray-300 : gray-600
    textMuted: safeTheme === 'dark' ? '#9CA3AF' : '#6B7280', // gray-400 : gray-500

    // Border colors
    border: safeTheme === 'dark' ? '#374151' : '#E5E7EB', // gray-700 : gray-200
    borderLight: safeTheme === 'dark' ? '#4B5563' : '#F3F4F6', // gray-600 : gray-100

    // Card colors
    card: safeTheme === 'dark' ? '#1F2937' : '#ffffff', // gray-800 : white
    cardBorder: safeTheme === 'dark' ? '#374151' : '#F3F4F6', // gray-700 : gray-100
    cardBackground: safeTheme === 'dark' ? '#374151' : '#F3F4F6', // gray-700 : gray-100
    featureCard: safeTheme === 'dark' ? '#374151' : '#ffffff', // gray-700 : white

    // Primary colors
    primary: safeTheme === 'dark' ? '#2563EB' : '#3B82F6', // blue-600 : blue-500
    primaryText: '#ffffff',
    primaryLight: safeTheme === 'dark' ? '#1D4ED8' : '#DBEAFE', // blue-700 : blue-100

    // Secondary colors
    success: safeTheme === 'dark' ? '#10B981' : '#059669', // green-500 : green-600
    warning: safeTheme === 'dark' ? '#F59E0B' : '#D97706', // amber-500 : amber-600
    error: safeTheme === 'dark' ? '#EF4444' : '#DC2626', // red-500 : red-600
    accent: safeTheme === 'dark' ? '#8B5CF6' : '#7C3AED', // violet-500 : violet-600

    // Icon colors
    icon: safeTheme === 'dark' ? '#D1D5DB' : '#6B7280', // gray-300 : gray-600
    iconActive: safeTheme === 'dark' ? '#FFFFFF' : '#111827', // white : gray-900

    // Shadow color
    shadow: theme === 'dark' ? '#000000' : '#000000',

    // Header colors
    headerBackground: theme === 'dark' ? '#111827' : '#ffffff',

    // Status bar
    statusBarStyle: (theme === 'dark' ? 'light-content' : 'dark-content') as
      | 'light-content'
      | 'dark-content',

    // Theme management functions
    theme,
    mode,
    setTheme,
    toggleTheme,
    isLoaded,
    systemTheme,
    isDark: theme === 'dark',
  };
};
