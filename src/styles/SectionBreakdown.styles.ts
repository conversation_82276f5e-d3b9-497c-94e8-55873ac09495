import { StyleSheet } from 'react-native';

// Note: This styles file contains hardcoded colors and should be replaced with theme-aware styling
// Consider using useThemeColors hook in components instead of this static stylesheet
export const sectionBreakdownStyles = StyleSheet.create({
  sectionContainer: {
    marginBottom: 24,
    backgroundColor: '#FFFFFF', // Should use colors.surface
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000', // Should use colors.shadow
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },

  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },

  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },

  contactIconContainer: {
    backgroundColor: '#10B981',
  },

  experienceIconContainer: {
    backgroundColor: '#3B82F6',
  },

  educationIconContainer: {
    backgroundColor: '#8B5CF6',
  },

  skillsIconContainer: {
    backgroundColor: '#EF4444',
  },

  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
  },

  // Progress bar styles
  progressBarContainer: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginBottom: 16,
    overflow: 'hidden',
  },

  progressBar: {
    height: '100%',
    borderRadius: 4,
  },

  contactProgressBar: {
    backgroundColor: '#10B981',
  },

  experienceProgressBar: {
    backgroundColor: '#3B82F6',
  },

  educationProgressBar: {
    backgroundColor: '#8B5CF6',
  },

  skillsProgressBar: {
    backgroundColor: '#EF4444',
  },

  // Content styles
  statusSection: {
    marginBottom: 12,
  },

  statusLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },

  goodStatusLabel: {
    color: '#059669',
  },

  badStatusLabel: {
    color: '#DC2626',
  },

  // Item container styles
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 4,
    borderRadius: 8,
  },

  goodItemContainer: {
    backgroundColor: '#ECFDF5',
  },

  badItemContainer: {
    backgroundColor: '#FEF2F2',
  },

  itemText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },

  goodItemText: {
    color: '#065F46',
  },

  badItemText: {
    color: '#991B1B',
  },
});
