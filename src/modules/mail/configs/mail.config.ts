import { registerAs } from '@nestjs/config';

import {
  IsString,
  IsInt,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ptional,
  IsBoolean,
} from 'class-validator';
import validateConfig from '@utils/validate-config';
import MailConfig from 'src/types/mail-config.type';
import { Transform } from 'class-transformer';

class EnvironmentVariablesValidator {
  @IsInt()
  @Min(0)
  @Max(65535)
  @IsOptional()
  MAIL_PORT: number;

  @IsString()
  MAIL_HOST: string;

  @IsString()
  MAIL_USER: string;

  @IsString()
  @IsOptional()
  MAIL_PASSWORD: string;

  @IsBoolean()
  @Transform(({ value }) => value === 'false')
  MAIL_IGNORE_TLS: boolean;

  @IsBoolean()
  @Transform(({ value }) => value === 'false')
  MAIL_SECURE: boolean;

  @IsBoolean()
  @Transform(({ value }) => value === 'false')
  MAIL_REQUIRE_TLS: boolean;
}

export default registerAs<MailConfig>('mail', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    port: process.env.MAIL_PORT ? parseInt(process.env.MAIL_PORT, 10) : 587,
    host: process.env.MAIL_HOST,
    user: process.env.MAIL_USER,
    password: process.env.MAIL_PASSWORD,
    ignoreTLS: process.env.MAIL_IGNORE_TLS === 'true',
    secure: process.env.MAIL_SECURE === 'true',
    requireTLS: process.env.MAIL_REQUIRE_TLS === 'true',
  };
});
