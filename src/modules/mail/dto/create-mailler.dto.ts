import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString } from 'class-validator';
export class WelcomeMailDTO {
  @ApiProperty({
    description: 'Username',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    description: 'Email',
    // example: '<EMAIL>',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}
