import {
  Controller,
  Post,
  Param,
  UseGuards,
  Body,
  Req,
  Get,
  Delete,
} from '@nestjs/common';
import { ChatAiService } from './chat-ai.service';
import { ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import { CreateChatAiDto } from './dto/create-chat-ai.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { ResponseContentChatAiDto } from './dto/response-chat-ai.dto';

@Controller({ path: 'chat-ai', version: '1' })
@ApiBearerAuth('Authorization')
export class ChatAiController {
  constructor(private readonly chatAiService: ChatAiService) {}

  @UseGuards(JwtAuthGuard)
  @ApiOperationAuto('Create a new session', 'Create a new session')
  @Post('chat-session/:sessionId')
  async chatSession(
    @Param('sessionId') sessionId: string,
    @Body() body: CreateChatAiDto,
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;
    const response = (await this.chatAiService.chatSession(
      sessionId,
      body,
      userId,
    )) as unknown as ResponseContentChatAiDto;
    return new ApiResponseDto(200, 'Chat successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperationAuto('Get all chat sessions', 'Get all chat sessions')
  @Get('chat-sessions')
  async getChatSessions() {
    const response = await this.chatAiService.getAll();
    return new ApiResponseDto(200, 'Chat sessions successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperationAuto(
    'Get a chat session by sessionId',
    'Get a chat session by sessionId',
  )
  @Get('chat-session/:sessionId')
  async getChatSession(@Param('sessionId') sessionId: string) {
    const response = await this.chatAiService.findHistoryBySessionId(sessionId);
    return new ApiResponseDto(200, 'Chat session successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperationAuto('Get all my chat sessions', 'Get all my chat sessions')
  @Get('get-my-sessions')
  async getMySessions(@Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.chatAiService.getMySessions(userId);
    return new ApiResponseDto(200, 'Chat sessions successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @ApiOperationAuto(
    'Delete a chat session by sessionId',
    'Delete a chat session by sessionId',
  )
  @Delete('chat-session/:sessionId')
  async deleteChatSession(@Param('sessionId') sessionId: string) {
    await this.chatAiService.deleteChatSession(sessionId);
    return new ApiResponseDto(200, 'Chat session successfully');
  }
}
