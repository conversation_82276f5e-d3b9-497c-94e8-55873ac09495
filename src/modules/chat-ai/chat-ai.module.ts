import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ChatAiService } from './chat-ai.service';
import { ChatAiController } from './chat-ai.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { History, HistorySchema } from './schemas/history.schema';
import { JwtModule } from '@nestjs/jwt';
import { AiModule } from '@modules/ai/ai.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: History.name, schema: HistorySchema }]),
    JwtModule,
    AiModule,
  ],
  controllers: [ChatAiController],
  providers: [ChatAiService],
})
export class ChatAiModule {}
