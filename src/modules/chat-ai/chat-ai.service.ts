import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { History, HistoryDocument } from './schemas/history.schema';
import { CreateChatAiDto } from './dto/create-chat-ai.dto';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import {
  ResponseChatAiDto,
  ResponseContentChatAiDto,
} from './dto/response-chat-ai.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import { plainToInstance } from 'class-transformer';
import { AiChatAgent } from '@modules/ai/agents/ai-chat.agent';

@Injectable()
export class ChatAiService {
  constructor(
    @InjectModel(History.name)
    private readonly historyModel: Model<HistoryDocument>,
    @InjectRedis()
    private readonly redis: Redis,
    private readonly aiChatAgent: AiChatAgent,
  ) {}

  async chatSession(
    sessionId: string,
    body: CreateChatAiDto,
    userId: string,
  ): Promise<ResponseContentChatAiDto> {
    const key = `chat_session_${sessionId}`;
    let historySession: ResponseContentChatAiDto[] = [];
    const rawMessages = await this.redis.lrange(key, 0, -1);

    if (rawMessages.length > 0) {
      historySession = rawMessages.map(
        (msg) => JSON.parse(msg) as ResponseContentChatAiDto,
      );
    } else {
      // if historySession is empty, get history from database
      const history = await this.findHistoryBySessionId(sessionId);

      // if historySession is still empty, create new session
      if (history?.content?.length) {
        historySession = history.content;
        // cache lại vào Redis
        const pipeline = this.redis.pipeline();
        historySession.forEach((msg) =>
          pipeline.rpush(key, JSON.stringify(msg)),
        );
        pipeline.expire(key, 60 * 60);
        await pipeline.exec();
      } else {
        const name = body.message.slice(0, 10);
        // create new session if historySession is empty
        await this.createSession(sessionId, userId, name);
      }
    }

    const userMessage = {
      role: 'user',
      content: body.message,
      timestamp: new Date().toISOString(),
    } as ResponseContentChatAiDto;

    historySession.push(userMessage);
    const responseAi = await this.aiChatAgent.aiChatAgent({
      history: historySession,
      userInput: body.message,
    });

    const assistantMessage = {
      role: 'assistant',
      content: responseAi,
      timestamp: new Date().toISOString(),
    } as ResponseContentChatAiDto;

    historySession.push(assistantMessage);

    await this.redis.rpush(key, JSON.stringify(userMessage));
    await this.redis.rpush(key, JSON.stringify(assistantMessage));
    await this.redis.expire(key, 60 * 60);
    await this.updateHistory(sessionId, [userMessage, assistantMessage]);

    return assistantMessage;
  }

  // create new session when user first time chat
  async createSession(
    sessionId: string,
    userId: string,
    name: string,
  ): Promise<ResponseChatAiDto> {
    const response = await this.historyModel.create({
      sessionId,
      userId,
      name,
    });

    const dtoInstance = plainToInstance(ResponseChatAiDto, response, {
      excludeExtraneousValues: true,
    });
    return dtoInstance;
  }

  async updateHistory(sessionId: string, content: ResponseContentChatAiDto[]) {
    await this.historyModel.updateOne(
      { sessionId },
      { $push: { content: { $each: content } } },
    );
  }

  async findHistoryBySessionId(sessionId: string): Promise<ResponseChatAiDto> {
    try {
      if (!sessionId) {
        throw new ValidationException(ErrorCode.C001);
      }
      const response = await this.historyModel.findOne({ sessionId });

      const dtoInstance = plainToInstance(ResponseChatAiDto, response, {
        excludeExtraneousValues: true,
      });
      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAll(): Promise<ResponseChatAiDto[]> {
    try {
      const response = await this.historyModel.find().sort({ updatedAt: -1 });
      const dtoInstance = plainToInstance(ResponseChatAiDto, response, {
        excludeExtraneousValues: true,
      });
      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async deleteChatSession(sessionId: string) {
    try {
      const response = await this.historyModel.deleteOne({ sessionId });
      await this.redis.del(`chat_session_${sessionId}`);
      if (response.deletedCount === 0) {
        throw new ValidationException(ErrorCode.C002);
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getMySessions(userId: string): Promise<ResponseChatAiDto[]> {
    const response = await this.historyModel
      .find({ userId })
      .sort({ updatedAt: -1 });
    const dtoInstance = plainToInstance(ResponseChatAiDto, response, {
      excludeExtraneousValues: true,
    });
    return dtoInstance;
  }
}
