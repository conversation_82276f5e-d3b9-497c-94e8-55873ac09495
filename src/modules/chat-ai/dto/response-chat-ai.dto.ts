import { Expose } from 'class-transformer';

export class ResponseContentChatAiDto {
  @Expose()
  role: 'user' | 'assistant';

  @Expose()
  content: string;

  @Expose()
  timestamp: string;
}

export class ResponseChatAiDto {
  @Expose()
  _id: string;

  @Expose()
  content: ResponseContentChatAiDto[];

  @Expose()
  name: string;

  @Expose()
  userId: string;

  @Expose()
  sessionId: string;
}
