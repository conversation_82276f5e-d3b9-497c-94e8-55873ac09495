import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 as uuidv4 } from 'uuid';
import { Transform } from 'class-transformer';

@Schema({ timestamps: true })
export class History {
  @Prop({ type: String, default: () => uuidv4(), required: true })
  @Transform(({ value }): string => value.toString())
  _id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true, unique: true })
  sessionId: string;

  @Prop({ required: true, type: [Object] })
  content: object[];
}

export type HistoryDocument = History & Document;
export const HistorySchema = SchemaFactory.createForClass(History);
