import { Modu<PERSON> } from '@nestjs/common';
import { RoadmapAiController } from './roadmap-ai.controller';
import { RoadmapAiService } from './roadmap-ai.service';
import { AiModule } from '@modules/ai/ai.module';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { Roadmap, RoadmapSchema } from './schemas/roadmap-ai.schema';
import { ImagekitModule } from '@modules/imagekit/imagekit.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Roadmap.name, schema: RoadmapSchema }]),
    JwtModule,
    AiModule,
    ImagekitModule,
  ],
  controllers: [RoadmapAiController],
  providers: [RoadmapAiService],
  exports: [RoadmapAiService],
})
export class RoadmapAiModule {}
