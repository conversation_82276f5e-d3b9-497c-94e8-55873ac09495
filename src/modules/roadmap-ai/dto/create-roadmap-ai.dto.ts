import { AiAgentAnalystType, AiAgentRoadmapAiType } from '@common/enum/agentType.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class CreateRoadmapAiRoleDescriptionDto {
  @ApiProperty({ example: AiAgentRoadmapAiType.AI_ROADMAP_AI_ROLE })
  @IsEnum(AiAgentRoadmapAiType)
  agentType: AiAgentRoadmapAiType;

  @ApiProperty({
    example: 'Lập trình viên Front-end Developer Senior 2 năm kinh nghiệm',
  })
  @IsString()
  roleDescription: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload CV file (PDF)',
  })
  @IsOptional()
  cvFile?: any;
}

export class CreateRoadmapAiJdDto {
  @ApiProperty({ example: AiAgentRoadmapAiType.AI_ROADMAP_AI_JD })
  @IsEnum(AiAgentRoadmapAiType)
  agentType: AiAgentRoadmapAiType;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload CV file (PDF)',
  })
  @IsOptional()
  cvFile?: any;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload JD file (PDF)',
  })
  @IsOptional()
  jdFile?: any;
}
