import { AiAgentRoadmapAiType } from '@common/enum/agentType.enum';
import { Expose } from 'class-transformer';

export class ResponseRoadmapAiByRoleDescriptionDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentRoadmapAiType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  roleDescription: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}

export class ResponseRoadmapAiByJdDescriptionDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentRoadmapAiType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  jdFileUrl: string;

  @Expose()
  jdText: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}

export class ResponseRoadmapAiDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentRoadmapAiType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  jdFileUrl: string;

  @Expose()
  jdText: string;

  @Expose()
  jobPosition: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
