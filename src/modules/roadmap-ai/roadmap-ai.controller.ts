import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger/dist/decorators/api-bearer.decorator';
import { RoadmapAiService } from './roadmap-ai.service';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import { ApiConsumes } from '@nestjs/swagger';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import {
  CreateRoadmapAiJdDto,
  CreateRoadmapAiRoleDescriptionDto,
} from './dto/create-roadmap-ai.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';

@Controller({ path: 'roadmap-ai', version: '1' })
@ApiBearerAuth('Authorization')
export class RoadmapAiController {
  constructor(private readonly roadmapAiService: RoadmapAiService) {}

  @UseGuards(JwtAuthGuard)
  @Post('roadmap-ai-by-role-description')
  @UseInterceptors(FileInterceptor('cvFile'))
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto(
    'Generate roadmap ai by role description',
    'Generate roadmap ai by role description',
  )
  async createRoadmapAiByRoleDescription(
    @Body() createRoadmapAiRoleDto: CreateRoadmapAiRoleDescriptionDto,
    @UploadedFile() cvFile: Express.Multer.File,
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;

    const response =
      await this.roadmapAiService.createRoadmapAiByRoleDescription(
        createRoadmapAiRoleDto,
        cvFile,
        userId,
      );

    return new ApiResponseDto(201, 'Roadmap AI created successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Post('roadmap-ai-by-jd-description')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'cvFile', maxCount: 1 },
      { name: 'jdFile', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto(
    'Generate roadmap ai by jd description',
    'Generate roadmap ai by jd description',
  )
  async createRoadmapAiByJdDescription(
    @Body() createRoadmapAiJdDto: CreateRoadmapAiJdDto,
    @UploadedFiles()
    files: {
      cvFile?: Express.Multer.File[];
      jdFile?: Express.Multer.File[];
    },
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;
    const cvFile = files.cvFile?.[0];
    const jdFile = files.jdFile?.[0];

    if (!cvFile || !jdFile) {
      throw new ValidationException(ErrorCode.F001);
    }

    const response = await this.roadmapAiService.createRoadmapAiByJdDescription(
      createRoadmapAiJdDto,
      cvFile,
      jdFile,
      userId,
    );

    return new ApiResponseDto(201, 'Roadmap AI created successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Get('roadmap-ai/:id')
  @ApiOperationAuto('Get roadmap ai by id', 'Get roadmap ai by id')
  async getRoadmapAiById(@Param('id') id: string) {
    const response = await this.roadmapAiService.getRoadmapAiById(id);
    return new ApiResponseDto(200, 'Roadmap AI fetched successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-roadmap-ai')
  @ApiOperationAuto('Get my roadmap ai', 'Get my roadmap ai')
  async getMyRoadmapAi(@Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.roadmapAiService.getMyRoadmapAi(userId);
    return new ApiResponseDto(200, 'Roadmap AI fetched successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Get('all-roadmap-ai')
  @ApiOperationAuto('Get all roadmap ai', 'Get all roadmap ai')
  async getAllRoadmapAi() {
    const response = await this.roadmapAiService.getAllRoadmapAi();
    return new ApiResponseDto(200, 'Roadmap AI fetched successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiOperationAuto('Delete roadmap ai by id', 'Delete roadmap ai')
  async deleteRoadmapAi(@Param('id') id: string) {
    await this.roadmapAiService.deleteRoadmapAi(id);
    return new ApiResponseDto(200, 'Roadmap AI deleted successfully');
  }
}
