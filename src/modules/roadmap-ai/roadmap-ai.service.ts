import { Injectable } from '@nestjs/common';
import { Roadmap, RoadmapDocument } from './schemas/roadmap-ai.schema';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  CreateRoadmapAiJdDto,
  CreateRoadmapAiRoleDescriptionDto,
} from './dto/create-roadmap-ai.dto';
import {
  ResponseRoadmapAiByJdDescriptionDto,
  ResponseRoadmapAiByRoleDescriptionDto,
} from './dto/response-roadmap-ai.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import * as pdfParse from 'pdf-parse';
import { plainToInstance } from 'class-transformer';
import { ResumeRoadmapAiRoleDescriptionAgent } from '@modules/ai/agents/roadmap-ai-role.agent';
import { ResumeRoadmapAiJdDescriptionAgent } from '@modules/ai/agents/roadmap-ai-jd.agent';
import { ImageKitService } from '@modules/imagekit/imagekit.service';

@Injectable()
export class RoadmapAiService {
  constructor(
    @InjectModel(Roadmap.name)
    private readonly roadmapAiModel: Model<RoadmapDocument>,
    private readonly ResumeRoadmapAiRoleDescriptionAgent: ResumeRoadmapAiRoleDescriptionAgent,
    private readonly ResumeRoadmapAiJdDescriptionAgent: ResumeRoadmapAiJdDescriptionAgent,
    private readonly imagekitService: ImageKitService,
  ) {}
  async createRoadmapAiByRoleDescription(
    createRoadmapAiDto: CreateRoadmapAiRoleDescriptionDto,
    cvFile: Express.Multer.File,
    userId: string,
  ): Promise<ResponseRoadmapAiByRoleDescriptionDto> {
    try {
      if (!cvFile || cvFile.mimetype !== 'application/pdf') {
        throw new ValidationException(ErrorCode.F001);
      }

      const result = await pdfParse(cvFile.buffer);
      const cvText = result.text;

      const [responseAiService, cvFileUrl] = await Promise.all([
        this.ResumeRoadmapAiRoleDescriptionAgent.createResumeRoleDescription({
          ...createRoadmapAiDto,
          cvText,
        }),
        this.imagekitService.uploadFile(cvFile, 'CV'),
      ]);

      const response = await this.roadmapAiModel.create({
        content: responseAiService,
        agentType: createRoadmapAiDto.agentType,
        userId,
        cvFileUrl,
        cvText,
        roleDescription: createRoadmapAiDto.roleDescription,
      });

      const dtoInstance = plainToInstance(
        ResponseRoadmapAiByRoleDescriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async createRoadmapAiByJdDescription(
    createRoadmapAiJdDto: CreateRoadmapAiJdDto,
    cvFile: Express.Multer.File,
    jdFile: Express.Multer.File,
    userId: string,
  ): Promise<ResponseRoadmapAiByJdDescriptionDto> {
    try {
      // 1. Parse PDF
      const [cvParse, jdParse] = await Promise.all([
        pdfParse(cvFile.buffer),
        pdfParse(jdFile.buffer),
      ]);
      const cvText = cvParse.text;
      const jdText = jdParse.text;

      // 2. Chạy AI và upload song song
      const [responseAiService, cvFileUrl, jdFileUrl] = await Promise.all([
        this.ResumeRoadmapAiJdDescriptionAgent.createResumeJdDescription({
          ...createRoadmapAiJdDto,
          cvText,
          jdText,
        }),
        this.imagekitService.uploadFile(cvFile, 'CV'),
        this.imagekitService.uploadFile(jdFile, 'JD'),
      ]);

      // 3. Lưu vào DB
      const response = await this.roadmapAiModel.create({
        content: responseAiService,
        agentType: createRoadmapAiJdDto.agentType,
        userId,
        cvFileUrl,
        jdFileUrl,
        cvText,
        jdText,
      });

      const dtoInstance = plainToInstance(
        ResponseRoadmapAiByJdDescriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getRoadmapAiById(
    id: string,
  ): Promise<ResponseRoadmapAiByJdDescriptionDto> {
    try {
      const response = await this.roadmapAiModel.findById(id);
      if (!response) {
        throw new ValidationException(ErrorCode.A001);
      }
      const dtoInstance = plainToInstance(
        ResponseRoadmapAiByJdDescriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getMyRoadmapAi(
    userId: string,
  ): Promise<ResponseRoadmapAiByJdDescriptionDto[]> {
    try {
      if (!userId) {
        throw new ValidationException(ErrorCode.U003);
      }
      const response = await this.roadmapAiModel.find({ userId });
      const dtoInstance = plainToInstance(
        ResponseRoadmapAiByJdDescriptionDto,
        response,
        { excludeExtraneousValues: true },
      );
      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAllRoadmapAi(): Promise<ResponseRoadmapAiByJdDescriptionDto[]> {
    try {
      const response = await this.roadmapAiModel.find();
      const dtoInstance = plainToInstance(
        ResponseRoadmapAiByJdDescriptionDto,
        response,
        {
          excludeExtraneousValues: true,
        },
      );
      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async deleteRoadmapAi(roadmapId: string) {
    try {
      const roadmap = await this.roadmapAiModel.findOne({
        _id: roadmapId,
      });

      if (!roadmap) {
        throw new ValidationException(ErrorCode.R001);
      }

      if (roadmap.cvFileUrl) {
        await this.imagekitService.deleteFileByUrl({
          url: roadmap.cvFileUrl,
        });
      }

      if (roadmap.jdFileUrl) {
        await this.imagekitService.deleteFileByUrl({
          url: roadmap.jdFileUrl,
        });
      }

      await this.roadmapAiModel.findByIdAndDelete(roadmapId);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
