import { Module } from '@nestjs/common';
import { AiService } from './ai.service';
import { CloudinaryModule } from '@modules/cloudinary/cloudinary.module';
import { JwtModule } from '@nestjs/jwt';
import { InterviewAgent } from './agents/interview.agent';
import { ResumeRoleDescriptionAgent } from './agents/resume-role.agent';
import { ResumeJdDescriptionAgent } from './agents/resume-jd.agent';
import { AiChatAgent } from './agents/ai-chat.agent';
import { ResumeRoadmapAiJdDescriptionAgent } from './agents/roadmap-ai-jd.agent';
import { ResumeRoadmapAiRoleDescriptionAgent } from './agents/roadmap-ai-role.agent';
import { FeedbackInterviewAgent } from './agents/feedback-interview.agent';
import { CoverLetterRoleDescriptionAgent } from './agents/cover-letter-role.agent';
import { CoverLetterJdDescriptionAgent } from './agents/cover-letter-jd.agent';
import { CoverLetterEvaluationAgent } from './agents/cover-letter-evaluation.agent';
import { AiParseCvAgent } from './agents/ai-parse-cv.agent';

@Module({
  providers: [
    AiService,
    InterviewAgent,
    ResumeRoleDescriptionAgent,
    ResumeJdDescriptionAgent,
    AiChatAgent,
    ResumeRoadmapAiJdDescriptionAgent,
    ResumeRoadmapAiRoleDescriptionAgent,
    FeedbackInterviewAgent,
    CoverLetterRoleDescriptionAgent,
    CoverLetterJdDescriptionAgent,
    CoverLetterEvaluationAgent,
    AiParseCvAgent,
  ],
  imports: [CloudinaryModule, JwtModule],
  exports: [
    AiService,
    InterviewAgent,
    ResumeRoleDescriptionAgent,
    ResumeJdDescriptionAgent,
    AiChatAgent,
    ResumeRoadmapAiJdDescriptionAgent,
    ResumeRoadmapAiRoleDescriptionAgent,
    FeedbackInterviewAgent,
    CoverLetterRoleDescriptionAgent,
    CoverLetterJdDescriptionAgent,
    CoverLetterEvaluationAgent,
    AiParseCvAgent,
  ],
})
export class AiModule {}
