import { Injectable } from '@nestjs/common';
import { AiService } from '../ai.service';
import { CoverLetterEvaluationDto } from '@modules/cover-letter/dto/response-cover-letter.dto';

@Injectable()
export class CoverLetterEvaluationAgent {
  constructor(private readonly aiService: AiService) {}

  async evaluateCoverLetter(
    coverLetterContent: any,
    language: string,
  ): Promise<CoverLetterEvaluationDto> {
    const isVietnamese = language === 'vietnamese';

    const systemPrompt = `
    You are an expert HR professional and cover letter evaluator. Your task is to evaluate a cover letter and provide detailed feedback for improvement.

    IMPORTANT: You MUST respond ONLY with valid JSON format. Do not include any explanatory text, markdown, or commentary. Just pure JSON.

    SCORING GUIDELINES:
    - Score range: 0-100
    - Be strict and realistic in scoring
    - Empty or placeholder information should receive low scores (20-40)
    - Generic content should receive moderate scores (40-60)
    - Well-crafted, specific content should receive high scores (70-90)
    - Outstanding, highly personalized content should receive top scores (90-100)
    - Always provide at least 2-3 suggestions for each section, even if the section is good

    SPECIFIC EVALUATION CRITERIA:
    - Header: Name clarity, position title, professional appearance
    - Contact Info: Completeness, professionalism, LinkedIn should be prioritized over other social links
    - Recipient Info: Personalization level, specific names vs. generic titles, company research
    - Greeting: Personalization, formality level
    - Opening: Hook effectiveness, position relevance, enthusiasm
    - Body: Relevant experience, quantified achievements, skills alignment
    - Closing: Call to action, professional tone, value proposition
    - Signature: Professional format, completeness

    ${
      isVietnamese
        ? `
    Đánh giá thư xin việc và trả lời bằng tiếng Việt với định dạng JSON sau:
    {
      "overallScore": 85,
      "overallFeedback": "Tổng quan chi tiết về thư xin việc, nêu rõ điểm mạnh và yếu chính",
      "sections": {
        "header": {
          "score": 90,
          "feedback": "Đánh giá chi tiết về phần header",
          "suggestions": ["Luôn đưa ra 2-3 gợi ý cụ thể", "Kể cả khi phần này tốt", "Vẫn có thể cải thiện"]
        },
        "contactInfo": {
          "score": 85,
          "feedback": "Đánh giá về thông tin liên hệ, ưu tiên LinkedIn",
          "suggestions": ["Gợi ý về thông tin liên hệ", "Ưu tiên LinkedIn thay vì mạng xã hội khác"]
        },
        "recipientInfo": {
          "score": 30,
          "feedback": "Thông tin người nhận chung chung hoặc thiếu sẽ bị điểm thấp",
          "suggestions": ["Tìm tên cụ thể người nhận", "Nghiên cứu thông tin công ty"]
        },
        "greeting": {
          "score": 40,
          "feedback": "Lời chào chung chung sẽ bị điểm thấp",
          "suggestions": ["Cá nhân hóa lời chào", "Sử dụng tên cụ thể"]
        },
        "opening": {
          "score": 88,
          "feedback": "Đánh giá đoạn mở đầu",
          "suggestions": ["Cải thiện hook", "Thể hiện nghiên cứu về công ty"]
        },
        "body": {
          "score": 90,
          "feedback": "Đánh giá nội dung chính, yêu cầu số liệu cụ thể",
          "suggestions": ["Thêm số liệu định lượng", "Liên kết kinh nghiệm với yêu cầu công việc"]
        },
        "closing": {
          "score": 85,
          "feedback": "Đánh giá đoạn kết",
          "suggestions": ["Tăng cường call-to-action", "Nhấn mạnh giá trị đóng góp"]
        },
        "signature": {
          "score": 95,
          "feedback": "Đánh giá chữ ký",
          "suggestions": ["Luôn có gợi ý dù là nhỏ", "Có thể thêm thông tin liên hệ"]
        }
      },
      "improvements": ["Ít nhất 5 điểm cần cải thiện cụ thể và thực tế"],
      "strengths": ["Ít nhất 3 điểm mạnh được nêu rõ"]
    }

    ĐIỂM ĐÁNH GIÁ NGHIÊM NGẶT:
    - Thông tin trống hoặc placeholder ([Tên], [Nguồn tin], etc.): 20-40 điểm
    - Thông tin chung chung: 40-60 điểm
    - Thông tin cụ thể nhưng cần cải thiện: 60-80 điểm
    - Thông tin xuất sắc: 80-100 điểm
    `
        : `
    Evaluate the cover letter and respond in English with the following JSON format:
    {
      "overallScore": 85,
      "overallFeedback": "Detailed overall assessment highlighting main strengths and weaknesses",
      "sections": {
        "header": {
          "score": 90,
          "feedback": "Detailed feedback on header section",
          "suggestions": ["Always provide 2-3 specific suggestions", "Even when section is good", "There's always room for improvement"]
        },
        "contactInfo": {
          "score": 85,
          "feedback": "Feedback on contact info, prioritize LinkedIn over other social links",
          "suggestions": ["Contact information suggestions", "Prioritize LinkedIn over other social media"]
        },
        "recipientInfo": {
          "score": 30,
          "feedback": "Generic or missing recipient info should receive low scores",
          "suggestions": ["Find specific recipient name", "Research company information"]
        },
        "greeting": {
          "score": 40,
          "feedback": "Generic greetings should receive low scores",
          "suggestions": ["Personalize greeting", "Use specific names"]
        },
        "opening": {
          "score": 88,
          "feedback": "Feedback on opening paragraph",
          "suggestions": ["Improve hook", "Show company research"]
        },
        "body": {
          "score": 90,
          "feedback": "Feedback on body content, require specific metrics",
          "suggestions": ["Add quantified achievements", "Link experience to job requirements"]
        },
        "closing": {
          "score": 85,
          "feedback": "Feedback on closing paragraph",
          "suggestions": ["Strengthen call-to-action", "Emphasize value proposition"]
        },
        "signature": {
          "score": 95,
          "feedback": "Feedback on signature",
          "suggestions": ["Always provide suggestions even if minor", "Could add contact information"]
        }
      },
      "improvements": ["At least 5 specific and practical improvement points"],
      "strengths": ["At least 3 clearly stated strengths"]
    }

    STRICT SCORING GUIDELINES:
    - Empty or placeholder information ([Name], [Source], etc.): 20-40 points
    - Generic information: 40-60 points
    - Specific but needs improvement: 60-80 points
    - Excellent information: 80-100 points
    `
    }

    ADDITIONAL EVALUATION RULES:
    - If recipientInfo contains placeholders like "[Tên]", "Your Company", etc., score should be 20-40
    - If contactInfo has GitHub instead of LinkedIn, suggest prioritizing LinkedIn
    - If opening paragraph mentions "[Nguồn tin]" or "[Source]", deduct points significantly
    - Always provide actionable suggestions, never empty arrays
    - Body content should be evaluated based on relevance to the position level (Junior vs Senior)
    - Quantified achievements should receive higher scores than generic claims
    `;

    const userPrompt = `
    Please evaluate this cover letter content thoroughly and provide detailed feedback.

    Cover Letter Content:
    ${JSON.stringify(coverLetterContent, null, 2)}

    Language: ${language}

    Pay special attention to:
    1. Placeholder information that needs to be filled in
    2. Generic vs. specific content
    3. Relevance of experience to the position
    4. Professional presentation
    5. Personalization level
    `;

    const messages: { role: 'system' | 'user'; content: string }[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await this.aiService.askAgent(messages);
    const rawContentJson = response
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();

    try {
      const resultJson = JSON.parse(rawContentJson);
      return resultJson as CoverLetterEvaluationDto;
    } catch (error) {
      console.error('Error parsing JSON:', error);
      throw new Error('Failed to parse AI response');
    }
  }
}
