import { Injectable } from '@nestjs/common';
import { AiService } from '../ai.service';
import { CreateParseCvDto } from '../dto/create-ai.dto';
import { ParsedCvResultDto } from '../dto/response-ai.dto';

@Injectable()
export class AiParseCvAgent {
  constructor(private readonly aiService: AiService) {}

  async parseCv(
    createParseCvDto: CreateParseCvDto,
  ): Promise<ParsedCvResultDto> {
    const systemPrompt = `
    You are an expert CV/Resume parser. Your task is to extract structured information from a resume/CV text and return it in a specific JSON format.

    You will receive:
    - A resume/CV in plain text format.

    Your task is to:
    - Extract all relevant information from the CV
    - Structure the data according to the specified JSON format
    - Be thorough and accurate in data extraction
    - Handle missing information gracefully (use null or empty arrays)
    - Preserve the original formatting and content as much as possible

    LANGUAGE:
    - If the CV contains Vietnamese content, remember to extract Vietnamese text as-is but keep technical terms, technology names, and standard jargon in English.
    - For English CVs, maintain all content in English.
    - Always respond with proper JSON structure regardless of language.

    EXTRACTION GUIDELINES:
    - Extract exact text from CV, don't paraphrase or interpret
    - For dates, try to standardize format but preserve original if unclear
    - For skills, group them by categories when possible (Technical, Soft Skills, etc.)
    - For work experience, separate responsibilities and achievements clearly
    - If information is unclear or missing, note it in parsing_notes

    OUTPUT FORMAT:
    Always return a valid **JSON object** with the following structure:

    {
      "personal_info": {
        "fullName": string,
        "email": string,
        "phoneNumber": string,
        "address": string | null,
        "linkedin": string | null,
        "github": string | null,
        "website": string | null,
        "dateOfBirth": string | null,
        "nationality": string | null,
        "maritalStatus": string | null
      },
      possitionJob: string,
      "summary": string | null,
      "work_experience": [
        {
          "position": string,
          "company": string,
          "duration": string,
          "startDate": string | null,
          "endDate": string | null,
          "location": string | null,
          "responsibilities": string[],
          "achievements": string[],
          "technologies": string[]
        }
      ],
      "education": [
        {
          "degree": string,
          "institution": string,
          "duration": string,
          "startDate": string | null,
          "endDate": string | null,
          "gpa": string | null,
          "major": string | null,
          "location": string | null,
          "achievements": string[]
        }
      ],
      "skills": [
        {
          "category": string,
          "skills": string[],
          "proficiencyLevel": string | null
        }
      ],
      "certifications": [
        {
          "name": string,
          "issuer": string,
          "issueDate": string | null,
          "expiryDate": string | null,
          "verificationUrl": string | null
        }
      ],
      "projects": [
        {
          "name": string,
          "description": string,
          "duration": string | null,
          "role": string | null,
          "teamSize": number,
          "technologies": string[],
          "achievements": string[],
          "url": string | null,
          "repositoryUrl": string | null
        }
      ],
      "languages": [
        {
          "language": string,
          "proficiency": string
        }
      ],
      "additional_info": {
        "hobbies": string[],
        "volunteer_experience": string[],
        "awards": string[],
        "publications": string[],
        "references": string[]
      },
      "parsing_notes": string[]
    }

    IMPORTANT:
    - Always respond ONLY with a valid JSON object. No extra commentary.
    - If a section is not found in the CV, return empty array [] or null as appropriate.
    - Be conservative with data extraction - don't invent information.
    - If you're unsure about something, add a note to parsing_notes array.
    `;

    const userPrompt = `
    Please parse the following CV/Resume:

    ${createParseCvDto.cvText}
    `;

    const messages: { role: 'system' | 'user'; content: string }[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await this.aiService.askAgent(messages);
    const rawContentJson = response
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();

    try {
      const resultJson = JSON.parse(rawContentJson);
      return resultJson as ParsedCvResultDto;
    } catch (error) {
      // If JSON parsing fails, return a structured error response
      return {
        personal_info: {
          fullName: '',
          email: '',
          phoneNumber: '',
          address: undefined,
          linkedin: undefined,
          github: undefined,
          website: undefined,
          dateOfBirth: undefined,
          nationality: undefined,
          maritalStatus: undefined,
        },
        summary: undefined,
        work_experience: [],
        education: [],
        skills: [],
        certifications: [],
        projects: [],
        languages: [],
        additional_info: {
          hobbies: [],
          volunteer_experience: [],
          awards: [],
          publications: [],
          references: [],
        },
        parsing_notes: [
          `Failed to parse CV: ${error.message}`,
          'Raw response: ' + response,
        ],
      } as ParsedCvResultDto;
    }
  }
}
