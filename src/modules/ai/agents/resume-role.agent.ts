import { Injectable } from '@nestjs/common';
import { AiService } from '../ai.service';
import { CreateResumeRoleDescriptionDto } from '../dto/create-ai.dto';
import { ResumeAnalysisByRoleDescriptionResultDto } from '../dto/response-ai.dto';

@Injectable()
export class ResumeRoleDescriptionAgent {
  constructor(private readonly aiService: AiService) {}

  async createResumeRoleDescription(
    createResumeRoleDescriptionDto: CreateResumeRoleDescriptionDto,
  ): Promise<ResumeAnalysisByRoleDescriptionResultDto> {
    const systemPrompt = `
    You are a top-tier AI recruiter evaluating a candidate's resume for both overall quality and its fitness for a specific job role (e.g., "Java Junior Developer").

    You will receive:
    - A resume in plain text
    - A short role description (e.g., job title or general role)

    Your task is to:
    - Analyze the **overall quality** of the resume (clarity, structure, relevance, formatting, specificity, measurable impact).
    - Evaluate the **fitness of the resume for the given role**, considering typical job requirements.
    - Be highly critical: penalize vague descriptions, lack of measurable outcomes, fluff, poor formatting, or irrelevant content.

    LANGUAGE:
    - If the ROLE description contains any Vietnamese word, always respond ONLY in Vietnamese.
    - However, keep technical terms, technology names, or standard jargon in English as-is within the Vietnamese response.
    - If the ROLE description contains only English words, always respond ONLY in English.
    - Your entire response MUST be in the same language as the ROLE description (Vietnamese or English), except that technical terms may remain in English inside Vietnamese answers.

    OUTPUT FORMAT:
    Always return your result as a valid **JSON object**, with the following schema:

    {
      "overall_score": number (0-100),
      "fit_score": number,
      "overall_feedback": string,
      "summary_comment": string,
      "sections": {
        "contact_info": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],   // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]
        },
        "experience": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],   // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]  
        },
        "education": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],   // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]
        },
        "skills": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],   // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]
        }
      }
    }

    SCORING GUIDELINES:
    - Be strict. Only excellent resumes should score above 85.
    - Prioritize resumes that use clear metrics, relevant technologies, precise language, and clean formatting.
    - Heavily penalize missing sections, vague descriptions, poor layout, or irrelevant content.
    - Each section must clearly explain **why the score is given**, with objective justifications.
    - The **fit_score** must accurately reflect how well the resume aligns with the **specific job role provided** in terms of skills, experience, and relevance.
    
    IMPORTANT:
    - Always respond ONLY with a valid JSON object. No extra commentary or text outside the JSON.
    - If the ROLE description contains any Vietnamese word, always respond ONLY in Vietnamese.
    - However, keep technical terms, technology names, or standard jargon in English as-is within the Vietnamese response.
    - If the ROLE description contains only English words, always respond ONLY in English.
    - Your entire response MUST be in the same language as the ROLE description (Vietnamese or English), except that technical terms may remain in English inside Vietnamese answers.
  `;

    const userPrompt = `
    Resume:
    ${createResumeRoleDescriptionDto.cvText}

    Role:
    ${createResumeRoleDescriptionDto.roleDescription}
    `;

    const messages: { role: 'system' | 'user'; content: string }[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await this.aiService.askAgent(messages);
    const rawContentJson = response
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();
    const resultJson = JSON.parse(rawContentJson);
    return resultJson as ResumeAnalysisByRoleDescriptionResultDto;
  }
}
