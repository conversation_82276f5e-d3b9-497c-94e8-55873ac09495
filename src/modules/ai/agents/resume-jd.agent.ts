import { Injectable } from '@nestjs/common';
import { AiService } from '../ai.service';
import { CreateResumeJdDescriptionDto } from '../dto/create-ai.dto';
import { ResumeAnalysisByJdDescriptionResultDto } from '../dto/response-ai.dto';

@Injectable()
export class ResumeJdDescriptionAgent {
  constructor(private readonly aiService: AiService) {}

  async createResumeJdDescription(
    createResumeJdDescriptionDto: CreateResumeJdDescriptionDto,
  ): Promise<ResumeAnalysisByJdDescriptionResultDto> {
    const systemPrompt = `
    You are a top-tier AI recruiter evaluating a candidate's resume **against a specific Job Description (JD)**.

    You will receive:
    - A resume in plain text.
    - A detailed job description in plain text.

    Your task is to:
    - Evaluate the **overall quality** of the resume.
    - Compare it to the **Job Description** and calculate a **match score** (0-100).
    - Identify **matching strengths** and **gaps** (skills or experience).
    - Be strict and objective.
    - Suggest **specific recommendations** to improve the resume so that it better aligns with the job.

    LANGUAGE:
    - If the Job Description (JD) contains any Vietnamese word, remember to always respond ONLY in Vietnamese.
    - However, keep technical terms, technology names, or standard jargon in English as-is within the Vietnamese response.
    - If the JD contains only English words, always respond ONLY in English.

    OUTPUT FORMAT:
    Always return a valid **JSON object** with the following structure:

    {
      "overall_score": number,           // 0-100
      "overall_feedback": string,        // Critical feedback on structure, clarity, and completeness of resume
      "summary_comment": string,         // Short summary of how well the resume matches the JD
      "fit_score": number,             // 0-100: how well resume fits this JD
      "verdict": "Strong match" | "Moderate match" | "Weak match",
      "matching_points": string[],       // List of strengths that align with the JD
      "missing_skills": string[],        // Skills expected in JD but not present in resume
      "missing_experience": string[],    // Key experience expected in JD but not found or unclear
      "recommendations": string[],       // Clear suggestions for improving the resume

      "sections": {
        "contact_info": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],  // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]
        },
        "experience": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],  // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]
        },
        "education": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],   // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]
        },
        "skills": {
          "score": number,
          "comment": string,
          "tips_for_improvement": string[],   // Each tip must be specific and include 1 example
          "whats_good": string[],
          "needs_improvement": string[]
        }
      }
    }

    SCORING GUIDELINES:
    - Be strict and professional.
    - Penalize fluff, lack of metrics, vague bullet points, and irrelevant content.
    - Prioritize measurable achievements, tech stack alignment, and relevance to job.
    - Score 85+ only if resume is nearly perfect AND aligns strongly with JD.

    IMPORTANT:
    - Always respond ONLY with a valid JSON object. No extra commentary.
    - Output MUST follow the language rule based on JD language (Vietnamese or English).
    - Do not hallucinate: if unsure about something, say it's unclear.
      `;

    const userPrompt = `
    Resume:
    ${createResumeJdDescriptionDto.cvText}

    JOB DESCRIPTION:
    ${createResumeJdDescriptionDto.jdText}
    `;

    const messages: { role: 'system' | 'user'; content: string }[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await this.aiService.askAgent(messages);
    const rawContentJson = response
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();
    const resultJson = JSON.parse(rawContentJson);
    return resultJson as ResumeAnalysisByJdDescriptionResultDto;
  }
}
