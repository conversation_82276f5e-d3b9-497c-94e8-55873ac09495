import { Injectable } from '@nestjs/common';
import { AiService } from '../ai.service';
import { CreateQuestionInterviewDto } from '../dto/create-ai.dto';
import { ResponseInterviewQuestionDto } from '../dto/response-ai.dto';

@Injectable()
export class InterviewAgent {
  constructor(private readonly aiService: AiService) {}

  async createQuestionInterview(
    createQuestionInterviewDto: CreateQuestionInterviewDto,
  ): Promise<ResponseInterviewQuestionDto> {
    const systemPrompt = `
    You are an expert technical interviewer.

    You will receive:
    - A **Job Title**
    - A **Job Description (JD)** in plain text
    - A specified **Interview Duration** (e.g., 15min, 30min, 60min)
    - A desired **Interview Type**: one of [Technical, Behavioral, Experience-based, Problem-solving, Leadership]

    Your task:
    1. Analyze the JD to identify:
      - Key responsibilities
      - Required technical and soft skills
      - Level of experience expected

    2. Based on the Interview Type and Duration:
      - Generate a **list of interview questions** that align with the role
      - Adjust the **number** and **depth** of questions to match the time limit
      - Use clear and concise language, no vague questions
      - Reflect the tone and structure of a real-life {{Interview Type}} interview
      - Provide a helpful **"tip" for each question**: this helps the candidate understand how to answer effectively

    3. Return ONLY a valid JSON object in the following format:
    {
      "interviewQuestions": [
        {
          "question": "Explain how you would optimize a large-scale React application for performance.",
          "type": "Technical",
          "estimated_time_minutes": 5,
          "tip": "Explain how you would optimize a large-scale React application for performance."
        },
        {
          "question": "Tell me about a time you resolved a team conflict.",
          "type": "Behavioral",
          "estimated_time_minutes": 4,
          "tip": "Tell me about a time you resolved a team conflict."
        }
      ],
      "interviewDuration": 30,
      "totalQuestion": 6,
      "interviewType": ["{{Interview Type}}"],
      "jobPosition": "{{Job Title}}",
      "jobDescriptions": "{{Job Description (JD)}}"
    }

    IMPORTANT:
    - If the JD contains any Vietnamese words → MUST always respond ONLY in Vietnamese (technical terms in English still allowed)
    - If the JD is fully English → MUST always respond ONLY in English
    - NEVER respond with extra commentary. Only return JSON.
    - NEVER hallucinate: if unclear from JD, say "Unclear from JD"
    - Tips should be 1–2 sentences, concise, practical, and specific to the question

    SCORING LOGIC:
    - ~1–2 mins → Short-answer / warm-up questions  
    - ~3–5 mins → Technical depth or experience-based  
    - ~8–10 mins → Complex problem-solving or system design

    Ensure all questions are relevant, structured, time-optimized, and aligned with {{Interview Type}} for a {{Job Title}} position.
    Make sure each question is **relevant, clearly written, time-optimized**, and has a **practical tip**.
    `;

    const userPrompt = `
    Job Title: ${createQuestionInterviewDto.jobPosition}
    Job Description: ${createQuestionInterviewDto.jdText}
    Interview Duration: ${createQuestionInterviewDto.interviewDuration}
    Interview Type: ${createQuestionInterviewDto.interviewType.join(',')}
    `;

    const messages: { role: 'system' | 'user'; content: string }[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await this.aiService.askAgent(messages);
    const rawContentJson = response
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();
    const resultJson = JSON.parse(rawContentJson);
    return resultJson as ResponseInterviewQuestionDto;
  }
}
