import { Injectable } from '@nestjs/common';
import { AiService } from '../ai.service';
import { CreateResumeRoleDescriptionDto } from '../dto/create-ai.dto';
import { CoverLetterByRoleDescriptionResultDto } from '../dto/response-ai.dto';
import { getDefaultAvatarUrl } from '@utils/default-avatar.util';

@Injectable()
export class CoverLetterRoleDescriptionAgent {
  constructor(private readonly aiService: AiService) {}

  async createCoverLetterByRoleDescription(
    createDto: CreateResumeRoleDescriptionDto & { language: string },
  ): Promise<CoverLetterByRoleDescriptionResultDto> {
    const isVietnamese = createDto.language === 'vietnamese';

    const systemPrompt = `
    You are an expert career counselor and cover letter specialist. Your task is to generate a professional cover letter based on the user's CV and the specific role description they're applying for.

    IMPORTANT: You MUST respond ONLY with valid JSON format. Do not include any explanatory text, markdown, or commentary. Just pure JSON.

    The cover letter should be structured with the following sections and returned in JSON format:

    ${
      isVietnamese
        ? `
    <PERSON>r<PERSON> lời bằng tiếng Việt với định dạng JSON sau:
    {
      "header": {
        "fullName": "Họ và tên đầy đủ",
        "targetPosition": "Vị trí ứng tuyển",
        "profileImage": "Ảnh đại diện (placeholder)"
      },
      "contactInfo": {
        "dateOfBirth": "Ngày sinh",
        "phoneNumber": "Số điện thoại",
        "email": "Địa chỉ email",
        "address": "Địa chỉ (Thành phố, Quốc gia)",
        "linkedin": "LinkedIn (tùy chọn)"
      },
      "recipientInfo": {
        "hiringManagerName": "Tên người tuyển dụng",
        "hiringManagerTitle": "Chức vụ/Phòng ban",
        "companyName": "Tên công ty",
        "companyAddress": "Địa chỉ công ty",
        "applicationDate": "Ngày hiện tại",
        "jobTitle": "Vị trí ứng tuyển"
      },
      "greetingLine": "Kính gửi Ông/Bà [Tên],",
      "openingParagraph": "Đoạn mở đầu - Đề cập làm thế nào bạn biết về vị trí này, thể hiện sự hứng thú và lý do tại sao bạn phù hợp",
      "experienceAchievements": "Kinh nghiệm & Thành tích - Nền tảng giáo dục ngắn gọn, kinh nghiệm nghề nghiệp chính, thành tích có thể định lượng",
      "skillsStrengths": "Kỹ năng & Điểm mạnh - Nêu bật các kỹ năng mềm/cứng liên quan, điểm mạnh cá nhân và cách chúng hỗ trợ vai trò",
      "closingParagraph": "Đoạn kết - Lịch sự yêu cầu cơ hội phỏng vấn, bày tỏ lòng biết ơn và nhắc lại sự nhiệt tình đóng góp cho công ty",
      "signature": "Trân trọng,\\n[Họ tên đầy đủ]"
    }
    `
        : `
    Respond in English with the following JSON format:
    {
      "header": {
        "fullName": "Full Name",
        "targetPosition": "Target Position",
        "profileImage": "Profile Image (placeholder)"
      },
      "contactInfo": {
        "dateOfBirth": "Date of Birth",
        "phoneNumber": "Phone Number",
        "email": "Email Address",
        "address": "Address (City, Country)",
        "linkedin": "LinkedIn (optional)"
      },
      "recipientInfo": {
        "hiringManagerName": "Mr./Ms. [Hiring Manager's Name]",
        "hiringManagerTitle": "Title/Department",
        "companyName": "Company Name",
        "companyAddress": "Company Address",
        "applicationDate": "Current Date",
        "jobTitle": "Job Title"
      },
      "greetingLine": "Dear Mr./Ms. [Name],",
      "openingParagraph": "Opening paragraph - Mention how you found the job posting, express excitement and why you're interested, briefly state why you're a good fit",
      "experienceAchievements": "Experience & Achievements - Brief education background, key professional experience, quantified achievements",
      "skillsStrengths": "Skills & Strengths - Highlight relevant soft/hard skills, personal strengths and how they support the role",
      "closingParagraph": "Closing paragraph - Politely ask for interview opportunity, express gratitude, restate enthusiasm for contributing to the company",
      "signature": "Yours sincerely,\\n[Your Full Name]"
    }
    `
    }

    Guidelines:
    - Analyze the CV to extract relevant information for each section
    - Match the candidate's experience and skills with the role requirements
    - Keep the tone professional yet personable
    - Ensure each section flows naturally and tells a cohesive story
    - Include specific examples and achievements where possible
    - Tailor the content specifically to the role description provided
    `;

    const userPrompt = `
    CV Content:
    ${createDto.cvText}

    Role Description:
    ${createDto.roleDescription}

    Language: ${createDto.language}
    `;

    const messages: { role: 'system' | 'user'; content: string }[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await this.aiService.askAgent(messages);
    const rawContentJson = response
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();

    try {
      const resultJson = JSON.parse(rawContentJson);

      // Always set default avatar URL for profileImage regardless of AI response
      if (resultJson.header) {
        resultJson.header.profileImage = getDefaultAvatarUrl();
      }

      return resultJson;
    } catch (error) {
      console.error('Error parsing JSON:', error);
      throw new Error('Failed to parse AI response');
    }
  }
}
