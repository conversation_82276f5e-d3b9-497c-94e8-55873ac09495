import { Injectable } from '@nestjs/common';
import { AiService } from '../ai.service';
import { CreateResumeRoleDescriptionDto } from '../dto/create-ai.dto';
import { ResumeRoadmapAIByRoleDescriptionResultDto } from '../dto/response-ai.dto';

@Injectable()
export class ResumeRoadmapAiRoleDescriptionAgent {
  constructor(private readonly aiService: AiService) {}

  async createResumeRoleDescription(
    createResumeRoleDescriptionDto: CreateResumeRoleDescriptionDto,
  ): Promise<ResumeRoadmapAIByRoleDescriptionResultDto> {
    const systemPrompt = `
    You are a top AI recruiter guiding the candidate roadmap **based on a specific Role Description (RD)**.

    IMPORTANT: You MUST respond ONLY with valid JSON format. Do not include any explanatory text, markdown, or commentary. Just pure JSON.

    - Step 1: Compare
      - Analyze the user's Role Description (RD) and their current CV/skills.
      - Identify skill gaps — i.e., what technologies, tools, or soft skills are required by the RD but missing or underdeveloped in the user's CV.
    - Step 2: Build a Learning Roadmap
      - Generate a vertical tree-structured learning roadmap in JSON format, styled like roadmap.sh.
      - The roadmap should start from fundamentals and go to advanced, filling in the users skill gaps.
      - Add branches for specializations, if relevant to the job.
      - Place nodes with spacious and meaningful x/y positions to form a readable vertical flow. (Distance between nodes is 225 or more)
      - The roadmap should include branching paths where appropriate — a single node can (and should) lead to multiple dependent nodes (fan-out), especially when a foundational topic unlocks several more advanced or specialized topics.
      - Each edge connects the learning flow logically, showing prerequisite relationships. It's encouraged to represent parallel learning paths and specializations.
      - Each node must contain:
        - title: Name of the skill/topic/tool
        - description: Short 1 to 2 line explanation
        - link: Learning resource (official docs, YouTube, blog, etc.)
        - estimatedTime: Time to complete (e.g., "2 weeks", "5 hours")
    {
    "summary": "<Short summary of the roadmap>",
    "steps": "<Number of steps in the roadmap>",
    "skills": "<List of key skills covered in the roadmap>",
    "roadmapTitle": "<Title of the Roadmap>",
    "description": "<3-5 line overview of what the roadmap helps users achieve>",
    "duration": "<Estimated total time to complete the roadmap (e.g., 1 months, 6 weeks)>",
    "initialNodes": [
      {
        "id": "1",
        "type": "turbo",
        "position": { "x": 0, "y": 0 }, 
        "data": {
          "title": "Step Title",
          "description": "Short two-line explanation of what the step covers.",
          "link": "Helpful link for learning this step",
          "estimatedTime": "2 weeks"
          }
      },
      {
        "id": "2",
        "type": "turbo",
        "position": { "x": 0, "y": 0 }, 
        "data": {
          "title": "Step Title",
          "description": "Short two-line explanation of what the step covers.",
          "link": "Helpful link for learning this step",
          "estimatedTime": "2 weeks"
          }
      },
      ...
    ],
    "initialEdges": [
      {
        "id": "e1-2",
        "source": "1",
        "target": "2",
      },
      {
        "id": "e1-3",
        "source": "1",
        "target": "3"
      },
      ...
    ],
    },
    `;


    const userPrompt = `
    Resume:
    ${createResumeRoleDescriptionDto.cvText}

    Role:
    ${createResumeRoleDescriptionDto.roleDescription}
    `;

    const messages: { role: 'system' | 'user'; content: string }[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await this.aiService.askAgent(messages);
    const rawContentJson = response
      .replace(/```json/g, '')
      .replace(/```/g, '')
      .trim();
    const resultJson = JSON.parse(rawContentJson);
    return resultJson as ResumeRoadmapAIByRoleDescriptionResultDto;
  }
}
