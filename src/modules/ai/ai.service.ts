import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenerativeAI } from '@google/generative-ai';

@Injectable()
export class AiService {
  private gemini: GoogleGenerativeAI;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    this.gemini = new GoogleGenerativeAI(apiKey || '');
  }

  async askAgent(
    messages: { role: 'user' | 'system' | 'assistant'; content: string }[],
    options?: { model?: string; temperature?: number },
  ): Promise<string> {
    const modelId = options?.model || 'gemini-2.5-flash';
    const temperature = options?.temperature ?? 0.7;

    const model = this.gemini.getGenerativeModel({ model: modelId });
    const systemPrompt =
      messages.find((m) => m.role === 'system')?.content || '';
    const chatHistory = messages.filter((m) => m.role !== 'system');

    const chat = model.startChat({
      history: [
        ...(systemPrompt
          ? [
              {
                role: 'user',
                parts: [{ text: systemPrompt }],
              },
            ]
          : []),
        ...chatHistory.map((msg) => ({
          role: msg.role === 'assistant' ? 'model' : msg.role,
          parts: [{ text: msg.content }],
        })),
      ],
      generationConfig: {
        temperature,
      },
    });

    const latestUserMessage = messages.at(-1)?.content || '';
    const result = await chat.sendMessage(latestUserMessage);
    const response = result.response;
    return response.text();
  }
}
