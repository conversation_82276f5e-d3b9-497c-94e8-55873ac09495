export class ResponseInterviewQuestionDto {
  interviewQuestions: {
    question: string;
    estimatedTimeMinutes: number;
    tip: string;
  }[];

  interviewDuration: number;
  totalQuestion: number;
  interviewType: string[];
  jobPosition: string;
  jobDescriptions: string;
}

export class ResumeAnalysisByRoleDescriptionResultDto {
  overall_score: number;
  fit_score: number;
  overall_feedback: string;
  summary_comment: string;
  sections: {
    contact_info: ResumeSection;
    experience: ResumeSection;
    education: ResumeSection;
    skills: ResumeSection;
  };
}

export class ResumeAnalysisByJdDescriptionResultDto {
  overall_score: number;
  fit_score: number;
  overall_feedback: string;
  summary_comment: string;
  verdict: string;
  matching_points: string[];
  missing_skills: string[];
  missing_experience: string[];
  recommendations: string[];
  sections: {
    contact_info: ResumeSection;
    experience: ResumeSection;
    education: ResumeSection;
    skills: ResumeSection;
  };
}

export class ResumeSection {
  score: number;
  comment: string;
  tips_for_improvement: string[];
  whats_good: string[];
  needs_improvement: string[];
}

export class RoadmapNodeDto {
  id: string;
  type: string;
  position: {
    x: number;
    y: number;
  };
  data: {
    title: string;
    description: string;
    link: string;
    estimatedTime: string;
  };
}

export class RoadmapEdgeDto {
  id: string;
  source: string;
  target: string;
}

export class ResumeRoadmapAIByJdDescriptionResultDto {
  roadmapTitle: string;
  description: string;
  duration: string;
  initialNodes: RoadmapNodeDto[];
  initialEdges: RoadmapEdgeDto[];
}

export class ResumeRoadmapAIByRoleDescriptionResultDto {
  roadmapTitle: string;
  description: string;
  duration: string;
  initialNodes: RoadmapNodeDto[];
  initialEdges: RoadmapEdgeDto[];
}

export class PerQuestionFeedbackDto {
  question: string;
  userAnswer: string;
  feedback: string;
  rating: number;
}

export class SummaryFeedbackDto {
  overallFeedback: string;
  skillsRating: {
    technicalSkills: number;
    communication: number;
    problemSolving: number;
    experience: number;
  };
  recommendationLevel: string;
  recommendationMsg: string;
}

export class FeedbackInterviewResultDto {
  perQuestionFeedback: PerQuestionFeedbackDto[];
  summary: SummaryFeedbackDto;
}

export class CoverLetterHeaderDto {
  fullName: string;
  targetPosition: string;
  profileImage: string;
}

export class CoverLetterContactInfoDto {
  dateOfBirth: string;
  phoneNumber: string;
  email: string;
  address: string;
  linkedin?: string;
}

export class CoverLetterRecipientInfoDto {
  hiringManagerName: string;
  hiringManagerTitle: string;
  companyName: string;
  companyAddress: string;
  applicationDate: string;
  jobTitle: string;
}

export class CoverLetterByRoleDescriptionResultDto {
  header: CoverLetterHeaderDto;
  contactInfo: CoverLetterContactInfoDto;
  recipientInfo: CoverLetterRecipientInfoDto;
  greetingLine: string;
  openingParagraph: string;
  experienceAchievements: string;
  skillsStrengths: string;
  closingParagraph: string;
  signature: string;
}

export class CoverLetterByJdDescriptionResultDto {
  header: CoverLetterHeaderDto;
  contactInfo: CoverLetterContactInfoDto;
  recipientInfo: CoverLetterRecipientInfoDto;
  greetingLine: string;
  openingParagraph: string;
  experienceAchievements: string;
  skillsStrengths: string;
  closingParagraph: string;
  signature: string;
}

export class PersonalInfoDto {
  fullName: string;
  email: string;
  phoneNumber: string;
  address?: string;
  linkedin?: string;
  github?: string;
  website?: string;
  dateOfBirth?: string;
  nationality?: string;
  maritalStatus?: string;
}

export class WorkExperienceDto {
  position: string;
  company: string;
  duration: string;
  startDate?: string;
  endDate?: string;
  location?: string;
  responsibilities: string[];
  achievements: string[];
  technologies?: string[];
}

export class EducationDto {
  degree: string;
  institution: string;
  duration: string;
  startDate?: string;
  endDate?: string;
  gpa?: string;
  major?: string;
  location?: string;
  achievements?: string[];
}

export class SkillDto {
  category: string;
  skills: string[];
  proficiencyLevel?: string;
  yearsOfExperience?: number;
}

export class CertificationDto {
  name: string;
  issuer: string;
  issueDate?: string;
  expiryDate?: string;
  verificationUrl?: string;
}

export class ProjectDto {
  name: string;
  description: string;
  duration?: string;
  role?: string;
  technologies: string[];
  achievements?: string[];
  teamSize: number;
  url?: string;
  repositoryUrl?: string;
}

export class LanguageDto {
  language: string;
  proficiency: string;
}

export class AdditionalInfoDto {
  hobbies?: string[];
  volunteer_experience?: string[];
  awards?: string[];
  publications?: string[];
  references?: string[];
}

export class ParsedCvResultDto {
  personal_info: PersonalInfoDto;
  summary?: string;
  work_experience: WorkExperienceDto[];
  education: EducationDto[];
  skills: SkillDto[];
  certifications: CertificationDto[];
  projects: ProjectDto[];
  languages: LanguageDto[];
  additional_info?: AdditionalInfoDto;
  parsing_notes?: string[];
}
