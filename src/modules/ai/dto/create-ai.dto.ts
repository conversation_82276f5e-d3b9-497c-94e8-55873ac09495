import { ResponseContentChatAiDto } from '@modules/chat-ai/dto/response-chat-ai.dto';
import { ConversationDto } from '@modules/conversation/dto/conversation.dto';

export class CreateQuestionInterviewDto {
  jobPosition: string;
  interviewDuration: string;
  interviewType: string[];
  jdText: string;
}

export class CreateResumeRoleDescriptionDto {
  roleDescription: string;
  cvText: string;
  language?: string;
}

export class CreateResumeJdDescriptionDto {
  jdText: string;
  cvText: string;
  language?: string;
}

export class CreateAiChatDto {
  history: ResponseContentChatAiDto[];
  userInput: string;
}

export class CreateFeedbackInterviewDto {
  interviewQuestions: object[];
  conversationLog: ConversationDto[];
}

export class CreateParseCvDto {
  cvText: string;
  language?: string;
}
