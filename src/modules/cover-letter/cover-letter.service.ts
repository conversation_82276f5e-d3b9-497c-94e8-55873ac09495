import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CoverLetter,
  CoverLetterDocument,
} from './schemas/cover-letter.schema';
import {
  CreateCoverLetterRoleDescriptionDto,
  CreateCoverLetterJdDto,
} from './dto/create-cover-letter.dto';
import {
  ResponseCoverLetterByRoleDescriptionDto,
  ResponseCoverLetterByJdDescriptionDto,
  ResponseCoverLetterDto,
  CoverLetterEvaluationDto,
  CoverLetterWithEvaluationDto,
} from './dto/response-cover-letter.dto';
import { UpdateCoverLetterDto } from './dto/update-cover-letter.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import { plainToInstance } from 'class-transformer';
import { CoverLetterRoleDescriptionAgent } from '@modules/ai/agents/cover-letter-role.agent';
import { CoverLetterJdDescriptionAgent } from '@modules/ai/agents/cover-letter-jd.agent';
import { CoverLetterEvaluationAgent } from '@modules/ai/agents/cover-letter-evaluation.agent';
import {
  CoverLetterByRoleDescriptionResultDto,
  CoverLetterByJdDescriptionResultDto,
} from '@modules/ai/dto/response-ai.dto';
import {
  COVER_LETTER_FIELD_MAPPINGS,
  COVER_LETTER_DIRECT_FIELDS,
  COVER_LETTER_META_FIELDS,
} from './constants/cover-letter-field-mappings.constant';
import { ImageKitService } from '@modules/imagekit/imagekit.service';
import * as pdfParse from 'pdf-parse';

@Injectable()
export class CoverLetterService {
  constructor(
    @InjectModel(CoverLetter.name)
    private readonly coverLetterModel: Model<CoverLetterDocument>,
    private readonly coverLetterRoleAgent: CoverLetterRoleDescriptionAgent,
    private readonly coverLetterJdAgent: CoverLetterJdDescriptionAgent,
    private readonly coverLetterEvaluationAgent: CoverLetterEvaluationAgent,
    private readonly imagekitService: ImageKitService,
  ) {}

  async createCoverLetterByRoleDescription(
    createCoverLetterDto: CreateCoverLetterRoleDescriptionDto,
    cvFile: Express.Multer.File,
    userId: string,
  ): Promise<ResponseCoverLetterByRoleDescriptionDto> {
    try {
      if (!cvFile || cvFile.mimetype !== 'application/pdf') {
        throw new ValidationException(ErrorCode.F001);
      }

      const result = await pdfParse(cvFile.buffer);
      const cvText = result.text;

      const [responseAiService, cvFileUrl]: [
        CoverLetterByRoleDescriptionResultDto,
        string,
      ] = await Promise.all([
        this.coverLetterRoleAgent.createCoverLetterByRoleDescription({
          roleDescription: createCoverLetterDto.roleDescription,
          cvText,
          language: createCoverLetterDto.language,
        }),
        this.imagekitService.uploadFile(cvFile, 'CV'),
      ]);

      const response = await this.coverLetterModel.create({
        content: responseAiService,
        agentType: createCoverLetterDto.agentType,
        userId,
        cvFileUrl,
        cvText,
        roleDescription: createCoverLetterDto.roleDescription,
        language: createCoverLetterDto.language,
        templateId: null,
        coverLetterFileUrl: null,
        fontSize: 'M',
        fontFamily: 'Roboto',
        lineHeight: '1.5',
        currentTheme: 'blue',
        sectionFormatting: {},
        detailedFormatting: {},
        evaluation: null,
      });

      const dtoInstance = plainToInstance(
        ResponseCoverLetterByRoleDescriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async createCoverLetterByJdDescription(
    createCoverLetterDto: CreateCoverLetterJdDto,
    cvFile: Express.Multer.File,
    jdFile: Express.Multer.File,
    userId: string,
  ): Promise<ResponseCoverLetterByJdDescriptionDto> {
    try {
      if (!cvFile || cvFile.mimetype !== 'application/pdf') {
        throw new ValidationException(ErrorCode.F001);
      }

      if (!jdFile || jdFile.mimetype !== 'application/pdf') {
        throw new ValidationException(ErrorCode.F001);
      }

      const [cvResult, jdResult] = await Promise.all([
        pdfParse(cvFile.buffer),
        pdfParse(jdFile.buffer),
      ]);

      const cvText = cvResult.text;
      const jdText = jdResult.text;

      const [responseAiService, cvFileUrl, jdFileUrl]: [
        CoverLetterByJdDescriptionResultDto,
        string,
        string,
      ] = await Promise.all([
        this.coverLetterJdAgent.createCoverLetterByJdDescription({
          jdText,
          cvText,
          language: createCoverLetterDto.language,
        }),
        this.imagekitService.uploadFile(cvFile, 'CV'),
        this.imagekitService.uploadFile(jdFile, 'JD'),
      ]);

      const response = await this.coverLetterModel.create({
        content: responseAiService,
        agentType: createCoverLetterDto.agentType,
        userId,
        cvFileUrl,
        jdFileUrl,
        cvText,
        jdText,
        language: createCoverLetterDto.language,
        templateId: null,
        coverLetterFileUrl: null,
        fontSize: 'M',
        fontFamily: 'Roboto',
        lineHeight: '1.5',
        currentTheme: 'blue',
        sectionFormatting: {},
        detailedFormatting: {},
        evaluation: null,
      });

      const dtoInstance = plainToInstance(
        ResponseCoverLetterByJdDescriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async getCoverLetterById(id: string): Promise<ResponseCoverLetterDto> {
    try {
      const response = await this.coverLetterModel.findById(id);
      if (!response) {
        throw new ValidationException(ErrorCode.CL001);
      }

      const dtoInstance = plainToInstance(ResponseCoverLetterDto, response, {
        excludeExtraneousValues: true,
      });

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getMyCoverLetter(userId: string): Promise<ResponseCoverLetterDto[]> {
    try {
      const response = await this.coverLetterModel.find({ userId });

      const dtoInstance = plainToInstance(ResponseCoverLetterDto, response, {
        excludeExtraneousValues: true,
      });

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAllCoverLetter(): Promise<ResponseCoverLetterDto[]> {
    try {
      const response = await this.coverLetterModel.find();

      const dtoInstance = plainToInstance(ResponseCoverLetterDto, response, {
        excludeExtraneousValues: true,
      });

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  private mergeContentSection(
    existingSection: any,
    updates: any,
    fieldsMapping: Record<string, string>,
  ): any {
    const mergedSection = { ...existingSection };

    for (const [dtoField, contentField] of Object.entries(fieldsMapping)) {
      if (updates[dtoField]) {
        mergedSection[contentField] = updates[dtoField];
      }
    }

    return mergedSection;
  }

  private buildUpdatedContent(
    currentContent: Record<string, any>,
    updateDto: UpdateCoverLetterDto,
  ): Record<string, any> {
    const updatedContent = { ...currentContent };

    // Update nested sections using mappings
    Object.entries(COVER_LETTER_FIELD_MAPPINGS).forEach(
      ([section, mapping]) => {
        if (currentContent[section]) {
          updatedContent[section] = this.mergeContentSection(
            currentContent[section],
            updateDto,
            mapping,
          );
        }
      },
    );

    // Update direct fields
    COVER_LETTER_DIRECT_FIELDS.forEach((field) => {
      if (updateDto[field]) {
        updatedContent[field] = updateDto[field];
      }
    });

    return updatedContent;
  }

  async updateCoverLetter(
    id: string,
    updateCoverLetterDto: UpdateCoverLetterDto,
    userId: string,
  ): Promise<ResponseCoverLetterDto> {
    try {
      const coverLetter = await this.coverLetterModel.findById(id);
      if (!coverLetter) {
        throw new ValidationException(ErrorCode.CL001);
      }

      if (coverLetter.userId !== userId) {
        throw new ValidationException(ErrorCode.CL002);
      }

      const updatedContent = this.buildUpdatedContent(
        coverLetter.content,
        updateCoverLetterDto,
      );

      const updateFields: any = { content: updatedContent };

      COVER_LETTER_META_FIELDS.forEach((field) => {
        if (updateCoverLetterDto[field] !== undefined) {
          updateFields[field] = updateCoverLetterDto[field];
        }
      });

      const response = await this.coverLetterModel.findByIdAndUpdate(
        id,
        updateFields,
        { new: true },
      );

      const dtoInstance = plainToInstance(ResponseCoverLetterDto, response, {
        excludeExtraneousValues: true,
      });

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async evaluateCoverLetter(
    id: string,
    userId: string,
  ): Promise<CoverLetterEvaluationDto> {
    try {
      const coverLetter = await this.coverLetterModel.findById(id);
      if (!coverLetter) {
        throw new ValidationException(ErrorCode.CL001);
      }

      if (coverLetter.userId !== userId) {
        throw new ValidationException(ErrorCode.CL002);
      }

      const evaluation =
        await this.coverLetterEvaluationAgent.evaluateCoverLetter(
          coverLetter.content,
          coverLetter.language,
        );

      // Save evaluation result to database
      await this.coverLetterModel.findByIdAndUpdate(
        id,
        { evaluation },
        { new: true },
      );

      return evaluation;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getCoverLetterEvaluation(
    id: string,
    userId: string,
  ): Promise<CoverLetterEvaluationDto | null> {
    try {
      const coverLetter = await this.coverLetterModel.findById(id);
      if (!coverLetter) {
        throw new ValidationException(ErrorCode.CL001);
      }

      if (coverLetter.userId !== userId) {
        throw new ValidationException(ErrorCode.CL002);
      }

      if (!coverLetter.evaluation) {
        return null;
      }

      return coverLetter.evaluation as CoverLetterEvaluationDto;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAllEvaluatedCoverLetters(
    userId: string,
  ): Promise<CoverLetterWithEvaluationDto[]> {
    try {
      const response = await this.coverLetterModel.find({
        userId,
        evaluation: { $ne: null }, // Only get cover letters that have been evaluated
      });

      // Transform
      const evaluationData = response.map((coverLetter) => ({
        _id: coverLetter._id,
        evaluation: coverLetter.evaluation,
        createdAt: (coverLetter as any).createdAt,
        updatedAt: (coverLetter as any).updatedAt,
      }));

      const dtoInstance = plainToInstance(
        CoverLetterWithEvaluationDto,
        evaluationData,
        {
          excludeExtraneousValues: true,
        },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async deleteCoverLetter(id: string): Promise<void> {
    try {
      const response = await this.coverLetterModel.findByIdAndDelete(id);
      if (!response) {
        throw new ValidationException(ErrorCode.CL001);
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async updateCoverLetterAvatar(
    id: string,
    avatarUrl: string,
    userId: string,
  ): Promise<ResponseCoverLetterDto> {
    try {
      const coverLetter = await this.coverLetterModel.findById(id);
      if (!coverLetter) {
        throw new ValidationException(ErrorCode.CL001);
      }

      if (coverLetter.userId !== userId) {
        throw new ValidationException(ErrorCode.CL002);
      }

      // Update the profileImage in the header section
      const updatedContent = {
        ...coverLetter.content,
        header: {
          ...coverLetter.content.header,
          profileImage: avatarUrl,
        },
      };

      const response = await this.coverLetterModel.findByIdAndUpdate(
        id,
        { content: updatedContent },
        { new: true },
      );

      const dtoInstance = plainToInstance(ResponseCoverLetterDto, response, {
        excludeExtraneousValues: true,
      });

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
