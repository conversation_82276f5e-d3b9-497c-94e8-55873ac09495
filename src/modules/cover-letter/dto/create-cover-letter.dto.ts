import { AiAgentCoverLetterType } from '@common/enum/agentType.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class CreateCoverLetterRoleDescriptionDto {
  @ApiProperty({ example: AiAgentCoverLetterType.AI_COVER_LETTER_ROLE })
  @IsEnum(AiAgentCoverLetterType)
  agentType: AiAgentCoverLetterType;

  @ApiProperty({
    example: 'Lập trình viên Front-end Developer Senior 2 năm kinh nghiệm',
  })
  @IsString()
  roleDescription: string;

  @ApiProperty({ example: 'vietnamese', enum: ['vietnamese', 'english'] })
  @IsString()
  language: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload CV file (PDF)',
  })
  @IsOptional()
  cvFile?: any;
}

export class CreateCoverLetterJdDto {
  @ApiProperty({ example: AiAgentCoverLetterType.AI_COVER_LETTER_JD })
  @IsEnum(AiAgentCoverLetterType)
  agentType: AiAgentCoverLetterType;

  @ApiProperty({ example: 'vietnamese', enum: ['vietnamese', 'english'] })
  @IsString()
  language: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload CV file (PDF)',
  })
  @IsOptional()
  cvFile?: any;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload JD file (PDF)',
  })
  @IsOptional()
  jdFile?: any;
}
