import { AiAgentCoverLetterType } from '@common/enum/agentType.enum';
import { Expose } from 'class-transformer';

export class ResponseCoverLetterByRoleDescriptionDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentCoverLetterType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  roleDescription: string;

  @Expose()
  language: string;

  @Expose()
  coverLetterFileUrl: string | null;

  @Expose()
  templateId: string | null;

  @Expose()
  fontSize: string;

  @Expose()
  fontFamily: string;

  @Expose()
  lineHeight: string;

  @Expose()
  currentTheme: string;

  @Expose()
  sectionFormatting: Record<
    string,
    {
      textAlign?: 'left' | 'center' | 'right' | 'justify';
      isBold?: boolean;
      isItalic?: boolean;
      isUnderline?: boolean;
      isHidden?: boolean;
    }
  >;

  @Expose()
  detailedFormatting: Record<
    string,
    Array<{
      start: number;
      end: number;
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
    }>
  >;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}

export class ResponseCoverLetterByJdDescriptionDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentCoverLetterType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  jdFileUrl: string;

  @Expose()
  jdText: string;

  @Expose()
  language: string;

  @Expose()
  coverLetterFileUrl: string | null;

  @Expose()
  templateId: string | null;

  @Expose()
  fontSize: string;

  @Expose()
  fontFamily: string;

  @Expose()
  lineHeight: string;

  @Expose()
  currentTheme: string;

  @Expose()
  sectionFormatting: Record<
    string,
    {
      textAlign?: 'left' | 'center' | 'right' | 'justify';
      isBold?: boolean;
      isItalic?: boolean;
      isUnderline?: boolean;
      isHidden?: boolean;
    }
  >;

  @Expose()
  detailedFormatting: Record<
    string,
    Array<{
      start: number;
      end: number;
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
    }>
  >;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}

export class ResponseCoverLetterDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentCoverLetterType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  jdFileUrl: string;

  @Expose()
  jdText: string;

  @Expose()
  roleDescription: string;

  @Expose()
  language: string;

  @Expose()
  coverLetterFileUrl: string | null;

  @Expose()
  templateId: string | null;

  @Expose()
  fontSize: string;

  @Expose()
  fontFamily: string;

  @Expose()
  lineHeight: string;

  @Expose()
  currentTheme: string;

  @Expose()
  sectionFormatting: Record<
    string,
    {
      textAlign?: 'left' | 'center' | 'right' | 'justify';
      isBold?: boolean;
      isItalic?: boolean;
      isUnderline?: boolean;
      isHidden?: boolean;
    }
  >;

  @Expose()
  detailedFormatting: Record<
    string,
    Array<{
      start: number;
      end: number;
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
    }>
  >;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}

export class CoverLetterEvaluationDto {
  @Expose()
  overallScore: number;

  @Expose()
  overallFeedback: string;

  @Expose()
  sections: {
    header: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
    contactInfo: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
    recipientInfo: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
    greeting: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
    opening: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
    body: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
    closing: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
    signature: {
      score: number;
      feedback: string;
      suggestions: string[];
    };
  };

  @Expose()
  improvements: string[];

  @Expose()
  strengths: string[];
}

export class CoverLetterWithEvaluationDto {
  @Expose()
  _id: string;

  @Expose()
  evaluation: CoverLetterEvaluationDto;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
