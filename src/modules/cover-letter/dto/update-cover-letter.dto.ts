import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class UpdateCoverLetterDto {
  @ApiProperty({ required: false, example: '<PERSON><PERSON><PERSON>' })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ required: false, example: 'Frontend Developer' })
  @IsOptional()
  @IsString()
  targetPosition?: string;

  @ApiProperty({
    required: false,
    example: 'https://example.com/profile-image.jpg',
  })
  @IsOptional()
  @IsString()
  profileImage?: string;

  @ApiProperty({ required: false, example: '1998-05-21' })
  @IsOptional()
  @IsString()
  dateOfBirth?: string;

  @ApiProperty({ required: false, example: '+84 912 345 678' })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({ required: false, example: 'nguyen<PERSON>@example.com' })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    required: false,
    example: '123 Nguyen Trai, District 5, Ho Chi Minh City, Vietnam',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    required: false,
    example: 'https://www.linkedin.com/in/nguyenvana',
  })
  @IsOptional()
  @IsString()
  linkedin?: string;

  @ApiProperty({ required: false, example: 'Tran Thi B' })
  @IsOptional()
  @IsString()
  hiringManagerName?: string;

  @ApiProperty({ required: false, example: 'HR Manager' })
  @IsOptional()
  @IsString()
  hiringManagerTitle?: string;

  @ApiProperty({ required: false, example: 'ABC Tech Solutions' })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiProperty({
    required: false,
    example: '456 Le Loi, District 1, Ho Chi Minh City, Vietnam',
  })
  @IsOptional()
  @IsString()
  companyAddress?: string;

  @ApiProperty({ required: false, example: '2025-07-06' })
  @IsOptional()
  @IsString()
  applicationDate?: string;

  @ApiProperty({ required: false, example: 'React Developer Intern' })
  @IsOptional()
  @IsString()
  jobTitle?: string;

  @ApiProperty({ required: false, example: 'Dear Ms. Tran,' })
  @IsOptional()
  @IsString()
  greetingLine?: string;

  @ApiProperty({
    required: false,
    example:
      'I am writing to express my strong interest in the React Developer Intern position at ABC Tech Solutions. With a solid foundation in front-end development and a passion for building user-friendly applications, I am confident in my ability to contribute effectively to your team.',
  })
  @IsOptional()
  @IsString()
  openingParagraph?: string;

  @ApiProperty({
    required: false,
    example:
      'During my internship at XYZ Company, I developed a dashboard using React and Redux, improving performance by 20%. I also collaborated with backend developers to integrate APIs and enhanced code readability through reusable components.',
  })
  @IsOptional()
  @IsString()
  experienceAchievements?: string;

  @ApiProperty({
    required: false,
    example:
      'My strengths lie in React, TypeScript, and modern UI libraries such as Tailwind CSS and Material UI. I am also experienced with Git, RESTful APIs, and Agile development methodologies.',
  })
  @IsOptional()
  @IsString()
  skillsStrengths?: string;

  @ApiProperty({
    required: false,
    example:
      'I would welcome the opportunity to further discuss how my background and enthusiasm for technology align with the needs of your team. Thank you for considering my application.',
  })
  @IsOptional()
  @IsString()
  closingParagraph?: string;

  @ApiProperty({ required: false, example: 'Sincerely,\nNguyen Van A' })
  @IsOptional()
  @IsString()
  signature?: string;

  @ApiProperty({
    required: false,
    example:
      'https://ik.imagekit.io/b78xd9lggb/cover-letter/cover-letter-123.pdf',
    description: 'URL of the cover letter PDF file uploaded to ImageKit',
  })
  @IsOptional()
  @IsString()
  coverLetterFileUrl?: string;

  @ApiProperty({
    example: 'temp1',
    enum: ['temp1', 'temp2', 'temp3'],
    required: false,
    description: 'Template ID for cover letter design',
  })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiProperty({
    example: 'M',
    enum: ['S', 'M', 'L', 'XL'],
    required: false,
    description: 'Global font size for the entire cover letter',
  })
  @IsOptional()
  @IsString()
  fontSize?: string;

  @ApiProperty({
    example: 'Roboto',
    required: false,
    description: 'Global font family for the entire cover letter',
  })
  @IsOptional()
  @IsString()
  fontFamily?: string;

  @ApiProperty({
    example: '1.5',
    required: false,
    description: 'Global line height for the entire cover letter',
  })
  @IsOptional()
  @IsString()
  lineHeight?: string;

  @ApiProperty({
    example: 'blue',
    enum: ['blue', 'green', 'red', 'purple'],
    required: false,
    description: 'Global theme for the entire cover letter',
  })
  @IsOptional()
  @IsString()
  currentTheme?: string;

  @ApiProperty({
    example: {
      header: {
        textAlign: 'center',
        isBold: true,
        isItalic: false,
        isUnderline: false,
        isHidden: false,
      },
      contactInfo: {
        textAlign: 'left',
        isBold: false,
        isItalic: false,
        isUnderline: false,
        isHidden: true,
      },
    },
    required: false,
    description: 'Per-section formatting settings',
  })
  @IsOptional()
  sectionFormatting?: Record<
    string,
    {
      textAlign?: 'left' | 'center' | 'right' | 'justify';
      isBold?: boolean;
      isItalic?: boolean;
      isUnderline?: boolean;
      isHidden?: boolean;
    }
  >;

  @ApiProperty({
    example: {
      openingParagraph: [
        {
          start: 0,
          end: 10,
          bold: true,
          italic: false,
          underline: false,
        },
        {
          start: 15,
          end: 25,
          bold: false,
          italic: true,
          underline: true,
        },
      ],
      experienceAchievements: [
        {
          start: 5,
          end: 20,
          bold: true,
          italic: false,
          underline: false,
        },
      ],
    },
    required: false,
    description:
      'Detailed text-level formatting for specific text ranges within fields',
  })
  @IsOptional()
  detailedFormatting?: Record<
    string,
    Array<{
      start: number;
      end: number;
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
    }>
  >;
}
