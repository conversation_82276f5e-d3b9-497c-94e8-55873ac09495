import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Transform } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';
import { AiAgentCoverLetterType } from '@common/enum/agentType.enum';

@Schema({ timestamps: true })
export class CoverLetter {
  @Prop({ type: String, default: () => uuidv4(), required: true })
  @Transform(({ value }): string => value.toString())
  _id: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ type: Object, required: true })
  content: Record<string, any>;

  @Prop({ enum: AiAgentCoverLetterType, required: true })
  agentType: AiAgentCoverLetterType;

  @Prop({ required: true })
  cvFileUrl: string;

  @Prop({ required: true })
  cvText: string;

  @Prop({ required: false })
  jdFileUrl?: string;

  @Prop({ required: false })
  jdText?: string;

  @Prop({ required: false })
  roleDescription?: string;

  @Prop({ required: true })
  language: string;

  @Prop({ required: false, default: null })
  coverLetterFileUrl?: string;

  @Prop({ required: false, default: null })
  templateId?: string;

  @Prop({ required: false, default: 'M' })
  fontSize?: string;

  @Prop({ required: false, default: 'Roboto' })
  fontFamily?: string;

  @Prop({ required: false, default: '1.5' })
  lineHeight?: string;

  @Prop({ required: false, default: 'blue' })
  currentTheme?: string;


  @Prop({
    type: Object,
    required: false,
    default: () => ({}),
  })
  sectionFormatting?: Record<
    string,
    {
      textAlign?: 'left' | 'center' | 'right' | 'justify';
      isBold?: boolean;
      isItalic?: boolean;
      isUnderline?: boolean;
      isHidden?: boolean;
    }
  >;

  @Prop({
    type: Object,
    required: false,
    default: () => ({}),
  })
  detailedFormatting?: Record<
    string, 
    Array<{
      start: number; 
      end: number; 
      bold?: boolean; 
      italic?: boolean; 
      underline?: boolean; 
    }>
  >;

  @Prop({ type: Object, required: false, default: null })
  evaluation?: Record<string, any>;
}

export type CoverLetterDocument = CoverLetter & Document;
export const CoverLetterSchema = SchemaFactory.createForClass(CoverLetter);
