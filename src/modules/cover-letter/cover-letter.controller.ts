import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  BadRequestException,
} from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger/dist/decorators/api-bearer.decorator';
import { ApiConsumes, ApiBody } from '@nestjs/swagger';
import { CoverLetterService } from './cover-letter.service';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import {
  CreateCoverLetterJdDto,
  CreateCoverLetterRoleDescriptionDto,
} from './dto/create-cover-letter.dto';
import { UpdateCoverLetterDto } from './dto/update-cover-letter.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import { ImageKitService } from '@modules/imagekit/imagekit.service';

@Controller({ path: 'cover-letter', version: '1' })
@ApiBearerAuth('Authorization')
export class CoverLetterController {
  constructor(
    private readonly coverLetterService: CoverLetterService,
    private readonly imagekitService: ImageKitService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Post('cover-letter-by-role-description')
  @UseInterceptors(FileInterceptor('cvFile'))
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto(
    'Generate cover letter by role description',
    'Generate cover letter by role description',
  )
  async createCoverLetterByRoleDescription(
    @Body() createCoverLetterRoleDto: CreateCoverLetterRoleDescriptionDto,
    @UploadedFile() cvFile: Express.Multer.File,
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;

    const response =
      await this.coverLetterService.createCoverLetterByRoleDescription(
        createCoverLetterRoleDto,
        cvFile,
        userId,
      );

    return new ApiResponseDto(
      201,
      'Cover letter created successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Post('cover-letter-by-jd-description')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'cvFile', maxCount: 1 },
      { name: 'jdFile', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto(
    'Generate cover letter by jd description',
    'Generate cover letter by jd description',
  )
  async createCoverLetterByJdDescription(
    @Body() createCoverLetterJdDto: CreateCoverLetterJdDto,
    @UploadedFiles()
    files: {
      cvFile?: Express.Multer.File[];
      jdFile?: Express.Multer.File[];
    },
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;
    const cvFile = files.cvFile?.[0];
    const jdFile = files.jdFile?.[0];

    if (!cvFile || !jdFile) {
      throw new ValidationException(ErrorCode.F001);
    }

    const response =
      await this.coverLetterService.createCoverLetterByJdDescription(
        createCoverLetterJdDto,
        cvFile,
        jdFile,
        userId,
      );

    return new ApiResponseDto(
      201,
      'Cover letter created successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('cover-letter/:id')
  @ApiOperationAuto('Get cover letter by id', 'Get cover letter by id')
  async getCoverLetterById(@Param('id') id: string) {
    const response = await this.coverLetterService.getCoverLetterById(id);
    return new ApiResponseDto(
      200,
      'Cover letter fetched successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-cover-letter')
  @ApiOperationAuto('Get my cover letter', 'Get my cover letter')
  async getMyCoverLetter(@Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.coverLetterService.getMyCoverLetter(userId);
    return new ApiResponseDto(
      200,
      'Cover letter fetched successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('all-cover-letter')
  @ApiOperationAuto('Get all cover letter', 'Get all cover letter')
  async getAllCoverLetter() {
    const response = await this.coverLetterService.getAllCoverLetter();
    return new ApiResponseDto(
      200,
      'Cover letter fetched successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Put('cover-letter/:id')
  @ApiOperationAuto('Update cover letter', 'Update cover letter by id')
  async updateCoverLetter(
    @Param('id') id: string,
    @Body() updateCoverLetterDto: UpdateCoverLetterDto,
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;
    const response = await this.coverLetterService.updateCoverLetter(
      id,
      updateCoverLetterDto,
      userId,
    );
    return new ApiResponseDto(
      200,
      'Cover letter updated successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Post('evaluate/:id')
  @ApiOperationAuto('Evaluate cover letter', 'AI evaluation of cover letter')
  async evaluateCoverLetter(@Param('id') id: string, @Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.coverLetterService.evaluateCoverLetter(
      id,
      userId,
    );
    return new ApiResponseDto(
      200,
      'Cover letter evaluated successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('evaluate/:id')
  @ApiOperationAuto(
    'Get cover letter evaluation',
    'Get evaluation result of a specific cover letter',
  )
  async getCoverLetterEvaluation(@Param('id') id: string, @Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.coverLetterService.getCoverLetterEvaluation(
      id,
      userId,
    );

    if (!response) {
      return new ApiResponseDto(
        404,
        'No evaluation found for this cover letter',
        null,
      );
    }

    return new ApiResponseDto(
      200,
      'Cover letter evaluation fetched successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-evaluate')
  @ApiOperationAuto(
    'Get all evaluated cover letters',
    'Get all cover letters that have been evaluated',
  )
  async getAllEvaluatedCoverLetters(@Req() req: any) {
    const userId = req.user.sub as string;
    const response =
      await this.coverLetterService.getAllEvaluatedCoverLetters(userId);
    return new ApiResponseDto(
      200,
      'Evaluated cover letters fetched successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiOperationAuto('Delete cover letter by id', 'Delete cover letter')
  async deleteCoverLetter(@Param('id') id: string) {
    await this.coverLetterService.deleteCoverLetter(id);
    return new ApiResponseDto(200, 'Cover letter deleted successfully');
  }

  @UseGuards(JwtAuthGuard)
  @Post('upload-avatar/:id')
  @ApiOperationAuto(
    'Upload avatar for cover letter',
    'Upload avatar image for the cover letter profile',
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, cb) => {
        if (!/\/(jpg|jpeg|png|gif)$/.test(file.mimetype)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  )
  async uploadCoverLetterAvatar(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    try {
      const userId = req.user.sub as string;

      // Upload to ImageKit in the "uploads" folder
      const avatarUrl = await this.imagekitService.uploadFile(file, 'uploads');

      const updatedCoverLetter =
        await this.coverLetterService.updateCoverLetterAvatar(
          id,
          avatarUrl,
          userId,
        );

      return new ApiResponseDto(200, 'Avatar uploaded successfully', {
        profileImage: updatedCoverLetter.content.header?.profileImage,
      });
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw error;
    }
  }
}
