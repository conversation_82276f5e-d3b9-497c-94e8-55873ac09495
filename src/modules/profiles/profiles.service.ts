import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Profile } from './entities/profile.entity';
import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { ResponseProfileDto } from './dto/response-profile.dto';
import { plainToInstance } from 'class-transformer';
import { ImageKitService } from '../imagekit/imagekit.service';
import { getDefaultAvatarUrl } from '../../utils/default-avatar.util';
import { User } from '../users/entities/user.entity';

@Injectable()
export class ProfilesService {
  constructor(
    @InjectRepository(Profile)
    private readonly profileRepository: Repository<Profile>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly imagekitService: ImageKitService,
  ) {}

  async create(
    createProfileDto: CreateProfileDto,
    userId: string,
  ): Promise<ResponseProfileDto> {
    const profile = this.profileRepository.create({
      ...createProfileDto,
      userId,
    });

    const savedProfile = await this.profileRepository.save(profile);
    return plainToInstance(ResponseProfileDto, savedProfile, {
      excludeExtraneousValues: true,
    });
  }

  async findByUserId(userId: string): Promise<ResponseProfileDto | null> {
    const profile = await this.profileRepository.findOne({
      where: { userId },
    });

    if (!profile) {
      return null;
    }

    return plainToInstance(ResponseProfileDto, profile, {
      excludeExtraneousValues: true,
    });
  }

  async createOrUpdate(
    userId: string,
    createOrUpdateProfileDto: UpdateProfileDto,
  ): Promise<ResponseProfileDto> {
    const existingProfile = await this.profileRepository.findOne({
      where: { userId },
    });

    if (existingProfile) {
      // Update existing profile
      await this.profileRepository.update(
        existingProfile.id,
        createOrUpdateProfileDto,
      );
      const updatedProfile = await this.profileRepository.findOne({
        where: { id: existingProfile.id },
      });
      if (!updatedProfile) {
        throw new NotFoundException('Profile not found after update');
      }
      return plainToInstance(ResponseProfileDto, updatedProfile, {
        excludeExtraneousValues: true,
      });
    } else {
      // Create new profile
      const profile = this.profileRepository.create({
        ...createOrUpdateProfileDto,
        userId,
      });
      const savedProfile = await this.profileRepository.save(profile);
      return plainToInstance(ResponseProfileDto, savedProfile, {
        excludeExtraneousValues: true,
      });
    }
  }

  async updateProfileAvatar(
    userId: string,
    avatarUrl: string,
  ): Promise<{ avatarUrl: string }> {
    // Update user avatar field
    await this.userRepository.update(userId, { avatar: avatarUrl });
    return { avatarUrl };
  }

  async deleteProfileAvatar(userId: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.avatar || user.avatar === getDefaultAvatarUrl()) {
      throw new NotFoundException('No custom avatar found');
    }

    try {
      // Delete the file from ImageKit 
      if (user.avatar !== getDefaultAvatarUrl()) {
        const deleteResult = await this.imagekitService.deleteFileByUrl({
          url: user.avatar,
        });

        if (!deleteResult) {
          console.log(
            'File not found in ImageKit, proceeding with database cleanup',
          );
        }
      }

      // Reset to default avatar
      await this.userRepository.update(userId, {
        avatar: getDefaultAvatarUrl(),
      });
    } catch (error) {
      console.log('Error deleting avatar:', error);
      throw error;
    }
  }
}
