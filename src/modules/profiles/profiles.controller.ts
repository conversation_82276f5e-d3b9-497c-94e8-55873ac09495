import {
  Controller,
  Post,
  Body,
  Put,
  Delete,
  UseGuards,
  Req,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Param,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ProfilesService } from './profiles.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { JwtAuthGuard } from '../../guards/jwt-guard/jwt.guard';
import { RolesGuard } from '../../guards/roles-guard/roles.guard';
import { ApiBearerAuth, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { ApiOperationAuto } from '../../decorators/swagger/api-operation.decorator';
import { ApiResponseDto } from '../../common/dto/api-response.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { ImageKitService } from '../imagekit/imagekit.service';

@Controller({ path: 'profiles', version: '1' })
@ApiBearerAuth('Authorization')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ProfilesController {
  constructor(
    private readonly profilesService: ProfilesService,
    private readonly imagekitService: ImageKitService,
  ) {}

  @Put()
  @ApiOperationAuto(
    'Create or update profile',
    'Create or update the current user profile',
  )
  async createOrUpdate(
    @Body() createOrUpdateProfileDto: UpdateProfileDto,
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;
    const response = await this.profilesService.createOrUpdate(
      userId,
      createOrUpdateProfileDto,
    );
    return new ApiResponseDto(200, 'Profile saved successfully', response);
  }

  @Post('upload-avatar')
  @ApiOperationAuto('Upload avatar', 'Upload avatar image for the user profile')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, cb) => {
        if (!/\/(jpg|jpeg|png|gif)$/.test(file.mimetype)) {
          return cb(
            new BadRequestException('Only image files are allowed!'),
            false,
          );
        }
        cb(null, true);
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  )
  async uploadAvatar(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    try {
      const userId = req.user.sub as string;

      // Upload to ImageKit in the "uploads" folder
      const avatarUrl = await this.imagekitService.uploadFile(file, 'uploads');

      const response = await this.profilesService.updateProfileAvatar(
        userId,
        avatarUrl,
      );
      return new ApiResponseDto(200, 'Avatar uploaded successfully', {
        ...response,
        avatarUrl,
      });
    } catch (error) {
      console.log('Avatar upload error:', error);
      throw new BadRequestException('Failed to upload avatar');
    }
  }

  @Delete('avatar/:id')
  @ApiOperationAuto(
    'Delete avatar',
    'Delete avatar image for the specified user',
  )
  async deleteAvatar(@Param('id') id: string, @Req() req: any) {
    try {
      await this.profilesService.deleteProfileAvatar(id);
      return new ApiResponseDto(200, 'Avatar deleted successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.log('Avatar deletion error:', error);
      throw new InternalServerErrorException('Failed to delete avatar');
    }
  }
}
