import { Inject, Injectable } from '@nestjs/common';
import ImageKit from 'imagekit';
import { CreateImagekitDto } from './dto/create-imagekit.dto';

@Injectable()
export class ImageKitService {
  constructor(@Inject('IMAGEKIT') private readonly imagekit: ImageKit) {}

  async uploadFile(file: Express.Multer.File, folder: string): Promise<string> {
    try {
      const base64 = file.buffer.toString('base64');

      const extension = file.originalname.split('.').pop();
      const fileNameWithoutExt = file.originalname.replace(/\.[^/.]+$/, '');
      const fileName = `${fileNameWithoutExt}.${extension}`;

      const response = await this.imagekit.upload({
        file: base64,
        fileName,
        folder,
      });

      return response.url;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async deleteFileByUrl(payload: CreateImagekitDto): Promise<boolean> {
    const urlObj = new URL(payload.url);
    let filePath = urlObj.pathname;

    if (filePath.startsWith('/')) {
      filePath = filePath.slice(1);
    }

    // Tách name file từ URL
    const parts = filePath.split('/');
    const fileName = parts[parts.length - 1];

    const files = await this.imagekit.listFiles({
      name: fileName,
      limit: 1,
    });

    if (!files.length) {
      return false;
    }

    const file = files[0];
    if ('fileId' in file) {
      await this.imagekit.deleteFile(file.fileId);
      return true;
    }

    return false;
  }

  async getAuthenticationParameters(): Promise<{
    signature: string;
    expire: number;
    token: string;
  }> {
    try {
      const authenticationParameters =
        this.imagekit.getAuthenticationParameters();
      return authenticationParameters;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
