import {
  Controller,
  Get,
  Post,
  Delete,
  UseInterceptors,
  UploadedFile,
  Body,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ImageKitService } from './imagekit.service';
import { Public } from '@decorators/auth/public.decorator';
import { ApiBody, ApiConsumes } from '@nestjs/swagger';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import { CreateImagekitDto } from './dto/create-imagekit.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';

@Controller('imagekit')
export class ImageKitController {
  constructor(private readonly imagekitService: ImageKitService) {}

  @Public()
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async upload(@UploadedFile() file: Express.Multer.File) {
    const result = await this.imagekitService.uploadFile(file, 'uploads');
    return new ApiResponseDto(200, 'Uploaded successfully', result);
  }

  @Public()
  @Delete()
  async delete(@Body() body: CreateImagekitDto) {
    await this.imagekitService.deleteFileByUrl(body);
    return new ApiResponseDto(200, 'File deleted successfully');
  }

  @Public()
  @Get('auth')
  @ApiOperationAuto(
    'Get ImageKit authentication parameters',
    'Get authentication parameters for frontend ImageKit SDK',
  )
  async getAuthenticationParameters() {
    const authParams = await this.imagekitService.getAuthenticationParameters();
    return authParams;
  }
}
