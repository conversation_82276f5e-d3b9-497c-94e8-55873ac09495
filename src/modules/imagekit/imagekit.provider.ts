import { ConfigService } from '@nestjs/config';

export const ImageKitProvider = {
  provide: 'IMAGEKIT',
  inject: [ConfigService],
  useFactory: async (configService: ConfigService) => {
    const ImageKitLib = await import('imagekit');
    const ImageKit = ImageKitLib.default || ImageKitLib;

    return new ImageKit({
      publicKey: configService.get<string>('imagekit.publicKey') || '',
      privateKey: configService.get<string>('imagekit.privateKey') || '',
      urlEndpoint: configService.get<string>('imagekit.urlEndpoint') || '',
    });
  },
};
