import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Transform } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';

@Schema({ _id: false })
export class PersonalInfo {
  @Prop({ required: true, default: '' })
  fullName: string;

  @Prop({ required: true, default: '' })
  email: string;

  @Prop({ required: true, default: '' })
  phoneNumber: string;

  @Prop()
  address?: string;

  @Prop()
  linkedin?: string;

  @Prop()
  github?: string;

  @Prop()
  website?: string;

  @Prop()
  dateOfBirth?: string;

  @Prop()
  nationality?: string;

  @Prop()
  maritalStatus?: string;
}

@Schema({ _id: false })
export class WorkExperience {
  @Prop({ required: true, default: '' })
  position: string;

  @Prop({ required: true, default: '' })
  company: string;

  @Prop({ required: true, default: '' })
  duration: string;

  @Prop()
  startDate?: string;

  @Prop()
  endDate?: string;

  @Prop()
  location?: string;

  @Prop({ type: [String], required: true, default: [] })
  responsibilities: string[];

  @Prop({ type: [String], required: true, default: [] })
  achievements: string[];

  @Prop({ type: [String] })
  technologies?: string[];
}

@Schema({ _id: false })
export class Education {
  @Prop({ required: true, default: '' })
  degree: string;

  @Prop({ required: true, default: '' })
  institution: string;

  @Prop({ required: true, default: '' })
  duration: string;

  @Prop()
  startDate?: string;

  @Prop()
  endDate?: string;

  @Prop()
  gpa?: string;

  @Prop()
  major?: string;

  @Prop()
  location?: string;

  @Prop({ type: [String] })
  achievements?: string[];
}

@Schema({ _id: false })
export class Skill {
  @Prop({ required: true })
  category: string;

  @Prop({ type: [String], required: true, default: [] })
  skills: string[];

  @Prop()
  proficiencyLevel?: string;

  @Prop()
  yearsOfExperience?: number;
}

@Schema({ _id: false })
export class Certification {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  issuer: string;

  @Prop()
  issueDate?: string;

  @Prop()
  expiryDate?: string;

  @Prop()
  verificationUrl?: string;
}

@Schema({ _id: false })
export class Project {
  @Prop({ required: true, default: '' })
  name: string;

  @Prop({ required: true, default: '' })
  description: string;

  @Prop()
  duration?: string;

  @Prop()
  role?: string;

  @Prop({ type: [String], required: true, default: [] })
  technologies: string[];

  @Prop({ type: [String] })
  achievements?: string[];

  @Prop({ required: true, default: 1 })
  teamSize: number;

  @Prop()
  url?: string;

  @Prop()
  repositoryUrl?: string;
}

@Schema({ _id: false })
export class Language {
  @Prop({ required: true, default: '' })
  language: string;

  @Prop({ required: true, default: '' })
  proficiency: string;
}

@Schema({ _id: false })
export class AdditionalInfo {
  @Prop({ type: [String] })
  hobbies?: string[];

  @Prop({ type: [String] })
  volunteer_experience?: string[];

  @Prop({ type: [String] })
  awards?: string[];

  @Prop({ type: [String] })
  publications?: string[];

  @Prop({ type: [String] })
  references?: string[];
}

@Schema({ timestamps: true })
export class CV {
  @Prop({ type: String, default: () => uuidv4(), required: true })
  @Transform(({ value }): string => value.toString())
  _id: string;

  @Prop({ type: PersonalInfo, required: true })
  personal_info: PersonalInfo;

  @Prop({ required: true, default: '' })
  possitionJob: string;

  @Prop()
  summary?: string;

  @Prop({ type: [WorkExperience] })
  work_experience: WorkExperience[];

  @Prop({ type: [Education] })
  education: Education[];

  @Prop({ type: [Skill] })
  skills: Skill[];

  @Prop({ type: [Certification] })
  certifications: Certification[];

  @Prop({ type: [Project] })
  projects: Project[];

  @Prop({ type: [Language] })
  languages: Language[];

  @Prop({ type: AdditionalInfo })
  additional_info?: AdditionalInfo;

  @Prop({ type: [String] })
  parsing_notes?: string[];

  @Prop({ required: true, default: '' })
  original_text: string;

  @Prop({ required: true, default: '' })
  file_name: string;

  @Prop()
  file_path?: string;

  @Prop({ type: Object })
  metadata?: Record<string, any>;
}

export type CVDocument = CV & Document;
export const CVSchema = SchemaFactory.createForClass(CV);
