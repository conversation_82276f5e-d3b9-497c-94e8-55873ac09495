import { Modu<PERSON> } from '@nestjs/common';
import { ParsePdfService } from './parse-pdf.service';
import { ParsePdfController } from './parse-pdf.controller';
import { AiModule } from '@modules/ai/ai.module';
import { MongooseModule } from '@nestjs/mongoose';
import { CV, CVSchema } from './schemas/cv.schema';
import { JwtModule } from '@nestjs/jwt';
@Module({
  controllers: [ParsePdfController],
  imports: [
    AiModule,
    MongooseModule.forFeature([{ name: CV.name, schema: CVSchema }]),
    JwtModule,
  ],
  providers: [ParsePdfService],
  exports: [ParsePdfService],
})
export class ParsePdfModule {}
