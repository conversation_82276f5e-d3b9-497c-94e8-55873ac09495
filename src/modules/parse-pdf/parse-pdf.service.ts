import { Injectable } from '@nestjs/common';
import { AiParseCvAgent } from '@modules/ai/agents/ai-parse-cv.agent';
import * as pdfParse from 'pdf-parse';
import { InjectModel } from '@nestjs/mongoose';
import { CVDocument } from './schemas/cv.schema';
import { Model } from 'mongoose';
import { CV } from './schemas/cv.schema';
import { ParsePdfCvResponseDto } from './dto/create-parse-pdf.dto';
import { plainToInstance } from 'class-transformer';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';

@Injectable()
export class ParsePdfService {
  constructor(
    private readonly aiParseCvAgent: AiParseCvAgent,
    @InjectModel(CV.name)
    private readonly cvModel: Model<CVDocument>,
  ) {}

  async createParsePdfCv(
    cvFile: Express.Multer.File,
  ): Promise<ParsePdfCvResponseDto> {
    try {
      if (!cvFile || cvFile.mimetype !== 'application/pdf') {
        throw new ValidationException(ErrorCode.F001);
      }

      const result = await pdfParse(cvFile.buffer);
      const cvText = result.text;

      const response = await this.aiParseCvAgent.parseCv({ cvText });
      const cv = await this.cvModel.create({
        ...response,
        original_text: cvText,
        file_name: cvFile.originalname,
        file_path: cvFile.path,
        metadata: {
          mime_type: cvFile.mimetype,
          size: cvFile.size,
          pages: result.numpages,
        },
      });

      return plainToInstance(ParsePdfCvResponseDto, cv);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  findAll() {
    return this.cvModel.find().exec();
  }

  findOne(id: number) {
    return this.cvModel.findById(id).exec();
  }

  remove(id: number) {
    return this.cvModel.findByIdAndDelete(id).exec();
  }
}
