import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseInterceptors,
  UseGuards,
  Req,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { ParsePdfService } from './parse-pdf.service';
import { CreateParseCvPdfDto } from './dto/create-parse-pdf.dto';

@Controller({ path: 'parse-pdf', version: '1' })
@ApiBearerAuth('Authorization')
export class ParsePdfController {
  constructor(private readonly parsePdfService: ParsePdfService) {}

  @Post('cv')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('cvFile'))
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto('Parse PDF CV', 'Parse PDF CV')
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto('Parse PDF CV', 'Parse PDF CV')
  async createParsePdfCv(
    @Body() createParseCvPdfDto: CreateParseCvPdfDto,
    @UploadedFile() cvFile: Express.Multer.File,
    @Req() req: any,
  ) {
    if (!cvFile) {
      throw new BadRequestException('CV file is required');
    }

    const userId = req.user.sub as string;
    const response = await this.parsePdfService.createParsePdfCv(cvFile);
    return new ApiResponseDto(201, 'Parse PDF CV successfully', response);
  }

  @Get()
  findAll() {
    return this.parsePdfService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.parsePdfService.findOne(+id);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.parsePdfService.remove(+id);
  }
}
