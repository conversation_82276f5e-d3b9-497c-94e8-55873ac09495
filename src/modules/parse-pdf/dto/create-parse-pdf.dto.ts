import {
  AdditionalInfoDto,
  CertificationDto,
  EducationDto,
  LanguageDto,
  PersonalInfoDto,
  ProjectDto,
  SkillDto,
  WorkExperienceDto,
} from '@modules/ai/dto/response-ai.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class CreateParseCvPdfDto {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload CV file (PDF)',
  })
  cvFile: any;
}

export class ParsePdfCvResponseDto {
  @Expose()
  _id: string;

  @Expose()
  personal_info: PersonalInfoDto;

  @Expose()
  position?: string;

  @Expose()
  summary?: string;

  @Expose()
  work_experience: WorkExperienceDto[];

  @Expose()
  education: EducationDto[];

  @Expose()
  skills: SkillDto[];

  @Expose()
  certifications: CertificationDto[];

  @Expose()
  projects: ProjectDto[];

  @Expose()
  languages: LanguageDto[];

  @Expose()
  additional_info?: AdditionalInfoDto;

  @Expose()
  parsing_notes?: string[];

  @Expose()
  original_text: string;

  @Expose()
  file_name: string;

  @Expose()
  file_path?: string;

  @Expose()
  metadata?: Record<string, any>;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
