import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Transform } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';

@Schema({ timestamps: true })
export class Conversation {
  @Prop({ type: String, default: () => uuidv4(), required: true })
  @Transform(({ value }): string => value.toString())
  _id: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true, ref: 'Interview', type: String })
  interviewId: string;

  @Prop({ required: true, type: [Object] })
  conversationLog: object[];

  @Prop({ required: true, type: Object })
  feedback: object;

  @Prop({ required: true })
  interviewDuration: string;
}

export type ConversationDocument = Conversation & Document;
export const ConversationSchema = SchemaFactory.createForClass(Conversation);
