import { Injectable } from '@nestjs/common';
import { CreateConversationDto } from './dto/create-conversation.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Conversation } from './schemas/conversation.schema';
import { Model } from 'mongoose';
import { FeedbackInterviewAgent } from '@modules/ai/agents/feedback-interview.agent';
import { InterviewService } from '@modules/interview/interview.service';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import { plainToInstance } from 'class-transformer';
import { ResponseCreateConversationDto } from './dto/response-conversation.dto';

@Injectable()
export class ConversationService {
  constructor(
    @InjectModel(Conversation.name)
    private conversationModel: Model<Conversation>,
    private readonly feedbackInterviewAgent: FeedbackInterviewAgent,
    private readonly interviewService: InterviewService,
  ) {}

  async create(
    createConversationDto: CreateConversationDto,
    userId: string,
  ): Promise<ResponseCreateConversationDto> {
    try {
      const interview = await this.interviewService.getQuestionInterviewById(
        createConversationDto.interviewId,
      );

      if (!interview) {
        throw new ValidationException(ErrorCode.I001);
      }

      const feedbackInterview =
        await this.feedbackInterviewAgent.feedbackInterview({
          interviewQuestions: interview.interviewQuestions,
          conversationLog: createConversationDto.conversationLog,
        });

      const result = await this.conversationModel.create({
        ...createConversationDto,
        feedback: feedbackInterview,
        userId,
      });

      return plainToInstance(ResponseCreateConversationDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async findAll(): Promise<ResponseCreateConversationDto[]> {
    try {
      const result = await this.conversationModel
        .find()
        .sort({ createdAt: -1 });

      if (!result) {
        return [];
      }

      return plainToInstance(ResponseCreateConversationDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async findById(id: string): Promise<ResponseCreateConversationDto> {
    try {
      const result = await this.conversationModel.findById(id);
      if (!result) {
        throw new ValidationException(ErrorCode.CV001);
      }
      return plainToInstance(ResponseCreateConversationDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async findByInterviewId(
    interviewId: string,
  ): Promise<ResponseCreateConversationDto[]> {
    try {
      const result = await this.conversationModel
        .find({ interviewId })
        .sort({ createdAt: -1 });

      if (!result) {
        return [];
      }
      return plainToInstance(ResponseCreateConversationDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async findByUserId(userId: string): Promise<ResponseCreateConversationDto[]> {
    try {
      const result = await this.conversationModel
        .find({ userId })
        .sort({ createdAt: -1 });

      if (!result) {
        return [];
      }
      return plainToInstance(ResponseCreateConversationDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async remove(id: string) {
    try {
      const result = await this.conversationModel.findByIdAndDelete(id);
      if (!result) {
        throw new ValidationException(ErrorCode.C001);
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
