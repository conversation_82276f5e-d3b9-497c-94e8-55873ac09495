import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsString } from 'class-validator';
import { ConversationDto } from './conversation.dto';

export class CreateConversationDto {
  @ApiProperty({ example: '2eadb054-6ac7-478e-ba5a-0083a48b8c56' })
  @IsString()
  interviewId: string;

  @ApiProperty({
    example: [
      // {
      //   role: 'assistant',
      //   content:
      //     'Hi Trinh Quoc Thang! Are you ready for your mock interview for the Front End Developer position?',
      // },
      // {
      //   role: 'user',
      //   content: "Yes, I'm ready. Let's begin.",
      // },
      // {
      //   role: 'assistant',
      //   content:
      //     "First question: Can you briefly explain what Dependency Injection is in Angular and why it's useful?",
      // },
      // {
      //   role: 'user',
      //   content:
      //     'Dependency Injection is a technique Angular uses to provide services or dependencies to components or other services. It helps in keeping the code clean, modular, and makes testing easier by allowing us to inject mock dependencies.',
      // },
      // {
      //   role: 'assistant',
      //   content:
      //     'Great! Now, have you ever faced performance issues with <PERSON><PERSON>? If yes, how did you resolve them?',
      // },
      // {
      //   role: 'user',
      //   content:
      //     'Yes, I once worked on a project where the app loaded slowly. I implemented lazy loading for modules and used the trackBy function in ngFor to reduce unnecessary re-renders. That helped improve load time significantly.',
      // },
      // {
      //   role: 'assistant',
      //   content:
      //     'Nice. Final question: Can you list a few best practices for optimizing Angular application performance?',
      // },
      // {
      //   role: 'user',
      //   content:
      //     'Sure. Some best practices include using the OnPush change detection strategy, avoiding unnecessary DOM manipulation, unsubscribing from observables properly, and using pure pipes instead of inline functions in templates.',
      // },
      // {
      //   role: 'assistant',
      //   content:
      //     'Thanks for your answers! That wraps up our mock interview. Keep practicing and best of luck with your job search!',
      // },
      // Todo: Add this to the conversation log
      {
        role: 'assistant',
        content:
          'Hi Trinh Quoc Thang, how are you? Ready for your interview for Front End Developer?',
      },
      {
        role: 'user',
        content: "Yes, let's begin.",
      },
      {
        role: 'assistant',
        content:
          'Can you briefly explain Dependency Injection in Angular and why it is useful?',
      },
      {
        role: 'user',
        content:
          "I think it's about importing services in the component file. You just call them when you need.",
      },
      {
        role: 'assistant',
        content:
          'Have you ever faced a performance issue in Angular? If so, how did you resolve it?',
      },
      {
        role: 'user',
        content:
          'Not really sure. I think I removed some files and the app ran better.',
      },
      {
        role: 'assistant',
        content:
          'Can you name a few best practices to optimize Angular application performance?',
      },
      {
        role: 'user',
        content:
          "Use less code, I guess. And maybe don't do too much in one function.",
      },
    ],
  })
  @IsArray()
  conversationLog: ConversationDto[];

  @ApiProperty({ example: '10:00' })
  @IsString()
  interviewDuration: string;
}
