import { Expose } from 'class-transformer';
import { ConversationDto } from './conversation.dto';

export class ResponseCreateConversationDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  interviewId: string;

  @Expose()
  conversationLog: ConversationDto[];

  @Expose()
  feedback: object;

  @Expose()
  interviewDuration: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
