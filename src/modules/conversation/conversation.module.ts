import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ConversationService } from './conversation.service';
import { ConversationController } from './conversation.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Conversation,
  ConversationSchema,
} from './schemas/conversation.schema';
import { JwtModule } from '@nestjs/jwt';
import { AiModule } from '@modules/ai/ai.module';
import { InterviewModule } from '@modules/interview/interview.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Conversation.name, schema: ConversationSchema },
    ]),
    JwtModule,
    AiModule,
    InterviewModule,
  ],
  controllers: [ConversationController],
  providers: [ConversationService],
})
export class ConversationModule {}
