import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
} from '@nestjs/common';
import { ConversationService } from './conversation.service';
import { CreateConversationDto } from './dto/create-conversation.dto';
import { ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { ResponseCreateConversationDto } from './dto/response-conversation.dto';

@ApiBearerAuth('Authorization')
@Controller({
  path: 'conversation',
  version: '1',
})
export class ConversationController {
  constructor(private readonly conversationService: ConversationService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  @ApiOperationAuto('Create conversation', 'Create conversation')
  async create(
    @Body() createConversationDto: CreateConversationDto,
    @Req() req: any,
  ): Promise<ApiResponseDto<ResponseCreateConversationDto>> {
    const userId = req.user.sub as string;
    const response = await this.conversationService.create(
      createConversationDto,
      userId,
    );
    return new ApiResponseDto(
      200,
      'Create conversation successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  @ApiOperationAuto('Get all conversations', 'Get all conversations')
  async findAll(): Promise<ApiResponseDto<ResponseCreateConversationDto[]>> {
    const response = await this.conversationService.findAll();
    return new ApiResponseDto(
      200,
      'Get all conversations successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('user')
  @ApiOperationAuto('Get all my conversations', 'Get all my conversations')
  async findByUserId(
    @Req() req: any,
  ): Promise<ApiResponseDto<ResponseCreateConversationDto[]>> {
    const userId = req.user.sub as string;
    const response = await this.conversationService.findByUserId(userId);
    return new ApiResponseDto(
      200,
      'Get all conversations by user id successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  @ApiOperationAuto('Get conversation by id', 'Get conversation by id')
  async findById(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<ResponseCreateConversationDto>> {
    const response = await this.conversationService.findById(id);
    return new ApiResponseDto(
      200,
      'Get conversation by id successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('interview/:id')
  @ApiOperationAuto(
    'Get conversation by interview id',
    'Get conversation by interview id',
  )
  async findByInterviewId(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<ResponseCreateConversationDto[]>> {
    const response = await this.conversationService.findByInterviewId(id);
    return new ApiResponseDto(
      200,
      'Get conversation by interview id successfully',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiOperationAuto('Delete conversation by id', 'Delete conversation by id')
  async remove(@Param('id') id: string) {
    await this.conversationService.remove(id);
    return new ApiResponseDto(200, 'Delete conversation by id successfully');
  }
}
