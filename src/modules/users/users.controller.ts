import {
  Controller,
  Get,
  Put,
  Param,
  Body,
  UseGuards,
  Req,
  Inject,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import { RolesGuard } from '@guards/roles-guard/roles.guard';
import { ApiBearerAuth } from '@nestjs/swagger';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { Roles } from '@decorators/auth/roles.decorator';
import { Role } from '../../common/enum/role.enum';
import { UpdateUserRolesDto } from './dto/update-user-roles.dto';
import { JwtService } from '@nestjs/jwt';
import { AuthJwtPayload } from '../auth/types/auth-jwt.payload';
import refreshJwtConfig from '../auth/configs/refresh-jwt.config';
import { ConfigType } from '@nestjs/config';

@ApiBearerAuth('Authorization')
@Controller({ path: 'users', version: '1' })
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    @Inject(refreshJwtConfig.KEY)
    private readonly refreshTokenConfig: ConfigType<typeof refreshJwtConfig>,
  ) {}

  @UseGuards(JwtAuthGuard, RolesGuard)
  // @Roles(Role.USER)
  @Get()
  @ApiOperationAuto('Get all users', 'Get all users - requires USER role')
  async findAll() {
    const users = await this.usersService.findAll();
    return new ApiResponseDto(200, 'Get all users successfully', users);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiOperationAuto('Get user profile', 'Get user profile')
  async getProfile(@Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.usersService.getProfile(userId);
    return new ApiResponseDto(200, 'Get user profile successfully', response);
  }

  // TEMPORARY ENDPOINT FOR TESTING - REMOVE IN PRODUCTION
  @UseGuards(JwtAuthGuard)
  @Put('promote-to-admin')
  @ApiOperationAuto(
    'Promote to admin (TESTING ONLY)',
    'Promote current user to admin - FOR TESTING ONLY',
  )
  async promoteToAdmin(@Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.usersService.updateUserRoles(
      userId,
      Role.ADMIN,
    );

    // Generate new tokens with updated role
    const payload: AuthJwtPayload = {
      sub: userId,
      role: Role.ADMIN,
    };

    const accessToken = await this.jwtService.signAsync(payload);
    const refreshToken = await this.jwtService.signAsync(
      payload,
      this.refreshTokenConfig,
    );

    await this.usersService.updateRefreshToken(userId, refreshToken);

    return new ApiResponseDto(200, 'User promoted to admin successfully', {
      user: response,
      accessToken,
      refreshToken,
    });
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(Role.ADMIN)
  @Put(':id/roles')
  @ApiOperationAuto(
    'Update user roles (ADMIN only)',
    'Update user roles - requires ADMIN role',
  )
  async updateUserRoles(
    @Param('id') id: string,
    @Body() updateUserRolesDto: UpdateUserRolesDto,
  ) {
    const response = await this.usersService.updateUserRoles(
      id,
      updateUserRolesDto.role,
    );
    return new ApiResponseDto(200, 'User roles updated successfully', response);
  }
}
