import { Exclude, Expose, Transform } from 'class-transformer';
import { Role } from '../../../common/enum/role.enum';
import { getDefaultAvatarUrl } from '../../../utils/default-avatar.util';

@Exclude()
export class ResponseUserDto {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  @Transform(({ obj }) => obj.profile?.firstName ?? obj.firstName ?? null)
  firstName?: string;

  @Expose()
  @Transform(({ obj }) => obj.profile?.lastName ?? obj.lastName ?? null)
  lastName?: string;

  @Expose({ groups: ['withPassword'] })
  password: string;

  @Expose()
  @Transform(({ value, obj }) => {
    if (typeof value === 'string') return value;
    if (obj.role) return obj.role;
    return Role.USER;
  })
  role: string;

  @Expose()
  @Transform(({ obj }) => obj.profile?.bio ?? obj.bio ?? null)
  bio?: string;

  @Expose()
  @Transform(({ obj }) => obj.avatar ?? getDefaultAvatarUrl())
  avatar?: string;

  @Expose()
  @Transform(({ obj }) => obj.profile?.phone ?? obj.phone ?? null)
  phone?: string;

  @Expose()
  @Transform(({ obj }) => obj.profile?.dateOfBirth ?? obj.dateOfBirth ?? null)
  dateOfBirth?: Date;

  @Expose()
  @Transform(({ obj }) => obj.profile?.address ?? obj.address ?? null)
  address?: string;

  @Expose()
  @Transform(({ obj }) => obj.profile?.createdAt ?? obj.createdAt)
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.profile?.updatedAt ?? obj.updatedAt)
  updatedAt: Date;
}
