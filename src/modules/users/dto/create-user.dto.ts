import { IsEmail, IsOptional, IsString, IsIn, IsEnum } from 'class-validator';
import { Role } from '../../../common/enum/role.enum';

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  avatar?: string;

  @IsOptional()
  @IsIn(['local', 'google'])
  provider?: string = 'local';

  @IsOptional()
  @IsEnum(Role)
  role?: Role = Role.USER;
}
