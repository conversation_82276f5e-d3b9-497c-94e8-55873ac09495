import { Injectable } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { convertToSeconds, hashString } from '@utils/auth';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { ResponseUserDto } from './dto/response-user.dto';
import { plainToInstance } from 'class-transformer';
import { ErrorCode } from '@constants/error-code.constant';
import { ValidationException } from '@exceptions/validation.exception';
import { ProfilesService } from '../profiles/profiles.service';
import { Role } from '../../common/enum/role.enum';
import { ImageKitService } from '../imagekit/imagekit.service';
import { getDefaultAvatarUrl } from '../../utils/default-avatar.util';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRedis()
    private readonly redis: Redis,
    private readonly configService: ConfigService,
    private readonly profilesService: ProfilesService,
    private readonly imagekitService: ImageKitService,
  ) {}

  async create(createUserDto: CreateUserDto) {
    // Extract firstName and lastName for profile
    const { firstName, lastName, ...userDto } = createUserDto;

    const user = this.userRepository.create(userDto);
    // Always set role to USER regardless of input
    user.role = Role.USER;
    // Set default avatar
    user.avatar = getDefaultAvatarUrl();

    const savedUser = await this.userRepository.save(user);

    try {
      await this.profilesService.create({ firstName, lastName }, savedUser.id);
    } catch (error) {
      console.log('Error creating profile:', error);
    }

    return savedUser;
  }

  async findAll(): Promise<ResponseUserDto[]> {
    const users = await this.userRepository.find({
      relations: ['profile'],
    });
    return users.map((user) =>
      plainToInstance(ResponseUserDto, user, {
        excludeExtraneousValues: true,
      }),
    );
  }

  async findByEmail(email: string): Promise<ResponseUserDto> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['profile'],
    });
    return plainToInstance(ResponseUserDto, user, {
      excludeExtraneousValues: true,
      groups: ['withPassword'],
    });
  }

  async findById(id: string): Promise<ResponseUserDto> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });
    return plainToInstance(ResponseUserDto, user, {
      excludeExtraneousValues: true,
      groups: ['withPassword'],
    });
  }

  async findUserEntityById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['profile'],
    });
    if (!user) {
      throw new ValidationException(ErrorCode.U004);
    }
    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    await this.userRepository.update(id, updateUserDto);
    return this.findById(id);
  }

  async updatePassword(userId: string, newPassword: string) {
    await this.userRepository.update(userId, { password: newPassword });
  }

  async updateRefreshToken(userId: string, refreshToken: string) {
    try {
      const refreshTokenTtl = this.configService.get(
        'refresh-jwt.expiresIn',
      ) as string;

      const hashedRefreshToken = await hashString(refreshToken);
      const refreshTokenTtlInSeconds = convertToSeconds(refreshTokenTtl);
      await this.redis.set(
        `RT_${userId}`,
        hashedRefreshToken,
        'EX',
        refreshTokenTtlInSeconds,
      );
    } catch (error) {
      console.error('Error updating refresh token:', error);
      throw error;
    }
  }

  async getRefreshToken(userId: string) {
    const refreshToken = await this.redis.get(`RT_${userId}`);
    return refreshToken;
  }

  async deleteRefreshToken(userId: string) {
    await this.redis.del(`RT_${userId}`);
  }

  async getProfile(userId: string): Promise<ResponseUserDto> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['profile'],
      });
      if (!user) throw new ValidationException(ErrorCode.U004);
      return plainToInstance(ResponseUserDto, user, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async updateUserRoles(userId: string, role: Role): Promise<ResponseUserDto> {
    await this.userRepository.update(userId, { role: role });
    return this.findById(userId);
  }

  async updateAvatar(
    userId: string,
    avatarUrl: string,
  ): Promise<ResponseUserDto> {
    await this.userRepository.update(userId, { avatar: avatarUrl });
    return this.findById(userId);
  }

  async deleteAvatar(userId: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new ValidationException(ErrorCode.U004);
    }

    if (!user.avatar || user.avatar === getDefaultAvatarUrl()) {
      throw new ValidationException(ErrorCode.U004); // or create a new error for "No custom avatar found"
    }

    try {
      // Delete the file from ImageKit
      if (user.avatar !== getDefaultAvatarUrl()) {
        const deleteResult = await this.imagekitService.deleteFileByUrl({
          url: user.avatar,
        });

        if (!deleteResult) {
          console.log(
            'File not found in ImageKit, proceeding with database cleanup',
          );
        }
      }

      // Reset to default avatar
      await this.userRepository.update(userId, {
        avatar: getDefaultAvatarUrl(),
      });
    } catch (error) {
      console.log('Error deleting avatar:', error);
      throw error;
    }
  }
}
