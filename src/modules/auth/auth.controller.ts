import {
  Body,
  Controller,
  Get,
  Post,
  Put,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { Public } from '@decorators/auth/public.decorator';
import { GoogleGuard } from '@guards/google-guard/google-guard.guard';
import { ConfigService } from '@nestjs/config';
import { LocalAuthGuard } from '@guards/local-guard/local-auth.guard';
import { RegisterDto } from './dto/register.dto';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { LoginDto } from './dto/login.dto';
import { ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import { ChangePasswordDto } from './dto/change-password.dto';
import { RefreshJwtDto } from './dto/refresh-jwt.dto';
import { RefreshTokenGuard } from '@guards/jwt-guard/refres-jwt.guard';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import { CreateOtpDto } from './dto/create-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';

@Controller({
  path: 'auth',
  version: '1',
})
@ApiBearerAuth('Authorization')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  @Public()
  @Post('register')
  @ApiOperationAuto('Register for user', 'Create a account for this product')
  async register(@Body() payload: RegisterDto) {
    await this.authService.register(payload);
    return new ApiResponseDto(201, 'Register Successfully!');
  }

  @Public()
  @Post('login')
  @ApiBody({ type: LoginDto })
  @ApiOperationAuto('Login for user', 'Authenticate user and return JWT token')
  @UseGuards(LocalAuthGuard)
  async login(@Req() req: any) {
    const response = await this.authService.login(req.user.id as string);
    return new ApiResponseDto(200, 'Login Successfully!', response);
  }

  @Public()
  @Get('google/login')
  @ApiOperationAuto('Login with Google', 'Authenticate user with Google')
  @UseGuards(GoogleGuard)
  googleLogin() {}

  @Public()
  @Get('google/callback')
  @ApiOperationAuto(
    'Google callback',
    'Callback from Google, rendirect to client',
  )
  @UseGuards(GoogleGuard)
  async googleCallback(@Req() req: any, @Res() res: any) {
    const userId = req.user.id as string;
    const response = await this.authService.login(userId);
    const redirectUrl = `${this.configService.get('FRONTEND_DOMAIN')}?accessToken=${response.accessToken}&refreshToken=${response.refreshToken}`;
    res.redirect(redirectUrl);
  }

  @Public()
  @Post('refresh-token')
  @ApiOperationAuto('Refresh token', 'Refresh token')
  @ApiBody({ type: RefreshJwtDto })
  @UseGuards(RefreshTokenGuard)
  async refreshToken(@Req() req: any) {
    const userId = req.user.sub as string;
    const refreshToken = req.body.refreshToken as string;
    const response = await this.authService.refreshToken(userId, refreshToken);
    return new ApiResponseDto(200, 'Refresh token successfully!', response);
  }

  @UseGuards(JwtAuthGuard)
  @Put('change-password')
  @ApiOperationAuto('Change password', 'Change password for user')
  async changePassword(@Body() payload: ChangePasswordDto, @Req() req: any) {
    await this.authService.changePassword(req.user.sub as string, payload);
    return new ApiResponseDto(200, 'Change password successfully!');
  }

  @Public()
  @Post('create-otp')
  @ApiOperationAuto('Create OTP', 'Send OTP to user email')
  async createOtp(@Body() payload: CreateOtpDto) {
    await this.authService.createOtp(payload.email);
    return new ApiResponseDto(200, 'OTP sent successfully');
  }

  @Public()
  @Post('verify-otp')
  @ApiOperationAuto('Verify OTP', 'Verify OTP from user email')
  async verifyOtp(@Body() payload: VerifyOtpDto) {
    await this.authService.verifyOtp(payload.email, payload.otp);
    return new ApiResponseDto(200, 'OTP verified successfully');
  }

  @Public()
  @Post('forgot-password')
  @ApiOperationAuto('Forgot password', 'Reset password with OTP')
  async forgotPassword(@Body() payload: ForgotPasswordDto) {
    await this.authService.forgotPassword(
      payload.email,
      payload.otp,
      payload.password,
      payload.confirmPassword,
    );
    return new ApiResponseDto(200, 'Password reset successfully');
  }
}
