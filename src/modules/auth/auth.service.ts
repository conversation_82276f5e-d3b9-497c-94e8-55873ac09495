import { CreateUserDto } from '@modules/users/dto/create-user.dto';
import { UsersService } from '@modules/users/users.service';
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
} from '@nestjs/common';
import { AuthJwtPayload } from './types/auth-jwt.payload';
import { JwtService } from '@nestjs/jwt';
import refreshJwtConfig from './configs/refresh-jwt.config';
import { ConfigType } from '@nestjs/config';
import { RegisterDto } from './dto/register.dto';
import { compareString, generateOTP, hashString } from '@utils/auth';
import { MaillerService } from '@modules/mail/mail.service';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import { ChangePasswordDto } from './dto/change-password.dto';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { ConfigService } from '@nestjs/config';
import { Role } from '../../common/enum/role.enum';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UsersService,
    private readonly jwtService: JwtService,
    @Inject(refreshJwtConfig.KEY)
    private readonly refreshTokenConfig: ConfigType<typeof refreshJwtConfig>,
    private readonly maillerService: MaillerService,
    @InjectRedis()
    private readonly redis: Redis,
    private readonly configService: ConfigService,
  ) {}

  async validateGoogleUser(googleUser: CreateUserDto) {
    try {
      const user = await this.userService.findByEmail(googleUser.email);
      if (user) return user;
      const newUser = await this.userService.create(googleUser);
      this.maillerService.sendMailWelcome({
        email: googleUser.email,
        username: googleUser.lastName || '',
      });
      return newUser;
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async validateUser(email: string, password: string) {
    const user = await this.userService.findByEmail(email);
    if (!user) throw new ValidationException(ErrorCode.U003);
    const isPasswordValid = await compareString(password, user.password);
    if (!isPasswordValid) throw new ValidationException(ErrorCode.U002);
    return user;
  }

  async register(payload: RegisterDto) {
    try {
      const checkExist = await this.userService.findByEmail(payload.email);
      if (checkExist) throw new ValidationException(ErrorCode.U005);

      const hashPassword = await hashString(payload.password);
      payload.password = hashPassword;
      await this.userService.create(payload);
      this.maillerService.sendMailWelcome({
        email: payload.email,
        username: payload.lastName,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async login(userId: string) {
    const { accessToken, refreshToken } = await this.generateTokens(userId);
    await this.userService.updateRefreshToken(userId, refreshToken);
    return {
      accessToken,
      refreshToken,
    };
  }

  async generateTokens(userId: string) {
    try {
      const user = await this.userService.findUserEntityById(userId);
      const payload: AuthJwtPayload = {
        sub: userId,
        role: user.role || Role.USER,
      };
      const [accessToken, refreshToken] = await Promise.all([
        this.jwtService.signAsync(payload),
        this.jwtService.signAsync(payload, this.refreshTokenConfig),
      ]);

      return {
        accessToken,
        refreshToken,
      };
    } catch (error) {
      console.error('Error generating tokens:', error);
      throw error;
    }
  }

  async refreshToken(userId: string, refresh_token: string) {
    try {
      const user = await this.userService.findById(userId);

      const refreshTokenRedis = (await this.redis.get(
        `RT_${userId}`,
      )) as string;

      if (!refreshTokenRedis) {
        throw new ForbiddenException('Access Denied');
      }
      const refreshTokenMatches = await compareString(
        refresh_token,
        refreshTokenRedis,
      );
      if (!refreshTokenMatches) throw new ForbiddenException('Access Denied');

      // tao moi  at va rt, luu rt vao db
      const tokens = await this.generateTokens(user.id);
      if (!tokens.accessToken || !tokens.refreshToken) {
        throw new ForbiddenException('Access Denied');
      }

      await this.userService.updateRefreshToken(user.id, tokens.refreshToken);
      return tokens;
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }

  async changePassword(userId: string, payload: ChangePasswordDto) {
    try {
      if (payload.newPassword !== payload.confirmPassword) {
        throw new ValidationException(
          ErrorCode.U002,
          'New password and confirm password do not match',
        );
      }
      const user = await this.userService.findById(userId);
      if (!user) throw new ValidationException(ErrorCode.U003);

      const isPasswordValid = await compareString(
        payload.oldPassword,
        user.password,
      );
      if (!isPasswordValid) throw new ValidationException(ErrorCode.U002);
      const hashPassword = await hashString(payload.newPassword);
      await this.userService.updatePassword(userId, hashPassword);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async createOtp(email: string) {
    try {
      const user = await this.userService.findByEmail(email);
      if (!user) throw new ValidationException(ErrorCode.U003);
      const userId = user.id;

      const otp = generateOTP();

      const otpExpiryMinutes =
        this.configService.get<number>('OTP_EXPIRY_MINUTES') || 5;

      const ttlOtp = otpExpiryMinutes * 60;

      await this.redis.set(`OTP_${userId}`, otp, 'EX', ttlOtp);

      void this.maillerService.sendMailOtp(
        email,
        user.lastName || user.firstName || 'User',
        otp,
        otpExpiryMinutes,
      );
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async verifyOtp(
    email: string,
    otp: string,
    shouldDelete: boolean = false,
  ): Promise<string> {
    try {
      const user = await this.userService.findByEmail(email);
      if (!user) throw new ValidationException(ErrorCode.U003);

      const userId = user.id;
      const storedOtp = await this.redis.get(`OTP_${userId}`);
      if (!storedOtp) {
        throw new ValidationException(
          ErrorCode.U002,
          'OTP has expired or not found',
        );
      }

      if (storedOtp !== otp) {
        throw new ValidationException(ErrorCode.U006);
      }

      if (shouldDelete) {
        await this.redis.del(`OTP_${userId}`);
      }
      return userId;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async forgotPassword(
    email: string,
    otp: string,
    newPassword: string,
    confirmPassword: string,
  ) {
    try {
      const userId = await this.verifyOtp(email, otp, true);

      if (newPassword !== confirmPassword) {
        throw new ValidationException(ErrorCode.U007);
      }

      const hashPassword = await hashString(newPassword);
      await this.userService.updatePassword(userId, hashPassword);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
