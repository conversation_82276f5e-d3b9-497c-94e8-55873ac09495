import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Analyst, AnalystDocument } from './schemas/analyst.schema';
import { Model } from 'mongoose';
import {
  CreateAnalystByJdDto,
  CreateAnalystByRoleDescriptionDto,
} from './dto/create-analyst.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import * as pdfParse from 'pdf-parse';
import { ResumeRoleDescriptionAgent } from '@modules/ai/agents/resume-role.agent';
import {
  ResponseAnalystByJdDecriptionDto,
  ResponseAnalystByRoleDecriptionDto,
  ResponseAnalystDto,
} from './dto/response-analyst.dto';
import { plainToInstance } from 'class-transformer';
import { ResumeJdDescriptionAgent } from '@modules/ai/agents/resume-jd.agent';
import { ImageKitService } from '@modules/imagekit/imagekit.service';

@Injectable()
export class AnalystService {
  constructor(
    @InjectModel(Analyst.name)
    private readonly analystModel: Model<AnalystDocument>,
    private readonly resumeRoleDescriptionAgent: ResumeRoleDescriptionAgent,
    private readonly resumeJdDescriptionAgent: ResumeJdDescriptionAgent,
    private readonly imagekitService: ImageKitService,
  ) {}

  async createAnalystByRoleDescription(
    createAnalystDto: CreateAnalystByRoleDescriptionDto,
    cvFile: Express.Multer.File,
    userId: string,
  ): Promise<ResponseAnalystByRoleDecriptionDto> {
    try {
      if (!cvFile || cvFile.mimetype !== 'application/pdf') {
        throw new ValidationException(ErrorCode.F001);
      }
      const result = await pdfParse(cvFile.buffer);
      const cvText = result.text;

      const [responseAiService, cvFileUrl] = await Promise.all([
        this.resumeRoleDescriptionAgent.createResumeRoleDescription({
          ...createAnalystDto,
          cvText,
        }),
        this.imagekitService.uploadFile(cvFile, 'CV'),
      ]);

      const response = await this.analystModel.create({
        content: responseAiService,
        agentType: createAnalystDto.agentType,
        userId,
        cvFileUrl,
        cvText,
        roleDescription: createAnalystDto.roleDescription,
      });

      const dtoInstance = plainToInstance(
        ResponseAnalystByRoleDecriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async createAnalystByJdDescription(
    createAnalystJdDto: CreateAnalystByJdDto,
    cvFile: Express.Multer.File,
    jdFile: Express.Multer.File,
    userId: string,
  ): Promise<ResponseAnalystByJdDecriptionDto> {
    try {
      // 1. Parse PDF
      const [cvParse, jdParse] = await Promise.all([
        pdfParse(cvFile.buffer),
        pdfParse(jdFile.buffer),
      ]);
      const cvText = cvParse.text;
      const jdText = jdParse.text;

      // 2. Chạy AI và upload song song
      const [responseAiService, cvFileUrl, jdFileUrl] = await Promise.all([
        this.resumeJdDescriptionAgent.createResumeJdDescription({
          ...createAnalystJdDto,
          cvText,
          jdText,
        }),
        this.imagekitService.uploadFile(cvFile, 'CV'),
        this.imagekitService.uploadFile(jdFile, 'JD'),
      ]);

      // 3. Lưu vào DB
      const response = await this.analystModel.create({
        content: responseAiService,
        agentType: createAnalystJdDto.agentType,
        userId,
        cvFileUrl,
        jdFileUrl,
        cvText,
        jdText,
      });

      const dtoInstance = plainToInstance(
        ResponseAnalystByJdDecriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async getAnalystById(id: string): Promise<ResponseAnalystByJdDecriptionDto> {
    try {
      const response = await this.analystModel.findById(id);

      if (!response) {
        throw new ValidationException(ErrorCode.A001);
      }
      const dtoInstance = plainToInstance(
        ResponseAnalystByJdDecriptionDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getMyAnalyst(
    userId: string,
  ): Promise<ResponseAnalystByJdDecriptionDto[]> {
    try {
      if (!userId) {
        throw new ValidationException(ErrorCode.U003);
      }
      const response = await this.analystModel
        .find({ userId })
        .sort({ createdAt: -1 });

      const dtoInstance = plainToInstance(
        ResponseAnalystByJdDecriptionDto,
        response,
        { excludeExtraneousValues: true },
      );
      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAllAnalyst(): Promise<ResponseAnalystDto[]> {
    try {
      const response = await this.analystModel.find().sort({ createdAt: -1 });

      const dtoInstance = plainToInstance(ResponseAnalystDto, response, {
        excludeExtraneousValues: true,
      });

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async deleteAnalyst(id: string) {
    try {
      const analyst = await this.analystModel.findOne({
        _id: id,
      });

      if (!analyst) {
        throw new ValidationException(ErrorCode.A001);
      }

      if (analyst.cvFileUrl) {
        await this.imagekitService.deleteFileByUrl({
          url: analyst.cvFileUrl,
        });
      }

      if (analyst.jdFileUrl) {
        await this.imagekitService.deleteFileByUrl({
          url: analyst.jdFileUrl,
        });
      }

      await this.analystModel.findByIdAndDelete(id);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
