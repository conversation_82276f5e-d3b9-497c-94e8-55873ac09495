import { AiAgentAnalystType } from '@common/enum/agentType.enum';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Transform } from 'class-transformer';
import { Schema as MongooseSchema } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

@Schema({ timestamps: true })
export class Analyst {
  @Prop({ type: String, default: () => uuidv4(), required: true })
  @Transform(({ value }): string => value.toString())
  _id: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true, type: MongooseSchema.Types.Mixed })
  content: Record<string, any>;

  @Prop({ required: true, type: String, enum: AiAgentAnalystType })
  agentType: AiAgentAnalystType;

  @Prop({ required: false })
  cvFileUrl: string;

  @Prop({ required: false })
  jdFileUrl: string;

  @Prop({ required: false })
  jdText: string;

  @Prop({ required: false })
  cvText: string;

  @Prop({ required: false })
  roleDescription: string;
}

export type AnalystDocument = Analyst & Document;
export const AnalystSchema = SchemaFactory.createForClass(Analyst);
