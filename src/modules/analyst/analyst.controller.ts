import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AnalystService } from './analyst.service';
import {
  CreateAnalystByJdDto,
  CreateAnalystByRoleDescriptionDto,
} from './dto/create-analyst.dto';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import { ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from '@nestjs/platform-express';
import { ApiResponseDto } from '@common/dto/api-response.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';

@Controller({ path: 'analyst', version: '1' })
@ApiBearerAuth('Authorization')
export class AnalystController {
  constructor(private readonly analystService: AnalystService) {}

  @UseGuards(JwtAuthGuard)
  @Post('analyst-by-role-description')
  @UseInterceptors(FileInterceptor('cvFile'))
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto(
    'Generate analyst by role description',
    'Generate analyst by role description',
  )
  async createAnalystByRoleDescription(
    @Body() createAnalystRoleDto: CreateAnalystByRoleDescriptionDto,
    @UploadedFile() cvFile: Express.Multer.File,
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;

    const response = await this.analystService.createAnalystByRoleDescription(
      createAnalystRoleDto,
      cvFile,
      userId,
    );

    return new ApiResponseDto(201, 'Analyst created successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Post('analyst-by-jd-description')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'cvFile', maxCount: 1 },
      { name: 'jdFile', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto(
    'Generate analyst by jd description',
    'Generate analyst by jd description',
  )
  async createAnalystByJdDescription(
    @Body() createAnalystJdDto: CreateAnalystByJdDto,
    @UploadedFiles()
    files: {
      cvFile?: Express.Multer.File[];
      jdFile?: Express.Multer.File[];
    },
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;
    const cvFile = files.cvFile?.[0];
    const jdFile = files.jdFile?.[0];

    if (!cvFile || !jdFile) {
      throw new ValidationException(ErrorCode.F001);
    }

    const response = await this.analystService.createAnalystByJdDescription(
      createAnalystJdDto,
      cvFile,
      jdFile,
      userId,
    );

    return new ApiResponseDto(201, 'Analyst created successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Get('analyst/:id')
  @ApiOperationAuto('Get analyst by id', 'Get analyst by id')
  async getAnalystById(@Param('id') id: string) {
    const response = await this.analystService.getAnalystById(id);
    return new ApiResponseDto(200, 'Analyst fetched successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Get('my-analyst')
  @ApiOperationAuto('Get my analyst', 'Get my analyst')
  async getMyAnalyst(@Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.analystService.getMyAnalyst(userId);
    return new ApiResponseDto(200, 'Analyst fetched successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Get('all-analyst')
  @ApiOperationAuto('Get all analyst', 'Get all analyst')
  async getAllAnalyst() {
    const response = await this.analystService.getAllAnalyst();
    return new ApiResponseDto(200, 'Analyst fetched successfully', response);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiOperationAuto('Delete analyst by id', 'Delete analyst by id')
  async deleteAnalyst(@Param('id') id: string) {
    await this.analystService.deleteAnalyst(id);
    return new ApiResponseDto(200, 'Analyst deleted successfully');
  }
}
