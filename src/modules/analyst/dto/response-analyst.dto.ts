import { AiAgentAnalystType } from '@common/enum/agentType.enum';
import { Expose } from 'class-transformer';

export class ResponseAnalystByRoleDecriptionDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentAnalystType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  roleDescription: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}

export class ResponseAnalystByJdDecriptionDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentAnalystType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  jdFileUrl: string;

  @Expose()
  jdText: string;

  @Expose()
  jobPosition: string;

  @Expose()
  createdAt: Date;
}

export class ResponseAnalystDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  content: Record<string, any>;

  @Expose()
  agentType: AiAgentAnalystType;

  @Expose()
  cvFileUrl: string;

  @Expose()
  cvText: string;

  @Expose()
  jdFileUrl: string;

  @Expose()
  jdText: string;

  @Expose()
  jobPosition: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
