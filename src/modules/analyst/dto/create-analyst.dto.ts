import { AiAgentAnalystType } from '@common/enum/agentType.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class CreateAnalystByRoleDescriptionDto {
  @ApiProperty({ example: AiAgentAnalystType.AI_RESUME_ANALYSIS_ROLE })
  @IsEnum(AiAgentAnalystType)
  agentType: AiAgentAnalystType;

  @ApiProperty({
    example: 'Lập trình viên Front-end Developer Senior 2 năm kinh nghiệm',
  })
  @IsString()
  roleDescription: string;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload CV file (PDF)',
  })
  @IsOptional()
  cvFile?: any;
}

export class CreateAnalystByJdDto {
  @ApiProperty({ example: AiAgentAnalystType.AI_RESUME_ANALYSIS_JD })
  @IsEnum(AiAgentAnalystType)
  agentType: AiAgentAnalystType;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload CV file (PDF)',
  })
  @IsOptional()
  cvFile?: any;

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload JD file (PDF)',
  })
  @IsOptional()
  jdFile?: any;
}
