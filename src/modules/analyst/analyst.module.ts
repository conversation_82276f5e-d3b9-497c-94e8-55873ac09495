import { Modu<PERSON> } from '@nestjs/common';
import { AnalystService } from './analyst.service';
import { AnalystController } from './analyst.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Analyst, AnalystSchema } from './schemas/analyst.schema';
import { JwtModule } from '@nestjs/jwt';
import { AiModule } from '@modules/ai/ai.module';
import { ImagekitModule } from '@modules/imagekit/imagekit.module';
import { ParsePdfModule } from '@modules/parse-pdf/parse-pdf.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Analyst.name, schema: AnalystSchema }]),
    JwtModule,
    AiModule,
    ImagekitModule,
    ParsePdfModule,
  ],
  controllers: [AnalystController],
  providers: [AnalystService],
  exports: [AnalystService],
})
export class AnalystModule {}
