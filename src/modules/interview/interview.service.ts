import { Injectable } from '@nestjs/common';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { ValidationException } from '@exceptions/validation.exception';
import { ErrorCode } from '@constants/error-code.constant';
import * as pdfParse from 'pdf-parse';
import { InterviewAgent } from '@modules/ai/agents/interview.agent';
import { Interview } from './schemas/interview.schema';
import { Model } from 'mongoose';
import { InterviewDocument } from './schemas/interview.schema';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { ResponseCreateQuestionInterviewDto } from './dto/response-interview.dto';
import { ImageKitService } from '@modules/imagekit/imagekit.service';

@Injectable()
export class InterviewService {
  constructor(
    @InjectModel(Interview.name)
    private readonly interviewModel: Model<InterviewDocument>,
    private readonly interviewAgent: InterviewAgent,
    private readonly imagekitService: ImageKitService,
  ) {}

  async create(
    createInterviewDto: CreateInterviewDto,
    jdFile: Express.Multer.File,
    userId: string,
  ): Promise<ResponseCreateQuestionInterviewDto> {
    try {
      if (!jdFile || jdFile.mimetype !== 'application/pdf') {
        throw new ValidationException(ErrorCode.F001);
      }

      const result = await pdfParse(jdFile.buffer);
      const jdText = result.text;

      const [responseAiService, jdFileUrl] = await Promise.all([
        this.interviewAgent.createQuestionInterview({
          ...createInterviewDto,
          jdText,
        }),
        this.imagekitService.uploadFile(jdFile, 'JD'),
      ]);

      const response = await this.interviewModel.create({
        ...responseAiService,
        userId,
        jdFileUrl,
      });

      const dtoInstance = plainToInstance(
        ResponseCreateQuestionInterviewDto,
        response,
        { excludeExtraneousValues: true },
      );

      return dtoInstance;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getAllQuestionInterview(): Promise<
    ResponseCreateQuestionInterviewDto[]
  > {
    try {
      const response = await this.interviewModel
        .aggregate([
          {
            $lookup: {
              from: 'conversations',
              localField: '_id',
              foreignField: 'interviewId',
              as: 'conversations',
            },
          },
        ])
        .sort({ createdAt: -1 });

      return plainToInstance(ResponseCreateQuestionInterviewDto, response, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getMyQuestionInterview(
    userId: string,
  ): Promise<ResponseCreateQuestionInterviewDto[]> {
    try {
      const response = await this.interviewModel
        .aggregate([
          {
            $match: {
              userId,
            },
          },
          {
            $lookup: {
              from: 'conversations',
              localField: '_id',
              foreignField: 'interviewId',
              as: 'conversations',
            },
          },
        ])
        .sort({ createdAt: -1 });

      return plainToInstance(ResponseCreateQuestionInterviewDto, response, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async getQuestionInterviewById(
    interviewId: string,
  ): Promise<ResponseCreateQuestionInterviewDto> {
    try {
      const response = await this.interviewModel.aggregate([
        {
          $match: {
            _id: interviewId,
          },
        },
        {
          $lookup: {
            from: 'conversations',
            localField: '_id',
            foreignField: 'interviewId',
            as: 'conversations',
          },
        },
      ]);

      if (!response) {
        throw new ValidationException(ErrorCode.I001);
      }

      return plainToInstance(ResponseCreateQuestionInterviewDto, response[0], {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async deleteInterview(id: string) {
    try {
      const interview = await this.interviewModel.findOne({
        _id: id,
      });

      if (!interview) {
        throw new ValidationException(ErrorCode.A001);
      }

      if (interview.jdFileUrl) {
        await this.imagekitService.deleteFileByUrl({
          url: interview.jdFileUrl,
        });
      }

      await this.interviewModel.findByIdAndDelete(id);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
