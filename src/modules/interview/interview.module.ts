import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { InterviewService } from './interview.service';
import { Interview<PERSON><PERSON>roller } from './interview.controller';
import { JwtModule } from '@nestjs/jwt';
import { AiModule } from '@modules/ai/ai.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Interview, InterviewSchema } from './schemas/interview.schema';
import { ImagekitModule } from '@modules/imagekit/imagekit.module';

@Module({
  controllers: [InterviewController],
  providers: [InterviewService],
  imports: [
    JwtModule,
    AiModule,
    MongooseModule.forFeature([
      { name: Interview.name, schema: InterviewSchema },
    ]),
    ImagekitModule,
  ],
  exports: [InterviewService],
})
export class InterviewModule {}
