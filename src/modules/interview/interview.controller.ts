import {
  Controller,
  Post,
  Body,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Req,
  Get,
  Param,
  Delete,
} from '@nestjs/common';
import { InterviewService } from './interview.service';
import { CreateInterviewDto } from './dto/create-interview.dto';
import { ApiConsumes } from '@nestjs/swagger';
import { JwtAuthGuard } from '@guards/jwt-guard/jwt.guard';
import { ApiBearerAuth } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiOperationAuto } from '@decorators/swagger/api-operation.decorator';
import { ApiResponseDto } from '@common/dto/api-response.dto';

@Controller({ path: 'interview', version: '1' })
@ApiBearerAuth('Authorization')
export class InterviewController {
  constructor(private readonly interviewService: InterviewService) {}

  @UseGuards(JwtAuthGuard)
  @Post('generate-question-interview')
  @UseInterceptors(FileInterceptor('jdFile'))
  @ApiConsumes('multipart/form-data')
  @ApiOperationAuto(
    'Generate question interview',
    'Generate question interview',
  )
  async create(
    @Body() createInterviewDto: CreateInterviewDto,
    @UploadedFile() jdFile: Express.Multer.File,
    @Req() req: any,
  ) {
    const userId = req.user.sub as string;
    const response = await this.interviewService.create(
      createInterviewDto,
      jdFile,
      userId,
    );

    return new ApiResponseDto(
      201,
      'Generate question interview success',
      response,
    );
  }

  @Get('get-all-question-interview')
  @ApiOperationAuto('Get all question interview', 'Get all question interview')
  async getAllQuestionInterview() {
    const response = await this.interviewService.getAllQuestionInterview();

    return new ApiResponseDto(
      200,
      'Get all question interview success',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('get-my-question-interview')
  @ApiOperationAuto('Get my question interview', 'Get my question interview')
  async getMyQuestionInterview(@Req() req: any) {
    const userId = req.user.sub as string;
    const response = await this.interviewService.getMyQuestionInterview(userId);

    return new ApiResponseDto(
      200,
      'Get my question interview success',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('get-question-interview-by-id/:interviewId')
  @ApiOperationAuto(
    'Get question interview by id',
    'Get question interview by id',
  )
  async getQuestionInterviewById(@Param('interviewId') interviewId: string) {
    const response =
      await this.interviewService.getQuestionInterviewById(interviewId);

    return new ApiResponseDto(
      200,
      'Get question interview by id success',
      response,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  @ApiOperationAuto('Delete interview by id', 'Delete interview by id')
  async deleteInterview(@Param('id') id: string) {
    await this.interviewService.deleteInterview(id);
    return new ApiResponseDto(200, 'Interview deleted successfully');
  }
}
