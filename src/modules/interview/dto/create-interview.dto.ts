import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsArray, ArrayNotEmpty, IsOptional } from 'class-validator';

export class CreateInterviewDto {
  @ApiProperty({ example: 'Front-end Developer Senior' })
  @IsString()
  jobPosition: string;

  @ApiProperty({ example: '30min' })
  @IsString()
  interviewDuration: string;

  @ApiProperty({
    example: ['technical', 'leadership'],
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value) as string[];
      } catch {
        return value.split(',').map((item) => item.trim());
      }
    }
    return value as string[];
  })
  interviewType: string[];

  @ApiProperty({
    type: 'string',
    format: 'binary',
    required: false,
    description: 'Upload JD file (PDF)',
  })
  @IsOptional()
  jdFile?: any;
}
