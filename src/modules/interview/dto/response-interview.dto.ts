import { ResponseCreateConversationDto } from '@modules/conversation/dto/response-conversation.dto';
import { Expose } from 'class-transformer';

export class ResponseCreateQuestionInterviewDto {
  @Expose()
  _id: string;

  @Expose()
  userId: string;

  @Expose()
  jobPosition: string;

  @Expose()
  interviewDuration: string;

  @Expose()
  interviewType: string[];

  @Expose()
  jobDescriptions: string;

  @Expose()
  totalQuestion: number;

  @Expose()
  interviewQuestions: object[];

  @Expose()
  jdFileUrl: string;

  @Expose()
  conversations: ResponseCreateConversationDto[];

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt: Date;
}
