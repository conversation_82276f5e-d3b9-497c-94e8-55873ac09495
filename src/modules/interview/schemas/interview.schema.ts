import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Transform } from 'class-transformer';
import { v4 as uuidv4 } from 'uuid';

@Schema({ timestamps: true })
export class Interview {
  @Prop({ type: String, default: () => uuidv4(), required: true })
  @Transform(({ value }): string => value.toString())
  _id: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  jobPosition: string;

  @Prop({ required: true })
  interviewDuration: string;

  @Prop({ required: true, type: [String] })
  interviewType: string[];

  @Prop({ required: true })
  jdFileUrl: string;

  @Prop({ required: true })
  jobDescriptions: string;

  @Prop({ required: true })
  totalQuestion: number;

  @Prop({ required: true, type: [Object] })
  interviewQuestions: object[];
}

export type InterviewDocument = Interview & Document;
export const InterviewSchema = SchemaFactory.createForClass(Interview);
