import { Injectable } from '@nestjs/common';
import { v2 as cloudinary } from 'cloudinary';
import { CloudinaryResponse } from './dto/cloudinary-response';

@Injectable()
export class CloudinaryService {
  uploadFile(
    file: Express.Multer.File,
    folder: string,
  ): Promise<CloudinaryResponse> {
    return new Promise<CloudinaryResponse>((resolve, reject) => {
      // Tự động lấy extension
      const extension = file.originalname.substring(
        file.originalname.lastIndexOf('.'),
      );

      const fileName = file.originalname
        .replace(/\.[^/.]+$/, '')
        .replace(/\./g, '-')
        .replace(/[^a-zA-Z0-9-_]/g, '');

      // Không lặp folder
      const publicId = `${fileName}${extension}`;

      const uploadOptions = {
        resource_type: 'raw' as const,
        public_id: publicId,
        folder: folder,
        use_filename: false,
        unique_filename: false,
        access_mode: 'public',
        type: 'upload',
        moderation: 'manual',
        context: {
          purpose: 'document_storage',
          type: 'business_document',
        },
      };

      cloudinary.uploader
        .upload_stream(uploadOptions, (error, result) => {
          if (error) {
            reject(error as Error);
          } else {
            resolve(result as CloudinaryResponse);
          }
        })
        .end(file.buffer);
    });
  }

  deleteFile(publicId: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      void cloudinary.uploader.destroy(publicId, (error) => {
        if (error) return reject(error as Error);
        resolve();
      });
    });
  }
}
