import React, {
  ReactNode,
  useEffect,
  useState,
  createContext,
  useContext,
} from 'react';
import { tokenService } from '@/services/tokenService';
import { setAuthStateChangeCallback } from '@/services/apiBase';

interface AuthProviderProps {
  children: ReactNode;
}

interface AuthContextType {
  isAuthenticated: boolean | null;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  const checkAuth = async () => {
    const hasTokens = await tokenService.hasTokens();
    setIsAuthenticated(hasTokens);

    if (hasTokens) {
      console.log('User is authenticated');
    } else {
      console.log('User is not authenticated');
    }
  };

  useEffect(() => {
    checkAuth();

    // Register callback to handle authentication state changes from API service
    setAuthStateChangeCallback(() => {
      console.log('Authentication state changed from API service');
      checkAuth();
    });
  }, []);

  const value = {
    isAuthenticated,
    checkAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
