export enum ErrorCode {
  // Common Validation
  V000 = 'common.validation.error',
  V001 = 'common.validation.is_invalid_email',
  V002 = 'common.validation.is_invalid_password',
  V003 = 'common.validation.is_invalid_string',
  V004 = 'common.validation.is_not_empty',
  V005 = 'common.validation.is_invalid_date_format',
  V006 = 'common.validation.is_invalid_time_format',
  V007 = 'common.validation.is_invalid_phone_number',

  // Validation User || Auth
  U001 = 'user.validation.is_invalid_email',
  U002 = 'user.validation.is_invalid_password',
  U003 = 'user.validation.not_found',
  U004 = 'user.validation.is_invalid_email_or_password',
  U005 = 'user.validation.email_already_exists',
  U006 = 'user.validation.otp_is_invalid',
  U007 = 'user.validation.passwords_do_not_match',

  // Validation file
  F001 = 'file.validation.is_not_pdf',

  // Validation Interview
  I001 = 'interview.validation.not_found',

  // Validation Analyst
  A001 = 'analyst.validation.not_found',

  // Validation Chat AI
  C001 = 'chat_ai.validation.session_id_required',
  C002 = 'chat_ai.validation.session_not_found',

  // Validation Roadmap AI
  R001 = 'roadmap_ai.validation.not_found',

  // Validation Imagekit
  IK001 = 'imagekit.validation.not_found',

  // Validation Conversation
  CV001 = 'conversation.validation.not_found',

  // Validation Cover Letter
  CL001 = 'cover_letter.validation.not_found',
  CL002 = 'cover_letter.validation.unauthorized',
}
