import {
  View,
  ScrollView,
  Text,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import React, { useEffect, useRef } from 'react';
import { useRoute, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { Ionicons } from '@expo/vector-icons';
import { ArrowLeft } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { getConversationById } from '@/redux/thunks/conversationThunks';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  selectConversationLoading,
  selectCurrentConversation,
} from '@/redux/selectors/conversationSelector';
import FeedbackDetailSkeleton from './components/FeedbackDetailSkeleton';
import ChatSection from '../start-interview/components/ChatSection';
import { useThemeColors } from '@/hooks/useThemeColors';
import FeedbackSummary from './components/FeedBackSumary';
import { SafeAreaView } from 'react-native-safe-area-context';

const FeedbackDetailScreen = () => {
  const route = useRoute();
  const navigation =
    useNavigation<NativeStackNavigationProp<MockInterviewStackParamList>>();
  const { conversationId } = route.params as { conversationId: string };
  const messagesEndRef = useRef<ScrollView>(null);

  const dispatch = useAppDispatch();
  const conversation = useAppSelector(selectCurrentConversation);
  const loading = useAppSelector(selectConversationLoading);
  const colors = useThemeColors();

  // Debug log để kiểm tra theme colors
  console.log('FeedbackDetailScreen colors:', {
    background: colors.background,
    text: colors.text,
  });

  // Sử dụng theme colors
  const backgroundColor = colors.background;

  useEffect(() => {
    dispatch(getConversationById(conversationId));
  }, [dispatch, conversationId]);

  const handleTryAgain = () => {
    if (conversation?.interviewId) {
      navigation.navigate('StartInterview', {
        interviewId: conversation.interviewId,
      });
    }
  };

  if (loading || !conversation) {
    return <FeedbackDetailSkeleton />;
  }

  return (
    <SafeAreaView
      style={{
        backgroundColor: backgroundColor,
        flex: 1,
      }}
      edges={['top', 'left', 'right']}
    >
      {/* Header with Back Button */}
      <View
        className="px-4 py-3 shadow-md flex-row items-center"
        style={{ backgroundColor: backgroundColor }}
      >
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="mr-3 w-10 h-10 rounded-full items-center justify-center"
          style={{ backgroundColor: colors.backgroundSecondary }}
          activeOpacity={0.8}
          hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
        >
          <ArrowLeft size={20} color={colors.text} />
        </TouchableOpacity>

        <Text
          className="text-lg font-bold flex-1"
          style={{ color: colors.text }}
        >
          Feedback Details
        </Text>
      </View>

      <ScrollView
        style={{
          backgroundColor: backgroundColor,
          flex: 1,
        }}
        contentContainerStyle={{
          backgroundColor: backgroundColor,
          flexGrow: 1,
        }}
      >
        <View
          className="h-[45vh] p-4 w-full"
          style={{ backgroundColor: backgroundColor }}
        >
          <ChatSection
            messages={conversation.conversationLog.map((log) => ({
              sender: log.role === 'user' ? 'you' : 'assistant',
              content: log.content,
              isComplete: true,
            }))}
            messagesEndRef={messagesEndRef}
            isInterviewing={false}
          />
        </View>
        <View
          className="flex-1 p-4"
          style={{ backgroundColor: backgroundColor }}
        >
          <View className="mt-4">
            <FeedbackSummary feedback={conversation.feedback} />
          </View>

          {/* Try Again Button */}
          <View className="mt-6 px-4">
            <TouchableOpacity activeOpacity={0.8} onPress={handleTryAgain}>
              <LinearGradient
                colors={['#6366F1', '#4F46E5']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={{
                  paddingVertical: 16,
                  paddingHorizontal: 24,
                  borderRadius: 16,
                  shadowColor: '#6366F1',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 8,
                }}
              >
                <View className="flex-row items-center justify-center">
                  <Ionicons name="refresh-outline" size={20} color="white" />
                  <Text className="text-base font-semibold text-white ml-2">
                    Try Again
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default FeedbackDetailScreen;
