import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { ConversationResponse } from '@/services/types/conversation';
import { useAppDispatch } from '@/redux/hooks';
import { deleteConversation } from '@/redux/thunks/conversationThunks';
import { showAuthToast } from '@/utils/toastUtils';

interface UseFeedbackInterviewProps {
  navigation: NativeStackNavigationProp<MockInterviewStackParamList>;
}

export const useFeedbackInterview = ({
  navigation,
}: UseFeedbackInterviewProps) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [deletingItemId, setDeletingItemId] = useState<string | null>(null);
  const dispatch = useAppDispatch();

  const handleItemPress = useCallback(
    (item: ConversationResponse) => {
      navigation.navigate('FeedbackDetail', { conversationId: item._id });
    },
    [navigation],
  );

  const handleDelete = useCallback(async (item: ConversationResponse) => {
    Alert.alert(
      'Delete Feedback',
      `Are you sure you want to delete the feedback for "${item._id}"?\n\nThis action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setDeletingItemId(item._id);
            try {
              await dispatch(deleteConversation(item._id)).unwrap();
              showAuthToast.success('Delete feedback successfully executed');
            } catch (error) {
              console.error('Error deleting feedback:', error);
              Alert.alert(
                'Error',
                'Cannot delete feedback. Please try again later.',
              );
            } finally {
              setDeletingItemId(null);
            }
          },
        },
      ],
    );
  }, []);

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  return {
    handleItemPress,
    handleDelete,
    currentPage,
    setCurrentPage,
    searchQuery,
    setSearchQuery,
    handlePageChange,
    deletingItemId,
  };
};
