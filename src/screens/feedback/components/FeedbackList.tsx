import React, { useMemo, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Pagination from '@/components/ui/Pagination';
import { MessageSquareText } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SwipeableItem, SearchBar } from '@/components/ui';
import { ConversationResponse } from '@/services/types/conversation';
import { useFeedbackInterview } from '../hooks/useFeedbackInterview';
import FeedbackInterviewItem from './FeedbackItem';
import { useThemeColors } from '@/hooks/useThemeColors';

interface FeedbackInterviewListProps {
  feedbackInterviews: ConversationResponse[];
  loading: boolean;
  itemsPerPage?: number;
  headerComponent?: React.ReactElement;
}

const FeedbackInterviewList: React.FC<FeedbackInterviewListProps> = ({
  feedbackInterviews,
  loading,
  itemsPerPage = 5,
  headerComponent,
}) => {
  const colors = useThemeColors();
  const navigation =
    useNavigation<NativeStackNavigationProp<MockInterviewStackParamList>>();
  const {
    handleItemPress,
    handleDelete,
    currentPage,
    setCurrentPage,
    searchQuery,
    setSearchQuery,
    handlePageChange,
    deletingItemId,
  } = useFeedbackInterview({
    navigation,
  });

  // Filter feedback interviews based on search query
  const filteredFeedbackInterviews = useMemo(() => {
    if (!searchQuery.trim()) {
      return feedbackInterviews;
    }

    const query = searchQuery.toLowerCase().trim();
    return feedbackInterviews.filter((item) => {
      return (
        item.feedback.summary.recommendationLevel
          .toLowerCase()
          .includes(query) ||
        item.feedback.summary.recommendationMsg.toLowerCase().includes(query)
      );
    });
  }, [feedbackInterviews, searchQuery]);

  // Calculate pagination for filtered data
  const totalPages = Math.ceil(
    filteredFeedbackInterviews.length / itemsPerPage,
  );
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredFeedbackInterviews.slice(startIndex, endIndex);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const renderItem = ({ item }: { item: ConversationResponse }) => {
    const isDeleting = deletingItemId === item._id;

    return (
      <SwipeableItem
        onDelete={() => handleDelete(item)}
        isDeleting={isDeleting}
        itemId={item._id}
        deleteTitle="Delete feedback interview"
        deleteMessage={`Are you sure you want to delete the feedback interview "${item._id}"?\n\nThis action cannot be undone and will permanently delete all related data.`}
      >
        <FeedbackInterviewItem
          item={item}
          onPress={handleItemPress}
          onDelete={() => {}}
          isLoading={false}
        />
      </SwipeableItem>
    );
  };

  const renderHeader = () => {
    return (
      <>
        {headerComponent}
        {/* List Header */}
        <View
          className="px-6 py-4 border-b"
          style={{
            backgroundColor: colors.surface,
            borderBottomColor: colors.border,
          }}
        >
          <View className="flex-row justify-between items-center">
            <View>
              <Text
                className="text-2xl font-bold"
                style={{ color: colors.text }}
              >
                Feedback Interview
              </Text>
              <Text
                className="text-sm mt-1"
                style={{ color: colors.textSecondary }}
              >
                {filteredFeedbackInterviews.length} feedback
                {searchQuery && ` (from ${feedbackInterviews.length} total)`} •
                Page {currentPage}/{totalPages}
              </Text>
            </View>
            <View className="flex-row items-center gap-3">
              <View
                className="px-3 py-1.5 rounded-full"
                style={{ backgroundColor: colors.primaryLight }}
              >
                <Text
                  className="font-semibold text-sm"
                  style={{ color: colors.text }}
                >
                  {currentItems.length}/{filteredFeedbackInterviews.length}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Search Bar */}
        <View className="px-6 py-4" style={{ backgroundColor: colors.surface }}>
          <SearchBar
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            placeholder="Search by location, rating, time..."
            resultCount={
              searchQuery.length > 0
                ? filteredFeedbackInterviews.length
                : undefined
            }
            resultLabel="feedback"
          />
        </View>
      </>
    );
  };

  const renderEmptyState = () => (
    <View className="flex-1 justify-center items-center px-6 py-12">
      <View
        className="rounded-2xl p-8 items-center border w-full max-w-sm"
        style={{
          backgroundColor: colors.surface,
          borderColor: colors.border,
        }}
      >
        <View
          className="w-16 h-16 rounded-full justify-center items-center mb-4"
          style={{ backgroundColor: colors.primaryLight }}
        >
          <MessageSquareText size={32} color={colors.primary} />
        </View>
        <Text className="font-bold text-xl mb-2" style={{ color: colors.text }}>
          {searchQuery ? 'No results found' : 'No feedback interviews yet'}
        </Text>
        <Text
          className="text-center mb-6 leading-6"
          style={{ color: colors.textSecondary }}
        >
          {searchQuery
            ? 'Adjust search keywords or remove filters to view all feedback'
            : 'Complete your mock interviews to receive detailed feedback on your performance'}
        </Text>
        <TouchableOpacity
          onPress={searchQuery ? () => setSearchQuery('') : undefined}
          className="rounded-xl py-3 px-6 flex-row items-center"
          style={{
            backgroundColor: colors.primary,
            shadowColor: colors.primary,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.2,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <MessageSquareText size={20} color="white" />
          <Text className="text-white font-semibold ml-2">
            {searchQuery ? 'Remove search' : 'Join mock interview'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderFooter = () => {
    if (totalPages <= 1) return null;

    return (
      <View
        className="border-t"
        style={{
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
        }}
      >
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          disabled={loading || deletingItemId !== null}
        />
      </View>
    );
  };

  if (filteredFeedbackInterviews.length === 0) {
    return (
      <ScrollView
        className="flex-1"
        style={{ backgroundColor: colors.backgroundSecondary }}
      >
        {renderHeader()}
        {renderEmptyState()}
      </ScrollView>
    );
  }

  return (
    <FlatList
      data={currentItems}
      renderItem={renderItem}
      keyExtractor={(item) => item._id}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={renderHeader}
      ListFooterComponent={renderFooter}
      contentContainerStyle={{
        backgroundColor: colors.backgroundSecondary,
        paddingTop: 0,
        paddingBottom: 16,
        flexGrow: 1,
      }}
      style={{
        backgroundColor: colors.backgroundSecondary,
        flex: 1,
      }}
    />
  );
};

export default FeedbackInterviewList;
