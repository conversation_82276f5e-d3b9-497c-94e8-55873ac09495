import { View, Text } from 'react-native';
import React from 'react';
import { Feedback } from '@/services/types/conversation';
import { useThemeColors } from '@/hooks/useThemeColors';

const SkillCard = ({ title, score }: { title: string; score: number }) => {
  const colors = useThemeColors();
  return (
    <View className="rounded-xl p-4 mb-3" style={{ backgroundColor: colors.card }}>
      <View className="flex-row justify-between items-center">
        <Text className="text-base font-medium" style={{ color: colors.text }}>{title}</Text>
        <View className="bg-pink-400 px-3 py-1 rounded-full">
          <Text className="text-white">{score}/10</Text>
        </View>
      </View>
    </View>
  );
};

interface Props {
  feedback: Feedback;
}

const FeedbackSummary = ({ feedback }: Props) => {
  const { summary, perQuestionFeedback } = feedback;
  const colors = useThemeColors();

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      {/* Recommendation Section */}
      <View className="mb-6">
        <Text className="text-xl font-bold mb-3" style={{ color: colors.text }}>Recommendation</Text>
        <View className="p-4 rounded-xl" style={{ backgroundColor: colors.card }}>
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-base font-medium" style={{ color: colors.text }}>Assessment Level:</Text>
            <View className="bg-emerald-500 px-4 py-1 rounded-full">
              <Text className="text-white">{summary.recommendationLevel}</Text>
            </View>
          </View>
          <Text style={{ color: colors.textSecondary }}>{summary.recommendationMsg}</Text>
        </View>
      </View>

      {/* Overall Feedback */}
      <View className="mb-6">
        <Text className="text-xl font-bold mb-3" style={{ color: colors.text }}>Overall Feedback</Text>
        <View className="p-4 rounded-xl" style={{ backgroundColor: colors.card }}>
          <Text style={{ color: colors.textSecondary }}>{summary.overallFeedback}</Text>
        </View>
      </View>

      {/* Skills Assessment */}
      <View className="mb-6">
        <Text className="text-xl font-bold mb-3" style={{ color: colors.text }}>Skills Assessment</Text>
        <SkillCard
          title="Technical Skills"
          score={summary.skillsRating.technicalSkills}
        />
        <SkillCard
          title="Communication"
          score={summary.skillsRating.communication}
        />
        <SkillCard
          title="Problem Solving"
          score={summary.skillsRating.problemSolving}
        />
        <SkillCard title="Experience" score={summary.skillsRating.experience} />
      </View>

      {/* Question Analysis */}
      <View>
        <Text className="text-xl font-bold mb-3" style={{ color: colors.text }}>Question Analysis</Text>
        {perQuestionFeedback.map((item, index) => (
          <View key={index} className="rounded-xl p-4 mb-3" style={{ backgroundColor: colors.card }}>
            <View className="flex-row justify-between items-center mb-2">
              <Text className="text-base font-medium" style={{ color: colors.text }}>
                Question {index + 1}
              </Text>
              <View className="bg-pink-400 px-3 py-1 rounded-full">
                <Text className="text-white">{item.rating}/10</Text>
              </View>
            </View>
            <Text className="mb-3" style={{ color: colors.textSecondary }}>{item.question}</Text>

            <View className="p-3 rounded-lg mb-2" style={{ backgroundColor: colors.surfaceSecondary }}>
              <Text className="mb-1" style={{ color: colors.textSecondary }}>Your Answer:</Text>
              <Text style={{ color: colors.text }}>{item.userAnswer}</Text>
            </View>

            <View className="p-3 rounded-lg" style={{ backgroundColor: colors.surfaceSecondary }}>
              <Text className="mb-1" style={{ color: colors.textSecondary }}>AI Feedback:</Text>
              <Text style={{ color: colors.text }}>{item.feedback}</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default FeedbackSummary;
