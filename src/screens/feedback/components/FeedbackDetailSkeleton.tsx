import React from 'react';
import { View, TouchableOpacity, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Skeleton } from '@/components/ui/Skeleton';
import { useThemeColors } from '@/hooks/useThemeColors';
import { ArrowLeft } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

const FeedbackDetailSkeleton = () => {
  const colors = useThemeColors();
  const navigation = useNavigation();

  return (
    <SafeAreaView className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar barStyle={colors.statusBarStyle} />

      {/* Header with Back Button Skeleton */}
      <View
        className="px-4 py-3 shadow-md flex-row items-center"
        style={{ backgroundColor: colors.background }}
      >
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="mr-3 w-10 h-10 rounded-full items-center justify-center"
          style={{ backgroundColor: colors.backgroundSecondary }}
          activeOpacity={0.8}
          hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
        >
          <ArrowLeft size={20} color={colors.text} />
        </TouchableOpacity>

        <Skeleton style={{ height: 20, width: 150 }} />
      </View>

      <View className="flex-1 p-4" style={{ backgroundColor: colors.background }}>
        {/* Chat Section Skeleton */}
        <View style={{
          borderRadius: 24,
          padding: 16,
          marginBottom: 16,
          backgroundColor: colors.card,
        }}>
          <Skeleton style={{ height: 24, width: '75%', marginBottom: 16 }} />
          <View style={{ gap: 16 }}>
            {[1, 2, 3].map((i) => (
              <View
                key={i}
                style={{
                  flex: 1,
                  alignItems: i % 2 === 0 ? 'flex-end' : 'flex-start',
                }}
              >
                <Skeleton style={{ height: 48, width: '80%', borderRadius: 12 }} />
              </View>
            ))}
          </View>
        </View>

        {/* Recommendation Skeleton */}
        <View style={{ marginBottom: 24 }}>
          <Skeleton style={{ height: 32, width: '50%', marginBottom: 12 }} />
          <Skeleton style={{ height: 96, width: '100%', borderRadius: 12 }} />
        </View>

        {/* Skills Assessment Skeleton */}
        <View style={{ marginBottom: 24 }}>
          <Skeleton style={{ height: 32, width: '50%', marginBottom: 12 }} />
          {[1, 2, 3, 4].map((i) => (
            <Skeleton
              key={i}
              style={{
                height: 64,
                width: '100%',
                borderRadius: 12,
                marginBottom: 12
              }}
            />
          ))}
        </View>

        {/* Question Analysis Skeleton */}
        <View>
          <Skeleton style={{ height: 32, width: '50%', marginBottom: 12 }} />
          {[1, 2].map((i) => (
            <Skeleton
              key={i}
              style={{
                height: 160,
                width: '100%',
                borderRadius: 12,
                marginBottom: 12
              }}
            />
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

export default FeedbackDetailSkeleton;
