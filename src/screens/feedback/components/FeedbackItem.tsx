import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Clock, MessageSquare, Star, TrendingUp } from 'lucide-react-native';
import { ConversationResponse } from '@/services/types/conversation';
import { useThemeColors } from '@/hooks/useThemeColors';
import { formatDateTime, formatTimeAgo } from '@/utils/timer';

interface FeedbackInterviewItemProps {
  item: ConversationResponse;
  onPress: (item: ConversationResponse) => void;
  onDelete: () => void;
  isLoading: boolean;
}

const FeedbackInterviewItem: React.FC<FeedbackInterviewItemProps> = ({
  item,
  onPress,
  isLoading,
}) => {
  const colors = useThemeColors();

  const getRecommendationColor = (level: string) => {
    switch (level) {
      case 'Excellent':
        return {
          bg: 'bg-green-100',
          text: 'text-green-600',
          border: 'border-green-200',
        };
      case 'Good':
        return {
          bg: 'bg-blue-100',
          text: 'text-blue-600',
          border: 'border-blue-200',
        };
      case 'Average':
        return {
          bg: 'bg-yellow-100',
          text: 'text-yellow-600',
          border: 'border-yellow-200',
        };
      case 'Poor':
        return {
          bg: 'bg-orange-100',
          text: 'text-orange-600',
          border: 'border-orange-200',
        };
      case 'Unsuitable':
        return {
          bg: 'bg-red-100',
          text: 'text-red-600',
          border: 'border-red-200',
        };
      default:
        return {
          bg: 'bg-gray-100',
          text: 'text-gray-600',
          border: 'border-gray-200',
        };
    }
  };

  const recommendationStyle = getRecommendationColor(
    item.feedback.summary.recommendationLevel,
  );

  // Tính điểm trung bình từ skills rating
  const averageRating = Math.round(
    (item.feedback.summary.skillsRating.technicalSkills +
      item.feedback.summary.skillsRating.communication +
      item.feedback.summary.skillsRating.problemSolving +
      item.feedback.summary.skillsRating.experience) /
      4,
  );

  return (
    <TouchableOpacity
      onPress={() => onPress(item)}
      disabled={isLoading}
      className="mx-6 mb-4 rounded-2xl border"
      style={{
        backgroundColor: colors.surface,
        borderColor: colors.border,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 2,
      }}
    >
      <View className="p-5">
        {/* Header */}
        <View className="flex-row justify-between items-start mb-4">
          <View className="flex-1 mr-4">
            <View className="flex-row items-center mb-2">
              <MessageSquare size={16} color={colors.primary} />
              <Text
                className="text-sm ml-2 font-medium"
                style={{ color: colors.textSecondary }}
              >
                Feedback Interview
              </Text>
            </View>
            <Text
              className="text-lg font-bold leading-6"
              style={{ color: colors.text }}
            >
              Feedback tạo lúc: {formatDateTime(item?.createdAt)}
            </Text>
          </View>

          <View
            className={`px-3 py-1.5 rounded-full border ${recommendationStyle.bg} ${recommendationStyle.border}`}
          >
            <Text
              className={`text-sm font-semibold ${recommendationStyle.text}`}
            >
              {item.feedback.summary.recommendationLevel}
            </Text>
          </View>
        </View>

        {/* Stats Row */}
        <View className="flex-row justify-between items-center mb-4">
          <View className="flex-row items-center">
            <Clock size={16} color={colors.textSecondary} />
            <Text
              className="text-sm ml-1.5"
              style={{ color: colors.textSecondary }}
            >
              {item.interviewDuration}
            </Text>
          </View>

          <View className="flex-row items-center">
            <Star size={16} color="#F59E0B" />
            <Text
              className="font-semibold text-sm ml-1.5"
              style={{ color: colors.text }}
            >
              {averageRating}/10
            </Text>
          </View>

          <View className="flex-row items-center">
            <TrendingUp size={16} color={colors.textSecondary} />
            <Text
              className="text-sm ml-1.5"
              style={{ color: colors.textSecondary }}
            >
              {item.feedback.perQuestionFeedback.length} questions
            </Text>
          </View>
        </View>

        {/* Skills Preview */}
        <View className="mb-4">
          <Text
            className="font-medium text-sm mb-2"
            style={{ color: colors.text }}
          >
            Skills Rating:
          </Text>
          <View className="flex-row justify-between">
            <View className="items-center">
              <Text
                className="text-xs mb-1"
                style={{ color: colors.textSecondary }}
              >
                Technical
              </Text>
              <Text
                className="text-sm font-bold"
                style={{ color: colors.text }}
              >
                {item.feedback.summary.skillsRating.technicalSkills}/10
              </Text>
            </View>
            <View className="items-center">
              <Text
                className="text-xs mb-1"
                style={{ color: colors.textSecondary }}
              >
                Communication
              </Text>
              <Text
                className="text-sm font-bold"
                style={{ color: colors.text }}
              >
                {item.feedback.summary.skillsRating.communication}/10
              </Text>
            </View>
            <View className="items-center">
              <Text
                className="text-xs mb-1"
                style={{ color: colors.textSecondary }}
              >
                Problem Solving
              </Text>
              <Text
                className="text-sm font-bold"
                style={{ color: colors.text }}
              >
                {item.feedback.summary.skillsRating.problemSolving}/10
              </Text>
            </View>
            <View className="items-center">
              <Text
                className="text-xs mb-1"
                style={{ color: colors.textSecondary }}
              >
                Experience
              </Text>
              <Text
                className="text-sm font-bold"
                style={{ color: colors.text }}
              >
                {item.feedback.summary.skillsRating.experience}/10
              </Text>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View
          className="flex-row justify-between items-center pt-3 border-t"
          style={{ borderTopColor: colors.border }}
        >
          <Text className="text-sm" style={{ color: colors.textSecondary }}>
            {formatTimeAgo(item.createdAt)}
          </Text>
          <View className="flex-row items-center">
            <Text
              className="text-sm font-medium mr-2"
              style={{ color: colors.primary }}
            >
              View details
            </Text>
            <Ionicons name="chevron-forward" size={16} color={colors.primary} />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default FeedbackInterviewItem;
