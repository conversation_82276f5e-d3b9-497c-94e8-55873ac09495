import { View, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { P, H4 } from '../../../components/ui/Typography';
import { HistoryCardProps } from '@/constants/history.constants';
import { useNavigation } from '@react-navigation/native';
import { useThemeColors } from '@/hooks/useThemeColors';

const HistoryCard = ({ item, onPress }: HistoryCardProps) => {
  const navigation = useNavigation();
  const colors = useThemeColors();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      navigation.navigate('HomeScreen' as never);
    }
  };

  return (
    <Pressable
      onPress={handlePress}
      style={{
        backgroundColor: colors.surface,
        borderRadius: 16,
        padding: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.border,
      }}
    >
      <View style={{ flex: 1 }}>
        <H4 style={{
          fontSize: 20,
          fontWeight: '600',
          color: colors.text,
          marginBottom: 4
        }}>
          {item?.role}
        </H4>
        <P style={{
          fontSize: 16,
          color: colors.textSecondary,
          marginBottom: 8
        }}>
          {item?.company}
        </P>
        <P style={{
          color: colors.textMuted
        }}>
          {item?.date}
        </P>
      </View>

      {/* Center: Rating */}
      <View>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 12,
        }}>
          <Ionicons name="star" size={16} color="#F59E0B" />
          <P style={{
            marginLeft: 4,
            color: colors.text
          }}>
            {item?.rating}
          </P>
        </View>

        <View style={{
          flexDirection: 'row',
          justifyContent: 'flex-end',
        }}>
          <Ionicons name="chevron-forward" size={20} color={colors.textMuted} />
        </View>
      </View>
    </Pressable>
  );
};

export default HistoryCard;
