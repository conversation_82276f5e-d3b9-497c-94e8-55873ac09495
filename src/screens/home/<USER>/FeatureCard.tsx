import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Feature } from '@/constants/features.constants';
import { useThemeColors } from '@/hooks/useThemeColors';

interface FeatureCardProps {
  feature: Feature;
  onPress?: () => void;
}

const FeatureCard = ({ feature, onPress }: FeatureCardProps) => {
  const themeColors = useThemeColors();

  // Convert Tailwind class to hex color
  const getIconBgColor = (bgClass: string) => {
    switch (bgClass) {
      case 'bg-purple-500': return '#8B5CF6';
      case 'bg-green-500': return '#10B981';
      case 'bg-orange-500': return '#F97316';
      case 'bg-blue-500': return '#3B82F6';
      default: return themeColors.primary;
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        width: '48%',
        borderRadius: 16,
        padding: 16,
        marginBottom: 16,
        backgroundColor: themeColors.surface,
        borderWidth: 1,
        borderColor: themeColors.border,
        shadowColor: themeColors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
        overflow: 'hidden',
      }}
    >
      <View
        style={{
          width: 48,
          height: 48,
          borderRadius: 24,
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 12,
          backgroundColor: getIconBgColor(feature.iconBgColor),
          overflow: 'hidden',
        }}
      >
        <Ionicons name={feature.icon} size={24} color="#fff" />
      </View>
      <Text style={{
        fontSize: 18,
        fontWeight: '500',
        marginBottom: 4,
        color: themeColors.text,
      }}>
        {feature.title}
      </Text>
      <Text style={{
        fontSize: 14,
        color: themeColors.textSecondary,
      }}>
        {feature.description}
      </Text>
    </TouchableOpacity>
  );
};

export default FeatureCard;
