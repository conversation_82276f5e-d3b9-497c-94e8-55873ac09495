import { View, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FlatList } from 'react-native-gesture-handler';
import { FEATURES } from '@/constants/features.constants';
import { HISTORY_ITEMS } from '@/constants/history.constants';
import { WelcomeHeader, H2 } from '@/components/ui';
import { FeatureCard, InterviewCard, HistoryCard } from './components';
import { useNavigation } from '@react-navigation/native';
import { HomeStackParamList } from '@/common/types/rootParamList';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useThemeColors } from '@/hooks/useThemeColors';

const HomeScreen = () => {
  const navigate =
    useNavigation<NativeStackNavigationProp<HomeStackParamList>>();
  const themeColors = useThemeColors();

  const onPressFeature = (navigateTo: string) => {
    navigate.navigate(navigateTo as never);
  };

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: themeColors.background }}
      edges={['top', 'left', 'right']}
    >
      <ScrollView
        style={{ flex: 1, backgroundColor: themeColors.background }}
        contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 16 }}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
      >
        <WelcomeHeader />

        <InterviewCard />

        <View className="mt-6">
          <H2
            style={{
              color: themeColors.text,
              marginBottom: 16,
            }}
          >
            Tính năng chính
          </H2>
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'space-between',
            }}
          >
            {FEATURES.map((feature) => (
              <FeatureCard
                key={feature.id}
                feature={feature}
                onPress={() => onPressFeature(feature.navigate)}
              />
            ))}
          </View>
        </View>

        <View className="mt-6">
          <H2
            style={{
              color: themeColors.text,
              marginBottom: 16,
            }}
          >
            Lịch sử phỏng vấn
          </H2>
          <FlatList
            data={HISTORY_ITEMS}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <View style={{ marginBottom: 16 }}>
                <HistoryCard
                  item={item}
                  onPress={() => {
                    // navigation.navigate('HistoryCardDetail' as never)
                  }}
                />
              </View>
            )}
            ListEmptyComponent={
              <H2
                style={{
                  textAlign: 'center',
                  color: themeColors.textMuted,
                }}
              >
                Chưa có lịch sử phỏng vấn
              </H2>
            }
            scrollEnabled={false}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
