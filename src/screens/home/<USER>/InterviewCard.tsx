import { LinearGradient } from 'expo-linear-gradient';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '@/common/types/rootParamList';
const InterviewCard = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const handleStartInterview = () => {
    navigation.navigate('MockInterview');
  };

  return (
    <View style={{
      borderRadius: 24,
      overflow: 'hidden',
      width: '100%',
      marginBottom: 24,
    }}>
      <LinearGradient
        colors={['#6366F1', '#4F46E5']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ padding: 30, alignItems: 'center' }}
      >
        <View style={{
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          padding: 16,
          borderRadius: 50,
          marginBottom: 16,
        }}>
          <Ionicons name="mic-outline" size={32} color="white" />
        </View>

        <Text style={{
          color: 'white',
          fontSize: 24,
          fontWeight: '600',
          marginBottom: 12,
          textAlign: 'center',
        }}>
          Bắt đầu phỏng vấn ngay
        </Text>

        <Text style={{
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: 16,
          textAlign: 'center',
          marginBottom: 20,
        }}>
          Thực hành với AI để cải thiện kỹ năng
        </Text>

        <TouchableOpacity
          activeOpacity={0.7}
          style={{
            backgroundColor: 'white',
            paddingHorizontal: 20,
            paddingVertical: 16,
            borderRadius: 50,
            flexDirection: 'row',
            alignItems: 'center',
          }}
          onPress={handleStartInterview}
        >
          <Text style={{
            color: '#2563eb',
            fontWeight: '500',
            marginRight: 4,
          }}>
            Bắt đầu
          </Text>
          <Ionicons name="chevron-forward" size={18} color="#2563eb" />
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );
};

export default InterviewCard;
