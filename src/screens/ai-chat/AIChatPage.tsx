import { KeyboardAvoidingView, Platform } from 'react-native';
import { StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ChatHeader, MessageList, MessageInput } from './components';
import { useAIChat } from './hooks/useAIChat';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function AIChatPage() {
  const {
    scrollViewRef,
    inputMessage,
    setInputMessage,
    currentSession,
    handleSend,
  } = useAIChat();
  const colors = useThemeColors();

  return (
    <SafeAreaView
      className="flex-1"
      edges={[]}
      style={{ backgroundColor: colors.background }}
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent={true}
      />
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <ChatHeader />

        {/* Message List */}
        <MessageList ref={scrollViewRef} currentSession={currentSession} />

        {/* Input Area */}
        <MessageInput
          inputMessage={inputMessage}
          setInputMessage={setInputMessage}
          onSend={handleSend}
          currentSession={currentSession}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
