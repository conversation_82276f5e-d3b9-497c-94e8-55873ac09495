import { useState, useRef, useEffect } from 'react';
import { ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch } from '@/redux/store';
import { fetchChatSessions, sendMessage } from '@/thunks/aiChatThunks';
import {
  selectCurrentChatSession,
  selectAiChatSessions,
} from '@/selectors/aiChatSelector';
import { setCurrentSession, setMessageChatSession } from '@/slices/aiChatSlice';
import * as Crypto from 'expo-crypto';

export function useAIChat() {
  const dispatch = useDispatch<AppDispatch>();
  const scrollViewRef = useRef<ScrollView>(null);
  const [inputMessage, setInputMessage] = useState('');
  const currentSession = useSelector(selectCurrentChatSession);
  const mySessions = useSelector(selectAiChatSessions);

  useEffect(() => {
    dispatch(fetchChatSessions());
  }, [dispatch]);

  useEffect(() => {
    // Only create new session if no current session exists
    if (!currentSession) {
      const newSessionId = Crypto.randomUUID();
      dispatch(
        setCurrentSession({
          _id: newSessionId,
          sessionId: newSessionId,
          content: [],
        }),
      );
    }
  }, [currentSession, dispatch]);

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [currentSession?.content]);

  const handleSend = async () => {
    if (!inputMessage.trim() || !currentSession?.sessionId) {
      return;
    }
    // add usser message now
    dispatch(
      setMessageChatSession({
        sessionId: currentSession.sessionId,
        message: {
          role: 'user',
          content: inputMessage.trim(),
          timestamp: new Date().toISOString(),
        },
      }),
    );

    const messagePayload = {
      sessionId: currentSession.sessionId,
      message: inputMessage.trim(),
    };

    setInputMessage('');

    dispatch(sendMessage(messagePayload))
      .unwrap()
      .then(() => {
        const sessionExists = mySessions.some(
          (session) => session.sessionId === currentSession.sessionId,
        );

        if (!sessionExists) {
          setTimeout(() => {
            dispatch(fetchChatSessions());
          }, 500);
        }
      })
      .catch(() => {});
  };

  return {
    scrollViewRef,
    inputMessage,
    setInputMessage,
    currentSession,
    handleSend,
  };
}
