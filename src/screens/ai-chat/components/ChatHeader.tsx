import { View, Pressable } from 'react-native';
import { H4, P } from '@/components/ui';
import { Ionicons } from '@expo/vector-icons';
import Avatar from '@/components/ui/Avatar';
import { useNavigation, DrawerActions } from '@react-navigation/native';
import { useThemeColors } from '@/hooks/useThemeColors';

const ChatHeader = () => {
  const navigation = useNavigation();
  const colors = useThemeColors();

  const toggleDrawer = () => {
    const parent = navigation.getParent();
    if (parent) {
      navigation.dispatch(DrawerActions.openDrawer());
    } else {
      console.warn('Drawer is not available on this screen.');
    }
  };
  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 50, // Space for status bar
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: colors.surface
    }}>
      <Pressable onPress={() => navigation.goBack()} style={{ marginRight: 16 }}>
        <Ionicons name="chevron-back" size={26} color={colors.text} />
      </Pressable>
      <Avatar size={40} />
      <View style={{ marginLeft: 16, flex: 1 }}>
        <H4 style={{
          color: colors.text,
          fontWeight: '600',
          fontSize: 18
        }}>
          AI Chat Assistant
        </H4>
        <P style={{
          color: colors.textSecondary,
          fontSize: 14
        }}>
          Trợ lý AI thông minh cho việc học tập
        </P>
      </View>
      <Pressable style={{ marginLeft: 8 }} onPress={toggleDrawer}>
        <Ionicons name="time-outline" size={26} color={colors.primary} />
      </Pressable>
    </View>
  );
};

export default ChatHeader;
