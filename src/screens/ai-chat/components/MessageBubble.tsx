import { View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { P, Avatar } from '@/components/ui';
import { ChatMessageContent } from '@/common/types/ai-chat.d';

interface MessageBubbleProps {
  message: ChatMessageContent;
  index: number;
}

export default function MessageBubble({ message, index }: MessageBubbleProps) {
  return (
    <View key={index} className="mb-6">
      <View
        className={`flex-row ${
          message.role === 'user' ? 'justify-end' : 'justify-start'
        }`}
      >
        {message.role === 'assistant' && (
          <View className="mr-3">
            <Avatar size={36} />
          </View>
        )}
        <View
          className={`max-w-[75%] ${
            message.role === 'user' ? 'items-end' : 'items-start'
          }`}
        >
          {message.role === 'user' ? (
            <LinearGradient
              colors={['#6366F1', '#4F46E5']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={{
                borderRadius: 16,
                borderBottomRightRadius: 6,
                paddingHorizontal: 20,
                paddingVertical: 16,
              }}
            >
              <P className="text-white text-lg leading-6">{message.content}</P>
            </LinearGradient>
          ) : (
            <View
              style={{
                backgroundColor: '#F3F4F6',
                borderRadius: 16,
                borderBottomLeftRadius: 6,
                paddingHorizontal: 20,
                paddingVertical: 16,
              }}
            >
              <P className="text-gray-800 text-lg leading-6">
                {message.content}
              </P>
            </View>
          )}
          <P className="text-gray-400 text-base mt-2 px-2">
            {message.timestamp
              ? new Date(message.timestamp).toLocaleTimeString('vi-VN', {
                  hour: '2-digit',
                  minute: '2-digit',
                })
              : ''}
          </P>
        </View>
      </View>
    </View>
  );
}
