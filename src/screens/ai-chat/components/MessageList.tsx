import { forwardRef } from 'react';
import { ScrollView } from 'react-native';
import { ChatSession } from '@/common/types/ai-chat.d';
import MessageBubble from './MessageBubble';
import { LoadingIndicator } from '@/components/ui';

interface MessageListProps {
  currentSession: ChatSession | null;
}

const MessageList = forwardRef<ScrollView, MessageListProps>(
  ({ currentSession }, ref) => {
    return (
      <ScrollView
        ref={ref}
        className="flex-1 px-5 py-4"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      >
        {currentSession?.content?.map((message, index) => (
          <MessageBubble key={index} message={message} index={index} />
        ))}

        {currentSession?.loading && <LoadingIndicator />}
      </ScrollView>
    );
  },
);

MessageList.displayName = 'MessageList';

export default MessageList;
