import { View, TextInput, Pressable } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ChatSession } from '@/common/types/ai-chat.d';
import { useThemeColors } from '@/hooks/useThemeColors';

interface MessageInputProps {
  inputMessage: string;
  setInputMessage: (message: string) => void;
  onSend: () => void;
  currentSession: ChatSession | null;
}

export default function MessageInput({
  inputMessage,
  setInputMessage,
  onSend,
  currentSession,
}: MessageInputProps) {
  const colors = useThemeColors();

  return (
    <View style={{
      paddingHorizontal: 20,
      paddingVertical: 32,
      backgroundColor: colors.surface,
      borderTopWidth: 1,
      borderTopColor: colors.border
    }}>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TextInput
          placeholder="Nhập tin nhắn..."
          value={inputMessage}
          onChangeText={setInputMessage}
          multiline
          maxLength={500}
          placeholderTextColor={colors.textMuted}
          style={{
            flex: 1,
            backgroundColor: colors.backgroundSecondary,
            borderWidth: 1,
            borderColor: colors.border,
            borderRadius: 16,
            paddingHorizontal: 16,
            marginRight: 12,
            fontSize: 16,
            color: colors.text,
            minHeight: 44,
            maxHeight: 120,
            textAlignVertical: 'center',
          }}
          returnKeyType="send"
          onSubmitEditing={onSend}
          blurOnSubmit={false}
        />
        <Pressable
          onPress={onSend}
          disabled={!inputMessage.trim() || currentSession?.loading}
          style={{
            opacity: !inputMessage.trim() || currentSession?.loading ? 0.5 : 1,
          }}
        >
          <LinearGradient
            colors={[colors.primary, colors.primaryDark || colors.primary]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={{
              width: 44,
              height: 44,
              borderRadius: 22,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Ionicons name="send" size={18} color="white" />
          </LinearGradient>
        </Pressable>
      </View>
    </View>
  );
}
