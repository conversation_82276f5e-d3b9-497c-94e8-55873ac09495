import { useRef, useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  Animated,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useFirstLaunch } from '@/hooks/useFirstLaunch';
import { Ionicons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import { RootStackParamList } from '@/common/types/rootParamList';

interface CarouselSlide {
  id: string;
  type: 'hero' | 'features' | 'benefits';
  title: string;
  subtitle?: string;
  content?: any;
}

const LandingPage = () => {
  const { markAsLaunched } = useFirstLaunch();
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollX = useRef(new Animated.Value(0)).current;

  const slides: CarouselSlide[] = [
    {
      id: '1',
      type: 'hero',
      title: 'Learn Vox',
      subtitle: 'AI Interview Pro',
    },
    {
      id: '2',
      type: 'features',
      title: 'Key Features',
      subtitle: 'Advanced technology for your career success',
    },
    {
      id: '3',
      type: 'benefits',
      title: 'Why Choose Us',
      subtitle: 'Benefits that thousands of users love',
    },
  ];

  const features = [
    {
      iconFamily: 'Ionicons',
      iconName: 'mic-outline',
      title: 'Mock Interview',
      description: 'Practice real interviews with AI feedback',
      color: '#8B5CF6',
      gradient: ['#8B5CF6', '#EC4899'],
    },
    {
      iconFamily: 'MaterialIcons',
      iconName: 'description',
      title: 'CV Analysis',
      description: 'Analyze and improve your resume',
      color: '#F59E0B',
      gradient: ['#F59E0B', '#EF4444'],
    },
    {
      iconFamily: 'Ionicons',
      iconName: 'chatbubble-outline',
      title: 'Career Mentor',
      description: 'Get professional career guidance 24/7',
      color: '#10B981',
      gradient: ['#10B981', '#3B82F6'],
    },
    {
      iconFamily: 'FontAwesome5',
      iconName: 'route',
      title: 'Career Roadmap',
      description: 'Personalized career development path',
      color: '#3B82F6',
      gradient: ['#3B82F6', '#8B5CF6'],
    },
  ];

  const benefits = [
    { text: 'Unlimited practice sessions', icon: '🎯', color: '#8B5CF6' },
    { text: 'Real-time AI feedback', icon: '⚡', color: '#F59E0B' },
    { text: 'Boost interview confidence', icon: '💪', color: '#10B981' },
    { text: 'Deep CV analysis', icon: '📊', color: '#EF4444' },
    { text: 'Personalized roadmap', icon: '🗺️', color: '#3B82F6' },
    { text: 'Learning community', icon: '👥', color: '#EC4899' },
  ];

  const onViewableItemsChanged = useRef(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  const handleNext = async () => {
    if (currentIndex < slides.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
    } else {
      // If on last slide, mark as launched and go to Login
      await markAsLaunched();
      navigation.navigate('Auth');
    }
  };

  const handleSkip = async () => {
    // Mark as launched when user skips onboarding
    await markAsLaunched();
    navigation.navigate('Auth');
  };

  const renderSlide = ({
    item,
    index,
  }: {
    item: CarouselSlide;
    index: number;
  }) => {
    switch (item.type) {
      case 'hero':
        return renderHeroSlide(item, index);
      case 'features':
        return renderFeaturesSlide(item, index);
      case 'benefits':
        return renderBenefitsSlide(item, index);
      default:
        return null;
    }
  };

  const renderHeroSlide = (item: CarouselSlide, index: number) => (
    <View style={{ width: screenWidth, height: screenHeight }}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 24,
        }}
      >
        {/* Floating animations */}
        <Animated.View
          style={{
            position: 'absolute',
            top: 100,
            right: 30,
            opacity: 0.3,
            transform: [
              {
                rotate: scrollX.interpolate({
                  inputRange: [
                    (index - 1) * screenWidth,
                    index * screenWidth,
                    (index + 1) * screenWidth,
                  ],
                  outputRange: ['-10deg', '0deg', '10deg'],
                  extrapolate: 'clamp',
                }),
              },
            ],
          }}
        >
          <Ionicons name="sparkles" size={40} color="white" />
        </Animated.View>

        <Animated.View
          style={{
            position: 'absolute',
            top: 150,
            left: 30,
            opacity: 0.3,
            transform: [
              {
                scale: scrollX.interpolate({
                  inputRange: [
                    (index - 1) * screenWidth,
                    index * screenWidth,
                    (index + 1) * screenWidth,
                  ],
                  outputRange: [0.8, 1, 0.8],
                  extrapolate: 'clamp',
                }),
              },
            ],
          }}
        >
          <Ionicons name="star" size={32} color="white" />
        </Animated.View>

        {/* Main content */}
        <Animated.View
          style={{
            alignItems: 'center',
            transform: [
              {
                translateY: scrollX.interpolate({
                  inputRange: [
                    (index - 1) * screenWidth,
                    index * screenWidth,
                    (index + 1) * screenWidth,
                  ],
                  outputRange: [50, 0, -50],
                  extrapolate: 'clamp',
                }),
              },
            ],
          }}
        >
          {/* Logo with glow effect */}
          <View
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 80,
              padding: 20,
              marginBottom: 30,
              shadowColor: '#fff',
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.5,
              shadowRadius: 20,
              elevation: 10,
            }}
          >
            <Image
              source={require('../../../assets/images/logo2.png')}
              style={{ width: 80, height: 80 }}
              resizeMode="contain"
            />
          </View>

          <Text
            style={{
              color: 'white',
              fontSize: 48,
              fontWeight: 'bold',
              textAlign: 'center',
              marginBottom: 16,
            }}
          >
            {item.title}
          </Text>

          <View
            style={{
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 25,
              paddingHorizontal: 20,
              paddingVertical: 10,
              marginBottom: 30,
            }}
          >
            <Text style={{ color: 'white', fontSize: 20, fontWeight: '600' }}>
              {item.subtitle}
            </Text>
          </View>

          <Text
            style={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: 18,
              textAlign: 'center',
              marginBottom: 40,
              lineHeight: 28,
              paddingHorizontal: 20,
            }}
          >
            Smart AI-powered interview practice platform{'\n'}
            Prepare for your dream career success
          </Text>
        </Animated.View>
      </LinearGradient>
    </View>
  );

  const renderFeaturesSlide = (item: CarouselSlide, index: number) => (
    <View style={{ width: screenWidth, height: screenHeight }}>
      <LinearGradient
        colors={['#1e3a8a', '#3b82f6', '#06b6d4']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1, paddingHorizontal: 24, paddingVertical: 60 }}
      >
        <View style={{ alignItems: 'center', marginBottom: 40 }}>
          <Text
            style={{
              color: 'white',
              fontSize: 36,
              fontWeight: 'bold',
              textAlign: 'center',
              marginBottom: 12,
            }}
          >
            {item.title}
          </Text>
          <Text
            style={{
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: 18,
              textAlign: 'center',
            }}
          >
            {item.subtitle}
          </Text>
        </View>

        <View style={{ flex: 1, justifyContent: 'center' }}>
          {features.map((feature, idx) => (
            <Animated.View
              key={idx}
              style={{
                marginBottom: 24,
                transform: [
                  {
                    translateX: scrollX.interpolate({
                      inputRange: [
                        (index - 1) * screenWidth,
                        index * screenWidth,
                        (index + 1) * screenWidth,
                      ],
                      outputRange: [
                        idx % 2 === 0 ? -100 : 100,
                        0,
                        idx % 2 === 0 ? 100 : -100,
                      ],
                      extrapolate: 'clamp',
                    }),
                  },
                ],
                opacity: scrollX.interpolate({
                  inputRange: [
                    (index - 1) * screenWidth,
                    index * screenWidth,
                    (index + 1) * screenWidth,
                  ],
                  outputRange: [0, 1, 0],
                  extrapolate: 'clamp',
                }),
              }}
            >
              <LinearGradient
                colors={feature.gradient as any}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={{
                  borderRadius: 20,
                  padding: 20,
                  shadowColor: feature.color,
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: 0.3,
                  shadowRadius: 16,
                  elevation: 8,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      borderRadius: 16,
                      padding: 16,
                      marginRight: 16,
                    }}
                  >
                    {feature.iconFamily === 'Ionicons' && (
                      <Ionicons
                        name={feature.iconName as any}
                        size={32}
                        color="white"
                      />
                    )}
                    {feature.iconFamily === 'MaterialIcons' && (
                      <MaterialIcons
                        name={feature.iconName as any}
                        size={32}
                        color="white"
                      />
                    )}
                    {feature.iconFamily === 'FontAwesome5' && (
                      <FontAwesome5
                        name={feature.iconName as any}
                        size={32}
                        color="white"
                      />
                    )}
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text
                      style={{
                        color: 'white',
                        fontSize: 20,
                        fontWeight: 'bold',
                        marginBottom: 4,
                      }}
                    >
                      {feature.title}
                    </Text>
                    <Text
                      style={{
                        color: 'rgba(255, 255, 255, 0.9)',
                        fontSize: 16,
                      }}
                    >
                      {feature.description}
                    </Text>
                  </View>
                </View>
              </LinearGradient>
            </Animated.View>
          ))}
        </View>
      </LinearGradient>
    </View>
  );

  const renderBenefitsSlide = (item: CarouselSlide, index: number) => (
    <View style={{ width: screenWidth, height: screenHeight }}>
      <LinearGradient
        colors={['#065f46', '#10b981', '#34d399']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1, paddingHorizontal: 24, paddingVertical: 60 }}
      >
        <View style={{ alignItems: 'center', marginBottom: 40 }}>
          <Text
            style={{
              color: 'white',
              fontSize: 36,
              fontWeight: 'bold',
              textAlign: 'center',
              marginBottom: 12,
            }}
          >
            {item.title}
          </Text>
          <Text
            style={{
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: 18,
              textAlign: 'center',
            }}
          >
            {item.subtitle}
          </Text>
        </View>

        <View style={{ flex: 1, justifyContent: 'center' }}>
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'space-between',
            }}
          >
            {benefits.map((benefit, idx) => (
              <Animated.View
                key={idx}
                style={{
                  width: '48%',
                  marginBottom: 16,
                  transform: [
                    {
                      scale: scrollX.interpolate({
                        inputRange: [
                          (index - 1) * screenWidth,
                          index * screenWidth,
                          (index + 1) * screenWidth,
                        ],
                        outputRange: [0.8, 1, 0.8],
                        extrapolate: 'clamp',
                      }),
                    },
                  ],
                  opacity: scrollX.interpolate({
                    inputRange: [
                      (index - 1) * screenWidth,
                      index * screenWidth,
                      (index + 1) * screenWidth,
                    ],
                    outputRange: [0, 1, 0],
                    extrapolate: 'clamp',
                  }),
                }}
              >
                <View
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.15)',
                    borderRadius: 16,
                    padding: 16,
                    alignItems: 'center',
                    minHeight: 120,
                    justifyContent: 'center',
                  }}
                >
                  <Text style={{ fontSize: 32, marginBottom: 8 }}>
                    {benefit.icon}
                  </Text>
                  <Text
                    style={{
                      color: 'white',
                      fontSize: 14,
                      fontWeight: '600',
                      textAlign: 'center',
                      lineHeight: 20,
                    }}
                  >
                    {benefit.text}
                  </Text>
                </View>
              </Animated.View>
            ))}
          </View>
        </View>
      </LinearGradient>
    </View>
  );

  const renderPagination = () => (
    <View
      style={{
        position: 'absolute',
        bottom: 50,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        gap: 8,
      }}
    >
      {slides.map((_, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => {
            setCurrentIndex(index);
            flatListRef.current?.scrollToIndex({ index, animated: true });
          }}
          style={{
            width: currentIndex === index ? 24 : 8,
            height: 8,
            borderRadius: 4,
            backgroundColor:
              currentIndex === index ? 'white' : 'rgba(255, 255, 255, 0.5)',
          }}
        />
      ))}
    </View>
  );

  const renderNavigationButtons = () => (
    <View
      style={{
        position: 'absolute',
        bottom: 100,
        left: 0,
        right: 0,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 24,
      }}
    >
      {/* Skip Button */}
      <TouchableOpacity
        onPress={handleSkip}
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          borderRadius: 25,
          paddingHorizontal: 20,
          paddingVertical: 12,
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.3)',
        }}
      >
        <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
          Skip
        </Text>
      </TouchableOpacity>

      {/* Next Button */}
      <TouchableOpacity
        onPress={handleNext}
        style={{
          backgroundColor: 'white',
          borderRadius: 25,
          paddingHorizontal: 24,
          paddingVertical: 12,
          flexDirection: 'row',
          alignItems: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 6,
        }}
      >
        <Text
          style={{
            color: '#8B5CF6',
            fontSize: 16,
            fontWeight: '600',
            marginRight: 8,
          }}
        >
          {currentIndex === slides.length - 1 ? 'Get Started' : 'Next'}
        </Text>
        <Ionicons name="arrow-forward" size={20} color="#8B5CF6" />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#000' }}>
      <View style={{ flex: 1 }}>
        <FlatList
          ref={flatListRef}
          data={slides}
          renderItem={renderSlide}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false },
          )}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          scrollEventThrottle={16}
        />

        {renderNavigationButtons()}
        {renderPagination()}
      </View>
    </SafeAreaView>
  );
};

export default LandingPage;
