import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useForm, Controller, FieldError } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Sparkles } from 'lucide-react-native';
import {
  durationOptions,
  interviewTypeOptions,
  MockInterviewFormData,
  mockInterviewSchema,
} from './schema/interviewQuestion';
import FormSelect from '@/components/ui/FormSelectField';
import FormInput from '@/components/ui/FormInputField';
import FormToggleField from '@/components/ui/FormToggleField';
import FormFileUpload from '@/components/ui/FormSingleUploadField';
import HeaderCreateInterviewQuestion from './components/HeaderCreateInterviewQuestion';
import { useCreateInterviewQuestion } from './hooks/useCreateInterviewQuestion';
import { useThemeColors } from '@/hooks/useThemeColors';

const CreateQuestionScreen = () => {
  const { createInterviewQuestion, loading, handleCancel } =
    useCreateInterviewQuestion();
  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<MockInterviewFormData>({
    resolver: zodResolver(mockInterviewSchema),
    defaultValues: {
      jobPosition: '',
      interviewDuration: '',
      interviewType: [],
      jdFile: undefined,
    },
  });

  const watchedValues = watch();

  const onSubmit = async (data: MockInterviewFormData) => {
    try {
      createInterviewQuestion(data);
    } catch (error) {
      Alert.alert('Error', 'Failed to generate questions. Please try again.');
    }
  };

  const themeColors = useThemeColors();

  return (
    <>
      {/* Header */}
      <HeaderCreateInterviewQuestion />

      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Form */}
        <ScrollView
          className="flex-1 px-6"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingVertical: 24 }}
          style={{ backgroundColor: themeColors.background }}
        >
          {/* Job Position */}
          <Controller
            control={control}
            name="jobPosition"
            render={({ field: { onChange, value } }) => (
              <FormInput
                label="Job Position"
                placeholder="e.g. Frontend Developer"
                value={value}
                onChangeText={onChange}
                error={errors.jobPosition}
                disabled={loading}
                required
              />
            )}
          />

          {/* Interview Duration */}
          <Controller
            control={control}
            name="interviewDuration"
            render={({ field: { onChange, value } }) => (
              <FormSelect
                label="Interview Duration"
                placeholder="Select Duration"
                options={durationOptions}
                value={value}
                onSelect={onChange}
                error={errors.interviewDuration}
                disabled={loading}
                required
              />
            )}
          />

          {/* Interview Types */}
          <Controller
            control={control}
            name="interviewType"
            render={({ field: { onChange, value } }) => (
              <FormToggleField
                label="Interview Type"
                description="Select one or more interview types"
                options={interviewTypeOptions}
                selectedValues={value}
                onSelect={onChange}
                error={errors.interviewType as FieldError}
                required
              />
            )}
          />

          {/* File Upload */}
          <Controller
            control={control}
            name="jdFile"
            render={({ field: { onChange, value } }) => (
              <FormFileUpload
                label="Upload JD File (PDF)"
                description="Upload a job description to get more personalized questions"
                value={value}
                onChange={onChange}
                error={errors.jdFile as FieldError}
                maxSize={5}
                disabled={loading}
              />
            )}
          />
        </ScrollView>

        {/* Action Buttons */}
        <View
          className="p-6 border-t"
          style={{
            backgroundColor: themeColors.background,
          }}
        >
          <View className="flex-row gap-3">
            <TouchableOpacity
              onPress={handleCancel}
              className="flex-1 rounded-xl py-4 items-center"
              style={{ backgroundColor: themeColors.cardBackground }}
            >
              <Text
                className="font-semibold"
                style={{ color: themeColors.text }}
              >
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleSubmit(onSubmit)}
              disabled={!isValid || loading || !watchedValues.jdFile}
              className="flex-1 rounded-xl py-4 items-center flex-row gap-2 justify-center"
              style={{
                backgroundColor:
                  isValid && !loading && watchedValues.jdFile
                    ? '#3B82F6'
                    : themeColors.surfaceSecondary,
              }}
            >
              {loading ? (
                <View className="flex-row items-center">
                  <Text className="text-white font-semibold mr-2">
                    Generating...
                  </Text>
                </View>
              ) : (
                <Text
                  className="font-semibold"
                  style={{
                    color:
                      isValid && !loading && watchedValues.jdFile
                        ? 'white'
                        : themeColors.textMuted,
                  }}
                >
                  Generate
                </Text>
              )}
              <Sparkles
                size={20}
                color={
                  isValid && !loading && watchedValues.jdFile
                    ? 'white'
                    : themeColors.textMuted
                }
              />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </>
  );
};

export default CreateQuestionScreen;
