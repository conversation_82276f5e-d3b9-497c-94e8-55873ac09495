import { FlatList, StatusBar, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppDispatch } from '@/redux/hooks';
import { getInterviewQuestionById } from '@/redux/thunks/interviewQuestionThunk';
import { useSelector } from 'react-redux';
import {
  selectCurrentInterviewQuestion,
  selectInterviewQuestionLoading,
} from '@/redux/selectors/interviewQuestionSelector';
import InterviewDetailHeader from './components/HeaderInterviewQuestionDetail';
import { InterviewQuestion } from '@/services/types/interview';
import InterviewQuestionCard from './components/InterviewQuestionCard';
import InterviewQuestionDetailSkeleton from './components/SkeletonInterviewQuestionDetail';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { useInterviewQuestion } from './hooks/useInterviewQuestion';
import FeedbackInterviewList from '../feedback/components/FeedbackList';
import { DoubleTabSelector } from '@/components/ui';
import { useThemeColors } from '@/hooks/useThemeColors';

type TabType = 'questions' | 'history';
const InterviewQuestionDetail = () => {
  const route = useRoute();
  const { interviewId } = route.params as { interviewId: string };
  const [activeTab, setActiveTab] = useState<TabType>('questions');
  const [listType] = useState<string[]>(['questions', 'history']);

  const navigation =
    useNavigation<NativeStackNavigationProp<MockInterviewStackParamList>>();

  const { handleStartInterview } = useInterviewQuestion({
    navigation,
  });

  const dispatch = useAppDispatch();
  const currentInterviewQuestion = useSelector(selectCurrentInterviewQuestion);
  const loading = useSelector(selectInterviewQuestionLoading);
  const themeColors = useThemeColors(); // Di chuyển lên trước early return

  useEffect(() => {
    if (interviewId) {
      dispatch(getInterviewQuestionById(interviewId));
    }
  }, [dispatch, interviewId]);

  const renderQuestion = ({
    item,
    index,
  }: {
    item: InterviewQuestion;
    index: number;
  }) => <InterviewQuestionCard question={item} questionNumber={index + 1} />;

  if (loading) {
    return <InterviewQuestionDetailSkeleton />;
  }

  const renderHeader = () => (
    <View>
      <InterviewDetailHeader
        jobPosition={currentInterviewQuestion?.jobPosition || ''}
        totalQuestions={currentInterviewQuestion?.totalQuestion || 0}
        interviewDuration={currentInterviewQuestion?.interviewDuration || ''}
        interviewTypes={currentInterviewQuestion?.interviewType || []}
        onStartInterview={() => handleStartInterview(interviewId)}
      />

      <DoubleTabSelector
        activeTab={activeTab}
        onTabChange={(tab) => setActiveTab(tab as TabType)}
        listType={['questions', 'history']}
        countItem1={currentInterviewQuestion?.interviewQuestions?.length || 0}
        countItem2={currentInterviewQuestion?.conversations?.length || 0}
        labelItem1="Questions"
        labelItem2="History"
      />
    </View>
  );

  return (
    <>
      {activeTab === 'questions' ? (
        <FlatList
          data={currentInterviewQuestion?.interviewQuestions}
          renderItem={renderQuestion}
          keyExtractor={(item, index) => `question-${index}`}
          horizontal={false}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={renderHeader}
          style={{ backgroundColor: themeColors.background }}
        />
      ) : (
        <FeedbackInterviewList
          feedbackInterviews={currentInterviewQuestion?.conversations || []}
          loading={loading}
          itemsPerPage={5}
          headerComponent={renderHeader()}
        />
      )}
    </>
  );
};

export default InterviewQuestionDetail;
