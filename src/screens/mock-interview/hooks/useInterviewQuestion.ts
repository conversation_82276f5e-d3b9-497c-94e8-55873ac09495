import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { useAppDispatch } from '@/redux/hooks';
import { deleteInterviewQuestionThunk } from '@/redux/thunks/interviewQuestionThunk';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useState } from 'react';

export const useInterviewQuestion = ({
  navigation,
}: {
  navigation: NativeStackNavigationProp<MockInterviewStackParamList>;
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [deletingItemId, setDeletingItemId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const dispatch = useAppDispatch();

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemPress = (item: InterviewQuestionSessionResponse) => {
    // Navigate to interview details or start interview
    console.log('Navigate to interview:', item._id);
    navigation.navigate('InterviewQuestionDetail', { interviewId: item._id });
  };

  const handleCreateNew = () => {
    navigation.navigate('CreateQuestion');
  };

  const handleDelete = async (item: InterviewQuestionSessionResponse) => {
    try {
      setDeletingItemId(item._id);
      dispatch(deleteInterviewQuestionThunk(item._id));
    } catch (error) {
      console.error('Error deleting interview question:', error);
    } finally {
      setDeletingItemId(null);
    }
  };

  const handleStartInterview = (interviewId: string) => {
    navigation.navigate('StartInterview', { interviewId });
  };

  return {
    handleItemPress,
    handleCreateNew,
    handleDelete,
    currentPage,
    setCurrentPage,
    searchQuery,
    setSearchQuery,
    handlePageChange,
    deletingItemId,
    handleStartInterview,
  };
};
