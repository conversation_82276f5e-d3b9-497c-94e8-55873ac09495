import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { selectInterviewQuestionLoading } from '@/redux/selectors/interviewQuestionSelector';
import { createInterviewQuestionThunk } from '@/redux/thunks/interviewQuestionThunk';
import { MockInterviewFormData } from '../schema/interviewQuestion';
import { Alert, Platform } from 'react-native';

import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { isApiSuccess } from '@/common/types/api';

export const useCreateInterviewQuestion = () => {
  const dispatch = useAppDispatch();
  const navigation =
    useNavigation<NativeStackNavigationProp<MockInterviewStackParamList>>();
  const loading = useAppSelector(selectInterviewQuestionLoading);

  const createInterviewQuestion = async (data: MockInterviewFormData) => {
    try {
      const formData = new FormData();
      formData.append('jobPosition', data.jobPosition);
      formData.append('interviewDuration', data.interviewDuration);
      formData.append('interviewType', data.interviewType.join(','));

      // Fix cho Android: format file object đúng cách
      if (data.jdFile) {
        const fileObject = {
          uri: data.jdFile.uri,
          type: 'application/pdf',
          name: data.jdFile.name || 'document.pdf',
        };

        console.log('File object being uploaded:', fileObject);
        console.log('Platform:', Platform.OS);

        formData.append('jdFile', fileObject as any);
      }

      const response = await dispatch(
        createInterviewQuestionThunk(formData),
      ).unwrap();

      if (isApiSuccess(response)) {
        navigation.reset({
          index: 1,
          routes: [
            { name: 'InterviewQuestion' },
            {
              name: 'InterviewQuestionDetail',
              params: { interviewId: response.data._id },
            },
          ],
        });
      } else {
        Alert.alert('Error', 'Failed to generate questions. Please try again.');
      }
    } catch (error) {
      console.error('Create interview question error:', error);
      Alert.alert('Error', 'Failed to generate questions. Please try again.');
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel Generation',
      'Are you sure you want to cancel? All entered data will be lost.',
      [
        { text: 'Continue Editing', style: 'cancel' },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: () => navigation.goBack(),
        },
      ],
    );
  };

  return { createInterviewQuestion, handleCancel, loading };
};
