import React from 'react';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useThemeColors } from '@/hooks/useThemeColors';

import HeaderInterviewQuestion from './components/HeaderInterviewQuestion';
import InterviewOverview from './components/InterviewOverview';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { useEffect, useState } from 'react';
import { getMyInterviewQuestion } from '@/redux/thunks/interviewQuestionThunk';
import {
  selectInterviewQuestionLoading,
  selectMyInterviewQuestion,
} from '@/redux/selectors/interviewQuestionSelector';
import InterviewQuestionList from './components/InterviewQuestionList';
import InterviewQuestionScreenSkeleton from './components/SkeletonInterviewQuestion';
import { getMyConversations } from '@/redux/thunks/conversationThunks';
import {
  selectConversationLoading,
  selectMyConversations,
} from '@/redux/selectors/conversationSelector';
import FeedbackInterviewList from '../feedback/components/FeedbackList';
import { DoubleTabSelector } from '@/components/ui';

type TabType = 'questions' | 'feedbacks';

const InterviewQuestionScreen = () => {
  const dispatch = useAppDispatch();
  const [activeTab, setActiveTab] = useState<TabType>('questions');
  const [listType] = useState<string[]>(['questions', 'feedbacks']);

  const myInterviewQuestion = useAppSelector(selectMyInterviewQuestion);
  const myConversations = useAppSelector(selectMyConversations);
  const loading = useAppSelector(selectInterviewQuestionLoading);
  const conversationLoading = useAppSelector(selectConversationLoading);
  const themeColors = useThemeColors(); // Di chuyển lên trước early return

  useEffect(() => {
    dispatch(getMyInterviewQuestion());
    dispatch(getMyConversations());
  }, [dispatch]);

  const renderListHeader = () => (
    <>
      <HeaderInterviewQuestion />
      <View className="bg-gray-50">
        <InterviewOverview
          myInterviewQuestions={myInterviewQuestion}
          myConversations={myConversations}
        />
      </View>
      {/* Tab Selector */}
      <DoubleTabSelector
        activeTab={activeTab}
        onTabChange={(tab) => setActiveTab(tab as TabType)}
        countItem1={myInterviewQuestion.length}
        countItem2={myConversations.length}
        labelItem1="Questions"
        labelItem2="History"
        listType={listType}
      />
    </>
  );

  // Show skeleton while loading and no data
  if (loading && myInterviewQuestion.length === 0) {
    return <InterviewQuestionScreenSkeleton />;
  }

  return (
    <>
      {activeTab === 'questions' ? (
        <InterviewQuestionList
          interviewQuestions={myInterviewQuestion}
          loading={loading}
          itemsPerPage={5}
          headerComponent={renderListHeader()}
        />
      ) : (
        <FeedbackInterviewList
          feedbackInterviews={myConversations}
          loading={conversationLoading}
          itemsPerPage={5}
          headerComponent={renderListHeader()}
        />
      )}
    </>
  );
};

export default InterviewQuestionScreen;
