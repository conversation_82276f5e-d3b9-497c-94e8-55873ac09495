import { z } from 'zod';
import * as DocumentPicker from 'expo-document-picker';

export const mockInterviewSchema = z.object({
  jobPosition: z.string().min(1, 'Job position is required'),
  interviewDuration: z.string().min(1, 'Interview duration is required'),
  interviewType: z
    .array(z.string())
    .min(1, 'At least one interview type is required'),
  jdFile: z.custom<DocumentPicker.DocumentPickerAsset>().optional(),
});

export type MockInterviewFormData = z.infer<typeof mockInterviewSchema>;

export const durationOptions = [
  { value: '10min', label: '10 minutes' },
  { value: '15min', label: '15 minutes' },
  { value: '20min', label: '20 minutes' },
  { value: '25min', label: '25 minutes' },
  { value: '30min', label: '30 minutes' },
];

export const interviewTypeOptions = [
  {
    value: 'technical',
    label: 'Technical',
    icon: 'code',
    color: '#3B82F6',
  },
  {
    value: 'behavioral',
    label: 'Behavioral',
    icon: 'users',
    color: '#10B981',
  },
  {
    value: 'experienced',
    label: 'Experienced',
    icon: 'briefcase',
    color: '#F59E0B',
  },
  {
    value: 'problemsolving',
    label: 'Problem Solving',
    icon: 'puzzle',
    color: '#8B5CF6',
  },
  {
    value: 'leadership',
    label: 'Leadership',
    icon: 'crown',
    color: '#EF4444',
  },
];
