import React from 'react';
import { View, Text } from 'react-native';
import { Star } from 'lucide-react-native';

interface InterviewHeaderTitleProps {
  jobPosition: string;
}

const InterviewHeaderTitle: React.FC<InterviewHeaderTitleProps> = ({
  jobPosition,
}) => {
  return (
    <View className="flex-col items-center justify-center mt-4">
      <View className="flex-row justify-center items-center mb-2">
        <Text className="text-white text-3xl font-bold">
          Interview Questions
        </Text>
      </View>

      {/* Job Position */}
      <Text className="text-white/80 text-sm mb-2">
        Prepare for your{' '}
        <Text className="font-semibold text-blue-200">{jobPosition}</Text>{' '}
        interview with confidence
      </Text>
    </View>
  );
};

export default InterviewHeaderTitle;
