import React from 'react';
import { View, Text } from 'react-native';
import { Clock } from 'lucide-react-native';
import { InterviewTypeEnum } from '@/common/enums/interviewQuestion.enum';
import { useThemeColors } from '@/hooks/useThemeColors';

interface QuestionHeaderProps {
  questionNumber: number;
  type: InterviewTypeEnum;
  estimatedTime: number;
}

const HeaderInterviewQuestionCard: React.FC<QuestionHeaderProps> = ({
  questionNumber,
  type,
  estimatedTime,
}) => {
  const colors = useThemeColors();

  const getTypeColor = (questionType: InterviewTypeEnum) => {
    const colorMap = {
      [InterviewTypeEnum.TECHNICAL]: {
        bg: '#3B82F6', // blue
        text: '#1E40AF',
      },
      [InterviewTypeEnum.BEHAVIORAL]: {
        bg: '#10B981', // green
        text: '#047857',
      },
      [InterviewTypeEnum.EXPERIENCED]: {
        bg: '#F59E0B', // orange
        text: '#D97706',
      },
      [InterviewTypeEnum.PROBLEM_SOLVING]: {
        bg: '#8B5CF6', // purple
        text: '#7C3AED',
      },
      [InterviewTypeEnum.LEADERSHIP]: {
        bg: '#EC4899', // pink
        text: '#DB2777',
      },
    };

    return colorMap[questionType] || colorMap[InterviewTypeEnum.TECHNICAL];
  };

  const typeStyle = getTypeColor(type);

  return (
    <View className="flex-row items-center justify-between mb-4">
      <View
        className="w-10 h-10 rounded-xl justify-center items-center"
        style={{ backgroundColor: colors.primary }}
      >
        <Text className="font-bold text-lg" style={{ color: colors.cardBackground }}>
          {questionNumber}
        </Text>
      </View>
      <View className="flex-row items-center gap-2">
        <View
          className="px-3 py-1 rounded-full border"
          style={{
            backgroundColor: typeStyle.bg + '20', // Add transparency
            borderColor: typeStyle.bg + '40',
          }}
        >
          <Text
            className="text-xs font-semibold"
            style={{ color: typeStyle.text }}
          >
            {type}
          </Text>
        </View>
        <View
          className="flex-row items-center px-3 py-1 rounded-full"
          style={{ backgroundColor: colors.surfaceSecondary }}
        >
          <Clock size={14} color={colors.textMuted} />
          <Text
            className="text-xs font-medium ml-1"
            style={{ color: colors.textMuted }}
          >
            {estimatedTime} mins
          </Text>
        </View>
      </View>
    </View>
  );
};

export default HeaderInterviewQuestionCard;
