import {
  ImageBackground,
  Text,
  TouchableOpacity,
  View,
  StatusBar,
} from 'react-native';
import React from 'react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { ArrowLeft, Upload } from 'lucide-react-native';
import { H1, P } from '@/components/ui';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const HeaderInterviewQuestion = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<MockInterviewStackParamList>>();
  const insets = useSafeAreaInsets();

  return (
    <View style={{ flex: 1 }}>
      <ImageBackground
        source={{
          uri: 'https://wallpapers.com/images/hd/interview-background-1920-x-1080-qukoh65i5piq5jto.jpg',
        }}
        style={{
          width: '100%',
          height: 250 + insets.top,
          opacity: 0.7,
        }}
        resizeMode="cover"
        className="flex-1 items-center justify-center"
      >
        <LinearGradient
          colors={['rgba(102, 126, 234, 0.6)', 'rgba(118, 75, 162, 0.8)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        >
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="absolute left-2 bg-white/20 rounded-full p-[8px] items-center justify-center z-10"
            style={{ top: insets.top + 8 }}
            activeOpacity={0.8}
            hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
          >
            <View pointerEvents="none">
              <ArrowLeft size={20} color="white" />
            </View>
          </TouchableOpacity>

          <View
            className="flex-1 items-center justify-center text-white"
            style={{ paddingTop: insets.top + 20 }}
          >
            <H1 className="mb-2 text-white">Mock Interview</H1>
            <P className="mb-5 text-white text-center w-4/5">
              Create your own mock interview questions and practice with them.
            </P>
            <TouchableOpacity
              className="bg-white rounded-xl py-4 px-8 flex-row items-center justify-center shadow-lg"
              onPress={() => navigation.navigate('CreateQuestion')}
              activeOpacity={0.8}
            >
              <Upload size={20} color="#6366f1" />
              <Text className="text-indigo-600 font-semibold ml-2 text-lg">
                Generate Questions
              </Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
};

export default HeaderInterviewQuestion;
