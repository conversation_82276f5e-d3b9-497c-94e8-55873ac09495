import React from 'react';
import { Text, View } from 'react-native';
import { InterviewQuestion } from '@/services/types/interview';
import HeaderInterviewQuestionCard from './HeaderInterviewQuestionCard';
import InterviewQuestionTips from './InterviewQuestionTips';
import { useThemeColors } from '@/hooks/useThemeColors';

interface InterviewQuestionCardProps {
  question: InterviewQuestion;
  questionNumber: number;
}

const InterviewQuestionCard: React.FC<InterviewQuestionCardProps> = ({
  question,
  questionNumber,
}) => {
  const colors = useThemeColors();

  return (
    <View
      className="rounded-2xl p-6 mb-4 border"
      style={{
        backgroundColor: colors.card,
        borderColor: colors.border,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.04,
        shadowRadius: 8,
        elevation: 2,
      }}
    >
      <HeaderInterviewQuestionCard
        questionNumber={questionNumber}
        type={question.type}
        estimatedTime={question.estimated_time_minutes}
      />

      <Text
        className="text-base font-medium leading-6 mb-4"
        style={{ color: colors.text }}
      >
        {question.question}
      </Text>

      <InterviewQuestionTips tip={question.tip} />
    </View>
  );
};

export default InterviewQuestionCard;
