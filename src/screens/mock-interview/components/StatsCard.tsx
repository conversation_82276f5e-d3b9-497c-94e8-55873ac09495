import React from 'react';
import { View, Text } from 'react-native';

interface StatsCardProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  backgroundColor: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  icon,
  label,
  value,
  backgroundColor,
}) => {
  return (
    <View className={`${backgroundColor} rounded-2xl p-2 w-[47%]`}>
      <View className="flex-row items-center mb-2">
        <View className="bg-white/20 p-2 rounded-lg mr-3">{icon}</View>
        <Text className="text-white/80 text-xs font-medium uppercase tracking-wide">
          {label}
        </Text>
      </View>
      <Text className="text-white text-md font-bold">{value}</Text>
    </View>
  );
};

export default StatsCard;
