// src/screens/mock-interview/components/skeletons/InterviewQuestionDetailSkeleton.tsx
import React from 'react';
import { FlatList, StatusBar, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import InterviewQuestionCardSkeleton from './SkeletonQuestionCard';
import InterviewDetailHeaderSkeleton from './SkeletonHeaderInterviewDetail';
import { useThemeColors } from '@/hooks/useThemeColors';

const InterviewQuestionDetailSkeleton = () => {
  const themeColors = useThemeColors();
  // Create dummy data for skeleton cards
  const skeletonData = Array.from({ length: 4 }, (_, index) => ({ id: index }));

  const renderSkeletonCard = () => <InterviewQuestionCardSkeleton />;

  const renderHeader = () => (
    <View>
      <InterviewDetailHeaderSkeleton />
      {/* Tab Selector Skeleton */}
      <View
        style={{
          backgroundColor: themeColors.surface,
          paddingHorizontal: 24,
          paddingVertical: 16,
          borderBottomWidth: 1,
          borderBottomColor: themeColors.border,
        }}
      >
        <View
          style={{
            flexDirection: 'row',
            backgroundColor: themeColors.backgroundSecondary,
            borderRadius: 12,
            padding: 4,
          }}
        >
          <View
            style={{
              flex: 1,
              backgroundColor: themeColors.primary,
              borderRadius: 8,
              paddingVertical: 12,
              paddingHorizontal: 16,
              marginRight: 4,
            }}
          >
            <View style={{ alignItems: 'center' }}>
              <View
                style={{
                  width: 60,
                  height: 16,
                  backgroundColor: 'rgba(255,255,255,0.3)',
                  borderRadius: 8,
                }}
              />
            </View>
          </View>
          <View
            style={{
              flex: 1,
              paddingVertical: 12,
              paddingHorizontal: 16,
              marginLeft: 4,
            }}
          >
            <View style={{ alignItems: 'center' }}>
              <View
                style={{
                  width: 50,
                  height: 16,
                  backgroundColor: themeColors.backgroundSecondary,
                  borderRadius: 8,
                }}
              />
            </View>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <>
      <StatusBar barStyle={themeColors.statusBarStyle} />
      <FlatList
        data={skeletonData}
        renderItem={renderSkeletonCard}
        keyExtractor={(item) => `skeleton-${item.id}`}
        ListHeaderComponent={renderHeader}
        showsVerticalScrollIndicator={false}
        style={{ backgroundColor: themeColors.background }}
      />
    </>
  );
};

export default InterviewQuestionDetailSkeleton;
