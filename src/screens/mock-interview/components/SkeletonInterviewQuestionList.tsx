import React from 'react';
import { View } from 'react-native';
import { Skeleton } from '@/components/ui/Skeleton';
import { useThemeColors } from '@/hooks/useThemeColors';

const InterviewQuestionItemSkeleton = () => {
  const colors = useThemeColors();

  return (
    <View style={{
      backgroundColor: colors.surface,
      marginHorizontal: 24,
      marginBottom: 16,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: colors.border,
    }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
      }}>
        <View style={{ flex: 1 }}>
          <Skeleton style={{ height: 20, width: '75%', marginBottom: 8 }} />
          <Skeleton style={{ height: 16, width: '50%' }} />
        </View>
        <Skeleton style={{ width: 32, height: 32, borderRadius: 16 }} />
      </View>

      {/* Content */}
      <View style={{ marginBottom: 12 }}>
        <Skeleton style={{ height: 16, width: '100%', marginBottom: 4 }} />
        <Skeleton style={{ height: 16, width: '66%' }} />
      </View>

      {/* Tags */}
      <View style={{
        flexDirection: 'row',
        marginBottom: 12,
      }}>
        <Skeleton style={{ height: 24, width: 64, borderRadius: 12, marginRight: 8 }} />
        <Skeleton style={{ height: 24, width: 80, borderRadius: 12, marginRight: 8 }} />
        <Skeleton style={{ height: 24, width: 56, borderRadius: 12 }} />
      </View>

      {/* Footer */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <Skeleton style={{ height: 16, width: 96 }} />
        <Skeleton style={{ height: 32, width: 80, borderRadius: 8 }} />
      </View>
    </View>
  );
};

const InterviewQuestionListSkeleton = () => {
  const colors = useThemeColors();

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.backgroundSecondary,
    }}>
      {/* List Header */}
      <View style={{
        paddingHorizontal: 24,
        paddingVertical: 16,
        backgroundColor: colors.surface,
        borderBottomWidth: 1,
        borderBottomColor: colors.border,
      }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <View>
            <Skeleton style={{ height: 28, width: 160, marginBottom: 8 }} />
            <Skeleton style={{ height: 16, width: 128 }} />
          </View>
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: 12,
          }}>
            <Skeleton style={{ width: 48, height: 28, borderRadius: 14 }} />
            <Skeleton style={{ width: 40, height: 40, borderRadius: 20 }} />
          </View>
        </View>
      </View>

      {/* List Items */}
      <View className="pt-4">
        {[1, 2, 3, 4, 5].map((index) => (
          <InterviewQuestionItemSkeleton key={index} />
        ))}
      </View>

      {/* Pagination */}
      <View style={{
        backgroundColor: colors.surface,
        borderTopWidth: 1,
        borderTopColor: colors.border,
        padding: 16,
      }}>
        <View className="flex-row justify-center items-center">
          <Skeleton className="w-8 h-8 rounded mr-2" />
          <Skeleton className="w-8 h-8 rounded mr-2" />
          <Skeleton className="w-8 h-8 rounded mr-2" />
          <Skeleton className="w-8 h-8 rounded mr-2" />
          <Skeleton className="w-8 h-8 rounded" />
        </View>
      </View>
    </View>
  );
};

export default InterviewQuestionListSkeleton;
