import React from 'react';
import { View, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import HeaderInterviewQuestionSkeleton from './SkeletonHeaderInterviewQuestion';
import InterviewOverviewSkeleton from './SkeletonInterviewQuestionOverview';
import InterviewQuestionListSkeleton from './SkeletonInterviewQuestionList';
import { useThemeColors } from '@/hooks/useThemeColors';

const InterviewQuestionScreenSkeleton = () => {
  const themeColors = useThemeColors();

  return (
    <SafeAreaView
      className="flex-1"
      style={{ backgroundColor: themeColors.background }}
      edges={['bottom', 'left', 'right']}
    >
      <StatusBar barStyle={themeColors.statusBarStyle} />

      <HeaderInterviewQuestionSkeleton />
      <InterviewOverviewSkeleton />
      <InterviewQuestionListSkeleton />
    </SafeAreaView>
  );
};

export default InterviewQuestionScreenSkeleton;
