import React from 'react';
import { View } from 'react-native';
import { Skeleton } from '@/components/ui/Skeleton';
import { LinearGradient } from 'expo-linear-gradient';

const HeaderInterviewQuestionSkeleton = () => {
  return (
    <LinearGradient
      colors={['#6366f1', '#8b5cf6']}
      style={{
        paddingTop: 60,
        paddingBottom: 32,
        paddingHorizontal: 24,
      }}
    >
      {/* Title */}
      <Skeleton className="h-8 w-3/4 mb-2 bg-white/20" />

      {/* Subtitle */}
      <Skeleton className="h-4 w-1/2 mb-6 bg-white/20" />

      {/* Stats Row */}
      <View className="flex-row justify-between">
        <View className="flex-1 mr-3">
          <Skeleton className="h-6 w-full mb-1 bg-white/20" />
          <Skeleton className="h-4 w-2/3 bg-white/20" />
        </View>
        <View className="flex-1 mx-1">
          <Skeleton className="h-6 w-full mb-1 bg-white/20" />
          <Skeleton className="h-4 w-2/3 bg-white/20" />
        </View>
        <View className="flex-1 ml-3">
          <Skeleton className="h-6 w-full mb-1 bg-white/20" />
          <Skeleton className="h-4 w-2/3 bg-white/20" />
        </View>
      </View>
    </LinearGradient>
  );
};

export default HeaderInterviewQuestionSkeleton;
