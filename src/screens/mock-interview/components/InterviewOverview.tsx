import React, { useMemo } from 'react';
import { View, Text, FlatList } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { HelpCircle, MessageCircle, Star } from 'lucide-react-native';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import {
  CalculatedInterviewStats,
  calculateInterviewQuestionHistory,
  formatLargeNumber,
  getRatingLevel,
} from '@/utils/calulateInterviewQuestionHistory';
import { ConversationResponse } from '@/services/types/conversation';
import { useThemeColors } from '@/hooks/useThemeColors';
const InterviewOverview = ({
  myInterviewQuestions,
  myConversations,
}: {
  myInterviewQuestions: InterviewQuestionSessionResponse[];
  myConversations: ConversationResponse[];
}) => {
  const calculatedStats = useMemo((): CalculatedInterviewStats => {
    const { totalQuestions, totalFeedback, averageRating } =
      calculateInterviewQuestionHistory(myInterviewQuestions, myConversations);

    return {
      totalQuestions,
      totalFeedback,
      averageRating,
      averageRatingText: averageRating > 0 ? `${averageRating}/10` : '0/10',
    };
  }, [myInterviewQuestions, myConversations]);

  // Generate stats data with real values
  const statsData = useMemo(() => {
    const { totalQuestions, totalFeedback, averageRatingText } =
      calculatedStats;

    return [
      {
        id: 1,
        title: 'Total Questions',
        value: formatLargeNumber(totalQuestions),
        icon: HelpCircle,
        color: '#6366f1',
      },
      {
        id: 2,
        title: 'Total Feedback',
        value: formatLargeNumber(totalFeedback),
        icon: MessageCircle,
        color: '#10b981',
      },
      {
        id: 3,
        title: 'Average Rating',
        value: averageRatingText,
        icon: Star,
        color: '#f59e0b',
      },
    ];
  }, [calculatedStats]);

  const renderStatsItem = ({ item, index }: { item: any; index: number }) => {
    const ratingLevel = getRatingLevel(calculatedStats.averageRating);

    return (
      <View className="mr-4">
        <LinearGradient
          colors={[item.color + '20', item.color + '10']}
          style={{
            borderRadius: 16,
            padding: 20,
            minWidth: 160,
            minHeight: 170,
            borderWidth: 1,
            borderColor: item.color + '30',
            justifyContent: 'space-between',
          }}
        >
          {/* Header Section */}
          <View className="flex-row items-center justify-between mb-4">
            <View
              className="w-12 h-12 rounded-full items-center justify-center"
              style={{ backgroundColor: item.color + '20' }}
            >
              <item.icon size={24} color={item.color} />
            </View>
            <View
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: item.color }}
            />
          </View>

          {/* Main Content Section */}
          <View className="flex-1 justify-center mb-4">
            <Text className="text-gray-600 text-sm font-medium mb-1">
              {item.title}
            </Text>
            <Text className="text-3xl font-bold" style={{ color: item.color }}>
              {item.value}
            </Text>
          </View>

          {/* Footer Section - Fixed height area */}
          <View style={{ minHeight: 20, justifyContent: 'flex-end' }}>
            {/* Rating level - only for Average Rating card */}
            {item.title === 'Average Rating' ? (
              <View
                className="px-2 py-1 rounded-full self-start"
                style={{ backgroundColor: ratingLevel.bgColor }}
              >
                <Text
                  className="text-xs font-medium"
                  style={{ color: ratingLevel.color }}
                >
                  {ratingLevel.level}
                </Text>
              </View>
            ) : (
              <View style={{ height: 24 }} />
            )}
          </View>
        </LinearGradient>
      </View>
    );
  };

  const themeColors = useThemeColors();

  return (
    <View className="py-8" style={{ backgroundColor: themeColors.surface }}>
      <View className="px-6 mb-4">
        <Text className="text-2xl font-bold mb-2" style={{ color: themeColors.text }}>
          Interview Overview
        </Text>
        <Text className="text-base" style={{ color: themeColors.textSecondary }}>
          Track your mock interview progress
        </Text>
        {/* Additional insights */}
        {calculatedStats.totalFeedback > 0 && (
          <View
            className="mt-3 p-3 rounded-lg border"
            style={{
              backgroundColor: themeColors.backgroundSecondary,
              borderColor: themeColors.border
            }}
          >
            <Text className="text-sm" style={{ color: themeColors.primary }}>
              💡 Your average rating is {calculatedStats.averageRating}/10.
              {calculatedStats.averageRating < 7
                ? ' Keep practicing to improve your interview skills!'
                : ' Excellent performance! Continue your great work.'}
            </Text>
          </View>
        )}
      </View>

      <FlatList
        data={statsData}
        renderItem={renderStatsItem}
        keyExtractor={(item) => item.id.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 24 }}
        decelerationRate="fast"
      />
    </View>
  );
};

export default InterviewOverview;
