import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import InterviewHeaderTitle from './HeaderInterverQuestionTitle';
import StatsCards from './ListStatsCard';
import ActionButtons from './ActionButton';
import { TouchableOpacity, View } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface InterviewDetailHeaderProps {
  jobPosition: string;
  totalQuestions: number;
  interviewDuration: string;
  interviewTypes: string[];
  onStartInterview: () => void;
}

const InterviewDetailHeader: React.FC<InterviewDetailHeaderProps> = ({
  jobPosition,
  totalQuestions,
  interviewDuration,
  interviewTypes,
  onStartInterview,
}) => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      className="px-6 pt-16 pb-8"
      style={{
        paddingTop: insets.top,
      }}
    >
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        className="absolute left-2 bg-white/20 rounded-full p-[8px] items-center justify-center z-10"
        style={{ top: insets.top + 8 }}
        activeOpacity={0.8}
        hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
      >
        <View pointerEvents="none">
          <ArrowLeft size={20} color="white" />
        </View>
      </TouchableOpacity>

      <InterviewHeaderTitle jobPosition={jobPosition} />

      <StatsCards
        jobPosition={jobPosition}
        totalQuestions={totalQuestions}
        interviewTypes={interviewTypes}
        interviewDuration={interviewDuration}
      />

      <ActionButtons onStartInterview={onStartInterview} />
    </LinearGradient>
  );
};

export default InterviewDetailHeader;
