import React from 'react';
import { View } from 'react-native';
import { Skeleton } from '@/components/ui/Skeleton';
import { useThemeColors } from '@/hooks/useThemeColors';

const InterviewQuestionCardSkeleton = () => {
  const colors = useThemeColors();

  return (
    <View
      style={{
        backgroundColor: colors.surface,
        borderRadius: 16,
        padding: 24,
        marginBottom: 16,
        borderWidth: 1,
        borderColor: colors.border,
        shadowColor: colors.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.04,
        shadowRadius: 8,
        elevation: 2,
      }}
    >
      {/* Header Card */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <Skeleton style={{ width: 32, height: 32, borderRadius: 16, marginRight: 12 }} />
          <View>
            <Skeleton style={{ height: 16, width: 64, marginBottom: 4 }} />
            <Skeleton style={{ height: 12, width: 48 }} />
          </View>
        </View>
        <Skeleton style={{ height: 24, width: 64, borderRadius: 12 }} />
      </View>

      {/* Question Text */}
      <View style={{ marginBottom: 16 }}>
        <Skeleton style={{ height: 16, width: '100%', marginBottom: 8 }} />
        <Skeleton style={{ height: 16, width: '100%', marginBottom: 8 }} />
        <Skeleton style={{ height: 16, width: '75%' }} />
      </View>

      {/* Tips Section */}
      <View style={{
        backgroundColor: colors.backgroundSecondary,
        borderRadius: 12,
        padding: 16,
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 8,
        }}>
          <Skeleton style={{ width: 20, height: 20, borderRadius: 4, marginRight: 8 }} />
          <Skeleton style={{ height: 16, width: 64 }} />
        </View>
        <Skeleton style={{ height: 16, width: '100%', marginBottom: 4 }} />
        <Skeleton style={{ height: 16, width: '66%' }} />
      </View>
    </View>
  );
};

export default InterviewQuestionCardSkeleton;
