import React from 'react';
import { View, Text } from 'react-native';
import { Lightbulb } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface QuestionTipProps {
  tip: string;
}

const InterviewQuestionTips: React.FC<QuestionTipProps> = ({ tip }) => {
  const colors = useThemeColors();

  return (
    <View
      className="border rounded-xl p-4"
      style={{
        backgroundColor: colors.surfaceSecondary,
        borderColor: colors.borderLight,
      }}
    >
      <View className="flex-row items-start">
        <View
          className="p-1.5 rounded-lg mr-3 mt-0.5"
          style={{
            backgroundColor: colors.primary + '20', // Add transparency
          }}
        >
          <Lightbulb size={16} color={colors.primary} />
        </View>
        <View className="flex-1">
          <Text
            className="text-xs font-semibold mb-1 uppercase tracking-wide"
            style={{ color: colors.primary }}
          >
            Tips:
          </Text>
          <Text
            className="text-sm leading-5"
            style={{ color: colors.textSecondary }}
          >
            {tip}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default InterviewQuestionTips;
