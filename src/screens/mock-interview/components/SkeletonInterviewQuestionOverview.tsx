import React from 'react';
import { View } from 'react-native';
import { Skeleton } from '@/components/ui/Skeleton';
import { useThemeColors } from '@/hooks/useThemeColors';

const InterviewOverviewSkeleton = () => {
  const colors = useThemeColors();

  return (
    <View style={{
      paddingVertical: 32,
      backgroundColor: colors.background,
    }}>
      <View className="px-6 mb-4">
        {/* Title */}
        <Skeleton className="h-7 w-48 mb-2" />

        {/* Subtitle */}
        <Skeleton className="h-4 w-64 mb-3" />

        {/* Insights box */}
        <View style={{
          padding: 12,
          backgroundColor: colors.backgroundSecondary,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: colors.border,
        }}>
          <Skeleton style={{ height: 16, width: '100%', marginBottom: 4 }} />
          <Skeleton style={{ height: 16, width: '75%' }} />
        </View>
      </View>

      {/* Stats Cards */}
      <View className="flex-row px-6">
        {[1, 2, 3].map((index) => (
          <View key={index} className="mr-4">
            <View
              style={{
                borderRadius: 16,
                padding: 20,
                minWidth: 160,
                minHeight: 170,
                backgroundColor: colors.surface,
                borderWidth: 1,
                borderColor: colors.border,
                justifyContent: 'space-between',
              }}
            >
              {/* Header Section */}
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: 16,
              }}>
                <Skeleton style={{ width: 48, height: 48, borderRadius: 24 }} />
                <Skeleton style={{ width: 8, height: 8, borderRadius: 4 }} />
              </View>

              {/* Main Content */}
              <View style={{
                flex: 1,
                justifyContent: 'center',
                marginBottom: 16,
              }}>
                <Skeleton style={{ height: 16, width: 96, marginBottom: 4 }} />
                <Skeleton style={{ height: 32, width: 64 }} />
              </View>

              {/* Footer */}
              <Skeleton style={{ height: 24, width: 64, borderRadius: 12 }} />
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default InterviewOverviewSkeleton;
