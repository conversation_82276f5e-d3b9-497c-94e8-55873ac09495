import { Text, TouchableOpacity, View, StatusBar } from 'react-native';
import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft, Sparkles } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';

const HeaderCreateInterviewQuestion = () => {
  const navigation = useNavigation();

  return (
    <>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ paddingTop: 30, paddingBottom: 20 }}
      >
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="absolute left-2 bg-white/20 rounded-full p-[8px] items-center justify-center z-10"
          style={{ top: 40 }}
          activeOpacity={0.8}
          hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
        >
          <View pointerEvents="none">
            <ArrowLeft size={20} color="white" />
          </View>
        </TouchableOpacity>

        <View className="flex-row justify-center items-center my-5">
          <View>
            <Text className="text-white text-2xl font-bold">
              Generate Mock Interview
            </Text>
            <Text className="text-white/80 text-base mt-1">
              Create personalized interview questions
            </Text>
          </View>
        </View>
      </LinearGradient>
    </>
  );
};

export default HeaderCreateInterviewQuestion;
