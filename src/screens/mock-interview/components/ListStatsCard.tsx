import React from 'react';
import { View, Text } from 'react-native';
import {
  Clock,
  ListCheckIcon,
  ShieldQuestion,
  Star,
} from 'lucide-react-native';
import StatsCard from './StatsCard';

interface StatsCardsProps {
  jobPosition: string;
  totalQuestions: number;
  interviewTypes: string[];
  interviewDuration: string;
}

const StatsCards: React.FC<StatsCardsProps> = ({
  jobPosition,
  totalQuestions,
  interviewTypes,
  interviewDuration,
}) => {
  const formatInterviewTypes = (types: string[]) => {
    return types.join(', ');
  };

  const formatDuration = (duration: string) => {
    const minutes = parseInt(duration.replace(/\D/g, ''));
    return `${minutes} minutes total`;
  };

  return (
    <View className="flex-row flex-wrap p-3 gap-3 w-full justify-center">
      {/* Role Card */}
      <StatsCard
        icon={<Star size={16} color="white" />}
        label="ROLE"
        value={jobPosition}
        backgroundColor="bg-blue-500"
      />

      {/* Questions Card */}
      <StatsCard
        icon={<ShieldQuestion size={16} color="white" />}
        label="QUESTIONS"
        value={`${totalQuestions} Questions`}
        backgroundColor="bg-green-500"
      />

      <StatsCard
        icon={<ListCheckIcon size={16} color="white" />}
        label="TYPES"
        value={formatInterviewTypes(interviewTypes).toUpperCase()}
        backgroundColor="bg-purple-500"
      />

      {/* Time Card */}
      <StatsCard
        icon={<Clock size={16} color="white" />}
        label="TIME"
        value={formatDuration(interviewDuration)}
        backgroundColor="bg-[#FFC6A8]"
      />
    </View>
  );
};

export default StatsCards;
