import React, { useMemo, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import InterviewQuestionItem from './InterviewQuestionItem';
import Pagination from '@/components/ui/Pagination';
import { Ionicons } from '@expo/vector-icons';
import { Plus } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { SwipeableItem, SearchBar } from '@/components/ui';
import { useInterviewQuestion } from '../hooks/useInterviewQuestion';
import { useThemeColors } from '@/hooks/useThemeColors';

interface InterviewQuestionListProps {
  interviewQuestions: InterviewQuestionSessionResponse[];
  loading: boolean;
  itemsPerPage?: number;
  headerComponent?: React.ReactElement;
}

const InterviewQuestionList: React.FC<InterviewQuestionListProps> = ({
  interviewQuestions,
  loading,
  itemsPerPage = 5,
  headerComponent,
}) => {
  const themeColors = useThemeColors();
  const navigation =
    useNavigation<NativeStackNavigationProp<MockInterviewStackParamList>>();
  const {
    handleCreateNew,
    handleDelete,
    handleItemPress,
    currentPage,
    setCurrentPage,
    searchQuery,
    setSearchQuery,
    handlePageChange,
    deletingItemId,
  } = useInterviewQuestion({
    navigation,
  });

  // Filter interview questions based on search query
  const filteredInterviewQuestions = useMemo(() => {
    if (!searchQuery.trim()) {
      return interviewQuestions;
    }

    const query = searchQuery.toLowerCase().trim();
    return interviewQuestions.filter((item) => {
      return (
        item.jobPosition.toLowerCase().includes(query) ||
        item.interviewDuration.toLowerCase().includes(query) ||
        item.interviewType.some((type) => type.toLowerCase().includes(query)) ||
        item.totalQuestion.toString().includes(query)
      );
    });
  }, [interviewQuestions, searchQuery]);

  // Calculate pagination for filtered data
  const totalPages = Math.ceil(
    filteredInterviewQuestions.length / itemsPerPage,
  );
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredInterviewQuestions.slice(startIndex, endIndex);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const renderItem = ({ item }: { item: InterviewQuestionSessionResponse }) => {
    const isDeleting = deletingItemId === item._id;

    return (
      <SwipeableItem
        onDelete={() => handleDelete(item)}
        isDeleting={isDeleting}
        itemId={item._id}
        deleteTitle="Delete interview question"
        deleteMessage={`Are you sure you want to delete the interview question "${item.jobPosition}"?\n\nThis action cannot be undone and will permanently delete all related data.`}
      >
        <InterviewQuestionItem
          item={item}
          onPress={handleItemPress}
          onDelete={() => { }}
          isLoading={false}
        />
      </SwipeableItem>
    );
  };

  const renderHeader = () => {
    return (
      <>
        {headerComponent}
        {/* List Header */}
        <View
          style={{
            paddingHorizontal: 24,
            paddingVertical: 16,
            borderBottomWidth: 1,
            backgroundColor: themeColors.surface,
            borderBottomColor: themeColors.borderLight
          }}
        >
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: themeColors.text,
              }}>
                Interview question list
              </Text>
              <Text style={{
                fontSize: 14,
                marginTop: 4,
                color: '#FFFFFF', // Force white color for testing
              }}>
                {filteredInterviewQuestions.length} interview questions
                {searchQuery && ` (from ${interviewQuestions.length} total)`} •
                Page {currentPage}/{totalPages}
              </Text>
            </View>
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 12,
            }}>
              <View
                style={{
                  paddingHorizontal: 12,
                  paddingVertical: 6,
                  borderRadius: 20,
                  backgroundColor: themeColors.primaryLight,
                }}
              >
                <Text style={{
                  fontWeight: '600',
                  fontSize: 14,
                  color: themeColors.text,
                }}>
                  {currentItems.length}/{filteredInterviewQuestions.length}
                </Text>
              </View>
              <TouchableOpacity
                onPress={handleCreateNew}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: themeColors.primary,
                  shadowColor: themeColors.primary,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.2,
                  shadowRadius: 4,
                  elevation: 3,
                }}
              >
                <Plus size={20} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Search Bar */}
        <View style={{
          paddingHorizontal: 24,
          paddingVertical: 16,
          backgroundColor: themeColors.surface,
        }}>
          <SearchBar
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            placeholder="Search by position, duration, interview type..."
            resultCount={
              searchQuery.length > 0
                ? filteredInterviewQuestions.length
                : undefined
            }
            resultLabel="interview"
          />
        </View>
      </>
    );
  };

  const renderEmptyState = () => (
    <View className="flex-1 justify-center items-center px-6 py-12">
      <View className="bg-white rounded-2xl p-8 items-center border border-gray-100 w-full max-w-sm">
        <View className="w-16 h-16 bg-indigo-100 rounded-full justify-center items-center mb-4">
          <Ionicons name="document-text-outline" size={32} color="#6366F1" />
        </View>
        <Text className="text-gray-900 font-bold text-xl mb-2">
          {searchQuery
            ? 'No results found'
            : 'No interview questions created yet'}
        </Text>
        <Text className="text-gray-500 text-center mb-6 leading-6">
          {searchQuery
            ? 'Try adjusting the search or remove filters to see all interview questions'
            : 'Create your first interview question to start practicing and improving your interview skills'}
        </Text>
        <TouchableOpacity
          onPress={searchQuery ? () => setSearchQuery('') : handleCreateNew}
          className="bg-indigo-600 rounded-xl py-3 px-6 flex-row items-center"
          style={{
            shadowColor: '#6366F1',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.2,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <Plus size={20} color="white" />
          <Text className="text-white font-semibold ml-2">
            {searchQuery ? 'Remove search' : 'Create first interview question'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderFooter = () => {
    if (totalPages <= 1) return null;

    return (
      <View
        className="border-t"
        style={{
          backgroundColor: themeColors.surface,
          borderTopColor: themeColors.borderLight
        }}
      >
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          disabled={loading || deletingItemId !== null}
        />
      </View>
    );
  };

  if (filteredInterviewQuestions.length === 0) {
    return (
      <ScrollView className="flex-1" style={{ backgroundColor: themeColors.backgroundSecondary }}>
        {renderHeader()}
        {renderEmptyState()}
      </ScrollView>
    );
  }

  return (
    <FlatList
      data={currentItems}
      renderItem={renderItem}
      keyExtractor={(item) => item._id}
      showsVerticalScrollIndicator={false}
      ListHeaderComponent={renderHeader}
      ListFooterComponent={renderFooter}
      contentContainerStyle={{
        backgroundColor: themeColors.backgroundSecondary,
        paddingTop: 0,
        paddingBottom: 16,
        flexGrow: 1,
      }}
      style={{
        backgroundColor: themeColors.backgroundSecondary,
        flex: 1,
      }}
    />
  );
};

export default InterviewQuestionList;
