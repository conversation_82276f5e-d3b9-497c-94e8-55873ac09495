import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Clock, Users, ChevronRight, Briefcase } from 'lucide-react-native';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import { InterviewTypeEnum } from '@/common/enums/interviewQuestion.enum';
import { formatTimeAgo } from '@/utils/timer';
import { useThemeColors } from '@/hooks/useThemeColors';

interface InterviewQuestionItemProps {
  item: InterviewQuestionSessionResponse;
  onPress: (item: InterviewQuestionSessionResponse) => void;
  onDelete: (item: InterviewQuestionSessionResponse) => void;
  isLoading?: boolean;
}

const InterviewQuestionItem = ({
  item,
  onPress,
  onDelete,
  isLoading = false,
}: InterviewQuestionItemProps) => {
  const themeColors = useThemeColors();

  const getInterviewTypeColors = (types: InterviewTypeEnum[]) => {
    const colorMap: { [key in InterviewTypeEnum]: string } = {
      [InterviewTypeEnum.TECHNICAL]: '#3B82F6',
      [InterviewTypeEnum.BEHAVIORAL]: '#10B981',
      [InterviewTypeEnum.EXPERIENCED]: '#F59E0B',
      [InterviewTypeEnum.PROBLEM_SOLVING]: '#8B5CF6',
      [InterviewTypeEnum.LEADERSHIP]: '#EF4444',
    };

    return types.map((type) => colorMap[type] || '#6B7280');
  };

  const colors = getInterviewTypeColors(item.interviewType);
  const primaryColor = colors[0] || '#6B7280';

  const handlePress = () => {
    if (!isLoading) {
      onPress(item);
    }
  };

  return (
    <View className="px-4 mb-4" style={{ backgroundColor: themeColors.backgroundSecondary }}>
      <TouchableOpacity
        onPress={handlePress}
        activeOpacity={0.7}
        disabled={isLoading}
        className="rounded-xl overflow-hidden border p-3"
        style={{
          backgroundColor: themeColors.surface,
          borderColor: themeColors.borderLight,
          shadowColor: themeColors.text,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          elevation: 6,
        }}
      >
        <LinearGradient
          colors={[primaryColor + '08', primaryColor + '04']}
          className="p-5"
        >
          {/* Header */}
          <View className="flex-row justify-between items-start mb-4">
            <View className="flex-1">
              <View className="flex-row items-center mb-2">
                <View
                  className="w-8 h-8 rounded-full mr-3 justify-center items-center"
                  style={{ backgroundColor: primaryColor + '20' }}
                >
                  <Briefcase size={16} color={primaryColor} />
                </View>
                <Text className="text-lg font-bold flex-1" style={{ color: themeColors.text }}>
                  {item.jobPosition}
                </Text>
              </View>
              <Text className="text-sm ml-11" style={{ color: themeColors.textSecondary }}>
                {formatTimeAgo(item.createdAt)}
              </Text>
            </View>
            <View className="flex-row items-center">
              {isLoading ? (
                <ActivityIndicator size="small" color={primaryColor} />
              ) : (
                <>
                  <Text className="text-sm mr-1" style={{ color: themeColors.textSecondary }}>
                    View details
                  </Text>
                  <ChevronRight size={16} color={themeColors.textMuted} />
                </>
              )}
            </View>
          </View>

          {/* Stats */}
          <View className="flex-row items-center mb-4 ml-11">
            <View className="flex-row items-center mr-6">
              <Clock size={16} color={themeColors.textMuted} />
              <Text className="text-sm ml-2 font-medium" style={{ color: themeColors.textSecondary }}>
                {item.interviewDuration}
              </Text>
            </View>
            <View className="flex-row items-center">
              <Users size={16} color={themeColors.textMuted} />
              <Text className="text-sm ml-2 font-medium" style={{ color: themeColors.textSecondary }}>
                {item.totalQuestion} questions
              </Text>
            </View>
          </View>

          {/* Interview Types */}
          <View className="flex-row flex-wrap ml-11">
            {item.interviewType.slice(0, 3).map((type, index) => (
              <View
                key={index}
                className="px-3 py-1.5 rounded-full mr-2 mb-2"
                style={{
                  backgroundColor: colors[index] + '15',
                  borderWidth: 1,
                  borderColor: colors[index] + '30',
                }}
              >
                <Text
                  className="text-xs font-semibold"
                  style={{ color: colors[index] }}
                >
                  {type}
                </Text>
              </View>
            ))}
            {item.interviewType.length > 3 && (
              <View
                className="px-3 py-1.5 rounded-full mr-2 mb-2"
                style={{
                  backgroundColor: themeColors.surfaceSecondary,
                  borderWidth: 1,
                  borderColor: themeColors.border
                }}
              >
                <Text
                  className="text-xs font-semibold"
                  style={{ color: themeColors.text }}
                >
                  +{item.interviewType.length - 3}
                </Text>
              </View>
            )}
          </View>

          {/* Progress indicator */}
          <View className="mt-4 pt-4 border-t" style={{ borderTopColor: themeColors.borderLight }}>
            <View className="flex-row justify-between items-center">
              <Text className="text-xs font-medium" style={{ color: themeColors.textMuted }}>
                ID: #{item._id.slice(-8).toUpperCase()}
              </Text>
              <View className="flex-row items-center">
                <View
                  className="w-2 h-2 rounded-full mr-2"
                  style={{ backgroundColor: themeColors.success }}
                />
                <Text
                  className="text-xs font-medium"
                  style={{ color: themeColors.success }}
                >
                  Ready
                </Text>
              </View>
            </View>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );
};

export default InterviewQuestionItem;
