import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Skeleton } from '@/components/ui/Skeleton';

const InterviewDetailHeaderSkeleton = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      className="px-6 pt-16 pb-8"
      style={{
        paddingTop: insets.top,
      }}
    >
      {/* Back Button */}
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        className="absolute left-2 bg-white/20 rounded-full p-[8px] items-center justify-center z-10"
        style={{ top: insets.top + 8 }}
        activeOpacity={0.8}
        hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
      >
        <View pointerEvents="none">
          <ArrowLeft size={20} color="white" />
        </View>
      </TouchableOpacity>

      {/* Header Title Skeleton */}
      <View className="flex-col items-center justify-center mt-4">
        <View className="flex-row justify-center items-center mb-2">
          <Skeleton className="h-8 w-60 bg-white/20" />
        </View>
        <Skeleton className="h-4 w-80 bg-white/20" />
      </View>

      {/* Stats Cards Skeleton */}
      <View className="flex-row flex-wrap p-3 gap-3 w-full justify-center">
        {/* Role Card */}
        <View className="bg-blue-500 rounded-lg p-3 min-w-[120px] items-center">
          <Skeleton className="h-4 w-8 mb-1 bg-white/20" />
          <Skeleton className="h-3 w-16 bg-white/20" />
        </View>

        {/* Questions Card */}
        <View className="bg-green-500 rounded-lg p-3 min-w-[120px] items-center">
          <Skeleton className="h-4 w-8 mb-1 bg-white/20" />
          <Skeleton className="h-3 w-20 bg-white/20" />
        </View>

        {/* Types Card */}
        <View className="bg-purple-500 rounded-lg p-3 min-w-[120px] items-center">
          <Skeleton className="h-4 w-8 mb-1 bg-white/20" />
          <Skeleton className="h-3 w-16 bg-white/20" />
        </View>

        {/* Time Card */}
        <View className="bg-[#FFC6A8] rounded-lg p-3 min-w-[120px] items-center">
          <Skeleton className="h-4 w-8 mb-1 bg-white/20" />
          <Skeleton className="h-3 w-18 bg-white/20" />
        </View>
      </View>

      {/* Action Button Skeleton */}
      <View className="flex items-center justify-center p-5">
        <Skeleton className="h-12 w-80 rounded-xl bg-white/20" />
      </View>
    </LinearGradient>
  );
};

export default InterviewDetailHeaderSkeleton;
