import React, { useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Clock, Play } from 'lucide-react-native';

interface ActionButtonsProps {
  onStartInterview: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ onStartInterview }) => {
  const animatedOpacity = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedOpacity, {
          toValue: 0.7,
          duration: 700,
          useNativeDriver: true,
        }),
        Animated.timing(animatedOpacity, {
          toValue: 1,
          duration: 700,
          useNativeDriver: true,
        }),
      ]),
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.delay(1000),
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(rotateAnim, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, []);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View className="flex items-center justify-center p-5 ">
      <TouchableOpacity
        onPress={onStartInterview}
        style={{
          shadowColor: '#ff6b6b',
          borderRadius: 12,
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 6,
        }}
        activeOpacity={0.8}
      >
        <Animated.View
          style={{
            opacity: animatedOpacity,
            borderRadius: 12,
            overflow: 'hidden',
          }}
        >
          <LinearGradient
            colors={['#ff6b6b', '#ff6a00']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={{
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 12,
              flexDirection: 'row',
              alignItems: 'center',
              borderWidth: 1,
              borderColor: 'orange',
            }}
          >
            <Animated.View style={{ transform: [{ rotate: spin }] }}>
              <Play size={30} color="white" fill="white" />
            </Animated.View>
            <Text className="text-white font-semibold ml-2 text-2xl">
              Press to start Interview
            </Text>
          </LinearGradient>
        </Animated.View>
      </TouchableOpacity>
    </View>
  );
};

export default ActionButtons;
