import React, { useEffect } from 'react';
import { View } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { useThemeColors } from '@/hooks/useThemeColors';

const RoadmapSkeleton: React.FC = () => {
  const colors = useThemeColors();
  const opacity = useSharedValue(0.3);

  useEffect(() => {
    opacity.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 1000 }),
        withTiming(0.3, { duration: 1000 })
      ),
      -1,
      false
    );
  }, [opacity]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const SkeletonBox = ({ style }: { style?: any }) => (
    <Animated.View
      style={[
        {
          backgroundColor: colors.backgroundSecondary,
          borderRadius: 6,
        },
        animatedStyle,
        style,
      ]}
    />
  );

  return (
    <View style={{
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: colors.border,
    }}>
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
      }}>
        <View style={{ flex: 1 }}>
          <SkeletonBox style={{
            height: 24,
            marginBottom: 8,
          }} />
          <SkeletonBox style={{
            height: 16,
            width: 80,
          }} />
        </View>
        <SkeletonBox style={{
          width: 24,
          height: 24,
        }} />
      </View>

      <View style={{
        marginBottom: 12,
        gap: 8,
      }}>
        <SkeletonBox style={{ height: 16 }} />
        <SkeletonBox style={{ height: 16, width: '75%' }} />
        <SkeletonBox style={{ height: 16, width: '50%' }} />
      </View>

      <View style={{
        gap: 8,
        marginBottom: 16,
      }}>
        <SkeletonBox style={{ height: 12, width: '33%' }} />
        <SkeletonBox style={{ height: 12, width: '25%' }} />
        <SkeletonBox style={{ height: 12, width: '50%' }} />
      </View>

      <View style={{
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: colors.border,
      }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}>
          <SkeletonBox style={{ height: 12, width: '33%' }} />
          <SkeletonBox style={{ height: 16, width: '25%' }} />
        </View>
      </View>
    </View>
  );
};

export default RoadmapSkeleton;
