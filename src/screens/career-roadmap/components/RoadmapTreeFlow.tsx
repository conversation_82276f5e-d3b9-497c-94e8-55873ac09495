import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Linking,
  Alert,
} from 'react-native';
import Animated, {
  useAnimatedReaction,
  runOnJS,
} from 'react-native-reanimated';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import Svg, {
  Circle,
  Text as SvgText,
  Rect,
  Defs,
  LinearGradient,
  Stop,
  Path,
  G,
} from 'react-native-svg';
import { Ionicons } from '@expo/vector-icons';
import { RoadmapNode, RoadmapEdge } from '@/common/types/career-roadmap';
import { useZoomPan } from '../hooks';
import { useThemeColors } from '@/hooks/useThemeColors';

interface RoadmapTreeFlowProps {
  nodes: RoadmapNode[];
  edges?: RoadmapEdge[];
  initialEdges?: RoadmapEdge[];
  title?: string;
}

interface TreeNode {
  id: string;
  data: RoadmapNode;
  children: TreeNode[];
  level: number;
  x: number;
  y: number;
}

const { width: screenWidth } = Dimensions.get('window');

const RoadmapTreeFlow: React.FC<RoadmapTreeFlowProps> = ({
  nodes,
  edges,
  initialEdges,
  title = 'Learning Roadmap - Tree View',
}) => {
  const colors = useThemeColors();
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [isGesturing, setIsGesturing] = useState(false);
  const {
    resetZoom,
    combinedGesture,
    animatedStyle,
    isGesturing: isGesturingShared,
  } = useZoomPan();

  // Watch for gesture changes
  useAnimatedReaction(
    () => isGesturingShared.value,
    (current) => {
      runOnJS(setIsGesturing)(current);
    },
  );

  const handleNodePress = useCallback(
    (nodeId: string) => {
      // Use requestAnimationFrame to ensure this runs outside of the gesture context
      requestAnimationFrame(() => {
        if (!isGesturing) {
          setSelectedNodeId((prev) => (prev === nodeId ? null : nodeId));
        }
      });
    },
    [isGesturing],
  );

  const handleViewDetails = async (link: string) => {
    try {
      const canOpen = await Linking.canOpenURL(link);
      if (canOpen) {
        await Linking.openURL(link);
      } else {
        Alert.alert(
          'Cannot open link',
          'This link cannot be opened on your device.',
          [{ text: 'OK' }],
        );
      }
    } catch (error) {
      Alert.alert(
        'Error',
        'An error occurred while opening the link. Please try again.',
        [{ text: 'OK' }],
      );
    }
  };

  if (!nodes || nodes.length === 0) {
    return (
      <View
        style={{
          backgroundColor: colors.surface,
          borderRadius: 12,
          padding: 24,
          alignItems: 'center',
        }}
      >
        <Ionicons
          name="git-branch-outline"
          size={48}
          color={colors.textMuted}
        />
        <Text
          style={{
            color: colors.textMuted,
            textAlign: 'center',
            marginTop: 8,
          }}
        >
          No roadmap data available
        </Text>
      </View>
    );
  }

  const buildTreeStructure = (): TreeNode[] => {
    const connectionEdges = (() => {
      if (initialEdges && initialEdges.length > 0) {
        return initialEdges;
      }
      if (edges && edges.length > 0) {
        return edges;
      }
      return nodes.slice(0, -1).map((node, index) => ({
        id: `edge-${node.id}-${nodes[index + 1].id}`,
        source: node.id,
        target: nodes[index + 1].id,
      }));
    })();

    const nodeMap = new Map<string, RoadmapNode>();
    nodes.forEach((node) => nodeMap.set(node.id, node));

    const hasParent = new Set<string>();
    connectionEdges.forEach((edge) => hasParent.add(edge.target));
    const rootNodes = nodes.filter((node) => !hasParent.has(node.id));

    const buildNode = (nodeId: string, level: number): TreeNode => {
      const node = nodeMap.get(nodeId)!;
      const children = connectionEdges
        .filter((edge) => edge.source === nodeId)
        .map((edge) => buildNode(edge.target, level + 1));

      return {
        id: nodeId,
        data: node,
        children,
        level,
        x: 0,
        y: 0,
      };
    };

    return rootNodes.map((node) => buildNode(node.id, 0));
  };

  const calculateTreeLayout = (trees: TreeNode[]): TreeNode[] => {
    const nodeWidth = 180;
    const levelHeight = 140;
    const nodeSpacing = 40;

    let currentY = 50;

    const layoutTree = (tree: TreeNode, startX: number = 50): number => {
      if (tree.children.length === 0) {
        tree.x = startX;
        tree.y = currentY + tree.level * levelHeight;
        return nodeWidth;
      }

      let childrenWidth = 0;
      let childX = startX;

      tree.children.forEach((child, index) => {
        if (index > 0) childX += nodeSpacing;
        const childWidth = layoutTree(child, childX);
        childX += childWidth;
        childrenWidth += childWidth + (index > 0 ? nodeSpacing : 0);
      });

      const firstChild = tree.children[0];
      const lastChild = tree.children[tree.children.length - 1];
      tree.x = (firstChild.x + lastChild.x + nodeWidth) / 2 - nodeWidth / 2;
      tree.y = currentY + tree.level * levelHeight;

      return Math.max(nodeWidth, childrenWidth);
    };

    trees.forEach((tree, index) => {
      if (index > 0) currentY += 50;
      layoutTree(tree);
    });

    return trees;
  };

  const trees = calculateTreeLayout(buildTreeStructure());

  const flattenTree = (tree: TreeNode): TreeNode[] => {
    return [tree, ...tree.children.flatMap((child) => flattenTree(child))];
  };

  const allNodes = trees.flatMap((tree) => flattenTree(tree));

  const maxX = Math.max(...allNodes.map((node) => node.x)) + 180 + 50;
  const maxY = Math.max(...allNodes.map((node) => node.y)) + 100 + 50;
  const svgWidth = Math.max(screenWidth, maxX);
  const svgHeight = Math.max(400, maxY);

  const getNodeStyle = (node: TreeNode) => {
    const level = node.level;
    const nodeType = node.data.type.toLowerCase();

    if (nodeType === 'start') {
      return {
        fill: 'url(#startGradient)',
        stroke: '#10B981',
        strokeWidth: 3,
        textColor: '#FFFFFF',
      };
    } else if (nodeType === 'end') {
      return {
        fill: 'url(#endGradient)',
        stroke: '#EF4444',
        strokeWidth: 3,
        textColor: '#FFFFFF',
      };
    } else {
      const gradients = [
        'url(#level0Gradient)',
        'url(#level1Gradient)',
        'url(#level2Gradient)',
        'url(#level3Gradient)',
      ];
      const strokes = ['#3B82F6', '#7C3AED', '#E11D48', '#F59E0B'];

      return {
        fill: gradients[level % gradients.length],
        stroke: strokes[level % strokes.length],
        strokeWidth: 2,
        textColor: '#FFFFFF',
      };
    }
  };

  const wrapText = (text: string, maxLength: number = 20) => {
    if (text.length <= maxLength) return [text];
    const words = text.split(' ');
    const lines = [];
    let currentLine = '';

    for (const word of words) {
      if ((currentLine + word).length <= maxLength) {
        currentLine += (currentLine ? ' ' : '') + word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
      }
    }
    if (currentLine) lines.push(currentLine);
    return lines.slice(0, 3); // Max 3 lines
  };

  const toggleNodeExpansion = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  return (
    <View
      style={{
        backgroundColor: colors.surface,
        borderRadius: 12,
        overflow: 'hidden',
      }}
    >
      {title && (
        <View
          style={{
            padding: 16,
            backgroundColor: colors.backgroundSecondary,
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: colors.text,
                  textAlign: 'center',
                }}
              >
                {title}
              </Text>
              <Text
                style={{
                  fontSize: 12,
                  color: colors.textSecondary,
                  textAlign: 'center',
                  marginTop: 4,
                }}
              >
                Tree structure • {nodes.length} nodes • {trees.length} root(s)
              </Text>
            </View>
            <TouchableOpacity
              onPress={resetZoom}
              style={{
                marginLeft: 12,
                backgroundColor: colors.surface,
                padding: 8,
                borderRadius: 8,
                shadowColor: colors.shadow,
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 2,
                elevation: 1,
              }}
            >
              <Ionicons name="refresh" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
      )}
      <GestureDetector gesture={combinedGesture}>
        <Animated.View style={{ flex: 1 }}>
          <Animated.View
            style={{ flex: 1, backgroundColor: colors.backgroundSecondary }}
          >
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ flex: 1 }}
              scrollEnabled={false}
            >
              <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ padding: 20 }}
                scrollEnabled={false}
              >
                <Animated.View style={animatedStyle}>
                  <Svg width={svgWidth} height={svgHeight}>
                    <Defs>
                      <LinearGradient
                        id="startGradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <Stop offset="0%" stopColor="#34D399" />
                        <Stop offset="100%" stopColor="#10B981" />
                      </LinearGradient>
                      <LinearGradient
                        id="endGradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <Stop offset="0%" stopColor="#F87171" />
                        <Stop offset="100%" stopColor="#EF4444" />
                      </LinearGradient>
                      <LinearGradient
                        id="level0Gradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <Stop offset="0%" stopColor="#60A5FA" />
                        <Stop offset="100%" stopColor="#3B82F6" />
                      </LinearGradient>
                      <LinearGradient
                        id="level1Gradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <Stop offset="0%" stopColor="#A78BFA" />
                        <Stop offset="100%" stopColor="#7C3AED" />
                      </LinearGradient>
                      <LinearGradient
                        id="level2Gradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <Stop offset="0%" stopColor="#FB7185" />
                        <Stop offset="100%" stopColor="#E11D48" />
                      </LinearGradient>
                      <LinearGradient
                        id="level3Gradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <Stop offset="0%" stopColor="#FBBF24" />
                        <Stop offset="100%" stopColor="#F59E0B" />
                      </LinearGradient>
                    </Defs>
                    {allNodes.map((node, nodeIndex) =>
                      node.children.map((child, childIndex) => {
                        const parentX = node.x + 90;
                        const parentY = node.y + 100;
                        const childX = child.x + 90;
                        const childY = child.y;

                        return (
                          <G
                            key={`connection-${nodeIndex}-${childIndex}-${node.id}-${child.id}`}
                          >
                            <Path
                              d={`M ${parentX} ${parentY} Q ${parentX} ${parentY + 30} ${childX} ${childY - 10}`}
                              stroke="#94A3B8"
                              strokeWidth="2"
                              fill="none"
                              strokeDasharray="3,3"
                            />
                            <Circle
                              cx={childX}
                              cy={childY - 5}
                              r="3"
                              fill="#94A3B8"
                            />
                          </G>
                        );
                      }),
                    )}
                    {allNodes.map((treeNode, index) => {
                      const nodeStyle = getNodeStyle(treeNode);
                      const textLines = wrapText(treeNode.data.data.title);
                      const isSelected = selectedNodeId === treeNode.id;
                      const hasChildren = treeNode.children.length > 0;
                      return (
                        <G key={`node-${index}-${treeNode.id}`}>
                          <Rect
                            x={treeNode.x + 3}
                            y={treeNode.y + 3}
                            width="180"
                            height="100"
                            rx="15"
                            ry="15"
                            fill="rgba(0,0,0,0.1)"
                          />
                          <Rect
                            x={treeNode.x}
                            y={treeNode.y}
                            width="180"
                            height="100"
                            rx="15"
                            ry="15"
                            fill={nodeStyle.fill}
                            stroke={isSelected ? '#3B82F6' : nodeStyle.stroke}
                            strokeWidth={isSelected ? 4 : nodeStyle.strokeWidth}
                          />
                          {/* Invisible touch area for better touch handling */}
                          <Rect
                            x={treeNode.x}
                            y={treeNode.y}
                            width="180"
                            height="100"
                            rx="15"
                            ry="15"
                            fill="transparent"
                            onPress={() => handleNodePress(treeNode.id)}
                          />
                          <Circle
                            cx={treeNode.x + 20}
                            cy={treeNode.y + 20}
                            r="12"
                            fill="rgba(255,255,255,0.9)"
                            stroke={nodeStyle.stroke}
                            strokeWidth="2"
                          />
                          <SvgText
                            x={treeNode.x + 18}
                            y={treeNode.y + 20}
                            textAnchor="middle"
                            alignmentBaseline="central"
                            fontSize="10"
                            fontWeight="bold"
                            fill={nodeStyle.stroke}
                          >
                            L{treeNode.level}
                          </SvgText>
                          {textLines.map((line, lineIndex) => (
                            <SvgText
                              key={`${treeNode.id}-line-${lineIndex}`}
                              x={treeNode.x + 90}
                              y={treeNode.y + 45 + lineIndex * 14}
                              textAnchor="middle"
                              fontSize="12"
                              fontWeight="600"
                              fill={nodeStyle.textColor}
                            >
                              {line}
                            </SvgText>
                          ))}
                          {treeNode.data.data.estimatedTime && (
                            <>
                              <Rect
                                x={treeNode.x + 120}
                                y={treeNode.y + 75}
                                width="55"
                                height="18"
                                rx="9"
                                ry="9"
                                fill="rgba(255,255,255,0.9)"
                                stroke={nodeStyle.stroke}
                                strokeWidth="1"
                              />
                              <SvgText
                                x={treeNode.x + 147.5}
                                y={treeNode.y + 86}
                                textAnchor="middle"
                                fontSize="9"
                                fontWeight="500"
                                fill="#374151"
                              >
                                {treeNode.data.data.estimatedTime}
                              </SvgText>
                            </>
                          )}
                          {hasChildren && (
                            <Circle
                              cx={treeNode.x + 160}
                              cy={treeNode.y + 20}
                              r="8"
                              fill="rgba(255,255,255,0.9)"
                              stroke={nodeStyle.stroke}
                              strokeWidth="2"
                            />
                          )}
                          {isSelected && (
                            <Circle
                              cx={treeNode.x + 90}
                              cy={treeNode.y - 8}
                              r="5"
                              fill="#3B82F6"
                            />
                          )}
                        </G>
                      );
                    })}
                  </Svg>
                </Animated.View>
              </ScrollView>
            </ScrollView>
          </Animated.View>
        </Animated.View>
      </GestureDetector>
      {selectedNodeId && (
        <View className="bg-green-50 p-4 border-t border-green-200">
          {(() => {
            const selectedNode = allNodes.find((n) => n.id === selectedNodeId);
            if (!selectedNode) return null;

            return (
              <View>
                <View className="flex-row items-center justify-between mb-3">
                  <View className="flex-1">
                    <Text className="font-semibold text-green-900 text-base">
                      {selectedNode.data.data.title}
                    </Text>
                    <Text className="text-xs text-green-600 mt-1">
                      Level {selectedNode.level} •{selectedNode.children.length}
                      children
                    </Text>
                  </View>
                  <TouchableOpacity onPress={() => setSelectedNodeId(null)}>
                    <Ionicons name="close" size={22} color="#059669" />
                  </TouchableOpacity>
                </View>

                {selectedNode.data.data.description && (
                  <Text className="text-sm text-green-800 mb-3 leading-5">
                    {selectedNode.data.data.description}
                  </Text>
                )}

                <View className="flex-row items-center justify-between mb-3">
                  <View className="flex-row items-center"></View>
                  {selectedNode.data.data.estimatedTime && (
                    <View className="flex-row items-center">
                      <Ionicons name="time" size={14} color="#059669" />
                      <Text className="ml-1 text-xs text-green-700">
                        {selectedNode.data.data.estimatedTime}
                      </Text>
                    </View>
                  )}
                </View>

                {selectedNode.data.data.link && (
                  <TouchableOpacity
                    onPress={() =>
                      handleViewDetails(selectedNode.data.data.link!)
                    }
                    className="bg-green-600 rounded-lg py-3 px-4 flex-row items-center justify-center mt-2"
                    activeOpacity={0.8}
                  >
                    <Ionicons name="open-outline" size={18} color="white" />
                    <Text className="text-white font-semibold ml-2">
                      View Details
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            );
          })()}
        </View>
      )}
      <View className="bg-gray-50 p-3 border-t border-gray-100">
        <Text className="text-xs text-gray-600 text-center">
          Tree view • {Math.max(...allNodes.map((n) => n.level)) + 1} levels •
          AI LearnVox
        </Text>
      </View>
    </View>
  );
};

export default RoadmapTreeFlow;
