import React from 'react';
import { View } from 'react-native';
import { RoadmapNode, RoadmapEdge } from '@/common/types/career-roadmap';
import RoadmapTreeFlow from './RoadmapTreeFlow';

interface RoadmapDisplayProps {
  nodes?: RoadmapNode[];
  edges?: RoadmapEdge[];
  initialEdges?: RoadmapEdge[];
  title?: string;
  height?: number;
}

const RoadmapDisplay: React.FC<RoadmapDisplayProps> = ({
  nodes,
  edges,
  initialEdges,
  title = 'Learning Roadmap',
  height = 500,
}) => {
  const displayNodes: RoadmapNode[] = nodes && nodes.length > 0 ? nodes : [];

  const displayEdges = (() => {
    if (initialEdges && initialEdges.length > 0) return initialEdges;
    if (edges && edges.length > 0) return edges;
    return undefined;
  })();

  return (
    <View style={{ height }}>
      <RoadmapTreeFlow
        nodes={displayNodes}
        edges={edges}
        initialEdges={displayEdges}
        title={title}
      />
    </View>
  );
};

export default RoadmapDisplay;
