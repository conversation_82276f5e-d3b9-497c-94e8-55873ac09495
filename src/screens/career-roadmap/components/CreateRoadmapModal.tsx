import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Animated,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useCreateRoadmap, useDocumentPicker, useTabAnimation } from '../hooks';
import { useThemeColors } from '@/hooks/useThemeColors';

interface CreateRoadmapModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateRoadmapModal: React.FC<CreateRoadmapModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const colors = useThemeColors();
  const { activeTab, slideAnim, tabWidth, setActiveTab } = useTabAnimation();
  const { pickDocument } = useDocumentPicker();
  const {
    loading,
    roleDescription,
    roleCvFile,
    jdCvFile,
    jdFile,
    setRoleDescription,
    setFile,
    resetForm,
    handleRoleSubmit,
    handleJdSubmit,
  } = useCreateRoadmap();

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleDocumentPick = (type: 'cv' | 'jd', tab: 'role' | 'jd') => {
    pickDocument(type, (file) => {
      setFile(type, tab, file);
    });
  };

  const handleSubmit = async () => {
    const success =
      activeTab === 'role'
        ? await handleRoleSubmit(onSuccess)
        : await handleJdSubmit(onSuccess);

    if (success) {
      handleClose();
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: 20,
          backgroundColor: colors.surface,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.05,
          shadowRadius: 4,
          elevation: 2,
        }}>
          <TouchableOpacity
            onPress={handleClose}
            style={{
              padding: 8,
              backgroundColor: colors.backgroundSecondary,
              borderRadius: 8,
            }}
          >
            <Ionicons name="close" size={22} color={colors.text} />
          </TouchableOpacity>
          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: colors.text
          }}>
            Create Career Roadmap
          </Text>
          <View style={{ width: 38 }} />
        </View>
        <View style={[styles.tabContainer, { backgroundColor: colors.backgroundSecondary }]}>
          <View style={styles.tabBackground}>
            <Animated.View
              style={[
                styles.tabIndicator,
                {
                  transform: [{ translateX: slideAnim }],
                  width: tabWidth,
                  backgroundColor: colors.surface,
                },
              ]}
            />

            <TouchableOpacity
              onPress={() => setActiveTab('role')}
              style={[styles.tabButton, { width: tabWidth }]}
              activeOpacity={0.7}
            >
              <View
                className={`p-1.5 rounded-full mr-2 ${activeTab === 'role' ? 'bg-blue-100' : 'bg-gray-100'}`}
              >
                <Ionicons
                  name="briefcase-outline"
                  size={16}
                  color={activeTab === 'role' ? '#2563EB' : '#6B7280'}
                />
              </View>
              <Text
                style={[
                  styles.tabText,
                  {
                    color: activeTab === 'role' ? colors.text : colors.textSecondary
                  }
                ]}
              >
                By Position
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setActiveTab('jd')}
              style={[styles.tabButton, { width: tabWidth }]}
              activeOpacity={0.7}
            >
              <View
                className={`p-1.5 rounded-full mr-2 ${activeTab === 'jd' ? 'bg-green-100' : 'bg-gray-100'}`}
              >
                <Ionicons
                  name="document-text-outline"
                  size={16}
                  color={activeTab === 'jd' ? '#059669' : '#6B7280'}
                />
              </View>
              <Text
                style={[
                  styles.tabText,
                  {
                    color: activeTab === 'jd' ? colors.text : colors.textSecondary
                  }
                ]}
              >
                By JD
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <ScrollView style={{ flex: 1, padding: 20 }} showsVerticalScrollIndicator={false}>
          {activeTab === 'role' ? (
            <View key={`${activeTab}-content`} style={styles.contentContainer}>
              <View
                key={`${activeTab}-description`}
                style={{
                  backgroundColor: colors.surface,
                  padding: 16,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: colors.border,
                  marginBottom: 16,
                  shadowColor: colors.shadow,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.05,
                  shadowRadius: 4,
                  elevation: 2,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <View style={{
                    backgroundColor: '#DBEAFE',
                    padding: 8,
                    borderRadius: 20,
                    marginRight: 12
                  }}>
                    <Ionicons name="create-outline" size={18} color="#2563EB" />
                  </View>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: colors.text
                  }}>
                    Desired Position Description
                  </Text>
                </View>
                <TextInput
                  key={`${activeTab}-text-input`}
                  style={[
                    styles.textInput,
                    {
                      borderWidth: 1,
                      borderColor: colors.border,
                      borderRadius: 8,
                      padding: 16,
                      color: colors.text,
                      backgroundColor: colors.backgroundSecondary,
                    }
                  ]}
                  placeholder="e.g., I want to become a Senior Frontend Developer with experience in React, TypeScript and Next.js..."
                  placeholderTextColor={colors.textMuted}
                  value={roleDescription}
                  onChangeText={setRoleDescription}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
              <View
                key={`${activeTab}-cv-upload`}
                style={{
                  backgroundColor: colors.surface,
                  padding: 16,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: colors.border,
                  marginBottom: 24,
                  shadowColor: colors.shadow,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.05,
                  shadowRadius: 4,
                  elevation: 2,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <View style={{
                    backgroundColor: '#F3E8FF',
                    padding: 8,
                    borderRadius: 20,
                    marginRight: 12
                  }}>
                    <Ionicons
                      name="document-text-outline"
                      size={18}
                      color="#7C3AED"
                    />
                  </View>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: colors.text
                  }}>
                    Upload CV
                  </Text>
                </View>
                <TouchableOpacity
                  key={`${activeTab}-cv-button`}
                  onPress={() => handleDocumentPick('cv', 'role')}
                  style={[styles.uploadButton, {
                    borderColor: colors.border,
                    backgroundColor: colors.backgroundSecondary,
                  }]}
                >
                  <View style={{
                    backgroundColor: '#DBEAFE',
                    padding: 16,
                    borderRadius: 20,
                    marginBottom: 12
                  }}>
                    <Ionicons
                      name="cloud-upload-outline"
                      size={32}
                      color="#2563EB"
                    />
                  </View>
                  <Text style={{
                    fontSize: 14,
                    color: colors.text,
                    fontWeight: '500',
                    textAlign: 'center',
                    marginBottom: 4
                  }}>
                    {roleCvFile ? roleCvFile.name : 'Select CV file'}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: colors.textSecondary,
                    textAlign: 'center'
                  }}>
                    PDF, DOC, DOCX
                  </Text>
                  {roleCvFile && (
                    <View
                      key={`${activeTab}-cv-success`}
                      style={{
                        marginTop: 12,
                        backgroundColor: '#D1FAE5',
                        paddingHorizontal: 16,
                        paddingVertical: 8,
                        borderRadius: 20
                      }}
                    >
                      <Text style={{
                        fontSize: 14,
                        color: '#065F46',
                        fontWeight: '600',
                        textAlign: 'center'
                      }}>
                        ✓ File selected
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
              <View key={`${activeTab}-submit`} style={styles.submitSection}>
                <TouchableOpacity
                  key={`${activeTab}-submit-button`}
                  onPress={handleSubmit}
                  disabled={loading || !roleDescription.trim() || !roleCvFile}
                  style={[
                    {
                      paddingVertical: 16,
                      paddingHorizontal: 24,
                      borderRadius: 12,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: loading || !roleDescription.trim() || !roleCvFile
                        ? colors.textMuted
                        : '#2563EB',
                    },
                    loading || !roleDescription.trim() || !roleCvFile
                      ? {}
                      : {
                        shadowColor: '#2563EB',
                        shadowOffset: { width: 0, height: 4 },
                        shadowOpacity: 0.3,
                        shadowRadius: 8,
                        elevation: 6,
                      }
                  ]}
                >
                  {loading ? (
                    <>
                      <ActivityIndicator
                        key={`${activeTab}-loading`}
                        color="white"
                        size="small"
                      />
                      <Text style={{
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: 18,
                        marginLeft: 12
                      }}>
                        Creating...
                      </Text>
                    </>
                  ) : (
                    <>
                      <Ionicons name="rocket" size={20} color="white" />
                      <Text
                        key={`${activeTab}-submit-text`}
                        style={{
                          color: 'white',
                          fontWeight: 'bold',
                          fontSize: 18,
                          marginLeft: 8
                        }}
                      >
                        Create Roadmap
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <View key={`${activeTab}-content`} style={styles.contentContainer}>
              <View
                key={`${activeTab}-cv-upload`}
                style={{
                  backgroundColor: colors.surface,
                  padding: 16,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: colors.border,
                  marginBottom: 16,
                  shadowColor: colors.shadow,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.05,
                  shadowRadius: 4,
                  elevation: 2,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <View style={{
                    backgroundColor: '#F3E8FF',
                    padding: 8,
                    borderRadius: 20,
                    marginRight: 12
                  }}>
                    <Ionicons
                      name="document-text-outline"
                      size={18}
                      color="#7C3AED"
                    />
                  </View>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: colors.text
                  }}>
                    Upload CV
                  </Text>
                </View>
                <TouchableOpacity
                  key={`${activeTab}-cv-button`}
                  onPress={() => handleDocumentPick('cv', 'jd')}
                  style={[styles.uploadButton, {
                    borderColor: colors.border,
                    backgroundColor: colors.backgroundSecondary,
                  }]}
                >
                  <View style={{
                    backgroundColor: '#F3E8FF',
                    padding: 16,
                    borderRadius: 20,
                    marginBottom: 12
                  }}>
                    <Ionicons
                      name="document-text-outline"
                      size={32}
                      color="#7C3AED"
                    />
                  </View>
                  <Text style={{
                    fontSize: 14,
                    color: colors.text,
                    fontWeight: '500',
                    textAlign: 'center',
                    marginBottom: 4
                  }}>
                    {jdCvFile ? jdCvFile.name : 'Select CV file'}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: colors.textSecondary,
                    textAlign: 'center'
                  }}>
                    PDF, DOC, DOCX
                  </Text>
                  {jdCvFile && (
                    <View
                      key={`${activeTab}-cv-success`}
                      style={{
                        marginTop: 12,
                        backgroundColor: '#D1FAE5',
                        paddingHorizontal: 16,
                        paddingVertical: 8,
                        borderRadius: 20
                      }}
                    >
                      <Text style={{
                        fontSize: 14,
                        color: '#065F46',
                        fontWeight: '600',
                        textAlign: 'center'
                      }}>
                        ✓ File selected
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
              <View
                key={`${activeTab}-file-upload`}
                style={{
                  backgroundColor: colors.surface,
                  padding: 16,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: colors.border,
                  marginBottom: 24,
                  shadowColor: colors.shadow,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.05,
                  shadowRadius: 4,
                  elevation: 2,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                  <View style={{
                    backgroundColor: '#FEF3C7',
                    padding: 8,
                    borderRadius: 20,
                    marginRight: 12
                  }}>
                    <Ionicons
                      name="briefcase-outline"
                      size={18}
                      color="#D97706"
                    />
                  </View>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: colors.text
                  }}>
                    Upload Job Description
                  </Text>
                </View>
                <TouchableOpacity
                  key={`${activeTab}-file-button`}
                  onPress={() => handleDocumentPick('jd', 'jd')}
                  style={[styles.uploadButton, {
                    borderColor: colors.border,
                    backgroundColor: colors.backgroundSecondary,
                  }]}
                >
                  <View style={{
                    backgroundColor: '#FEF3C7',
                    padding: 16,
                    borderRadius: 20,
                    marginBottom: 12
                  }}>
                    <Ionicons
                      name="briefcase-outline"
                      size={32}
                      color="#D97706"
                    />
                  </View>
                  <Text style={{
                    fontSize: 14,
                    color: colors.text,
                    fontWeight: '500',
                    textAlign: 'center',
                    marginBottom: 4
                  }}>
                    {jdFile ? jdFile.name : 'Select Job Description file'}
                  </Text>
                  <Text style={{
                    fontSize: 12,
                    color: colors.textSecondary,
                    textAlign: 'center'
                  }}>
                    PDF, DOC, DOCX
                  </Text>
                  {jdFile && (
                    <View
                      key={`${activeTab}-file-success`}
                      style={{
                        marginTop: 12,
                        backgroundColor: '#D1FAE5',
                        paddingHorizontal: 16,
                        paddingVertical: 8,
                        borderRadius: 20
                      }}
                    >
                      <Text style={{
                        fontSize: 14,
                        color: '#065F46',
                        fontWeight: '600',
                        textAlign: 'center'
                      }}>
                        ✓ File selected
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
              <View key={`${activeTab}-submit`} style={styles.submitSection}>
                <TouchableOpacity
                  key={`${activeTab}-submit-button`}
                  onPress={handleSubmit}
                  disabled={loading || !jdCvFile || !jdFile}
                  style={[
                    {
                      paddingVertical: 16,
                      paddingHorizontal: 24,
                      borderRadius: 12,
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: loading || !jdCvFile || !jdFile
                        ? colors.textMuted
                        : '#059669',
                    },
                    loading || !jdCvFile || !jdFile
                      ? {}
                      : {
                        shadowColor: '#059669',
                        shadowOffset: { width: 0, height: 4 },
                        shadowOpacity: 0.3,
                        shadowRadius: 8,
                        elevation: 6,
                      }
                  ]}
                >
                  {loading ? (
                    <>
                      <ActivityIndicator
                        key={`${activeTab}-loading`}
                        color="white"
                        size="small"
                      />
                      <Text style={{
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: 18,
                        marginLeft: 12
                      }}>
                        Creating...
                      </Text>
                    </>
                  ) : (
                    <>
                      <Ionicons name="rocket" size={20} color="white" />
                      <Text
                        key={`${activeTab}-submit-text`}
                        style={{
                          color: 'white',
                          fontWeight: 'bold',
                          fontSize: 18,
                          marginLeft: 8
                        }}
                      >
                        Create Roadmap
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 12,
    padding: 4,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tabBackground: {
    flexDirection: 'row',
    position: 'relative',
  },
  tabIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    zIndex: 1,
  },
  tabText: {
    fontSize: 15,
    fontWeight: '600',
  },

  contentContainer: {
    flex: 1,
    paddingTop: 8,
  },
  inputSection: {
    marginBottom: 24,
  },
  textInput: {
    minHeight: 100,
    textAlignVertical: 'top',
    fontSize: 15,
    lineHeight: 22,
  },
  uploadButton: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  submitSection: {
    marginTop: 20,
    marginBottom: 24,
  },
});

export default CreateRoadmapModal;
