import React from 'react';
import { View, Text, TouchableOpacity, ImageBackground, StatusBar } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ArrowLeft } from 'lucide-react-native';

interface CareerRoadmapHeaderProps {
  roadmapCount?: number;
}

const CareerRoadmapHeader: React.FC<CareerRoadmapHeaderProps> = ({
  roadmapCount = 0,
}) => {
  const navigation = useNavigation();
  const colors = useThemeColors();
  const insets = useSafeAreaInsets();

  return (
    <View style={{ marginTop: -insets.top }}>
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&h=600&fit=crop',
        }}
        style={{ width: '100%', height: 220 + insets.top }}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[colors.primary + 'B3', colors.success + 'B3']} // B3 = 70% opacity
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            paddingTop: 16,
            paddingHorizontal: 24,
            paddingBottom: 24,
          }}
        >
         <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="absolute left-2 bg-white/20 rounded-full p-[8px] items-center justify-center z-10"
            style={{ top: insets.top + 40 }}
            activeOpacity={0.7}
            hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
          >
            <View pointerEvents="none">
              <ArrowLeft size={20} color="white" />
            </View>
          </TouchableOpacity>
          <View className="flex-1 justify-center items-center mt-8">
            <View className="flex-row items-center mb-2">
              <Ionicons
                name="map"
                size={32}
                color="white"
                style={{ marginRight: 12 }}
              />
              <Text className="text-white text-center text-3xl font-bold">
                Career Roadmap
              </Text>
            </View>
            <Text className="text-white/90 text-center text-lg mb-4 px-4">
              {roadmapCount > 0
                ? `${roadmapCount} career development roadmaps created`
                : 'Discover your career development roadmap'}
            </Text>
            {roadmapCount > 0 && (
              <View className="bg-white/20 rounded-xl py-2 px-4 flex-row items-center justify-center">
                <Ionicons
                  name="checkmark-circle"
                  size={16}
                  color="white"
                  style={{ marginRight: 6 }}
                />
                <Text className="text-white font-medium text-sm">
                  Created by AI LearnVox
                </Text>
              </View>
            )}
          </View>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
};

export default CareerRoadmapHeader;
