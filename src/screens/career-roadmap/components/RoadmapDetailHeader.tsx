import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ImageBackground,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ArrowLeft } from 'lucide-react-native';

interface RoadmapDetailHeaderProps {
  title?: string;
  agentType?: string;
}

const RoadmapDetailHeader: React.FC<RoadmapDetailHeaderProps> = ({
  title = 'Roadmap Detail',
  agentType,
}) => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  const getAgentTypeInfo = (type?: string) => {
    switch (type) {
      case 'AI_ROADMAP_AI_ROLE':
        return {
          text: 'Role Based AI',
          icon: 'briefcase' as keyof typeof Ionicons.glyphMap,
        };
      case 'AI_ROADMAP_AI_JD':
        return {
          text: 'Job Description AI',
          icon: 'document-text' as keyof typeof Ionicons.glyphMap,
        };
      default:
        return {
          text: 'AI Generated',
          icon: 'analytics' as keyof typeof Ionicons.glyphMap,
        };
    }
  };

  const agentInfo = getAgentTypeInfo(agentType);

  return (
    <View style={{ marginTop: -insets.top }}>
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&h=600&fit=crop',
        }}
        style={{ width: '100%', height: 220 + insets.top }}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(59, 130, 246, 0.7)', 'rgba(16, 185, 129, 0.7)']}
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            paddingTop: 16,
            paddingHorizontal: 24,
            paddingBottom: 24,
          }}
        >
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="absolute left-2 bg-white/20 rounded-full p-[8px] items-center justify-center z-20"
            style={{ top: insets.top + 40 }}
            activeOpacity={0.7}
            hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
          >
            <View pointerEvents="none">
              <ArrowLeft size={20} color="white" />
            </View>
          </TouchableOpacity>
          <View
            className="flex-1 justify-center items-center"
            style={{
              paddingHorizontal: 60,
              paddingTop: insets.top + 20,
              marginTop: 8,
            }}
          >
            <Text
              className="text-white text-center font-bold mb-2"
              numberOfLines={2}
              adjustsFontSizeToFit={true}
              minimumFontScale={0.7}
              style={{
                fontSize: title.length > 30 ? 18 : 24,
                lineHeight: title.length > 30 ? 22 : 28,
              }}
            >
              {title}
            </Text>
            <Text className="text-white/90 text-center text-lg mb-4 px-4">
              Career development roadmap details
            </Text>
            <View className="bg-white/20 rounded-xl py-2 px-4 flex-row items-center justify-center">
              <Ionicons
                name={agentInfo.icon}
                size={16}
                color="white"
                style={{ marginRight: 6 }}
              />
              <Text className="text-white font-medium text-sm">
                {agentInfo.text}
              </Text>
            </View>
          </View>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
};

export default RoadmapDetailHeader;
