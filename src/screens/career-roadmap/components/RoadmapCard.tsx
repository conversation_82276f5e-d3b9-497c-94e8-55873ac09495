import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ResponseRoadmapAiDto } from '@/common/types/career-roadmap';
import { useThemeColors } from '@/hooks/useThemeColors';

interface RoadmapCardProps {
  roadmap: ResponseRoadmapAiDto;
  onPress: () => void;
  onDelete: () => void;
}

const RoadmapCard: React.FC<RoadmapCardProps> = ({
  roadmap,
  onPress,
  onDelete,
}) => {
  const colors = useThemeColors();

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getAgentTypeBadge = (agentType: string) => {
    switch (agentType) {
      case 'AI_ROADMAP_AI_ROLE':
        return {
          text: 'Role Based',
          bgColor: colors.primary + '20',
          textColor: colors.primary,
        };
      case 'AI_ROADMAP_AI_JD':
        return {
          text: 'JD Based',
          bgColor: colors.success + '20',
          textColor: colors.success,
        };
      default:
        return {
          text: 'Unknown',
          bgColor: colors.textMuted + '20',
          textColor: colors.textMuted,
        };
    }
  };

  const badge = getAgentTypeBadge(roadmap.agentType);
  return (
    <TouchableOpacity
      onPress={onPress}
      className="rounded-xl p-5 mb-4 border shadow-sm"
      style={{
        backgroundColor: colors.card,
        borderColor: colors.border,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 8,
        elevation: 3,
      }}
    >
      <View className="flex-row justify-between items-start mb-4">
        <View className="flex-1 pr-3">
          <Text
            className="text-lg font-bold mb-2 leading-6"
            style={{ color: colors.text }}
            numberOfLines={2}
          >
            {roadmap.content?.roadmapTitle || 'AI Career Roadmap'}
          </Text>
          <View
            className="px-3 py-1.5 rounded-full self-start"
            style={{ backgroundColor: badge.bgColor }}
          >
            <Text
              className="text-xs font-semibold"
              style={{ color: badge.textColor }}
            >
              {badge.text}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          onPress={onDelete}
          className="p-2 rounded-lg"
          style={{ backgroundColor: colors.error + '10' }}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="trash-outline" size={18} color={colors.error} />
        </TouchableOpacity>
      </View>
      {roadmap.content?.description && (
        <View
          className="p-3 rounded-lg mb-4"
          style={{ backgroundColor: colors.surfaceSecondary }}
        >
          <Text
            className="text-sm leading-5"
            style={{ color: colors.textSecondary }}
            numberOfLines={3}
          >
            {roadmap.content.description}
          </Text>
        </View>
      )}
      <View className="flex-row">
        {/* Left Column */}
        <View className="flex-1 pr-2">
          <View
            className="flex-row items-center p-2 rounded-lg mb-3"
            style={{ backgroundColor: colors.primary + '10' }}
          >
            <View
              className="p-1 rounded-full mr-3"
              style={{ backgroundColor: colors.primary + '20' }}
            >
              <Ionicons name="calendar-outline" size={14} color={colors.primary} />
            </View>
            <Text
              className="text-sm font-medium flex-1"
              style={{ color: colors.primary }}
            >
              Created on {formatDate(roadmap.createdAt)}
            </Text>
          </View>
          {roadmap.content?.duration && (
            <View
              className="flex-row items-center p-2 rounded-lg"
              style={{ backgroundColor: colors.warning + '10' }}
            >
              <View
                className="p-1 rounded-full mr-3"
                style={{ backgroundColor: colors.warning + '20' }}
              >
                <Ionicons name="time-outline" size={14} color={colors.warning} />
              </View>
              <Text
                className="text-sm font-medium flex-1"
                style={{ color: colors.warning }}
              >
                {roadmap.content.duration}
              </Text>
            </View>
          )}
        </View>
        {/* Right Column */}
        <View className="flex-1 pl-2">
          {roadmap.content?.skills && roadmap.content.skills.length > 0 && (
            <View
              className="flex-row items-center p-2 rounded-lg mb-3"
              style={{ backgroundColor: colors.success + '10' }}
            >
              <View
                className="p-1 rounded-full mr-3"
                style={{ backgroundColor: colors.success + '20' }}
              >
                <Ionicons name="bulb-outline" size={14} color={colors.success} />
              </View>
              <Text
                className="text-sm font-medium flex-1"
                style={{ color: colors.success }}
              >
                {roadmap.content.skills.length} skills
              </Text>
            </View>
          )}
          {(roadmap.cvFileUrl || roadmap.jdFileUrl) && (
            <View
              className="p-2 rounded-lg"
              style={{ backgroundColor: colors.accent + '10' }}
            >
              <View className="flex-row items-center flex-wrap">

                <Text
                  className="text-xs font-semibold mr-2"
                  style={{ color: colors.accent }}
                >
                  Documents:
                </Text>
                {roadmap.cvFileUrl && (
                  <View
                    className="flex-row items-center px-2 py-1 rounded-full mr-1"
                    style={{ backgroundColor: colors.accent + '20' }}
                  >
                    <Ionicons
                      name="document-text-outline"
                      size={10}
                      color={colors.accent}
                    />
                    <Text
                      className="text-xs ml-1 font-medium"
                      style={{ color: colors.accent }}
                    >
                      CV
                    </Text>
                  </View>
                )}
                {roadmap.jdFileUrl && (
                  <View
                    className="flex-row items-center px-2 py-1 rounded-full"
                    style={{ backgroundColor: colors.accent + '20' }}
                  >
                    <Ionicons
                      name="briefcase-outline"
                      size={10}
                      color={colors.accent}
                    />
                    <Text
                      className="text-xs ml-1 font-medium"
                      style={{ color: colors.accent }}
                    >
                      JD
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
      </View>
      <View
        className="mt-5 pt-4 border-t"
        style={{ borderTopColor: colors.border }}
      >
        <View className="flex-row justify-between items-center">
          <View className="flex-row items-center">
            <View
              className="p-1 rounded-full mr-2"
              style={{ backgroundColor: colors.accent + '20' }}
            >
              <Ionicons name="list-outline" size={12} color={colors.accent} />
            </View>
            <Text
              className="text-sm font-medium"
              style={{ color: colors.accent }}
            >
              {roadmap.content?.initialNodes?.length || 0} steps
            </Text>
          </View>
          <View
            className="flex-row items-center px-4 py-2 rounded-lg"
            style={{ backgroundColor: colors.primary }}
          >

            <Text
              className="text-sm font-semibold mr-1"
              style={{ color: colors.text }}
            >
              View Details
            </Text>
            <Ionicons name="chevron-forward" size={16} style={{ color: colors.text }} />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default RoadmapCard;
