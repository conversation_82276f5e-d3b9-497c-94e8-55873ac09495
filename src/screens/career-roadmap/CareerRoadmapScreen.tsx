import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { CareerRoadmapStackParamList } from '@/common/types/rootParamList';
import { useRoadmaps } from '@/screens/career-roadmap/hooks/useRoadmap';
import RoadmapCard from '@/screens/career-roadmap/components/RoadmapCard';
import RoadmapSkeleton from '@/screens/career-roadmap/components/RoadmapSkeleton';
import CreateRoadmapModal from '@/screens/career-roadmap/components/CreateRoadmapModal';
import CareerRoadmapHeader from '@/screens/career-roadmap/components/CareerRoadmapHeader';
import { useThemeColors } from '@/hooks/useThemeColors';

type NavigationProp = NativeStackNavigationProp<
  CareerRoadmapStackParamList,
  'CareerRoadmapMain'
>;

const CareerRoadmapScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const { roadmaps, loading, error, refetch, deleteRoadmap } = useRoadmaps();
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const colors = useThemeColors();

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleRoadmapPress = (roadmapId: string) => {
    navigation.navigate('RoadmapDetail', { id: roadmapId });
  };
  const handleDeleteRoadmap = (roadmapId: string) => {
    Alert.alert(
      'Delete Roadmap',
      'Are you sure you want to delete this roadmap?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteRoadmap(roadmapId);
            if (success) {
              Alert.alert('Success', 'Roadmap has been deleted');
            } else {
              Alert.alert('Error', 'Unable to delete roadmap');
            }
          },
        },
      ],
    );
  };

  const handleCreateSuccess = () => {
    refetch();
  };

  const renderContent = () => {
    if (loading && !refreshing) {
      return (
        <View style={{ paddingHorizontal: 16 }}>
          {[...Array(3)].map((_, index) => (
            <RoadmapSkeleton key={index} />
          ))}
        </View>
      );
    }
    if (error) {
      return (
        <View className="flex-1 justify-center items-center px-6">
          <View
            className="p-6 rounded-2xl items-center"
            style={{ backgroundColor: colors.error + '10' }}
          >
            <View
              className="p-4 rounded-full mb-4"
              style={{ backgroundColor: colors.error + '20' }}
            >
              <Ionicons
                name="alert-circle-outline"
                size={48}
                color={colors.error}
              />
            </View>
            <Text
              className="text-xl font-bold mb-2 text-center"
              style={{ color: colors.text }}
            >
              An error occurred
            </Text>
            <Text
              className="text-center mb-6 leading-5"
              style={{ color: colors.textSecondary }}
            >
              {error}
            </Text>
            <TouchableOpacity
              onPress={refetch}
              className="px-8 py-3 rounded-xl flex-row items-center"
              style={{
                backgroundColor: colors.error,
                shadowColor: colors.error,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              <Ionicons
                name="refresh"
                size={20}
                color={colors.cardBackground}
              />
              <Text
                className="font-bold ml-2"
                style={{ color: colors.cardBackground }}
              >
                Try Again
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    if (roadmaps.length === 0) {
      return (
        <View className="flex-1 justify-center items-center px-6">
          <View
            className="p-8 rounded-2xl items-center"
            style={{ backgroundColor: colors.primary + '10' }}
          >
            <View
              className="p-6 rounded-full mb-6"
              style={{ backgroundColor: colors.primary + '20' }}
            >
              <Ionicons name="map-outline" size={64} color={colors.primary} />
            </View>
            <Text
              className="text-2xl font-bold mb-3 text-center"
              style={{ color: colors.text }}
            >
              No roadmaps yet
            </Text>
            <Text
              className="text-center mb-8 leading-6 text-base"
              style={{ color: colors.textSecondary }}
            >
              Create your first roadmap to start your career development journey
            </Text>
            <TouchableOpacity
              onPress={() => setIsCreateModalVisible(true)}
              className="px-8 py-4 rounded-xl flex-row items-center"
              style={{
                backgroundColor: colors.primary,
                shadowColor: colors.primary,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              <Ionicons
                name="add-circle"
                size={24}
                color={colors.cardBackground}
              />
              <Text
                className="font-bold ml-2 text-lg"
                style={{ color: colors.cardBackground }}
              >
                Create Roadmap
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return (
      <View style={{ paddingHorizontal: 16 }}>
        {Array.isArray(roadmaps) &&
          roadmaps.map((roadmap) => (
            <RoadmapCard
              key={roadmap._id}
              roadmap={roadmap}
              onPress={() => handleRoadmapPress(roadmap._id)}
              onDelete={() => handleDeleteRoadmap(roadmap._id)}
            />
          ))}
      </View>
    );
  };
  return (
    <View style={{ backgroundColor: colors.background, flex: 1 }}>
      <CareerRoadmapHeader roadmapCount={roadmaps.length} />
      {roadmaps.length > 0 && (
        <View
          className="mx-4 mt-3 p-5 rounded-xl border shadow-sm"
          style={{
            backgroundColor: colors.card,
            borderColor: colors.border,
          }}
        >
          <View className="flex-row justify-between">
            <View
              className="px-5 py-4 rounded-xl flex-1 mr-2"
              style={{
                backgroundColor: colors.primary + '10',
              }}
            >
              <View className="flex-row items-center mb-2">
                <View
                  className="p-2 rounded-full mr-3"
                  style={{ backgroundColor: colors.primary }}
                >
                  <Ionicons
                    name="map"
                    size={18}
                    color={colors.cardBackground}
                  />
                </View>
                <Text
                  className="text-sm font-semibold"
                  style={{ color: colors.primary }}
                >
                  Total
                </Text>
              </View>
              <Text
                className="text-2xl font-bold mb-1"
                style={{ color: colors.text }}
              >
                {roadmaps.length}
              </Text>
              <Text
                className="text-sm font-medium"
                style={{ color: colors.primary }}
              >
                Roadmap
              </Text>
            </View>

            <View
              className="px-5 py-4 rounded-xl flex-1 ml-2"
              style={{
                backgroundColor: colors.success + '10',
              }}
            >
              <View className="flex-row items-center mb-2">
                <View
                  className="p-2 rounded-full mr-3"
                  style={{ backgroundColor: colors.success }}
                >
                  <Ionicons
                    name="sparkles"
                    size={18}
                    color={colors.cardBackground}
                  />
                </View>
                <Text
                  className="text-sm font-semibold"
                  style={{ color: colors.success }}
                >
                  Created by
                </Text>
              </View>
              <Text
                className="text-2xl font-bold mb-1"
                style={{ color: colors.text }}
              >
                AI
              </Text>
              <Text
                className="text-sm font-medium"
                style={{ color: colors.success }}
              >
                LearnVox
              </Text>
            </View>
          </View>
        </View>
      )}
      <ScrollView
        className="flex-1"
        contentContainerStyle={{ paddingVertical: 8 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderContent()}
      </ScrollView>
      <TouchableOpacity
        onPress={() => setIsCreateModalVisible(true)}
        className="absolute bottom-8 right-6 w-16 h-16 rounded-full items-center justify-center"
        style={{
          backgroundColor: colors.primary,
          shadowColor: colors.primary,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 12,
          elevation: 8,
        }}
      >
        <View
          className="w-8 h-8 rounded-full items-center justify-center"
          style={{ backgroundColor: colors.cardBackground }}
        >
          <Ionicons name="add" size={24} color={colors.primary} />
        </View>
      </TouchableOpacity>
      <CreateRoadmapModal
        visible={isCreateModalVisible}
        onClose={() => setIsCreateModalVisible(false)}
        onSuccess={handleCreateSuccess}
      />
    </View>
  );
};

export default CareerRoadmapScreen;
