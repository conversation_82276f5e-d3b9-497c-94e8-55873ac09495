import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { CareerRoadmapStackParamList } from '@/common/types/rootParamList';
import { useRoadmapById } from '@/screens/career-roadmap/hooks/useRoadmap';
import RoadmapTreeFlow from '@/screens/career-roadmap/components/RoadmapTreeFlow';
import RoadmapDetailHeader from '@/screens/career-roadmap/components/RoadmapDetailHeader';
import { useThemeColors } from '@/hooks/useThemeColors';

type NavigationProp = NativeStackNavigationProp<
  CareerRoadmapStackParamList,
  'RoadmapDetail'
>;
type RoutePropType = RouteProp<CareerRoadmapStackParamList, 'RoadmapDetail'>;

const RoadmapDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RoutePropType>();
  const { id } = route.params;
  const colors = useThemeColors();

  const { roadmap, loading, error } = useRoadmapById(id);
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getAgentTypeBadge = (agentType: string) => {
    switch (agentType) {
      case 'AI_ROADMAP_AI_ROLE':
        return {
          text: 'Role Based AI',
          backgroundColor: colors.primaryLight,
          textColor: colors.primary,
        };
      case 'AI_ROADMAP_AI_JD':
        return {
          text: 'Job Description AI',
          backgroundColor: colors.success + '20', // 20% opacity
          textColor: colors.success,
        };
      default:
        return {
          text: 'Unknown',
          backgroundColor: colors.surfaceSecondary,
          textColor: colors.textSecondary,
        };
    }
  };
  if (loading) {
    return (
      <View style={{ backgroundColor: colors.background, flex: 1 }}>
        <RoadmapDetailHeader />
        <View
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        >
          <View
            style={{
              backgroundColor: colors.surface,
              padding: 32,
              borderRadius: 24,
              alignItems: 'center',
              shadowColor: colors.shadow,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 8,
            }}
          >
            <View
              style={{
                backgroundColor: colors.primaryLight,
                width: 80,
                height: 80,
                borderRadius: 40,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 24,
              }}
            >
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
            <Text
              style={{
                color: colors.text,
                fontSize: 20,
                fontWeight: 'bold',
                marginBottom: 8,
              }}
            >
              Loading...
            </Text>
            <Text
              style={{
                color: colors.textSecondary,
                textAlign: 'center',
              }}
            >
              Please wait a moment
            </Text>
          </View>
        </View>
      </View>
    );
  }
  if (error || !roadmap) {
    return (
      <View style={{ backgroundColor: colors.background, flex: 1 }}>
        <RoadmapDetailHeader />
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingHorizontal: 24,
          }}
        >
          <View
            style={{
              backgroundColor: colors.surface,
              padding: 32,
              borderRadius: 24,
              alignItems: 'center',
              width: '100%',
              maxWidth: 320,
              shadowColor: colors.shadow,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 8,
            }}
          >
            <View
              style={{
                backgroundColor: colors.error + '20',
                width: 80,
                height: 80,
                borderRadius: 40,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 24,
              }}
            >
              <Ionicons
                name="alert-circle-outline"
                size={40}
                color={colors.error}
              />
            </View>
            <Text
              style={{
                color: colors.text,
                fontSize: 20,
                fontWeight: 'bold',
                textAlign: 'center',
                marginBottom: 12,
              }}
            >
              Unable to load roadmap
            </Text>
            <Text
              style={{
                color: colors.textSecondary,
                textAlign: 'center',
                marginBottom: 32,
                lineHeight: 24,
              }}
            >
              {error || 'Roadmap does not exist'}
            </Text>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{
                backgroundColor: colors.primary,
                paddingHorizontal: 32,
                paddingVertical: 16,
                borderRadius: 16,
                shadowColor: colors.primary,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 8,
              }}
            >
              <Text
                style={{
                  color: colors.primaryText,
                  fontWeight: 'bold',
                  fontSize: 18,
                }}
              >
                Go Back
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }
  const badge = getAgentTypeBadge(roadmap.agentType);

  return (
    <View style={{ backgroundColor: colors.background, flex: 1 }}>
      <RoadmapDetailHeader
        title={
          roadmap.content?.roadmapTitle ||
          roadmap.jobPosition ||
          'AI Career Roadmap'
        }
        agentType={roadmap.agentType}
      />
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <View
          style={{
            backgroundColor: colors.surface,
            padding: 24,
            marginBottom: 16,
            marginTop: 8,
            marginHorizontal: 16,
            borderRadius: 16,
            borderWidth: 1,
            borderColor: colors.border,
            shadowColor: colors.shadow,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.05,
            shadowRadius: 4,
            elevation: 2,
          }}
        >
          {roadmap.content?.description && (
            <View style={{ marginBottom: 24 }}>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 12,
                }}
              >
                <View
                  style={{
                    backgroundColor: colors.primaryLight,
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}
                >
                  <Ionicons
                    name="document-text-outline"
                    size={16}
                    color={colors.primary}
                  />
                </View>
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: colors.text,
                  }}
                >
                  Description
                </Text>
              </View>
              <Text
                style={{
                  color: colors.textSecondary,
                  fontSize: 16,
                  lineHeight: 28,
                  paddingLeft: 44,
                }}
              >
                {roadmap.content.description}
              </Text>
            </View>
          )}

          <View style={{ gap: 16 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: colors.backgroundSecondary,
                padding: 16,
                borderRadius: 12,
              }}
            >
              <View
                style={{
                  backgroundColor: colors.primaryLight,
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 16,
                }}
              >
                <Ionicons
                  name="calendar-outline"
                  size={20}
                  color={colors.primary}
                />
              </View>
              <View>
                <Text
                  style={{
                    fontSize: 12,
                    fontWeight: '600',
                    color: colors.textMuted,
                    textTransform: 'uppercase',
                    letterSpacing: 0.5,
                  }}
                >
                  Created Date
                </Text>
                <Text
                  style={{
                    color: colors.text,
                    fontWeight: '600',
                  }}
                >
                  {formatDate(roadmap.createdAt)}
                </Text>
              </View>
            </View>

            {roadmap.content?.duration && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: colors.backgroundSecondary,
                  padding: 16,
                  borderRadius: 12,
                }}
              >
                <View
                  style={{
                    backgroundColor: colors.success + '20',
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 16,
                  }}
                >
                  <Ionicons
                    name="time-outline"
                    size={20}
                    color={colors.success}
                  />
                </View>
                <View>
                  <Text
                    style={{
                      fontSize: 12,
                      fontWeight: '600',
                      color: colors.textMuted,
                      textTransform: 'uppercase',
                      letterSpacing: 0.5,
                    }}
                  >
                    Duration
                  </Text>
                  <Text
                    style={{
                      color: colors.text,
                      fontWeight: '600',
                    }}
                  >
                    {roadmap.content.duration}
                  </Text>
                </View>
              </View>
            )}

            {roadmap.content?.skills && roadmap.content.skills.length > 0 && (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: colors.backgroundSecondary,
                  padding: 16,
                  borderRadius: 12,
                }}
              >
                <View
                  style={{
                    backgroundColor: colors.accent + '20',
                    width: 40,
                    height: 40,
                    borderRadius: 20,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 16,
                  }}
                >
                  <Ionicons
                    name="bulb-outline"
                    size={20}
                    color={colors.accent}
                  />
                </View>
                <View>
                  <Text
                    style={{
                      fontSize: 12,
                      fontWeight: '600',
                      color: colors.textMuted,
                      textTransform: 'uppercase',
                      letterSpacing: 0.5,
                    }}
                  >
                    Skills
                  </Text>
                  <Text
                    style={{
                      color: colors.text,
                      fontWeight: '600',
                    }}
                  >
                    {roadmap.content.skills.length} skills to learn
                  </Text>
                </View>
              </View>
            )}
          </View>
        </View>
        {roadmap.content?.initialNodes &&
          Array.isArray(roadmap.content.initialNodes) &&
          roadmap.content.initialNodes.length > 0 && (
            <View style={{ marginHorizontal: 16, marginBottom: 16 }}>
              <RoadmapTreeFlow
                nodes={roadmap.content.initialNodes}
                initialEdges={roadmap.content?.initialEdges}
                title="Learning Roadmap"
              />
            </View>
          )}
        {roadmap.content?.skills &&
          Array.isArray(roadmap.content.skills) &&
          roadmap.content.skills.length > 0 && (
            <View
              style={{
                backgroundColor: colors.surface,
                padding: 24,
                marginHorizontal: 16,
                marginBottom: 16,
                borderRadius: 16,
                borderWidth: 1,
                borderColor: colors.border,
                shadowColor: colors.shadow,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.05,
                shadowRadius: 4,
                elevation: 2,
              }}
            >
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 16,
                }}
              >
                <View
                  style={{
                    backgroundColor: colors.accent + '20',
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}
                >
                  <Ionicons
                    name="star-outline"
                    size={16}
                    color={colors.accent}
                  />
                </View>
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: colors.text,
                  }}
                >
                  Skills to Develop
                </Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                }}
              >
                {roadmap.content.skills.map((skill, index) => (
                  <View
                    key={index}
                    style={{
                      backgroundColor: colors.accent + '15',
                      borderWidth: 1,
                      borderColor: colors.accent + '30',
                      borderRadius: 20,
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                      marginRight: 8,
                      marginBottom: 12,
                      shadowColor: colors.shadow,
                      shadowOffset: { width: 0, height: 1 },
                      shadowOpacity: 0.05,
                      shadowRadius: 2,
                      elevation: 1,
                    }}
                  >
                    <Text
                      style={{
                        color: colors.accent,
                        fontSize: 14,
                        fontWeight: '500',
                      }}
                    >
                      {skill}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}
        {roadmap.content?.steps &&
          Array.isArray(roadmap.content.steps) &&
          roadmap.content.steps.length > 0 && (
            <View
              style={{
                backgroundColor: colors.surface,
                padding: 24,
                marginHorizontal: 16,
                marginBottom: 16,
                borderRadius: 16,
                borderWidth: 1,
                borderColor: colors.border,
                shadowColor: colors.shadow,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.05,
                shadowRadius: 4,
                elevation: 2,
              }}
            >
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 20,
                }}
              >
                <View
                  style={{
                    backgroundColor: colors.primaryLight,
                    width: 32,
                    height: 32,
                    borderRadius: 16,
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: 12,
                  }}
                >
                  <Ionicons
                    name="list-outline"
                    size={16}
                    color={colors.primary}
                  />
                </View>
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: 'bold',
                    color: colors.text,
                  }}
                >
                  Implementation Steps
                </Text>
              </View>
              {roadmap.content.steps.map((step, index) => (
                <View
                  key={index}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    marginBottom:
                      index === roadmap.content!.steps!.length - 1 ? 0 : 16,
                  }}
                >
                  <View
                    style={{
                      backgroundColor: colors.primary,
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginRight: 16,
                      marginTop: 4,
                      shadowColor: colors.primary,
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.2,
                      shadowRadius: 4,
                      elevation: 2,
                    }}
                  >
                    <Text
                      style={{
                        color: colors.primaryText,
                        fontSize: 14,
                        fontWeight: 'bold',
                      }}
                    >
                      {index + 1}
                    </Text>
                  </View>
                  <View
                    style={{
                      flex: 1,
                      backgroundColor: colors.backgroundSecondary,
                      padding: 16,
                      borderRadius: 12,
                    }}
                  >
                    <Text
                      style={{
                        color: colors.textSecondary,
                        lineHeight: 24,
                        fontWeight: '500',
                      }}
                    >
                      {step}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          )}
        {roadmap.content?.analysis && (
          <View
            style={{
              backgroundColor: colors.surface,
              padding: 24,
              marginHorizontal: 16,
              marginBottom: 16,
              borderRadius: 16,
              borderWidth: 1,
              borderColor: colors.border,
              shadowColor: colors.shadow,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.05,
              shadowRadius: 4,
              elevation: 2,
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 16,
              }}
            >
              <View
                style={{
                  backgroundColor: colors.success + '20',
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 12,
                }}
              >
                <Ionicons
                  name="analytics-outline"
                  size={16}
                  color={colors.success}
                />
              </View>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: colors.text,
                }}
              >
                AI Analysis
              </Text>
            </View>
            <View
              style={{
                backgroundColor: colors.success + '10',
                padding: 16,
                borderRadius: 12,
                borderWidth: 1,
                borderColor: colors.success + '20',
              }}
            >
              <Text
                style={{
                  color: colors.textSecondary,
                  lineHeight: 28,
                  fontWeight: '500',
                }}
              >
                {roadmap.content.analysis}
              </Text>
            </View>
          </View>
        )}
        {roadmap.content?.summary && (
          <View
            style={{
              backgroundColor: colors.surface,
              padding: 24,
              marginHorizontal: 16,
              marginBottom: 24,
              borderRadius: 16,
              borderWidth: 1,
              borderColor: colors.border,
              shadowColor: colors.shadow,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.05,
              shadowRadius: 4,
              elevation: 2,
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 16,
              }}
            >
              <View
                style={{
                  backgroundColor: colors.warning + '20',
                  width: 32,
                  height: 32,
                  borderRadius: 16,
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginRight: 12,
                }}
              >
                <Ionicons
                  name="bookmark-outline"
                  size={16}
                  color={colors.warning}
                />
              </View>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: colors.text,
                }}
              >
                Summary
              </Text>
            </View>
            <View
              style={{
                backgroundColor: colors.warning + '10',
                padding: 16,
                borderRadius: 12,
                borderWidth: 1,
                borderColor: colors.warning + '20',
              }}
            >
              <Text
                style={{
                  color: colors.textSecondary,
                  lineHeight: 28,
                  fontWeight: '500',
                }}
              >
                {roadmap.content.summary}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default RoadmapDetailScreen;
