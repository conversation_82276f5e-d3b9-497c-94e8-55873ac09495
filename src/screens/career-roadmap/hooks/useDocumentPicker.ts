import { Alert } from 'react-native';
import * as DocumentPicker from 'expo-document-picker';

interface FileData {
  uri: string;
  name: string;
  type: string;
  size: number;
}

export const useDocumentPicker = () => {
  const pickDocument = async (
    type: 'cv' | 'jd',
    onFileSelected: (file: FileData) => void,
  ) => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ],
        copyToCacheDirectory: true,
      });
      if (!result.canceled && result.assets[0]) {
        const file = result.assets[0];
        const fileData: FileData = {
          uri: file.uri,
          name: file.name,
          type: file.mimeType || 'application/octet-stream',
          size: file.size || 0,
        };
        onFileSelected(fileData);
      }
    } catch (error) {
      Alert.alert('Error', 'Cannot select file');
    }
  };

  return {
    pickDocument,
  };
};
