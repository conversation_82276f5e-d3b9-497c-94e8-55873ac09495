import { useState, useEffect } from 'react';
import { careerRoadmapApi } from '@/services/careerRoadmapApi';
import { ResponseRoadmapAiDto } from '@/common/types/career-roadmap';

export const useRoadmaps = () => {
  const [roadmaps, setRoadmaps] = useState<ResponseRoadmapAiDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fetchMyRoadmaps = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await careerRoadmapApi.getMyRoadmaps();
      setRoadmaps(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch roadmaps');
    } finally {
      setLoading(false);
    }
  };

  const deleteRoadmap = async (id: string) => {
    try {
      await careerRoadmapApi.deleteRoadmap(id);
      setRoadmaps(prev => prev.filter(roadmap => roadmap._id !== id));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete roadmap');
      return false;
    }
  };

  const refetch = () => {
    fetchMyRoadmaps();
  };

  useEffect(() => {
    fetchMyRoadmaps();
  }, []);

  return {
    roadmaps,
    loading,
    error,
    refetch,
    deleteRoadmap,
  };
};

export const useRoadmapById = (id: string) => {
  const [roadmap, setRoadmap] = useState<ResponseRoadmapAiDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fetchRoadmap = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await careerRoadmapApi.getRoadmapById(id);
      setRoadmap(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch roadmap');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoadmap();
  }, [id]);

  return {
    roadmap,
    loading,
    error,
    refetch: fetchRoadmap,
  };
};
