import { useState, useRef, useEffect } from 'react';
import { Animated, Dimensions } from 'react-native';

export const useTabAnimation = () => {
  const [activeTab, setActiveTab] = useState<'role' | 'jd'>('role');
  const slideAnim = useRef(new Animated.Value(0)).current;
  const { width } = Dimensions.get('window');
  const tabWidth = (width - 32 - 8) / 2;

  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: activeTab === 'role' ? 0 : tabWidth,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [activeTab, tabWidth, slideAnim]);

  const switchToTab = (tab: 'role' | 'jd') => {
    setActiveTab(tab);
  };

  return {
    activeTab,
    slideAnim,
    tabWidth,
    setActiveTab: switchToTab,
  };
};
