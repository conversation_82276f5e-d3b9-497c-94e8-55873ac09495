import { useMemo } from 'react';

export const useRoadmapValidation = () => {
  const validateRoleForm = (roleDescription: string, cvFile: any) => {
    return useMemo(() => {
      const errors: string[] = [];
      if (!roleDescription?.trim()) {
        errors.push('Please enter position description');
      }

      if (!cvFile) {
        errors.push('Please select CV file');
      }

      const isValid = errors.length === 0;

      return {
        isValid,
        errors,
        canSubmit: isValid && roleDescription.trim().length > 0 && cvFile,
      };
    }, [roleDescription, cvFile]);
  };

  const validateJdForm = (cvFile: any, jdFile: any) => {
    return useMemo(() => {
      const errors: string[] = [];
      if (!cvFile) {
        errors.push('Please select CV file');
      }

      if (!jdFile) {
        errors.push('Please select Job Description file');
      }

      const isValid = errors.length === 0;

      return {
        isValid,
        errors,
        canSubmit: isValid && cvFile && jdFile,
      };
    }, [cvFile, jdFile]);
  };
  const getFileValidationMessage = (file: any, fileType: 'CV' | 'JD') => {
    if (!file) {
      return `No ${fileType} file selected`;
    }

    const validExtensions = ['.pdf', '.doc', '.docx'];
    const fileName = file.name?.toLowerCase() || '';
    const hasValidExtension = validExtensions.some((ext) =>
      fileName.endsWith(ext),
    );

    if (!hasValidExtension) {
      return `${fileType} file must be in PDF, DOC or DOCX format`;
    }

    return `✓ ${fileType} file selected`;
  };

  return {
    validateRoleForm,
    validateJdForm,
    getFileValidationMessage,
  };
};
