import { useState } from 'react';
import { Alert } from 'react-native';
import { careerRoadmapApi } from '@/services/careerRoadmapApi';

interface FileData {
  uri: string;
  name: string;
  type: string;
  size: number;
}

export const useCreateRoadmap = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'role' | 'jd'>('role');

  const [roleDescription, setRoleDescription] = useState('');
  const [roleCvFile, setRoleCvFile] = useState<FileData | null>(null);

  const [jdCvFile, setJdCvFile] = useState<FileData | null>(null);
  const [jdFile, setJdFile] = useState<FileData | null>(null);

  const resetForm = () => {
    setActiveTab('role');
    setRoleDescription('');
    setRoleCvFile(null);
    setJdCvFile(null);
    setJdFile(null);
  };
  const handleRoleSubmit = async (onSuccess?: () => void) => {
    if (!roleDescription.trim() || !roleCvFile) {
      Alert.alert(
        'Error',
        'Please enter position description and select CV file',
      );
      return false;
    }

    try {
      setLoading(true);
      console.log('Creating roadmap by role with:', {
        roleDescription: roleDescription.trim(),
        cvFile: roleCvFile,
      });

      await careerRoadmapApi.createRoadmapByRole({
        roleDescription: roleDescription.trim(),
        cvFile: roleCvFile,
      });
      Alert.alert('Success', 'Roadmap has been created successfully!');
      resetForm();
      onSuccess?.();
      return true;
    } catch (error: any) {
      console.error('Error creating roadmap:', error);
      let errorMessage = 'Unable to create roadmap. Please try again.';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      Alert.alert('Error', errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleJdSubmit = async (onSuccess?: () => void) => {
    if (!jdCvFile || !jdFile) {
      Alert.alert(
        'Error',
        'Please select both CV file and Job Description file',
      );
      return false;
    }

    try {
      setLoading(true);
      console.log('Creating roadmap by JD with:', {
        cvFile: jdCvFile,
        jdFile: jdFile,
      });

      await careerRoadmapApi.createRoadmapByJd({
        cvFile: jdCvFile,
        jdFile: jdFile,
      });
      Alert.alert('Success', 'Roadmap has been created successfully!');
      resetForm();
      onSuccess?.();
      return true;
    } catch (error: any) {
      console.error('Error creating roadmap by JD:', error);
      let errorMessage = 'Unable to create roadmap. Please try again.';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }
      Alert.alert('Error', errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const setFile = (
    type: 'cv' | 'jd',
    tab: 'role' | 'jd',
    file: FileData | null,
  ) => {
    if (tab === 'role' && type === 'cv') {
      setRoleCvFile(file);
    } else if (tab === 'jd' && type === 'cv') {
      setJdCvFile(file);
    } else if (tab === 'jd' && type === 'jd') {
      setJdFile(file);
    }
  };

  return {
    loading,
    activeTab,
    roleDescription,
    roleCvFile,
    jdCvFile,
    jdFile,

    setActiveTab,
    setRoleDescription,
    setFile,
    resetForm,
    handleRoleSubmit,
    handleJdSubmit,
  };
};
