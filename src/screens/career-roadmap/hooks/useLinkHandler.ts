import { useState } from 'react';
import { Linking, Alert } from 'react-native';

export const useLinkHandler = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleViewDetails = async (link: string) => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      const canOpen = await Linking.canOpenURL(link);
      if (canOpen) {
        await Linking.openURL(link);
      } else {
        Alert.alert(
          'Cannot open link',
          'This link cannot be opened on your device.',
          [{ text: 'OK' }],
        );
      }
    } catch (error) {
      Alert.alert(
        'Error',
        'An error occurred while opening the link. Please try again.',
        [{ text: 'OK' }],
      );
    } finally {
      setIsLoading(false);
    }
  };

  return {
    handleViewDetails,
    isLoading,
  };
};
