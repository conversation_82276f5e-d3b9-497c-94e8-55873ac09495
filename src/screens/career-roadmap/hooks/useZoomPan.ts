import {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import { Gesture } from 'react-native-gesture-handler';
import { useCallback } from 'react';

export const useZoomPan = () => {
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const startTranslateX = useSharedValue(0);
  const startTranslateY = useSharedValue(0);
  const isGesturing = useSharedValue(false);

  const MIN_SCALE = 0.5;
  const MAX_SCALE = 3;

  const resetZoom = useCallback(() => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    startTranslateX.value = 0;
    startTranslateY.value = 0;
  }, []);

  const pinchGestureHandler = Gesture.Pinch()
    .onStart(() => {
      'worklet';
      isGesturing.value = true;
    })
    .onUpdate((event) => {
      'worklet';
      const newScale = Math.min(
        Math.max(event.scale, MIN_SCALE),
        MAX_SCALE,
      );
      scale.value = newScale;
    })
    .onEnd(() => {
      'worklet';
      if (scale.value < MIN_SCALE) {
        scale.value = withSpring(MIN_SCALE);
      } else if (scale.value > MAX_SCALE) {
        scale.value = withSpring(MAX_SCALE);
      }
      isGesturing.value = false;
    });

  const panGestureHandler = Gesture.Pan()
    .onStart(() => {
      'worklet';
      isGesturing.value = true;
      startTranslateX.value = translateX.value;
      startTranslateY.value = translateY.value;
    })
    .onUpdate((event) => {
      'worklet';
      translateX.value = startTranslateX.value + event.translationX;
      translateY.value = startTranslateY.value + event.translationY;
    })
    .onEnd(() => {
      'worklet';
      isGesturing.value = false;
    });
  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const combinedGesture = Gesture.Simultaneous(panGestureHandler, pinchGestureHandler);

  return {
    scale,
    translateX,
    translateY,
    resetZoom,
    combinedGesture,
    animatedStyle,
    isGesturing,
    MIN_SCALE,
    MAX_SCALE,
  };
};
