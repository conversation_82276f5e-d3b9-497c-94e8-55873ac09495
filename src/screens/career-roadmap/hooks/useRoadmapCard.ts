import { useMemo } from 'react';
import { ResponseRoadmapAiDto } from '@/common/types/career-roadmap';

export const useRoadmapCard = () => {
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getAgentTypeBadge = (agentType: string) => {
    switch (agentType) {
      case 'AI_ROADMAP_AI_ROLE':
        return {
          text: 'Role Based',
          color: 'bg-blue-100',
          textColor: 'text-blue-800',
        };
      case 'AI_ROADMAP_AI_JD':
        return {
          text: 'JD Based',
          color: 'bg-green-100',
          textColor: 'text-green-800',
        };
      default:
        return {
          text: 'Unknown',
          color: 'bg-gray-100',
          textColor: 'text-gray-800',
        };
    }
  };

  const getRoadmapDisplayData = (roadmap: ResponseRoadmapAiDto) => {
    return useMemo(() => {
      const badge = getAgentTypeBadge(roadmap.agentType);
      const formattedDate = formatDate(roadmap.createdAt);
      const title = roadmap.content?.roadmapTitle || 'AI Career Roadmap';
      const description = roadmap.content?.description;
      const duration = roadmap.content?.duration;
      const skillsCount = roadmap.content?.skills?.length || 0;
      const nodesCount = roadmap.content?.initialNodes?.length || 0;
      const hasCv = !!roadmap.cvFileUrl;
      const hasJd = !!roadmap.jdFileUrl;

      return {
        badge,
        formattedDate,
        title,
        description,
        duration,
        skillsCount,
        nodesCount,
        hasCv,
        hasJd,
      };
    }, [roadmap]);
  };

  return {
    formatDate,
    getAgentTypeBadge,
    getRoadmapDisplayData,
  };
};
