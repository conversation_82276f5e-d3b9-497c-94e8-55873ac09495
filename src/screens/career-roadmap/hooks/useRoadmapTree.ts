import { useState, useMemo } from 'react';
import { Dimensions } from 'react-native';
import { RoadmapNode, RoadmapEdge } from '@/common/types/career-roadmap';

interface TreeNode {
  id: string;
  data: RoadmapNode;
  children: TreeNode[];
  level: number;
  x: number;
  y: number;
}

const { width: screenWidth } = Dimensions.get('window');

export const useRoadmapTree = (
  nodes: RoadmapNode[],
  edges?: RoadmapEdge[],
  initialEdges?: RoadmapEdge[]
) => {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  const trees = useMemo(() => {
    if (!nodes || nodes.length === 0) return [];

    const buildTreeStructure = (): TreeNode[] => {
      const connectionEdges = (() => {
        if (initialEdges && initialEdges.length > 0) {
          return initialEdges;
        }
        if (edges && edges.length > 0) {
          return edges;
        }
        return nodes.slice(0, -1).map((node, index) => ({
          id: `edge-${node.id}-${nodes[index + 1].id}`,
          source: node.id,
          target: nodes[index + 1].id,
        }));
      })();

      const nodeMap = new Map<string, RoadmapNode>();
      nodes.forEach((node) => nodeMap.set(node.id, node));

      const hasParent = new Set<string>();
      connectionEdges.forEach((edge) => hasParent.add(edge.target));
      const rootNodes = nodes.filter((node) => !hasParent.has(node.id));

      const buildNode = (nodeId: string, level: number): TreeNode => {
        const node = nodeMap.get(nodeId)!;
        const children = connectionEdges
          .filter((edge) => edge.source === nodeId)
          .map((edge) => buildNode(edge.target, level + 1));

        return {
          id: nodeId,
          data: node,
          children,
          level,
          x: 0,
          y: 0,
        };
      };

      return rootNodes.map((node) => buildNode(node.id, 0));
    };

    const calculateTreeLayout = (trees: TreeNode[]): TreeNode[] => {
      const nodeWidth = 180;
      const levelHeight = 140;
      const nodeSpacing = 40;

      let currentY = 50;

      const layoutTree = (tree: TreeNode, startX: number = 50): number => {
        if (tree.children.length === 0) {
          tree.x = startX;
          tree.y = currentY + tree.level * levelHeight;
          return nodeWidth;
        }

        let childrenWidth = 0;
        let childX = startX;

        tree.children.forEach((child, index) => {
          if (index > 0) childX += nodeSpacing;
          const childWidth = layoutTree(child, childX);
          childX += childWidth;
          childrenWidth += childWidth + (index > 0 ? nodeSpacing : 0);
        });

        const firstChild = tree.children[0];
        const lastChild = tree.children[tree.children.length - 1];
        tree.x = (firstChild.x + lastChild.x + nodeWidth) / 2 - nodeWidth / 2;
        tree.y = currentY + tree.level * levelHeight;

        return Math.max(nodeWidth, childrenWidth);
      };

      trees.forEach((tree, index) => {
        if (index > 0) currentY += 50;
        layoutTree(tree);
      });

      return trees;
    };

    return calculateTreeLayout(buildTreeStructure());
  }, [nodes, edges, initialEdges]);

  const allNodes = useMemo(() => {
    const flattenTree = (tree: TreeNode): TreeNode[] => {
      return [tree, ...tree.children.flatMap((child) => flattenTree(child))];
    };

    return trees.flatMap((tree) => flattenTree(tree));
  }, [trees]);

  const svgDimensions = useMemo(() => {
    if (allNodes.length === 0) {
      return { width: screenWidth, height: 400 };
    }

    const maxX = Math.max(...allNodes.map((node) => node.x)) + 180 + 50;
    const maxY = Math.max(...allNodes.map((node) => node.y)) + 100 + 50;
    const svgWidth = Math.max(screenWidth, maxX);
    const svgHeight = Math.max(400, maxY);

    return { width: svgWidth, height: svgHeight };
  }, [allNodes]);

  const getNodeStyle = (node: TreeNode) => {
    const level = node.level;
    const nodeType = node.data.type.toLowerCase();

    if (nodeType === 'start') {
      return {
        fill: 'url(#startGradient)',
        stroke: '#10B981',
        strokeWidth: 3,
        textColor: '#FFFFFF',
      };
    } else if (nodeType === 'end') {
      return {
        fill: 'url(#endGradient)',
        stroke: '#EF4444',
        strokeWidth: 3,
        textColor: '#FFFFFF',
      };
    } else {
      const gradients = [
        'url(#level0Gradient)',
        'url(#level1Gradient)',
        'url(#level2Gradient)',
        'url(#level3Gradient)',
      ];

      const gradientIndex = Math.min(level, gradients.length - 1);
      const strokes = ['#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444'];
      const strokeIndex = Math.min(level, strokes.length - 1);

      return {
        fill: gradients[gradientIndex],
        stroke: strokes[strokeIndex],
        strokeWidth: 2,
        textColor: '#FFFFFF',
      };
    }
  };

  const toggleNodeExpansion = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  const isNodeExpanded = (nodeId: string) => {
    return expandedNodes.has(nodeId);
  };

  return {
    trees,
    allNodes,
    svgDimensions,
    selectedNodeId,
    setSelectedNodeId,
    expandedNodes,
    toggleNodeExpansion,
    isNodeExpanded,
    getNodeStyle,
  };
};
