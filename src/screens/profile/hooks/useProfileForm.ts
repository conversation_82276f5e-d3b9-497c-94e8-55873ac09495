import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { updateProfile } from '@/redux/thunks/userThunks';
import {
  selectIsUpdatingProfile,
  selectUserMessage,
} from '@/redux/selectors/userSelector';
import { UpdateProfileRequest } from '@/services/userApi';

const profileSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  phone: z
    .string()
    .regex(/^[+]?[0-9\s-()]+$/, 'Phone number is invalid')
    .optional()
    .or(z.literal('')),
  address: z.string().optional(),
  bio: z.string().max(500, 'Bio must be at most 500 characters').optional(),
  dateOfBirth: z
    .string()
    .optional()
    .refine((date) => {
      if (!date) return true; // Optional field

      // Parse date string (YYYY-MM-DD format)
      const selectedDate = new Date(date);
      const currentDate = new Date();

      // Reset time to compare only dates
      currentDate.setHours(23, 59, 59, 999);

      return selectedDate <= currentDate;
    }, 'Date of birth cannot be in the future'),
});

export type ProfileFormData = z.infer<typeof profileSchema>;

export interface ProfileData {
  firstName: string;
  lastName: string;
  email?: string;
  avatar?: string;
  phone?: string;
  address?: string;
  bio?: string;
  dateOfBirth?: string;
}

export const useProfileForm = (initialData: ProfileData) => {
  const dispatch = useAppDispatch();
  const isUpdating = useAppSelector(selectIsUpdatingProfile);
  const message = useAppSelector(selectUserMessage);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: initialData.firstName || '',
      lastName: initialData.lastName || '',
      phone: initialData.phone || '',
      address: initialData.address || '',
      bio: initialData.bio || '',
      dateOfBirth: initialData.dateOfBirth || '',
    },
  });

  const {
    formState: { errors },
    reset,
    handleSubmit,
    control,
  } = form;

  const resetForm = () => {
    reset({
      firstName: initialData.firstName || '',
      lastName: initialData.lastName || '',
      phone: initialData.phone || '',
      address: initialData.address || '',
      bio: initialData.bio || '',
      dateOfBirth: initialData.dateOfBirth || '',
    });
  };

  const onSubmit = async (data: ProfileFormData): Promise<boolean> => {
    const profileRequest: UpdateProfileRequest = {
      firstName: data.firstName,
      lastName: data.lastName,
      phone: data.phone || undefined,
      address: data.address || undefined,
      bio: data.bio || undefined,
      dateOfBirth: data.dateOfBirth || undefined,
    };

    try {
      const result = await dispatch(updateProfile(profileRequest));
      return updateProfile.fulfilled.match(result);
    } catch (error) {
      return false;
    }
  };

  return {
    control,
    errors,
    handleSubmit,
    reset: resetForm,
    isUpdating,
    message,
    onSubmit,
  };
};
