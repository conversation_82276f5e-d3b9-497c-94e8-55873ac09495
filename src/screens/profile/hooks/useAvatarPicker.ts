import { useState } from 'react';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { uploadAvatar } from '@/redux/thunks/userThunks';
import {
  selectIsUploadingAvatar,
  selectUserMessage,
} from '@/redux/selectors/userSelector';

export const useAvatarPicker = (
  onAvatarUpload?: (imageUri: string) => void,
) => {
  const dispatch = useAppDispatch();
  const isUploadingAvatar = useAppSelector(selectIsUploadingAvatar);
  const message = useAppSelector(selectUserMessage);
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(null);

  const handleAvatarPress = async () => {
    // Request permissions
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Access Denied',
        'Photo library access is required to change your avatar.',
      );
      return;
    }

    Alert.alert(
      'Choose Avatar',
      'Where do you want to select your avatar from?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Library', onPress: () => pickImageFromLibrary() },
        { text: 'Camera', onPress: () => takePhoto() },
      ],
    );
  };

  const pickImageFromLibrary = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setSelectedAvatar(imageUri);
        await uploadAvatarToServer(imageUri);
        if (onAvatarUpload) {
          onAvatarUpload(imageUri);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Unable to pick image from library');
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Access Denied',
          'Camera access is required to take a photo.',
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setSelectedAvatar(imageUri);
        await uploadAvatarToServer(imageUri);
        if (onAvatarUpload) {
          onAvatarUpload(imageUri);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Unable to take photo');
    }
  };

  const uploadAvatarToServer = async (imageUri: string): Promise<boolean> => {
    try {
      const result = await dispatch(uploadAvatar(imageUri));
      const success = uploadAvatar.fulfilled.match(result);

      if (!success) {
        Alert.alert(
          'Upload Failed',
          'Failed to upload avatar. Please try again.',
        );
      }

      return success;
    } catch (error) {
      Alert.alert(
        'Upload Failed',
        'Failed to upload avatar. Please try again.',
      );
      return false;
    }
  };

  const resetAvatar = () => {
    setSelectedAvatar(null);
  };

  return {
    selectedAvatar,
    handleAvatarPress,
    isUploadingAvatar,
    message,
    uploadAvatarToServer,
    resetAvatar,
  };
};
