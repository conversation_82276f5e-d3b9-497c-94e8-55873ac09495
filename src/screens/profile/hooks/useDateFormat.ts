const useDateFormat = () => {
  const formatToYYYYMMDD = (dateString?: string): string => {
    if (!dateString) return '';

    if (!dateString.includes('T') && /^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return dateString;
    }

    try {
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };
  const parseFromYYYYMMDD = (dateString?: string): Date => {
    if (!dateString) return new Date();
    return new Date(dateString);
  };

  const formatDateToYYYYMMDD = (date?: Date | null): string => {
    if (!date) return '';
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  };

  return {
    formatToYYYYMMDD,
    parseFromYYYYMMDD,
    formatDateToYYYYMMDD,
  };
};

export default useDateFormat;
