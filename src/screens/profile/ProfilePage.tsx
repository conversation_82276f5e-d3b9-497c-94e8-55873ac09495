import { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  SkeletonSettings,
  SkeletonProfile,
  ProfileHeader,
  ProfileEditModal,
} from './components';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { getProfile } from '@/redux/thunks/userThunks';
import {
  selectUserProfile,
  selectUserLoading,
} from '@/redux/selectors/userSelector';
import { useDateFormat } from './hooks';
import UserProfileCard from './components/UserProfileCard';
import { useThemeColors } from '@/hooks/useThemeColors';
import { useFocusEffect } from '@react-navigation/native';

const ProfilePage = () => {
  const colors = useThemeColors();
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const dispatch = useAppDispatch();
  const userProfile = useAppSelector(selectUserProfile);
  const loading = useAppSelector(selectUserLoading);
  const { formatToYYYYMMDD } = useDateFormat();

  useFocusEffect(
    useCallback(() => {
      dispatch(getProfile());
    }, [dispatch]),
  );

  const handleEditPress = () => {
    if (userProfile && userProfile.id) {
      setIsEditModalVisible(true);
    }
  };

  return (
    <SafeAreaView
      className="flex-1"
      edges={['left', 'right']}
      style={{ backgroundColor: colors.background }}
    >
      <ProfileHeader />
      {/* Content Section */}
      <ScrollView className="flex-1 px-6 pt-6">
        {loading ? (
          <>
            <SkeletonProfile />
            <SkeletonSettings />
          </>
        ) : (
          <>
            {/* User Profile Card */}
            {userProfile && (
              <UserProfileCard
                userProfile={userProfile}
                handleEditPress={handleEditPress}
              />
            )}

            {/* Settings Sections */}
            <View
              className="rounded-3xl p-6 mb-4 shadow-sm"
              style={{ backgroundColor: colors.cardBackground }}
            >
              <Text
                className="text-base font-medium mb-2"
                style={{ color: colors.text }}
              >
                Privacy & Security
              </Text>
              <Text className="text-sm" style={{ color: colors.textMuted }}>
                Manage your privacy settings and account security.
              </Text>
            </View>
          </>
        )}
      </ScrollView>
      {userProfile && userProfile.id && (
        <ProfileEditModal
          visible={isEditModalVisible}
          onClose={() => setIsEditModalVisible(false)}
          initialData={{
            email: userProfile.email,
            firstName: userProfile.firstName,
            lastName: userProfile.lastName,
            phone: userProfile.phone,
            address: userProfile.address,
            bio: userProfile.bio,
            avatar: userProfile.avatar,
            dateOfBirth: userProfile.dateOfBirth
              ? formatToYYYYMMDD(userProfile.dateOfBirth)
              : undefined,
          }}
        />
      )}
    </SafeAreaView>
  );
};

export default ProfilePage;
