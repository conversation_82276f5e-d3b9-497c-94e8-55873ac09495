import { Text, TouchableOpacity, StatusBar, Animated } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useEffect, useRef } from 'react';
import { useThemeColors } from '@/hooks/useThemeColors';

const ProfileHeader = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const colors = useThemeColors();

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <>
      <StatusBar
        barStyle="light-content"
        backgroundColor="#6366F1"
        translucent={false}
      />

      <LinearGradient
        colors={['#6366F1', '#4F46E5']}
        style={{
          paddingHorizontal: 24,
          paddingTop: insets.top,
          paddingBottom: 32,
          borderBottomLeftRadius: 24,
          borderBottomRightRadius: 24,
        }}
      >
        {/* Back Button */}
        <TouchableOpacity
          onPress={handleBackPress}
          className="absolute left-5 bg-white/20 rounded-full p-2 items-center justify-center z-10 shadow-sm"
          style={{
            top: insets.top + 30,
          }}
          activeOpacity={0.7}
          hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
        >
          <Ionicons name="arrow-back" size={20} color="white" />
        </TouchableOpacity>

        <Animated.View
          className="items-center"
          style={{
            marginTop: 35,
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}
        >
          <Text className="text-white text-3xl font-bold mb-3 text-center">
            Account Settings
          </Text>
          <Text className="text-white/80 text-lg text-center">
            Manage your personal information and preferences
          </Text>
        </Animated.View>
      </LinearGradient>
    </>
  );
};

export default ProfileHeader;
