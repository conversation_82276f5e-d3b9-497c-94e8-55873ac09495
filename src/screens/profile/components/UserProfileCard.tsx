import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Avatar from '@/ui/Avatar';
import { useThemeColors } from '@/hooks/useThemeColors';
interface UserProfileCardProps {
  userProfile: any;
  handleEditPress: () => void;
}

const DEFAULT_AVATAR_URL =
  'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';

const UserProfileCard = ({
  userProfile,
  handleEditPress,
}: UserProfileCardProps) => {
  const colors = useThemeColors();
  const getAvatarUrl = () => {
    return userProfile.avatar || DEFAULT_AVATAR_URL;
  };

  return (
    <View
      className="rounded-3xl p-6 mb-4 shadow-sm"
      style={{ backgroundColor: colors.cardBackground }}
    >
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-lg font-semibold" style={{ color: colors.text }}>
          Personal Information
        </Text>
        <TouchableOpacity onPress={handleEditPress}>
          <Text className="font-medium" style={{ color: colors.primary }}>
            Edit
          </Text>
        </TouchableOpacity>
      </View>

      {/* Avatar Section */}
      <View className="items-center mb-6">
        <Avatar
          size={80}
          className="mb-3"
          imageUrl={getAvatarUrl()}
          borderColor="#E5E7EB"
          onPress={handleEditPress}
        />
      </View>

      {/* Profile Info */}
      <View className="space-y-4">
        <View className="flex-row items-center">
          <View className="w-8 h-8 items-center justify-center mr-3">
            <Ionicons name="mail-outline" size={20} color={colors.icon} />
          </View>
          <Text className="flex-1" style={{ color: colors.text }}>
            {userProfile.email}
          </Text>
        </View>

        {userProfile.phone && (
          <View className="flex-row items-center">
            <View className="w-8 h-8 items-center justify-center mr-3">
              <Ionicons name="call-outline" size={20} color={colors.icon} />
            </View>
            <Text className="flex-1" style={{ color: colors.text }}>
              {userProfile.phone}
            </Text>
          </View>
        )}

        {userProfile.address && (
          <View className="flex-row items-center">
            <View className="w-8 h-8 items-center justify-center mr-3">
              <Ionicons name="location-outline" size={20} color={colors.icon} />
            </View>
            <Text className="flex-1" style={{ color: colors.text }}>
              {userProfile.address}
            </Text>
          </View>
        )}

        {userProfile.dateOfBirth && (
          <View className="flex-row items-center">
            <View className="w-8 h-8 items-center justify-center mr-3">
              <Ionicons name="calendar-outline" size={20} color={colors.icon} />
            </View>
            <Text className="flex-1" style={{ color: colors.text }}>
              {new Date(userProfile.dateOfBirth).toLocaleDateString('vi-VN')}
            </Text>
          </View>
        )}

        {userProfile.bio && (
          <View className="mt-4 pt-4 border-t border-gray-100">
            <Text
              className="text-base font-medium mb-2"
              style={{ color: colors.text }}
            >
              About Me
            </Text>
            <Text className="text-sm leading-5" style={{ color: colors.text }}>
              {userProfile.bio}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

export default UserProfileCard;
