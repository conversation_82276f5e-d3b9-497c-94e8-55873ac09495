import { View } from 'react-native';
import SkeletonLoader from './SkeletonLoader';
import { useThemeColors } from '@/hooks/useThemeColors';

const SkeletonProfile = () => {
  const colors = useThemeColors();

  return (
    <View
      className="bg-white rounded-xl p-6 mb-4 shadow-sm"
      style={{ backgroundColor: colors.cardBackground }}
    >
      <View className="flex-row items-center justify-between mb-4">
        <SkeletonLoader width="33%" height={20} style={{ marginBottom: 4 }} />
        <SkeletonLoader width={48} height={16} />
      </View>

      {/* Avatar Skeleton */}
      <View className="items-center mb-6">
        <SkeletonLoader
          width={80}
          height={80}
          borderRadius={40}
          style={{ marginBottom: 12 }}
        />
      </View>

      {/* Profile Info Skeleton */}
      <View className="space-y-4">
        <View className="flex-row items-center">
          <SkeletonLoader
            width={32}
            height={32}
            borderRadius={16}
            style={{ marginRight: 12 }}
          />
          <SkeletonLoader width="75%" height={16} />
        </View>

        <View className="flex-row items-center">
          <SkeletonLoader
            width={32}
            height={32}
            borderRadius={16}
            style={{ marginRight: 12 }}
          />
          <SkeletonLoader width="50%" height={16} />
        </View>

        <View className="flex-row items-center">
          <SkeletonLoader
            width={32}
            height={32}
            borderRadius={16}
            style={{ marginRight: 12 }}
          />
          <SkeletonLoader width="66%" height={16} />
        </View>

        <View className="flex-row items-center">
          <SkeletonLoader
            width={32}
            height={32}
            borderRadius={16}
            style={{ marginRight: 12 }}
          />
          <SkeletonLoader width="25%" height={16} />
        </View>

        <View
          className="mt-4 pt-4 border-t border-gray-100"
          style={{ borderTopColor: colors.border }}
        >
          <SkeletonLoader width="25%" height={20} style={{ marginBottom: 8 }} />
          <SkeletonLoader
            width="100%"
            height={16}
            style={{ marginBottom: 8 }}
          />
          <SkeletonLoader width="83%" height={16} style={{ marginBottom: 8 }} />
          <SkeletonLoader width="66%" height={16} />
        </View>
      </View>
    </View>
  );
};

export default SkeletonProfile;
