import { useEffect, useRef } from 'react';
import {
  Animated,
  StyleSheet,
  ViewProps,
  ViewStyle,
  DimensionValue,
} from 'react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

interface SkeletonProps extends ViewProps {
  width?: DimensionValue;
  height?: DimensionValue;
  borderRadius?: number;
}

const SkeletonLoader = ({
  width = '100%',
  height = 15,
  borderRadius = 4,
  style,
  ...props
}: SkeletonProps) => {
  const colors = useThemeColors();
  const animatedValue = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    // Animation loop
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0.3,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [animatedValue]);

  const viewStyle: ViewStyle = {
    width: width,
    height: height,
    borderRadius: borderRadius ?? 4,
  };

  return (
    <Animated.View
      style={[
        styles.skeleton,
        viewStyle,
        { opacity: animatedValue, backgroundColor: colors.borderLight },
        style,
      ]}
      {...props}
    />
  );
};

const styles = StyleSheet.create({
  skeleton: {
    // backgroundColor will be set dynamically by theme
  },
});

export default SkeletonLoader;
