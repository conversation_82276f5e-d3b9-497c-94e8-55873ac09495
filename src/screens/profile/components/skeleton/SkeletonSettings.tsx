import { View } from 'react-native';
import SkeletonLoader from './SkeletonLoader';
import { useThemeColors } from '@/hooks/useThemeColors';

const SkeletonSettings = () => {
  const colors = useThemeColors();

  return (
    <View
      className="bg-white rounded-3xl p-6 mb-4 shadow-sm"
      style={{ backgroundColor: colors.cardBackground }}
    >
      <SkeletonLoader width="33%" height={20} style={{ marginBottom: 12 }} />
      <SkeletonLoader width="83%" height={16} />
    </View>
  );
};

export default SkeletonSettings;
