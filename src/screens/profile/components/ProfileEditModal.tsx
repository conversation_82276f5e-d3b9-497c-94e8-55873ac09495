import { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image,
  ActivityIndicator,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Ionicons } from '@expo/vector-icons';
import { Controller } from 'react-hook-form';
import { Input, Textarea } from '@/components/ui';
import { useAppSelector } from '@/redux/hooks';
import {
  selectIsUpdatingProfile,
  selectUserProfile,
} from '@/redux/selectors/userSelector';
import {
  useProfileForm,
  ProfileFormData,
  ProfileData,
  useAvatarPicker,
  useDateFormat,
} from '../hooks';
import { useThemeColors } from '@/hooks/useThemeColors';

interface ProfileEditModalProps {
  visible: boolean;
  onClose: () => void;
  initialData: ProfileData;
}

const ProfileEditModal = ({
  visible,
  onClose,
  initialData,
}: ProfileEditModalProps) => {
  const colors = useThemeColors();
  const isUpdatingProfile = useAppSelector(selectIsUpdatingProfile);
  const currentUserProfile = useAppSelector(selectUserProfile);

  const { control, errors, handleSubmit, reset, isUpdating, onSubmit } =
    useProfileForm(initialData);
  const { selectedAvatar, handleAvatarPress, isUploadingAvatar } =
    useAvatarPicker();

  const handleSave = handleSubmit(async (data: ProfileFormData) => {
    const success = await onSubmit(data);
    if (success) {
      onClose();
    }
  });

  const handleCancel = () => {
    reset();
    onClose();
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const isLoading = isUpdatingProfile || isUploadingAvatar;

  const { formatToYYYYMMDD, parseFromYYYYMMDD, formatDateToYYYYMMDD } =
    useDateFormat();

  if (initialData.dateOfBirth) {
    initialData.dateOfBirth = formatToYYYYMMDD(initialData.dateOfBirth);
  }

  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [selectedDate, setSelectedDate] = useState<Date>(
    parseFromYYYYMMDD(initialData.dateOfBirth),
  );

  const handleOpenDatePicker = (): void => {
    setShowDatePicker(true);
  };

  const handleDateChange = (event: any, date?: Date | null): void => {
    setShowDatePicker(Platform.OS === 'ios');
    if (date && event.type !== 'dismissed') {
      // Kiểm tra ngày không được lớn hơn ngày hiện tại
      const currentDate = new Date();
      currentDate.setHours(23, 59, 59, 999); // Set to end of day

      if (date <= currentDate) {
        setSelectedDate(date);
        const formattedDate = formatDateToYYYYMMDD(date);
        initialData.dateOfBirth = formattedDate;
      } else {
        // Nếu chọn ngày trong tương lai, reset về ngày hiện tại
        console.warn('Cannot select future date');
        setSelectedDate(currentDate);
        const formattedDate = formatDateToYYYYMMDD(currentDate);
        initialData.dateOfBirth = formattedDate;
      }
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleCancel}
      style={{ backgroundColor: colors.surface }}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
        style={{ backgroundColor: colors.background }}
      >
        {/* Header */}
        <View
          className="px-6 py-4 pt-12 border-b"
          style={{
            backgroundColor: colors.surface,
            borderBottomColor: colors.border,
          }}
        >
          <View className="flex-row items-center justify-between">
            <TouchableOpacity onPress={handleCancel} disabled={isLoading}>
              <Text
                className="text-base font-medium"
                style={{ color: colors.primary }}
              >
                Cancel
              </Text>
            </TouchableOpacity>
            <Text
              className="text-lg font-semibold"
              style={{ color: colors.text }}
            >
              Edit Profile
            </Text>
            <TouchableOpacity onPress={handleSave} disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color={colors.primary} />
              ) : (
                <Text
                  className="text-base font-medium"
                  style={{ color: colors.primary }}
                >
                  Save
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView
          className="flex-1"
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          style={{ backgroundColor: colors.surface }}
        >
          {/* Avatar Section */}
          <View
            className="px-6 py-8 items-center"
            style={{ backgroundColor: colors.surface }}
          >
            <TouchableOpacity
              onPress={handleAvatarPress}
              disabled={isUploadingAvatar}
              className="relative"
            >
              {selectedAvatar ||
              currentUserProfile.avatar ||
              initialData.avatar ? (
                <Image
                  source={{
                    uri:
                      selectedAvatar ||
                      currentUserProfile.avatar ||
                      initialData.avatar,
                  }}
                  className="w-32 h-32 rounded-full"
                  style={{ backgroundColor: colors.borderLight }}
                />
              ) : (
                <View
                  className="w-32 h-32 rounded-full items-center justify-center"
                  style={{ backgroundColor: colors.primary }}
                >
                  <Text className="text-white text-3xl font-bold">
                    {getInitials(initialData.firstName, initialData.lastName)}
                  </Text>
                </View>
              )}

              {/* Upload indicator */}
              {isUploadingAvatar && (
                <View className="absolute inset-0 bg-black/50 rounded-full items-center justify-center">
                  <ActivityIndicator size="large" color="#ffffff" />
                </View>
              )}

              {/* Camera icon */}
              <View
                className="absolute bottom-0 right-0 w-10 h-10 rounded-full items-center justify-center border-4"
                style={{
                  backgroundColor: colors.primary,
                  borderColor: colors.surface,
                }}
              >
                <Ionicons name="camera" size={20} color="white" />
              </View>
            </TouchableOpacity>
            <Text
              className="mt-3 text-base font-medium"
              style={{ color: colors.primary }}
            >
              Change Photo
            </Text>
          </View>

          {/* Bio Section */}
          <View
            className="px-6 py-6 mt-2"
            style={{ backgroundColor: colors.surface }}
          >
            <View className="flex-row items-center justify-between mb-4">
              <Text
                className="text-xl font-bold"
                style={{ color: colors.text }}
              >
                Bio
              </Text>
            </View>

            <Controller
              control={control}
              name="bio"
              render={({ field: { onChange, value } }) => (
                <View>
                  <Textarea
                    value={value}
                    onChangeText={onChange}
                    placeholder="Add a bio..."
                    className="min-h-[60px] text-base border-0 p-0"
                    style={{
                      borderWidth: 0,
                      backgroundColor: 'transparent',
                      textAlignVertical: 'top',
                      color: colors.text,
                    }}
                    maxLength={500}
                    multiline
                    editable={!isLoading}
                  />
                  {value && (
                    <Text
                      className="text-sm mt-2"
                      style={{ color: colors.text }}
                    >
                      {value.length}/500
                    </Text>
                  )}
                  {errors.bio && (
                    <Text
                      className="text-red-500 text-sm mt-1"
                      style={{ color: colors.text }}
                    >
                      {errors.bio.message}
                    </Text>
                  )}
                </View>
              )}
            />
          </View>

          {/* Personal Information */}
          <View
            className="px-6 py-6 mt-2"
            style={{ backgroundColor: colors.surface }}
          >
            <View className="flex-row items-center justify-between mb-4">
              <Text
                className="text-xl font-bold"
                style={{ color: colors.text }}
              >
                Personal Information
              </Text>
            </View>

            {/* Email - Read only */}
            {initialData.email && (
              <View
                className="flex-row items-center mb-4 p-4 rounded-lg"
                style={{ backgroundColor: colors.backgroundSecondary }}
              >
                <Ionicons name="mail-outline" size={20} color={colors.icon} />
                <Text
                  className="text-base ml-3 flex-1"
                  style={{ color: colors.text }}
                >
                  {initialData.email}
                </Text>
              </View>
            )}

            {/* First Name */}
            <Controller
              control={control}
              name="firstName"
              render={({ field: { onChange, value } }) => (
                <Input
                  style={{ color: colors.text }}
                  labelClassName="mb-1"
                  label="First Name"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Enter your first name"
                  error={errors.firstName?.message}
                  editable={!isLoading}
                />
              )}
            />

            {/* Last Name */}
            <Controller
              control={control}
              name="lastName"
              render={({ field: { onChange, value } }) => (
                <Input
                  style={{ color: colors.text }}
                  labelClassName="mb-1"
                  label="Last Name"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Enter your last name"
                  error={errors.lastName?.message}
                  editable={!isLoading}
                />
              )}
            />

            {/* Phone */}
            <Controller
              control={control}
              name="phone"
              render={({ field: { onChange, value } }) => (
                <Input
                  style={{ color: colors.text }}
                  labelClassName="mb-1"
                  label="Phone"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Enter your phone number"
                  keyboardType="phone-pad"
                  error={errors.phone?.message}
                  editable={!isLoading}
                />
              )}
            />

            {/* Date of Birth */}
            <Controller
              control={control}
              name="dateOfBirth"
              render={({ field: { onChange, value } }) => (
                <View className="mb-4">
                  <View>
                    <Text
                      className="text-sm mb-1 font-medium leading-none"
                      style={{ color: colors.text }}
                    >
                      Date of Birth
                    </Text>

                    {/* Custom Input with Calendar Icon */}
                    <TouchableOpacity
                      onPress={handleOpenDatePicker}
                      disabled={isLoading}
                      className="flex-row items-center border rounded-xl px-4 py-2.5"
                      style={{
                        borderColor: colors.border,
                        backgroundColor: colors.surface,
                      }}
                    >
                      <Text
                        className={`flex-1 text-base`}
                        style={{
                          color: value ? colors.text : colors.textSecondary,
                        }}
                      >
                        {value || 'YYYY-MM-DD'}
                      </Text>
                      <Ionicons
                        name="calendar-outline"
                        size={20}
                        color={colors.icon}
                      />
                    </TouchableOpacity>

                    {errors.dateOfBirth && (
                      <Text className="text-red-500 mt-1 text-sm">
                        {errors.dateOfBirth.message}
                      </Text>
                    )}
                  </View>

                  {showDatePicker && (
                    <DateTimePicker
                      value={selectedDate}
                      mode="date"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      maximumDate={new Date()} // Giới hạn không cho chọn ngày trong tương lai
                      onChange={(event, date) => {
                        handleDateChange(event, date);
                        if (date) {
                          const formattedDate = formatDateToYYYYMMDD(date);
                          onChange(formattedDate);
                        }
                      }}
                    />
                  )}
                </View>
              )}
            />

            {/* Address */}
            <Controller
              control={control}
              name="address"
              render={({ field: { onChange, value } }) => (
                <Input
                  style={{ color: colors.text }}
                  labelClassName="mb-1"
                  label="Address"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Enter your address"
                  error={errors.address?.message}
                  editable={!isLoading}
                />
              )}
            />
          </View>

          {/* Save Button */}
          <View className="p-6" style={{ backgroundColor: colors.surface }}>
            <TouchableOpacity
              onPress={handleSave}
              disabled={isLoading}
              className="py-4 rounded-2xl"
              style={{
                backgroundColor: isLoading ? colors.textMuted : colors.primary,
              }}
              activeOpacity={0.8}
            >
              {isLoading ? (
                <View className="flex-row items-center justify-center">
                  <ActivityIndicator size="small" color="#ffffff" />
                  <Text className="text-white text-center text-base font-semibold ml-2">
                    Saving...
                  </Text>
                </View>
              ) : (
                <Text className="text-white text-center text-base font-semibold">
                  Save Changes
                </Text>
              )}
            </TouchableOpacity>
          </View>

          <View className="h-8" />
        </ScrollView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default ProfileEditModal;
