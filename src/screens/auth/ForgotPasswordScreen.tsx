import React from 'react';
import { StatusBar, View, TouchableOpacity } from 'react-native';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input, Button, P, H2 } from '@/components/ui';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { forgotPasswordSchema } from './schemas/authSchema';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from './hooks/useAuth';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@/common/types/rootParamList';
import { SafeAreaView } from 'react-native-safe-area-context';

const ForgotPasswordScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();

  const { sendOtp, isLoading } = useAuth(navigation);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<z.infer<typeof forgotPasswordSchema>>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: z.infer<typeof forgotPasswordSchema>) => {
    await sendOtp({ email: data.email });
  };

  return (
    <SafeAreaView className="flex-1">
      <StatusBar barStyle="dark-content" backgroundColor="#7C3AED" />

      <LinearGradient
        colors={['#7C3AED', '#8B5CF6', '#A855F7']}
        className="flex-1"
      >
        {/* Header */}
        <View className="flex-row items-center px-4 pt-12 pb-5">
          <TouchableOpacity className="mr-4" onPress={() => navigation.pop()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <P className="text-lg font-semibold text-white">Forgot Password</P>
        </View>

        {/* Content Container */}
        <View className="bg-white rounded-t-3xl mt-8 p-4">
          <View className="items-center w-full">
            <H2 className="font-bold text-gray-800 mb-3">Mail Address Here</H2>
            <P className="text-gray-500 text-center mb-8 px-4 leading-6 text-sm">
              Enter the email address associated with your account.
            </P>

            <View className="w-full mb-8">
              <P className="text-left text-gray-600 mb-2 font-medium">Email</P>
              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, value } }) => (
                  <Input
                    value={value}
                    onChangeText={onChange}
                    placeholder="<EMAIL>"
                    error={errors.email?.message}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    className="w-full"
                  />
                )}
              />
            </View>

            <Button
              onPress={handleSubmit(onSubmit)}
              className="w-full bg-purple-600 p-4 rounded-xl shadow-lg"
              disabled={isLoading}
            >
              <P className="text-white font-semibold text-center text-base">
                {isLoading ? 'Sending...' : 'Recover Password'}
              </P>
            </Button>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default ForgotPasswordScreen;
