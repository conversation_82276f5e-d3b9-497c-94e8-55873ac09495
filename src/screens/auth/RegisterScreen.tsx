import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { registerSchema } from './schemas/authSchema';
import AppLogo from '@/components/ui/AppLogo';
import { useAuth } from './hooks/useAuth';
import { SafeAreaView } from 'react-native-safe-area-context';
import { RegisterFormInputs, TermsAgreement } from './components';
import {
  View,
  Text,
  TouchableOpacity,
  ImageBackground,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@/common/types/rootParamList';

const RegisterScreen = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
  });

  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { register, isLoading } = useAuth();

  const onSubmit = (data: z.infer<typeof registerSchema>) => {
    register(data);
  };

  return (
    <>
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />
      <View style={{ flex: 1, backgroundColor: '#F8FAFC' }}>
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '45%',
          }}
        >
          <ImageBackground
            source={require('../../../assets/images/background-login.jpg')}
            style={{
              flex: 1,
              width: '100%',
              height: '100%',
            }}
            resizeMode="cover"
            blurRadius={3}
          >
            {/* Overlay for better readability */}
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(102, 126, 234, 0.6)',
              }}
            />
          </ImageBackground>
        </View>

        <SafeAreaView style={{ flex: 1 }}>
          {/* Header Section - Background area */}
          <View style={{ flex: 0.28, paddingHorizontal: 20 }}>
            <View className="pt-2 pb-1">
              <View className="items-center">
                <View style={{ backgroundColor: 'transparent' }}>
                  <AppLogo />
                </View>
              </View>
            </View>
          </View>

          {/* Main Content - White bottom section */}
          <View
            style={{
              flex: 0.72,
              backgroundColor: 'white',
              borderTopLeftRadius: 30,
              borderTopRightRadius: 30,
              paddingHorizontal: 24,
              paddingTop: 24,
              paddingBottom: 12,
            }}
          >
            <RegisterFormInputs
              control={control}
              errors={errors}
              isLoading={isLoading}
            />

            <TermsAgreement control={control} isLoading={isLoading} />

            <TouchableOpacity
              onPress={handleSubmit(onSubmit)}
              disabled={isLoading}
              className="rounded-xl py-2.5 mt-3 mb-1"
              style={{
                backgroundColor: isLoading ? '#667eeaAA' : '#667eea',
                shadowColor: '#667eea',
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text className="text-white font-semibold text-base text-center">
                  Đăng ký
                </Text>
              )}
            </TouchableOpacity>

            {/* Bottom Section */}
            <View className="mt-1">
              <TouchableOpacity
                onPress={() => navigation.goBack()}
                className="py-3"
              >
                <Text className="text-slate-600 text-center text-sm ">
                  Bạn đã có tài khoản?{' '}
                  <Text className="font-semibold" style={{ color: '#856EF3' }}>
                    Đăng nhập
                  </Text>
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </>
  );
};

export default RegisterScreen;
