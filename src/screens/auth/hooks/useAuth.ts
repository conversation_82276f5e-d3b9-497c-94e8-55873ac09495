import { useAuthContext } from '@/providers/AuthProvider';
import { verifyOtp } from './../../../redux/thunks/authThunks';
import { useAppDispatch } from '@/redux/hooks';
import {
  selectAuthLoading,
  selectOtpVerified,
  selectResetEmail,
  selectVerifiedOtp,
} from '@/redux/selectors';
import { clearOtpState, setResetEmail } from '@/redux/slices/authSlice';
import {
  createOtp,
  forgotPassword,
  loginUser,
  logoutUser,
  registerUser,
} from '@/redux/thunks/authThunks';
import { getProfile } from '@/redux/thunks/userThunks';
import {
  CreateOtpCredentials,
  ForgotPasswordCredentials,
  LoginCredentials,
  RegisterCredentials,
  VerifyOtpCredentials,
} from '@/services/types/auth';
import { showAuthToast } from '@/utils/toastUtils';
import { useSelector } from 'react-redux';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import {
  AuthStackParamList,
  RootStackParamList,
} from '@/common/types/rootParamList';
import { useNavigation } from '@react-navigation/native';

export const useAuth = (
  navigation?: NativeStackNavigationProp<AuthStackParamList>,
) => {
  const dispatch = useAppDispatch();
  const navigate =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { checkAuth } = useAuthContext();

  const isLoading = useSelector(selectAuthLoading);
  const otpVerified = useSelector(selectOtpVerified);
  const resetEmail = useSelector(selectResetEmail);
  const verifiedOtp = useSelector(selectVerifiedOtp);

  const login = async (payload: LoginCredentials) => {
    try {
      const responseLogin = await dispatch(loginUser(payload));
      if (loginUser.fulfilled.match(responseLogin)) {
        showAuthToast.success('Login successfully');
        const responseProfile = await dispatch(getProfile());
        if (getProfile.fulfilled.match(responseProfile)) {
          await checkAuth();
        }
      } else {
        showAuthToast.error('Login failed');
      }
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const register = async (payload: RegisterCredentials) => {
    try {
      const responseRegister = await dispatch(registerUser(payload));
      if (registerUser.fulfilled.match(responseRegister)) {
        showAuthToast.success('Register successfully');
        navigate.reset({
          index: 0,
          routes: [{ name: 'Login' as never }],
        });
      } else {
        showAuthToast.error('Register failed');
      }
    } catch (error) {
      console.error('Register failed:', error);
    }
  };

  const sendOtp = async (payload: CreateOtpCredentials) => {
    try {
      const responseCreateOtp = await dispatch(createOtp(payload));
      if (createOtp.fulfilled.match(responseCreateOtp)) {
        dispatch(setResetEmail(payload.email));
        showAuthToast.success('OTP sent to your email');
        navigation?.navigate('EmailVerification');
      } else {
        showAuthToast.error('Failed to send OTP');
      }
    } catch (error) {
      console.error('Send OTP failed:', error);
    }
  };

  const verifyOtpCode = async (payload: VerifyOtpCredentials) => {
    try {
      const response = await dispatch(verifyOtp(payload));
      if (verifyOtp.fulfilled.match(response)) {
        showAuthToast.success('OTP verified successfully');
        console.log('navigate to reset password:', navigation);
        navigation?.navigate('ResetPassword');
      } else {
        showAuthToast.error('Invalid OTP');
      }
    } catch (error) {
      console.error('Verify OTP failed:', error);
    }
  };

  const resetPassword = async (payload: ForgotPasswordCredentials) => {
    try {
      const response = await dispatch(forgotPassword(payload));
      if (forgotPassword.fulfilled.match(response)) {
        showAuthToast.success('Password reset successfully');
        dispatch(clearOtpState());
        navigate.reset({
          index: 0,
          routes: [{ name: 'Login' as never }],
        });
        return true;
      } else {
        showAuthToast.error('Failed to reset password');
        return false;
      }
    } catch (error) {
      console.error('Reset password failed:', error);
      return false;
    }
  };

  const logout = () => {
    dispatch(logoutUser());
    dispatch(clearOtpState());
  };

  return {
    login,
    register,
    logout,
    sendOtp,
    verifyOtpCode,
    resetPassword,
    otpVerified,
    verifiedOtp,
    resetEmail,
    isLoading,
  };
};
