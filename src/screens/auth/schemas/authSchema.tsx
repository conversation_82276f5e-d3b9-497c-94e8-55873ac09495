import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email('<PERSON><PERSON> không hợp lệ'),
  password: z.string().min(6, 'M<PERSON>t khẩu phải có ít nhất 6 ký  tự'),
});

const registerSchema = z
  .object({
    firstName: z.string().min(1, '<PERSON><PERSON> không được để trống'),
    lastName: z.string().min(1, 'Tên không được để trống'),
    email: z.string().email('Email không hợp lệ'),
    password: z.string().min(6, '<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự'),
    confirmPassword: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
    termsAgreement: z.boolean().refine((val) => val === true, {
      message: '<PERSON><PERSON><PERSON> phải đồng ý với Điều khoản dịch vụ và <PERSON>h sách bảo mật',
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Mật khẩu không khớp',
    path: ['confirmPassword'],
  });
const forgotPasswordSchema = z.object({
  email: z.string().email('Email không hợp lệ'),
});
const resetPasswordSchema = z.object({
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z
    .string()
    .min(6, 'Confirm Password must be at least 6 characters'),
  // other fields if any
});
export {
  loginSchema,
  registerSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
};
