import React, { useState } from 'react';
import { View, ActivityIndicator, TouchableOpacity, Text, ImageBackground, StatusBar } from 'react-native';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { loginSchema } from './schemas/authSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import AppLogo from '@/components/ui/AppLogo';
import { useAuth } from './hooks/useAuth';
import { useNavigation } from '@react-navigation/native';
import { LoginCredentials } from '@/services/types/auth';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AuthStackParamList } from '@/common/types/rootParamList';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ForgotPasswordLink, GoogleLogin, LoginFormInputs } from './components';

const LoginScreen = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
  });

  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { login, isLoading } = useAuth(navigation);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const onSubmit = async (data: LoginCredentials) => {
    try {
      await login(data);
      reset();
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    try {
      console.log('Login with Google');
    } catch (err) {
      console.error('Google login failed:', err);
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={{ flex: 1, backgroundColor: '#F8FAFC' }}>
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '45%',
          }}
        >
          <ImageBackground
            source={require('../../../assets/images/background-login.jpg')} // Thay đổi đường dẫn ảnh theo project của bạn
            style={{
              flex: 1,
              width: '100%',
              height: '100%',
            }}
            resizeMode="cover"
            blurRadius={3} // Độ mờ của ảnh
          >
            {/* Overlay for better readability */}
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(102, 126, 234, 0.6)', // Overlay màu với độ trong suốt
              }}
            />
          </ImageBackground>
        </View>

        <SafeAreaView style={{ flex: 1 }}>
          {/* Header Section - Background area */}
          <View style={{ flex: 0.4, paddingHorizontal: 20 }}>
            <View className="pt-8 pb-4">
              <View className="items-center">
                <View style={{ backgroundColor: 'transparent' }}>
                  <AppLogo />
                </View>
              </View>
            </View>
          </View>

          {/* Main Content - White bottom section */}
          <View
            style={{
              flex: 0.6,
              backgroundColor: 'white',
              borderTopLeftRadius: 30,
              borderTopRightRadius: 30,
              paddingHorizontal: 24,
              paddingTop: 32,
              paddingBottom: 20,
            }}
          >
            <LoginFormInputs control={control} errors={errors} />

            <ForgotPasswordLink navigation={navigation} />

            <TouchableOpacity
              onPress={handleSubmit(onSubmit)}
              disabled={isLoading}
              className="rounded-xl py-2.5 mt-3 mb-1"
              style={{
                backgroundColor: isLoading ? '#667eeaAA' : '#667eea',
                shadowColor: '#667eea',
                shadowOffset: { width: 0, height: 3 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 6,
              }}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text className="text-white font-semibold text-base text-center">
                  Đăng nhập
                </Text>
              )}
            </TouchableOpacity>

            <GoogleLogin
              navigation={navigation}
              isGoogleLoading={isGoogleLoading}
              handleGoogleLogin={handleGoogleLogin}
            />

            {/* Bottom Section */}
            <View className="mt-6">
              <TouchableOpacity
                onPress={() => navigation.navigate('Register')}
                className="py-3"
              >
                <Text className="text-slate-600 text-center text-sm">
                  Chưa có tài khoản?{' '}
                  <Text
                    className="font-semibold"
                    style={{ color: '#667eea' }}
                  >
                    Đăng ký ngay
                  </Text>
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </>
  );
};

export default LoginScreen;