import React from 'react';
import { Controller, Control, FieldErrors } from 'react-hook-form';
import { View } from 'react-native';
import { z } from 'zod';
import { Input } from '@/components/ui';
import { registerSchema } from '../schemas/authSchema';

type RegisterFormInputsProps = {
  control: Control<z.infer<typeof registerSchema>>;
  errors: FieldErrors<z.infer<typeof registerSchema>>;
  isLoading: boolean;
};

const RegisterFormInputs = ({
  control,
  errors,
  isLoading,
}: RegisterFormInputsProps) => {
  return (
    <View style={{ gap: 16 }}>
      {/* Name Fields Row */}
      <View style={{ flexDirection: 'row', gap: 12 }}>
        <View style={{ flex: 1 }}>
          <Controller
            control={control}
            name="firstName"
            render={({ field: { onChange, value } }) => (
              <Input
                label="Họ"
                value={value}
                onChangeText={onChange}
                placeholder="Nguyen"
                error={errors.firstName?.message}
                editable={!isLoading}
                style={{
                  borderRadius: 12,
                  borderWidth: 1.5,
                  borderColor: errors.firstName ? '#ef4444' : '#e2e8f0',
                  backgroundColor: '#f8fafc',
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                }}
              />
            )}
          />
        </View>
        <View style={{ flex: 1 }}>
          <Controller
            control={control}
            name="lastName"
            render={({ field: { onChange, value } }) => (
              <Input
                label="Tên"
                value={value}
                onChangeText={onChange}
                placeholder="Van A"
                error={errors.lastName?.message}
                editable={!isLoading}
                style={{
                  borderRadius: 12,
                  borderWidth: 1.5,
                  borderColor: errors.lastName ? '#ef4444' : '#e2e8f0',
                  backgroundColor: '#f8fafc',
                  paddingHorizontal: 16,
                  paddingVertical: 14,
                }}
              />
            )}
          />
        </View>
      </View>
      {/* Email Field */}
      <Controller
        control={control}
        name="email"
        render={({ field: { onChange, value } }) => (
          <Input
            label="Email"
            value={value}
            onChangeText={onChange}
            placeholder="<EMAIL>"
            error={errors.email?.message}
            editable={!isLoading}
            style={{
              borderRadius: 12,
              borderWidth: 1.5,
              borderColor: errors.email ? '#ef4444' : '#e2e8f0',
              backgroundColor: '#f8fafc',
              paddingHorizontal: 16,
              paddingVertical: 14,
            }}
          />
        )}
      />

      {/* Password Field */}
      <Controller
        control={control}
        name="password"
        render={({ field: { onChange, value } }) => (
          <Input
            label="Mật khẩu"
            value={value}
            onChangeText={onChange}
            placeholder="••••••••"
            type="password"
            error={errors.password?.message}
            editable={!isLoading}
            style={{
              borderRadius: 12,
              borderWidth: 1.5,
              borderColor: errors.password ? '#ef4444' : '#e2e8f0',
              backgroundColor: '#f8fafc',
              paddingHorizontal: 16,
              paddingVertical: 14,
            }}
          />
        )}
      />

      {/* Confirm Password Field */}
      <Controller
        control={control}
        name="confirmPassword"
        render={({ field: { onChange, value } }) => (
          <Input
            label="Xác nhận mật khẩu"
            value={value}
            onChangeText={onChange}
            placeholder="••••••••"
            type="password"
            error={errors.confirmPassword?.message}
            editable={!isLoading}
            style={{
              borderRadius: 12,
              borderWidth: 1.5,
              borderColor: errors.confirmPassword ? '#ef4444' : '#e2e8f0',
              backgroundColor: '#f8fafc',
              paddingHorizontal: 16,
              paddingVertical: 14,
            }}
          />
        )}
      />
    </View>
  );
};

export default RegisterFormInputs;
