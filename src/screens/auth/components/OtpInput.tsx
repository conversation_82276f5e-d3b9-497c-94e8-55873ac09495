import React, { useRef } from 'react';
import { View, TextInput } from 'react-native';

type CodeInputProps = {
  code: string[];
  setCode: React.Dispatch<React.SetStateAction<string[]>>;
  handleVerify: (otpCodeAuto?: string[]) => Promise<void>;
};

const CodeInput: React.FC<CodeInputProps> = ({
  code,
  setCode,
  handleVerify,
}) => {
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }

      if (newCode.join('').length === 6) {
        handleVerify(newCode);
      }
    }
  };

  const handleKeyPress = (index: number, key: string) => {
    if (key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <View className="flex-row justify-between mb-12 w-full">
      {code.map((digit, index) => (
        <TextInput
          key={index}
          ref={(ref) => {
            inputRefs.current[index] = ref;
          }}
          className="w-16 h-16 border-2 border-gray-300 rounded-2xl text-3xl font-bold text-gray-900 bg-white text-center focus:border-purple-600 focus:shadow-md"
          value={digit}
          onChangeText={(value) => handleCodeChange(index, value)}
          onKeyPress={({ nativeEvent }) =>
            handleKeyPress(index, nativeEvent.key)
          }
          keyboardType="numeric"
          maxLength={1}
          selectTextOnFocus={false}
        />
      ))}
    </View>
  );
};

export default CodeInput;
