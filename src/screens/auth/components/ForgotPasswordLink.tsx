import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { P } from '@/components/ui';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@/common/types/rootParamList';

type ForgotPasswordLinkProps = {
  navigation: NativeStackNavigationProp<AuthStackParamList>;
};

const ForgotPasswordLink = ({ navigation }: ForgotPasswordLinkProps) => {
  return (
    <View className="flex-row justify-end mt-2">
      <TouchableOpacity
        onPress={() => navigation.navigate('ForgotPassword')}
        accessibilityLabel="Quên mật khẩu"
        accessibilityRole="button"
      >
        <P className="text-blue-600 text-sm font-medium">Quên mật khẩu</P>
      </TouchableOpacity>
    </View>
  );
};

export default ForgotPasswordLink;
