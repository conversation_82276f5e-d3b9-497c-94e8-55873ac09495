import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

type ResendSectionProps = {
  resendCooldown: number;
  isLoading: boolean;
  handleResend: () => Promise<void>;
};

const ResendSection: React.FC<ResendSectionProps> = ({
  resendCooldown,
  isLoading,
  handleResend,
}) => {
  return (
    <View className="flex-row items-center justify-center mb-6">
      <Text className="text-gray-500 text-base">Didn't receive the code?</Text>
      <TouchableOpacity
        onPress={handleResend}
        disabled={resendCooldown > 0 || isLoading}
      >
        <Text
          className={`font-semibold ml-1 ${
            resendCooldown > 0 || isLoading
              ? 'text-gray-400'
              : 'text-purple-600 underline'
          }`}
        >
          {resendCooldown > 0 ? `Resend in ${resendCooldown}s` : 'Resend Code'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ResendSection;
