import React, { useState } from 'react';
import { View, Text } from 'react-native';
import { Controller, Control } from 'react-hook-form';
import { z } from 'zod';
import { Checkbox } from '@/components/ui/Checkbox';
import PrivacyPolicyModal from '@/components/ui/PrivacyPolicy';
import TermsOfServiceModal from '@/components/ui/TermOfService';
import { registerSchema } from '../schemas/authSchema';

type TermsAgreementProps = {
  control: Control<z.infer<typeof registerSchema>>;
  isLoading: boolean;
};

const TermsAgreement = ({ control, isLoading }: TermsAgreementProps) => {
  const [checked, setChecked] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);

  return (
    <>
      <Controller
        control={control}
        name="termsAgreement"
        render={({ field: { onChange, value } }) => (
          <View className="flex-row items-center mt-2 mb-4">
            <Checkbox
              checked={checked}
              disabled={isLoading}
              onCheckedChange={(newChecked) => {
                setChecked(newChecked);
                onChange(newChecked);
              }}
            />
            <View className="flex-1 ml-3">
              <Text
                className="text-gray-700 text-sm leading-5"
                numberOfLines={1}
                adjustsFontSizeToFit
              >
                Tôi đồng ý với{' '}
                <Text
                  className="text-blue-600 underline"
                  onPress={() => !isLoading && setShowTermsModal(true)}
                >
                  Điều khoản dịch vụ
                </Text>{' '}
                và{' '}
                <Text
                  className="text-blue-600 underline"
                  onPress={() => !isLoading && setShowPrivacyModal(true)}
                >
                  Chính sách bảo mật
                </Text>
              </Text>
            </View>
          </View>
        )}
      />
      <TermsOfServiceModal
        visible={showTermsModal}
        onClose={() => setShowTermsModal(false)}
      />
      <PrivacyPolicyModal
        visible={showPrivacyModal}
        onClose={() => setShowPrivacyModal(false)}
      />
    </>
  );
};

export default TermsAgreement;
