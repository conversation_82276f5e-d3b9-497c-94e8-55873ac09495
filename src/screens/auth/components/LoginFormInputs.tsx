import React from 'react';
import { Controller, Control, FieldErrors } from 'react-hook-form';
import { View } from 'react-native';
import { z } from 'zod';
import { Input } from '@/components/ui';
import { loginSchema } from '../schemas/authSchema';

type LoginFormInputsProps = {
  control: Control<z.infer<typeof loginSchema>>;
  errors: FieldErrors<z.infer<typeof loginSchema>>;
};

const LoginFormInputs = ({ control, errors }: LoginFormInputsProps) => {
  return (
    <View style={{ gap: 20 }}>
      {/* Email Field */}
      <Controller
        control={control}
        name="email"
        render={({ field: { onChange, value } }) => (
          <Input
            label="Email"
            value={value}
            onChangeText={onChange}
            placeholder="<EMAIL>"
            error={errors.email?.message}
            accessibilityLabel="Email"
            style={{
              borderRadius: 12,
              borderWidth: 1.5,
              borderColor: errors.email ? '#ef4444' : '#e2e8f0',
              backgroundColor: '#f8fafc',
              paddingHorizontal: 16,
              paddingVertical: 16,
              fontSize: 16,
            }}
          />
        )}
      />

      {/* Password Field */}
      <Controller
        control={control}
        name="password"
        render={({ field: { onChange, value } }) => (
          <Input
            label="Mật khẩu"
            value={value}
            onChangeText={onChange}
            placeholder="••••••••"
            type="password"
            error={errors.password?.message}
            accessibilityLabel="Mật khẩu"
            style={{
              borderRadius: 12,
              borderWidth: 1.5,
              borderColor: errors.password ? '#ef4444' : '#e2e8f0',
              backgroundColor: '#f8fafc',
              paddingHorizontal: 16,
              paddingVertical: 16,
              fontSize: 16,
            }}
          />
        )}
      />
    </View>
  );
};

export default LoginFormInputs;
