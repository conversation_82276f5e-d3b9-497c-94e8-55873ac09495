import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StatusBar } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { <PERSON><PERSON>, P } from '@/components/ui';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from './hooks/useAuth';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '@/common/types/rootParamList';
import { cn } from '@/utils/utils';
import { CodeInput, ResendSection } from './components';

const EmailVerificationScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { verifyOtpCode, sendOtp, resetEmail, isLoading } = useAuth(navigation);

  const [resendCooldown, setResendCooldown] = useState(0);
  const [code, setCode] = useState<string[]>(['', '', '', '', '', '']);

  const handleResend = async () => {
    if (resendCooldown > 0 || !resetEmail || isLoading) return;

    try {
      await sendOtp({ email: resetEmail });
      setCode(['', '', '', '', '', '']);
      setResendCooldown(60);
      const timer = setInterval(() => {
        setResendCooldown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error('Resend OTP failed:', error);
    }
  };

  const handleVerify = async (otpCodeAuto: string[] = []) => {
    const otpCode =
      otpCodeAuto.length > 0 ? otpCodeAuto.join('') : code.join('');
    if (otpCode.length !== 6 || !resetEmail || isLoading) return;

    try {
      await verifyOtpCode({
        email: resetEmail,
        otp: otpCode,
      });
    } catch (error) {
      console.error('Verify OTP failed:', error);
      // Reset code on error to allow retry
      setCode(['', '', '', '', '', '']);
    }
  };

  const isCodeComplete = code.every((digit) => digit !== '');

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#7C3AED" />

      <LinearGradient
        colors={['#7C3AED', '#8B5CF6', '#A855F7']}
        className="flex-1"
      >
        {/* Header */}
        <View className="flex-row items-center px-6 pt-16 pb-8">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="mr-5"
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text className="text-2xl font-bold text-white">
            Email Verification
          </Text>
        </View>

        {/* Content */}
        <View className="bg-white rounded-t-3xl px-6 pt-14">
          <View className="items-center">
            <Text className="text-4xl font-bold text-gray-900 mb-6">
              Verify Your Email
            </Text>
            <Text className="text-gray-600 text-center text-lg mb-12 px-6 leading-7">
              Enter the 6-digit code sent to your email to proceed.
            </Text>

            {/* Code Input */}
            <CodeInput
              code={code}
              setCode={setCode}
              handleVerify={handleVerify}
            />

            {/* Resend Text */}
            <ResendSection
              resendCooldown={resendCooldown}
              isLoading={isLoading}
              handleResend={handleResend}
            />

            {/* Verify Button */}
            <Button
              onPress={() => handleVerify()}
              className={cn('w-full py-4 rounded-xl')}
              style={{
                backgroundColor:
                  isCodeComplete && !isLoading ? '#7C3AED' : '#D1D5DB',
                shadowColor:
                  isCodeComplete && !isLoading ? '#000' : 'transparent',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: isCodeComplete && !isLoading ? 0.25 : 0,
                shadowRadius: isCodeComplete && !isLoading ? 3.84 : 0,
                elevation: isCodeComplete && !isLoading ? 5 : 0,
              }}
              disabled={!isCodeComplete || isLoading}
            >
              <P className="text-white font-bold text-xl text-center">
                {isLoading ? 'Verifying...' : 'Verify Now'}
              </P>
            </Button>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default EmailVerificationScreen;
