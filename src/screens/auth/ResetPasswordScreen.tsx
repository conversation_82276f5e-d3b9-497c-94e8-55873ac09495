import React, { useState } from 'react';
import { View, Text, StatusBar, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Button, Input, P } from '@/components/ui';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { resetPasswordSchema } from './schemas/authSchema';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from './hooks/useAuth';
import { AuthStackParamList } from '@/common/types/rootParamList';

const ResetPasswordScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const { resetPassword, resetEmail, isLoading, verifiedOtp } =
    useAuth(navigation);
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<z.infer<typeof resetPasswordSchema>>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleReset = async (data: z.infer<typeof resetPasswordSchema>) => {
    if (!resetEmail) {
      navigation.navigate('Login');
      return;
    }

    const success = await resetPassword({
      email: resetEmail,
      otp: verifiedOtp ?? '',
      password: data.password,
      confirmPassword: data.confirmPassword,
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="#7C3AED" />

      <LinearGradient colors={['#7C3AED', '#8B5CF6', '#A855F7']}>
        <View className="flex-row items-center px-6 pt-16 pb-8">
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons
              name="arrow-back"
              size={24}
              color="white"
              className="mr-5"
            />
          </TouchableOpacity>
          <Text className="text-2xl font-bold text-white">Reset Password</Text>
        </View>
        <View className="bg-white rounded-t-3xl mt-10 px-6 pt-12">
          <View className="items-center">
            <Text className="text-4xl font-bold text-gray-900 mb-6">
              Enter New Password
            </Text>
            <Text className="text-gray-600 text-center text-base mb-10 px-4 leading-6">
              Your new password must be different from previously used
              passwords.
            </Text>
            <View className="w-full">
              <Controller
                control={control}
                name="password"
                render={({ field: { onChange, value } }) => (
                  <View className="w-full mb-4">
                    <Input
                      label="New Password"
                      value={value || ''}
                      onChangeText={onChange}
                      placeholder="******"
                      error={errors.password?.message}
                      className="w-full"
                      type="password"
                    />
                  </View>
                )}
              />
            </View>
            <View className="w-full">
              <Controller
                control={control}
                name="confirmPassword"
                render={({ field: { onChange, value } }) => (
                  <View className="w-full mb-6">
                    <Input
                      label="Confirm Password"
                      value={value || ''}
                      onChangeText={onChange}
                      placeholder="******"
                      error={errors.confirmPassword?.message}
                      className="w-full"
                      type="password"
                    />
                  </View>
                )}
              />
            </View>
            <Button
              onPress={handleSubmit(handleReset)}
              className="w-full bg-purple-600 py-4 rounded-xl shadow-lg"
              disabled={isLoading}
            >
              <P className="text-white font-semibold text-center text-lg">
                {isLoading ? 'Resetting...' : 'Reset Password'}
              </P>
            </Button>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

export default ResetPasswordScreen;
