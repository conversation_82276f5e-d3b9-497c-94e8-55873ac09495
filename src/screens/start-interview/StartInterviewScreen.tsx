import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import ChatSection from './components/ChatSection';
import VideoSection from './components/VideoSection';
import { useStartInterview } from './hooks/useStartInterview';
import HeaderStartInterview from './components/HeaderStartInterview';
import { LinearGradient } from 'expo-linear-gradient';
import { formatMinutesTime } from '@/utils/timer';
import { LoadingOverlay } from '@/components/ui';
import { useThemeColors } from '@/hooks/useThemeColors';

const StartInterviewScreen = () => {
  const {
    isMuted,
    setIsMuted,
    messages,
    handleToggleCall,
    isInterviewing,
    messagesEndRef,
    handleBack,
    viewFeedbackAnalysis,
    handleFeedbackAnalysis,
    timer,
    loading,
  } = useStartInterview();
  const themeColors = useThemeColors();

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: themeColors.background }}
      edges={['top', 'left', 'right']}
    >
      <LoadingOverlay visible={loading} />

      {/* Modern Header with Back Button */}
      <HeaderStartInterview
        handleBack={handleBack}
        timeRemaining={formatMinutesTime(timer)}
      />

      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ paddingHorizontal: 24, paddingVertical: 16 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Video Section with Enhanced Design */}
        <VideoSection
          isMuted={isMuted}
          setIsMuted={setIsMuted}
          handleToggleCall={handleToggleCall}
          isInterviewing={isInterviewing}
        />

        {/* Action Buttons with Modern Design */}
        <View className="flex-row justify-center gap-3 mb-6">
          <TouchableOpacity
            disabled={!viewFeedbackAnalysis || loading}
            onPress={handleFeedbackAnalysis}
            activeOpacity={0.8}
            style={{ flex: 1 }}
          >
            <LinearGradient
              colors={
                viewFeedbackAnalysis
                  ? ['#8b5cf6', '#7c3aed']
                  : ['#d1d5db', '#9ca3af']
              }
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{
                paddingVertical: 16,
                paddingHorizontal: 24,
                borderRadius: 16,
                shadowColor: '#8b5cf6',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 8,
              }}
            >
              <View className="flex-row items-center justify-center">
                <Ionicons name="analytics-outline" size={20} color="white" />
                <Text className="text-base font-semibold text-white ml-2">
                  Analytics
                </Text>
                {loading && <ActivityIndicator size="small" color="white" />}
              </View>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Chat Section */}
        <ChatSection
          messages={messages}
          messagesEndRef={messagesEndRef}
          isInterviewing={isInterviewing}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

export default StartInterviewScreen;
