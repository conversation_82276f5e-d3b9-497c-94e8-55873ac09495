import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Message } from '../hooks/useStartInterview';
import { useThemeColors } from '@/hooks/useThemeColors';

interface MessageBubbleProps {
  message: Message;
}

const MessageBubble = ({ message }: MessageBubbleProps) => {
  const isUser = message.sender === 'you';
  const themeColors = useThemeColors();

  return (
    <View
      className={`flex`}
      style={{ flexDirection: isUser ? 'row-reverse' : 'row' }}
    >
      <View className={`max-w-[85%] ${isUser ? 'items-end' : 'items-start'}`}>
        {/* Message Bubble */}
        <View
          className={`px-5 py-4 rounded-3xl shadow-lg ${isUser
            ? 'rounded-br-lg'
            : 'rounded-bl-lg'
            }`}
          style={{
            backgroundColor: isUser ? themeColors.primary : themeColors.card,
            borderColor: isUser ? 'transparent' : themeColors.border,
            borderWidth: isUser ? 0 : 1,
            shadowColor: isUser ? themeColors.primary : '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: isUser ? 0.3 : 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <Text
            style={{
              color: isUser ? '#ffffff' : themeColors.text,
              fontSize: 16,
              lineHeight: 24,
            }}
          >
            {message.content}
            {!message.isComplete && (
              <Text className="inline-block w-1 h-4 bg-current opacity-70 animate-pulse ml-1">
                |
              </Text>
            )}
          </Text>
        </View>

        {/* Message Info */}
        <View
          className={`flex-row items-center mt-2 px-2 ${isUser ? 'justify-end' : 'justify-start'
            }`}
        >
          <View className="flex-row items-center">
            <View
              className="w-6 h-6 rounded-full items-center justify-center mr-2"
              style={{
                backgroundColor: isUser ? themeColors.primaryLight : themeColors.surfaceSecondary
              }}
            >
              <Ionicons
                name={isUser ? 'person' : 'hardware-chip'}
                size={12}
                color={isUser ? themeColors.primary : '#8b5cf6'}
              />
            </View>
            <Text className="text-xs font-medium" style={{ color: themeColors.textSecondary }}>
              {isUser ? 'You' : 'AI Interviewer'}
            </Text>
            <Text className="text-xs mx-2" style={{ color: themeColors.textMuted }}>•</Text>
            <Text className="text-xs" style={{ color: themeColors.textMuted }}>{message.timestamp}</Text>
            {!message.isComplete && (
              <>
                <Text className="text-xs mx-2" style={{ color: themeColors.textMuted }}>•</Text>
                <Text className="text-xs font-medium" style={{ color: themeColors.primary }}>
                  typing...
                </Text>
              </>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

export default MessageBubble;
