import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '@/hooks/useThemeColors';

const HeaderStartInterview = ({
  handleBack,
  timeRemaining = '00:00',
}: {
  handleBack: () => void;
  timeRemaining?: string;
}) => {
  const themeColors = useThemeColors();

  return (
    <View
      className="relative px-6 py-4 shadow-sm border-b"
      style={{
        backgroundColor: themeColors.surface,
        borderBottomColor: themeColors.borderLight
      }}
    >
      {/* Back Button */}
      <TouchableOpacity
        className="absolute left-6 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full items-center justify-center z-10"
        onPress={handleBack}
        style={{
          transform: [{ translateY: -20 }],
          backgroundColor: themeColors.backgroundSecondary
        }}
      >
        <Ionicons name="arrow-back" size={20} color={themeColors.text} />
      </TouchableOpacity>

      {/* Header Content */}
      <View className="flex-row justify-between items-center ml-14">
        <View>
          <Text className="text-xl font-bold" style={{ color: themeColors.text }}>
            AI Interview
          </Text>
          <Text className="text-sm mt-1" style={{ color: themeColors.textSecondary }}>
            Live conversation session
          </Text>
        </View>
        <View
          className="items-end px-4 py-2 rounded-full"
          style={{ backgroundColor: themeColors.backgroundSecondary }}
        >
          <Text className="text-xs font-medium mb-1" style={{ color: themeColors.primary }}>
            Time Left
          </Text>
          <Text className="text-lg font-bold" style={{ color: themeColors.text }}>
            {timeRemaining}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default HeaderStartInterview;

const styles = StyleSheet.create({});
