import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '@/hooks/useThemeColors';

interface ChatHeaderProps {
  isInterviewing: boolean;
}

const ChatHeader = ({ isInterviewing }: ChatHeaderProps) => {
  const themeColors = useThemeColors();

  return (
    <View
      className="border-b px-6 py-4"
      style={{
        backgroundColor: themeColors.backgroundSecondary,
        borderBottomColor: themeColors.borderLight
      }}
    >
      <View className="flex-row justify-between items-center">
        <View className="flex-row items-center">
          <View
            className="w-10 h-10 rounded-xl items-center justify-center shadow-lg"
            style={{ backgroundColor: themeColors.primary }}
          >
            <Ionicons name="chatbubbles" size={20} color="white" />
          </View>
          <View className="ml-3">
            <Text className="text-lg font-bold" style={{ color: themeColors.text }}>
              Interview Chat
            </Text>
            <Text className="text-sm" style={{ color: themeColors.textSecondary }}>
              Real-time conversation
            </Text>
          </View>
        </View>

        <View
          className="px-4 py-2 rounded-full flex-row items-center shadow-sm"
          style={{
            backgroundColor: isInterviewing ? '#dcfce7' : themeColors.backgroundSecondary,
            borderColor: isInterviewing ? '#bbf7d0' : themeColors.border,
            borderWidth: 1
          }}
        >
          <View
            className="w-2 h-2 rounded-full mr-2"
            style={{
              backgroundColor: isInterviewing ? '#10b981' : themeColors.textMuted
            }}
          />
          {isInterviewing && (
            <Ionicons
              name="mic"
              size={12}
              color="#10b981"
              style={{ marginRight: 4 }}
            />
          )}
          <Text
            className="text-sm font-semibold"
            style={{
              color: isInterviewing ? '#047857' : themeColors.textSecondary
            }}
          >
            {isInterviewing ? 'AI Active' : 'Offline'}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ChatHeader;
