import React, { useEffect, useRef, useState } from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CameraType, CameraView, Camera } from 'expo-camera';
import InterviewerVideo from './InterviewerVideo';
import UserVideo from './Uservideo';
import ControlButtons from './ControlButton';
import InterviewStatus from './InterviewStatus';
import { useThemeColors } from '@/hooks/useThemeColors';

interface VideoSectionProps {
  isMuted: boolean;
  setIsMuted: React.Dispatch<React.SetStateAction<boolean>>;
  handleToggleCall: () => void;
  isInterviewing: boolean;
}

const VideoSection = ({
  isMuted,
  setIsMuted,
  handleToggleCall,
  isInterviewing,
}: VideoSectionProps) => {
  const colors = useThemeColors();
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [type, setType] = useState<CameraType>('front');
  const cameraRef = useRef<CameraView>(null);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  const toggleCamera = () => {
    setIsCameraOn(!isCameraOn);
  };

  const flipCamera = () => {
    setType((current) => (current === 'front' ? 'back' : 'front'));
  };

  if (hasPermission === null) {
    return (
      <View className="mb-6">
        <View
          className="relative w-full aspect-video rounded-3xl overflow-hidden shadow-lg flex items-center justify-center"
          style={{ backgroundColor: colors.cardBackground }}
        >
          <Text
            className="font-medium"
            style={{ color: colors.textSecondary }}
          >
            Requesting camera permissions...
          </Text>
        </View>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View className="mb-6">
        <View
          className="relative w-full aspect-video rounded-3xl overflow-hidden shadow-lg flex items-center justify-center"
          style={{ backgroundColor: '#FEE2E2' }}
        >
          <View className="items-center px-8">
            <Ionicons name="videocam-off" size={48} color="#ef4444" />
            <Text className="text-red-600 text-center font-medium mt-3">
              Camera access required for video interview
            </Text>
            <Text className="text-red-500 text-center text-sm mt-1">
              Please enable camera permissions in settings
            </Text>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="mb-6">
      {/* Main Video Container */}
      <View
        className="relative w-full aspect-video rounded-3xl overflow-hidden shadow-2xl border"
        style={{ borderColor: colors.border }}
      >
        <InterviewerVideo />
        <UserVideo isCameraOn={isCameraOn} type={type} cameraRef={cameraRef} />
      </View>

      <ControlButtons
        isCameraOn={isCameraOn}
        isMuted={isMuted}
        isInterviewing={isInterviewing}
        toggleCamera={toggleCamera}
        flipCamera={flipCamera}
        setIsMuted={setIsMuted}
        handleToggleCall={handleToggleCall}
      />

      <InterviewStatus isInterviewing={isInterviewing} />
    </View>
  );
};

export default VideoSection;
