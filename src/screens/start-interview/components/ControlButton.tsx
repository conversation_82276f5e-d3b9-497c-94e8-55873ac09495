import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ControlButtonsProps {
  isCameraOn: boolean;
  isMuted: boolean;
  isInterviewing: boolean;
  toggleCamera: () => void;
  flipCamera: () => void;
  setIsMuted: React.Dispatch<React.SetStateAction<boolean>>;
  handleToggleCall: () => void;
}

const ControlButtons = ({
  isCameraOn,
  isMuted,
  isInterviewing,
  toggleCamera,
  flipCamera,
  setIsMuted,
  handleToggleCall,
}: ControlButtonsProps) => {
  return (
    <View className="flex-row justify-center items-center gap-4 mt-6">
      {/* Camera Toggle Button */}
      <TouchableOpacity
        className={`w-14 h-14 rounded-2xl items-center justify-center shadow-lg ${
          isCameraOn ? 'bg-white border border-gray-200' : 'bg-red-500'
        }`}
        onPress={toggleCamera}
        style={{
          shadowColor: isCameraOn ? '#000' : '#ef4444',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 4,
        }}
      >
        <Ionicons
          name={isCameraOn ? 'videocam' : 'videocam-off'}
          size={22}
          color={isCameraOn ? '#374151' : 'white'}
        />
      </TouchableOpacity>

      {/* Flip Camera Button */}
      {isCameraOn && (
        <TouchableOpacity
          className="w-14 h-14 bg-white border border-gray-200 rounded-2xl items-center justify-center shadow-lg"
          onPress={flipCamera}
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 4,
          }}
        >
          <Ionicons name="camera-reverse" size={22} color="#374151" />
        </TouchableOpacity>
      )}

      {/* Microphone Button */}
      <TouchableOpacity
        className={`w-14 h-14 rounded-2xl items-center justify-center shadow-lg ${
          isMuted ? 'bg-red-500' : 'bg-white border border-gray-200'
        }`}
        onPress={() => setIsMuted(!isMuted)}
        style={{
          shadowColor: isMuted ? '#ef4444' : '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 4,
        }}
      >
        <Ionicons
          name={isMuted ? 'mic-off' : 'mic'}
          size={22}
          color={isMuted ? 'white' : '#374151'}
        />
      </TouchableOpacity>

      {/* Call Control Button */}
      <TouchableOpacity
        className={`w-16 h-16 rounded-2xl items-center justify-center shadow-xl ${
          isInterviewing ? 'bg-red-500' : 'bg-green-500'
        }`}
        onPress={handleToggleCall}
        style={{
          shadowColor: isInterviewing ? '#ef4444' : '#10b981',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 12,
          elevation: 8,
        }}
      >
        <Ionicons
          name={isInterviewing ? 'call' : 'call-outline'}
          size={24}
          color="white"
        />
      </TouchableOpacity>
    </View>
  );
};

export default ControlButtons;
