import React, { useEffect } from 'react';
import { ScrollView, useWindowDimensions, View, Platform } from 'react-native';
import { Message } from '../hooks/useStartInterview';
import MessageBubble from './MessageBubble';
import ChatEmptyState from './ChatEmptyState';
import { useThemeColors } from '@/hooks/useThemeColors';

interface MessageListProps {
  messages: Message[];
  messagesEndRef: React.RefObject<ScrollView | null>;
  isInterviewing: boolean;
}

const MessageList = ({
  messages,
  messagesEndRef,
  isInterviewing = false,
}: MessageListProps) => {
  const { height: screenHeight } = useWindowDimensions();
  const themeColors = useThemeColors();

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  return (
    <ScrollView
      className="flex-1 px-3"
      style={{ backgroundColor: themeColors.background }}
      contentContainerStyle={{
        flexGrow: 1,
        minHeight: screenHeight * 0.3,
        backgroundColor: themeColors.background,
      }}
      showsVerticalScrollIndicator={true}
      ref={messagesEndRef}
      nestedScrollEnabled={true}
      keyboardShouldPersistTaps="handled"
      scrollEventThrottle={16}
      {...(Platform.OS === 'android' && {
        overScrollMode: 'never',
        bounces: false,
      })}
    >
      {messages.length === 0 ? (
        <ChatEmptyState isInterviewing={isInterviewing} />
      ) : (
        <View className="space-y-4">
          {messages.map((message, index) => (
            <MessageBubble key={message.id || index} message={message} />
          ))}
          <View />
        </View>
      )}
    </ScrollView>
  );
};

export default MessageList;
