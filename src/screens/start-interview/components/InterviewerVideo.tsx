import React from 'react';
import { View, Text, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

const InterviewerVideo = () => {
  return (
    <LinearGradient
      colors={['#1e3a8a', '#312e81']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}
    >
      <View className="items-center">
        <View className="w-24 h-24 rounded-full border-4 border-white/20 overflow-hidden shadow-xl">
          <Image
            source={require('../../../../assets/images/logo.png')}
            style={{ width: '100%', height: '100%', resizeMode: 'cover' }}
          />
        </View>
        <Text className="text-white font-semibold mt-4 text-lg">
          AI Interviewer
        </Text>
        <View className="flex-row items-center mt-2 bg-black/30 px-3 py-1 rounded-full">
          <View className="w-2 h-2 bg-green-400 rounded-full mr-2" />
          <Text className="text-white text-sm">Online</Text>
        </View>
      </View>

      {/* Interviewer Label */}
      <View className="absolute top-6 left-6 bg-black/40 backdrop-blur-sm px-4 py-2 rounded-full">
        <Text className="text-white text-sm font-medium">Interviewer</Text>
      </View>
    </LinearGradient>
  );
};

export default InterviewerVideo;
