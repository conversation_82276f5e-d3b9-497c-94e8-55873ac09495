import React from 'react';
import { View, Text } from 'react-native';

interface InterviewStatusProps {
  isInterviewing: boolean;
}

const InterviewStatus = ({ isInterviewing }: InterviewStatusProps) => {
  return (
    <View className="flex-row justify-center items-center mt-4">
      <View
        className={`flex-row items-center px-4 py-2 rounded-full ${
          isInterviewing ? 'bg-green-50' : 'bg-gray-50'
        }`}
      >
        <View
          className={`w-2 h-2 rounded-full mr-2 ${
            isInterviewing ? 'bg-green-500' : 'bg-gray-400'
          }`}
        />
        <Text
          className={`text-sm font-medium ${
            isInterviewing ? 'text-green-700' : 'text-gray-600'
          }`}
        >
          {isInterviewing ? 'Interview Active' : 'Ready to Start'}
        </Text>
      </View>
    </View>
  );
};

export default InterviewStatus;
