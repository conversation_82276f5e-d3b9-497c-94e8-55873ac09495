import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '@/hooks/useThemeColors';

interface EmptyStateProps {
  isInterviewing: boolean;
}

const ChatEmptyState = ({ isInterviewing }: EmptyStateProps) => {
  const themeColors = useThemeColors();

  if (isInterviewing) {
    return (
      <View className="justify-center items-center ">
        <View
          className="w-16 h-16 rounded-2xl items-center justify-center mb-4"
          style={{ backgroundColor: themeColors.backgroundSecondary }}
        >
          <Ionicons name="hourglass-outline" size={24} color={themeColors.primary} />
        </View>
        <Text
          className="text-center font-medium text-lg mb-2"
          style={{ color: themeColors.text }}
        >
          Waiting for AI interviewer...
        </Text>
        <Text
          className="text-center text-sm"
          style={{ color: themeColors.textMuted }}
        >
          The interview will begin shortly
        </Text>
        <View className="flex-row mt-4">
          {[0, 1, 2].map((i) => (
            <View
              key={i}
              className="w-2 h-2 rounded-full mx-1 animate-pulse"
              style={{
                backgroundColor: themeColors.primary,
                animationDelay: `${i * 0.2}s`,
                animationDuration: '1s',
              }}
            />
          ))}
        </View>
      </View>
    );
  }

  return (
    <View className="justify-start items-center">
      <View
        className="w-20 h-20 rounded-3xl items-center justify-center mb-6"
        style={{ backgroundColor: themeColors.backgroundSecondary }}
      >
        <Ionicons name="play-circle-outline" size={32} color={themeColors.textMuted} />
      </View>
      <Text
        className="text-center font-semibold text-xl mb-3"
        style={{ color: themeColors.text }}
      >
        Ready to Start
      </Text>
      <Text
        className="text-center text-base max-w-64"
        style={{ color: themeColors.textSecondary }}
      >
        Click the start button to begin your AI-powered interview session
      </Text>
    </View>
  );
};

export default ChatEmptyState;
