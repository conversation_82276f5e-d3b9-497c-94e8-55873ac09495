import React from 'react';
import { View, Text } from 'react-native';
import { CameraType, CameraView } from 'expo-camera';
import { LinearGradient } from 'expo-linear-gradient';

interface UserVideoProps {
  isCameraOn: boolean;
  type: CameraType;
  cameraRef: React.RefObject<CameraView | null>;
}

const UserVideo = ({ isCameraOn, type, cameraRef }: UserVideoProps) => {
  return (
    <View className="absolute bottom-4 right-4 w-32 h-24 bg-gray-900 rounded-2xl overflow-hidden border-2 border-white/20 shadow-2xl">
      {isCameraOn ? (
        <CameraView ref={cameraRef} facing={type} style={{ flex: 1 }} />
      ) : (
        <View className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
          <LinearGradient
            colors={['#4b5563', '#374151']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={{
              width: 48,
              height: 48,
              borderRadius: 9999,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 2,
              borderColor: '#6b7280',
            }}
          >
            <Text className="text-white font-bold text-lg">U</Text>
          </LinearGradient>
        </View>
      )}
      <View className="absolute top-2 left-2 bg-black/60 backdrop-blur-sm px-2 py-1 rounded-md">
        <Text className="text-white text-xs font-medium">You</Text>
      </View>

      {/* Camera Status Indicator */}
      <View className="absolute top-2 right-2">
        <View
          className={`w-2 h-2 rounded-full ${isCameraOn ? 'bg-green-400' : 'bg-red-400'}`}
        />
      </View>
    </View>
  );
};

export default UserVideo;
