import React from 'react';
import { View, ScrollView } from 'react-native';
import { Message } from '../hooks/useStartInterview';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import { useThemeColors } from '@/hooks/useThemeColors';

interface ChatSectionProps {
  messages: Message[];
  messagesEndRef: React.RefObject<ScrollView | null>;
  assistantSpeech?: string;
  userSpeech?: string;
  isInterviewing?: boolean;
}

const ChatSection = ({
  messages,
  messagesEndRef,
  assistantSpeech,
  userSpeech,
  isInterviewing = false,
}: ChatSectionProps) => {
  const themeColors = useThemeColors();

  // Debug log để kiểm tra theme colors
  console.log('ChatSection colors:', {
    surface: themeColors.surface,
    borderLight: themeColors.borderLight,
    background: themeColors.background
  });

  return (
    <View
      className="flex-1 rounded-3xl shadow-xl border-2 overflow-hidden"
      style={{
        backgroundColor: themeColors.background, // Thay đổi từ surface sang background
        borderColor: themeColors.borderLight
      }}
    >
      <ChatHeader isInterviewing={isInterviewing} />
      <MessageList
        messages={messages}
        messagesEndRef={messagesEndRef}
        isInterviewing={isInterviewing}
      />
    </View>
  );
};

export default ChatSection;
