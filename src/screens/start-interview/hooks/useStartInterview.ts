import { MockInterviewStackParamList } from '@/common/types/rootParamList';
import { useVapi } from '@/hooks/useVapi';
import { useAppDispatch } from '@/redux/hooks';
import { selectCurrentInterviewQuestion } from '@/redux/selectors/interviewQuestionSelector';
import { createConversation } from '@/redux/thunks/conversationThunks';
import { ConversationRequest } from '@/services/types/conversation';
import { InterviewQuestionSessionResponse } from '@/services/types/interview';
import { distinctTime } from '@/utils/timer';
import { isApiSuccess } from '@/common/types/api';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useEffect, useRef, useState } from 'react';
import { ScrollView } from 'react-native';
import { useSelector } from 'react-redux';
import { showAuthToast } from '@/utils/toastUtils';
import { selectConversationLoading } from '@/redux/selectors/conversationSelector';
import { selectUserProfile } from '@/redux/selectors/userSelector';
import { userService } from '@/services';
import { User } from '@/redux/types/userType';

export interface Message {
  id?: number;
  sender: 'you' | 'user' | 'assistant';
  content: string;
  timestamp?: string;
  isComplete?: boolean;
}

export const useStartInterview = () => {
  const {
    startCall,
    endCall,
    assistantSpeech,
    userSpeech,
    conversationLog,
    isCallActive,
    conversationLatest,
  } = useVapi();

  const dispatch = useAppDispatch();
  const interviewQuestion = useSelector(selectCurrentInterviewQuestion);
  const loading = useSelector(selectConversationLoading);

  const durationInMinutes = parseInt(
    interviewQuestion?.interviewDuration || '0',
  );
  const [inputMessage, setInputMessage] = useState('');
  const [isMuted, setIsMuted] = useState(false);
  const [isInterviewing, setIsInterviewing] = useState<boolean>(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [viewFeedbackAnalysis, setViewFeedbackAnalysis] = useState(false);
  const [timer, setTimer] = useState(durationInMinutes * 60);
  const [profile, setProfile] = useState<User | null>(null);
  const messagesEndRef = useRef<ScrollView | null>(null);

  const navigation =
    useNavigation<NativeStackNavigationProp<MockInterviewStackParamList>>();

  useEffect(() => {
    const fetchProfile = async () => {
      const profile = await userService.getUserProfile();
      setProfile(profile);
    };
    fetchProfile();
  }, []);

  const handleToggleCall = () => {
    setIsInterviewing(!isInterviewing);
    if (isInterviewing) {
      endCall();
    } else {
      setMessages([]);
      startCall(
        interviewQuestion as InterviewQuestionSessionResponse,
        `${profile?.firstName} ${profile?.lastName}`,
      );
    }
  };

  const handleFeedbackAnalysis = async () => {
    const startTime =
      parseInt(interviewQuestion?.interviewDuration || '0') * 60;
    const conversationRequest: ConversationRequest = {
      interviewId: interviewQuestion?._id as string,
      interviewDuration: distinctTime(startTime, timer),
      conversationLog: (conversationLatest as any[])
        .filter((conv) => conv.role !== 'system')
        .map((conv) => ({
          role: conv.role,
          content: conv.content || conv.message,
        })),
    };
    const response = await dispatch(
      createConversation(conversationRequest),
    ).unwrap();
    if (isApiSuccess(response)) {
      showAuthToast.success('Feedback analysis successful');
      navigation.navigate('FeedbackDetail', {
        conversationId: response.data._id,
      });
    } else {
      showAuthToast.error(response.message as string);
    }
  };

  useEffect(() => {
    if (interviewQuestion?.interviewDuration) {
      const durationInMinutes = parseInt(
        interviewQuestion.interviewDuration,
        10,
      );
      setTimer(durationInMinutes * 60);
    }
  }, [interviewQuestion]);

  useEffect(() => {
    if (isInterviewing) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);

      if (timer <= 0) {
        endCall();
        handleFeedbackAnalysis();
      }
      return () => clearInterval(interval);
    }
  }, [isInterviewing, timer]);

  useEffect(() => {
    if (conversationLog.length > 0) {
      const vapiMessages = conversationLog.map((log, index) => ({
        id: index + 1,
        sender: log.role === 'assistant' ? 'assistant' : 'you',
        content: log.message,
        timestamp: log.timestamp,
        isComplete: log.isComplete,
      }));

      console.log('💬 VAPI Messages:', vapiMessages);

      setMessages(vapiMessages as Message[]);
    }
  }, [conversationLog]);

  useEffect(() => {
    setIsInterviewing(isCallActive);
  }, [isCallActive]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (conversationLatest?.length > 0 && !isInterviewing) {
      setViewFeedbackAnalysis(true);
    }
  }, [conversationLatest, isInterviewing]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollToEnd({ animated: true });
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return {
    interviewQuestion,
    handleToggleCall,
    inputMessage,
    setInputMessage,
    isMuted,
    setIsMuted,
    messages,
    setMessages,
    messagesEndRef,
    isInterviewing,
    assistantSpeech,
    userSpeech,
    conversationLatest,
    viewFeedbackAnalysis,
    handleBack,
    handleFeedbackAnalysis,
    timer,
    loading,
  };
};
