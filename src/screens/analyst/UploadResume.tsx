import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  ArrowLeft,
  FileText,
  Zap,
  Upload,
  FileSearch,
  CheckCircle,
  MessageSquareQuote,
} from 'lucide-react-native';
import { <PERSON><PERSON>, <PERSON> } from '@/components/ui';
import { CardContent } from '@/components/ui/Card';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function AIResumeAnalyzer() {
  const navigation =
    useNavigation<NativeStackNavigationProp<AnalysisStackParamList>>();

  return (
    <LinearGradient
      colors={['#8B5CF6', '#3B82F6']}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      style={{ flex: 1 }}
    >
      <SafeAreaView style={{ flex: 1 }} edges={['top', 'left', 'right']}>
        {/* Header */}
        <View className="flex-row items-center px-4 py-3 mb-4">
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="absolute top-1 left-4 bg-white/20 rounded-full p-3 items-center justify-center z-10"
            activeOpacity={0.7}
            hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
          >
            <View pointerEvents="none">
              <ArrowLeft size={20} color="white" />
            </View>
          </TouchableOpacity>
          <Text className="flex-1 text-white text-center ml-6 text-2xl font-bold">
            AI Resume Analyzer
          </Text>
          <View className="w-10" />
        </View>

        <ScrollView
          className="flex-1 px-4"
          showsVerticalScrollIndicator={false}
        >
          {/* Testimonial */}
          <Card className="mb-6 bg-white/10 border border-white/20">
            <CardContent className="p-4">
              <View className="flex-row items-start">
                <Image
                  source={{
                    uri: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
                  }}
                  className="w-12 h-12 rounded-full mr-4"
                />
                <View className="flex-1">
                  <MessageSquareQuote color="#FFD600" />
                  <Text className="text-white text-base italic mb-2 mt-2">
                    "Thanks to AI Resume Analyzer, I landed 5 more interviews!"
                  </Text>
                  <Text className="text-white/70 text-lg italic">
                    ~ Geoffrey Hinton
                  </Text>
                </View>
              </View>
            </CardContent>
          </Card>

          {/* Analysis Options */}
          <Card className="mb-4">
            <CardContent className="p-4">
              <View className="flex-row items-center mb-3">
                <FileText size={24} color="#3B82F6" />
                <Text className="ml-3 text-gray-800 text-lg font-semibold flex-1">
                  Analyze Resume by Role
                </Text>
              </View>
              <Text className="text-gray-600 text-base mb-4">
                Upload Your Resume or Enter a Link to Analyze for Your Desired
                Role
              </Text>
              <Button
                onPress={() => {
                  navigation.navigate('UploadCVRole');
                }}
                className="bg-blue-500 flex-row items-center justify-center"
              >
                <Text className="text-white font-semibold mr-2">
                  Start Analysis
                </Text>
                <ArrowLeft
                  size={16}
                  color="white"
                  style={{ transform: [{ rotate: '180deg' }] }}
                />
              </Button>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardContent className="p-4">
              <View className="flex-row items-center mb-3">
                <Zap size={24} color="#8B5CF6" />
                <Text className="ml-3 text-gray-800 text-lg font-semibold flex-1">
                  Analyze Resume with Job Description
                </Text>
              </View>
              <Text className="text-gray-600 text-base mb-4">
                Compare Your Resume with a Job Description to Assess Fit
              </Text>
              <Button
                onPress={() => {
                  navigation.navigate('UploadCVJD');
                }}
                className="bg-blue-500 flex-row items-center justify-center"
              >
                <Text className="text-white font-semibold mr-2">
                  Start Comparison
                </Text>
                <ArrowLeft
                  size={16}
                  color="white"
                  style={{ transform: [{ rotate: '180deg' }] }}
                />
              </Button>
            </CardContent>
          </Card>

          {/* Quick Guide */}
          <Text className="text-center text-fuchsia-300 text-xl mb-5 font-semibold">
            Swift 3-Step Guide
          </Text>

          {/* Bottom Actions */}
          <View className="flex-row justify-around mb-4">
            <TouchableOpacity className="items-center">
              <View className="w-12 h-12 bg-white/20 rounded-full items-center justify-center mb-2">
                <Upload size={24} color="white" />
              </View>
              <Text className="text-white text-sm text-center">
                Upload Resume
              </Text>
            </TouchableOpacity>

            <TouchableOpacity className="items-center">
              <View className="w-12 h-12 bg-white/20 rounded-full items-center justify-center mb-2">
                <FileSearch size={24} color="white" />
              </View>
              <Text className="text-white text-sm text-center">
                Select Role/JD
              </Text>
            </TouchableOpacity>

            <TouchableOpacity className="items-center">
              <View className="w-12 h-12 bg-white/20 rounded-full items-center justify-center mb-2">
                <CheckCircle size={24} color="white" />
              </View>
              <Text className="text-white text-sm text-center">
                Get Results
              </Text>
            </TouchableOpacity>
          </View>

          {/* Coming Soon Banner */}
          <View
            className="rounded-full mb-6 mt-1"
            style={{ overflow: 'hidden' }}
          >
            <LinearGradient
              colors={['#F97316', '#EF4444']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{ borderRadius: 9999, height: 60 }}
            >
              <Button className="bg-transparent flex-1 flex-row items-center justify-between">
                <View className="flex-row items-center justify-center">
                  <Text className="text-xl mr-2">🚀</Text>
                  <Text className="text-white text-lg font-semibold ml-1">
                    Coming Soon: Cover Letter Analysis
                  </Text>
                </View>
                <View className="items-center justify-center">
                  <ArrowLeft
                    size={22}
                    color="white"
                    style={{ transform: [{ rotate: '180deg' }] }}
                  />
                </View>
              </Button>
            </LinearGradient>
          </View>

          {/* Bottom spacing */}
          <View className="h-4" />
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}
