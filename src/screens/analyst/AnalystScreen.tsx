import React, { useEffect, useState } from 'react';
import { ScrollView, Alert, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import { AnalystResponse } from '@/services/types/analyst';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';

import {
  selectAnalystLoading,
  selectMyAnalysts,
} from '@/redux/selectors/analystSelectors';
import {
  deleteAnalyst,
  getAnalystById,
  getMyAnalysts,
} from '@/redux/thunks/analystThunks';
import Header from './components/MainHeader';
import AnalyticsOverview from './components/MainAnalyticsOverview';
import RecentAnalyses from './components/RecentAnalyses';
import { QuickTipsSection } from '@/components/ui';
import AIAssistantSection from '@/components/ui/AIAssistant';
import FeaturesSection from './components/FeaturesSection';
import { showAuthToast } from '@/utils/toastUtils';
import { useThemeColors } from '@/hooks/useThemeColors';

const ResumeAnalysisScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AnalysisStackParamList>>();
  const dispatch = useAppDispatch();

  const myAnalysts = useAppSelector(selectMyAnalysts);
  const loading = useAppSelector(selectAnalystLoading);
  const colors = useThemeColors();
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingItemId, setLoadingItemId] = useState<string | null>(null);
  const [deletingItemId, setDeletingItemId] = useState<string | null>(null);
  const limit = 4;

  useEffect(() => {
    dispatch(getMyAnalysts());
  }, [dispatch]);

  // Get paginated data
  const paginatedAnalysts = myAnalysts.slice(0, currentPage * limit);
  const hasMoreData = myAnalysts.length > paginatedAnalysts.length;

  const handleAnalystPress = async (analyst: AnalystResponse) => {
    try {
      setLoadingItemId(analyst._id);
      const result = await dispatch(getAnalystById(analyst._id)).unwrap();

      if (result.data) {
        if (analyst.agentType === 'AI_RESUME_ANALYSIS_ROLE') {
          navigation.navigate('AnalystRole', { analysisId: analyst._id });
        } else if (analyst.agentType === 'AI_RESUME_ANALYSIS_JD') {
          navigation.navigate('AnalystJD', { analystId: analyst._id });
        }
      }
    } catch (error) {
      console.error('Error fetching analyst details:', error);
      Alert.alert('Error', 'Failed to fetch analyst details');
    } finally {
      setLoadingItemId(null);
    }
  };

  const handleDeleteAnalyst = async (id: string) => {
    try {
      setDeletingItemId(id);
      await dispatch(deleteAnalyst(id)).unwrap();
      showAuthToast.success('Delete analysis successfully executed');
    } catch (error) {
      console.error('Error deleting analyst:', error);
      Alert.alert('Error', 'Failed to delete analysis');
    } finally {
      setDeletingItemId(null);
    }
  };

  const loadMore = () => {
    if (hasMoreData && !loading) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  return (
    <>
      <ScrollView
        style={{ flex: 1, backgroundColor: colors.background }}
        showsVerticalScrollIndicator={false}
      >
        {/* Main Header Analyst */}
        <Header />

        {/* Analytics Overview Section */}
        <AnalyticsOverview myAnalysts={myAnalysts} />

        {/* Recent Analyses Section */}
        <RecentAnalyses
          paginatedAnalysts={paginatedAnalysts}
          allAnalysts={myAnalysts}
          loading={loading}
          hasMoreData={hasMoreData}
          loadingItemId={loadingItemId}
          deletingItemId={deletingItemId}
          onLoadMore={loadMore}
          onAnalystPress={handleAnalystPress}
          onDeleteAnalyst={handleDeleteAnalyst}
        />

        {/* Quick Tips Section */}
        <QuickTipsSection />

        {/* AI Assistant Section */}
        <AIAssistantSection />

        {/* Features Section */}
        <FeaturesSection />
      </ScrollView>
    </>
  );
};

export default ResumeAnalysisScreen;
