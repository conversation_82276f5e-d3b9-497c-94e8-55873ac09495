import React, { useEffect } from 'react';
import { View, Text, ScrollView, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { ArrowLeft } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';
import {
  ActionButtons,
  Button,
  GradientHeader,
  HelpModal,
} from '@/components/ui';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { selectAnalystLoading } from '@/redux/selectors/analystSelectors';
import { createAnalystByJD } from '@/redux/thunks/analystThunks';
import { showAuthToast } from '@/utils/toastUtils';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { UploadFormData, uploadSchema } from './schemas/jdSchema';
import { useFileUpload } from '@/hooks/useFileUpload';
import { FileUploadCard } from './components/FileUploadCard';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function CVJDUploadScreen() {
  const navigation =
    useNavigation<NativeStackNavigationProp<AnalysisStackParamList>>();
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectAnalystLoading);
  const [isModalVisible, setIsModalVisible] = React.useState(false);
  const colors = useThemeColors();

  const { uploadFile, formatFileSize } = useFileUpload();

  // React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<UploadFormData>({
    resolver: zodResolver(uploadSchema),
    defaultValues: {
      cvFile: null,
      jdFile: null,
    },
  });

  // Reset form when component mounts
  useEffect(() => {
    reset();
  }, [reset]);

  const handleFileUpload = async (type: 'cv' | 'jd') => {
    const result = await uploadFile();
    if (result) {
      if (type === 'cv') {
        setValue('cvFile', result);
        trigger('cvFile');
        showAuthToast.success('CV file uploaded successfully!');
      } else {
        setValue('jdFile', result);
        trigger('jdFile');
        showAuthToast.success('JD file uploaded successfully!');
      }
    }
  };

  const handleAnalyze = async (data: UploadFormData) => {
    try {
      const cvFile = data.cvFile.assets[0];
      const jdFile = data.jdFile.assets[0];

      const cvFileObj = {
        uri: cvFile.uri,
        type: cvFile.mimeType ?? 'application/pdf',
        name: cvFile.name,
      } as any;

      const jdFileObj = {
        uri: jdFile.uri,
        type: jdFile.mimeType ?? 'application/pdf',
        name: jdFile.name,
      } as any;

      const credentials = {
        agentType: 'AI_RESUME_ANALYSIS_JD' as const,
        cvFile: cvFileObj,
        jdFile: jdFileObj,
      };

      const result = await dispatch(createAnalystByJD(credentials)).unwrap();

      showAuthToast.success('Analysis completed successfully!');
      navigation.navigate('AnalystJD', { analystId: result.data._id });

      // Reset form after successful navigation
      reset();
    } catch (error: any) {
      console.error('Analysis error:', error);
      showAuthToast.error(
        error?.message ?? 'Analysis failed. Please try again.',
      );
    }
  };

  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message && typeof error.message === 'string')
      return error.message;
    return 'Invalid file';
  };

  return (
    <View style={{ backgroundColor: colors.background, flex: 1 }}>
      {/* Custom Header */}
      <GradientHeader
        title="Upload CV & JD"
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView
        className="flex-1 px-4 py-6"
        style={{
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          marginTop: -20,
          backgroundColor: colors.background,
        }}
      >
        <View className="mb-6">
          <Text
            className="text-xl font-bold mb-2"
            style={{ color: colors.text }}
          >
            Analysis based on CV and JD
          </Text>
          <Text
            className="text-gray-600 text-sm leading-5"
            style={{ color: colors.textMuted }}
          >
            Upload CV and JD to receive questions suitable for the job position
          </Text>
        </View>

        {/* CV Section */}
        <View className="mb-8">
          <Text
            className="text-lg font-semibold mb-4"
            style={{ color: colors.text }}
          >
            Curriculum Vitae (CV)
          </Text>
          <Controller
            control={control}
            name="cvFile"
            render={({ field: { value } }) => (
              <FileUploadCard
                selectedFile={value}
                onUpload={() => handleFileUpload('cv')}
                formatFileSize={formatFileSize}
                disabled={isLoading}
              />
            )}
          />
          {errors.cvFile && (
            <Text className="text-red-500 mt-2">
              {getErrorMessage(errors.cvFile)}
            </Text>
          )}
        </View>

        {/* JD Section */}
        <View className="mb-8">
          <Text
            className="text-lg font-semibold mb-4"
            style={{ color: colors.text }}
          >
            Job Description (JD)
          </Text>
          <Controller
            control={control}
            name="jdFile"
            render={({ field: { value } }) => (
              <FileUploadCard
                selectedFile={value}
                onUpload={() => handleFileUpload('jd')}
                formatFileSize={formatFileSize}
                disabled={isLoading}
              />
            )}
          />
          {errors.jdFile && (
            <Text className="text-red-500 mt-2">
              {getErrorMessage(errors.jdFile)}
            </Text>
          )}
        </View>

        {/* Bottom Actions */}
        <ActionButtons
          onCancel={() => navigation.goBack()}
          onSubmit={handleSubmit(handleAnalyze)}
          isLoading={isLoading}
          loadingText="Analyzing..."
        />

        <HelpModal
          visible={isModalVisible}
          onClose={() => setIsModalVisible(false)}
          navigation={navigation}
        />
      </ScrollView>
    </View>
  );
}
