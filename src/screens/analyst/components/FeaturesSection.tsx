import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { FEATURES } from '@/common/constants/analyst.constant';
import { useThemeColors } from '@/hooks/useThemeColors';

const FeaturesSection: React.FC = () => {
  const colors = useThemeColors();

  return (
    <View style={{
      paddingHorizontal: 24,
      paddingVertical: 24,
      paddingBottom: 32,
      backgroundColor: colors.surface
    }}>
      <Text style={{
        fontSize: 24,
        fontWeight: 'bold',
        color: colors.text,
        marginBottom: 8
      }}>
        Powerful Resume Analysis Features
      </Text>
      <Text style={{
        color: colors.textSecondary,
        marginBottom: 32,
        fontSize: 18
      }}>
        Get detailed insights to improve your resume
      </Text>

      {FEATURES.map((feature, index) => (
        <TouchableOpacity
          key={index}
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
            marginBottom: 24,
            backgroundColor: colors.surfaceSecondary,
            padding: 20,
            borderRadius: 12,
            borderWidth: 1,
            borderColor: colors.border,
          }}
          activeOpacity={0.7}
        >
          <View style={{
            width: 48,
            height: 48,
            backgroundColor: colors.primaryLight,
            borderRadius: 12,
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 16
          }}>
            <feature.icon size={24} color={colors.primary} />
          </View>
          <View style={{ flex: 1 }}>
            <Text style={{
              fontWeight: '600',
              color: colors.text,
              marginBottom: 8,
              fontSize: 18
            }}>
              {feature.title}
            </Text>
            <Text style={{ color: colors.textSecondary }}>
              {feature.description}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default FeaturesSection;
