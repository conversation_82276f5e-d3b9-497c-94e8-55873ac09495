import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from 'react-native';
import Animated, {
  FadeInDown,
  BounceIn,
  SlideInRight,
} from 'react-native-reanimated';
import { ChevronRight } from 'lucide-react-native';
import { AnalystResponse } from '@/services/types/analyst';
import { getTypeColor } from '@/utils/analysisColor';
import { extractCVInfo, formatRole } from '@/utils/cvParser';
import { useThemeColors } from '@/hooks/useThemeColors';

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

interface AnalysisCardProps {
  analyst: AnalystResponse;
  index: number;
  isLoading: boolean;
  isDeleting: boolean;
  onPress: (analyst: AnalystResponse) => void;
}

export const AnalysisCard: React.FC<AnalysisCardProps> = ({
  analyst,
  index,
  isLoading,
  isDeleting,
  onPress,
}) => {
  const colors = useThemeColors();

  const typeColors = getTypeColor(
    analyst.agentType === 'AI_RESUME_ANALYSIS_ROLE'
      ? 'Role Analysis'
      : 'JD Analysis',
  );
  const cvInfo = extractCVInfo(analyst.cvText ?? '');

  const getRandomAvatar = (id: string) => {
    const avatars = [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=150&h=150&fit=crop&crop=face',
      'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=150&h=150&fit=crop&crop=face',
    ];
    const avatarIndex =
      parseInt(id.replace(/\D/g, '').slice(-1) || '0') % avatars.length;
    return avatars[avatarIndex];
  };

  return (
    <AnimatedTouchableOpacity
      activeOpacity={0.7}
      onPress={() => !isDeleting && onPress(analyst)}
      disabled={isLoading || isDeleting}
      entering={FadeInDown.delay(index * 100).springify()}
      style={{
        backgroundColor: colors.surface,
        borderWidth: 1,
        borderColor: colors.border,
        borderRadius: 12,
        padding: 20,
        marginBottom: 16,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
        opacity: isDeleting ? 0.6 : 1,
      }}
    >
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
          <Animated.Image
            source={{ uri: getRandomAvatar(analyst._id) }}
            style={{ width: 56, height: 56, borderRadius: 28, marginRight: 16 }}
            entering={BounceIn.delay(index * 100 + 200)}
          />
          <View style={{ flex: 1 }}>
            <Text style={{
              fontWeight: '600',
              color: colors.text,
              fontSize: 18
            }}>
              {cvInfo.name}
            </Text>
            <Text style={{
              color: colors.textSecondary,
              fontSize: 14,
              marginTop: 4
            }}>
              {formatRole(cvInfo.role)}
            </Text>
            <Animated.View
              style={{
                marginTop: 8,
                paddingHorizontal: 12,
                paddingVertical: 4,
                borderRadius: 20,
                alignSelf: 'flex-start',
                backgroundColor: typeColors.bg === 'blue' ? colors.primaryLight :
                  typeColors.bg === 'green' ? colors.success + '20' : colors.primaryLight,
                borderWidth: 1,
                borderColor: typeColors.bg === 'blue' ? colors.primary + '30' :
                  typeColors.bg === 'green' ? colors.success + '30' : colors.primary + '30',
              }}
              entering={SlideInRight.delay(index * 100 + 300)}
            >
              <Text style={{
                fontSize: 12,
                fontWeight: '600',
                color: analyst.agentType === 'AI_RESUME_ANALYSIS_ROLE'
                  ? colors.text
                  : (typeColors.bg === 'green' ? colors.success : colors.primary),
              }}>
                {analyst.agentType === 'AI_RESUME_ANALYSIS_ROLE'
                  ? 'Role Analysis'
                  : 'JD Analysis'}
              </Text>
            </Animated.View>
          </View>
        </View>

        <View style={{ alignItems: 'flex-end' }}>
          <Text style={{
            fontWeight: 'bold',
            fontSize: 24,
            color: colors.success
          }}>
            {analyst.content.overall_score}/100
          </Text>

          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
            {isLoading ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <>
                <Text style={{
                  color: colors.primary,
                  fontSize: 14,
                  fontWeight: '500',
                  marginRight: 4
                }}>
                  View Report
                </Text>
                <ChevronRight size={16} color={colors.primary} />
              </>
            )}
          </View>
        </View>
      </View>
    </AnimatedTouchableOpacity>
  );
};
