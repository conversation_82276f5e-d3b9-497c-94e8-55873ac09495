import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Card, ProgressBar } from '@/components/ui';
import { AnalystSection } from '@/services/types/analyst';
import { useThemeColors } from '@/hooks/useThemeColors';

interface ScoreCardProps {
  title: string;
  score: number;
  color: string;
  iconName: any;
  iconColor: string;
  section?: AnalystSection;
}

export const ScoreCard: React.FC<ScoreCardProps> = ({
  title,
  score,
  color,
  iconName,
  iconColor,
  section,
}) => {
  const colors = useThemeColors();

  const getScoreColor = (score: number): string => {
    if (score >= 80) return colors.success;
    if (score >= 60) return colors.warning;
    return colors.error;
  };

  return (
    <Card
      style={{
        marginBottom: 24,
        borderRadius: 16,
        backgroundColor: colors.surface,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
        elevation: 8,
        borderWidth: 0,
      }}
    >
      <View style={{ padding: 24 }}>
        {/* Header with gradient background */}
        <LinearGradient
          colors={[iconColor + '15', iconColor + '05']}
          style={{
            padding: 16,
            borderRadius: 12,
            marginBottom: 16,
            borderWidth: 1,
            borderColor: iconColor + '20'
          }}
        >
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View
                style={{
                  padding: 12,
                  borderRadius: 20,
                  marginRight: 12,
                  backgroundColor: iconColor + '20'
                }}
              >
                <Ionicons name={iconName} size={24} color={iconColor} />
              </View>
              <Text style={{
                color: colors.text,
                fontWeight: 'bold',
                fontSize: 18
              }}>
                {title}
              </Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              <Text
                style={{
                  fontSize: 24,
                  fontWeight: 'bold',
                  color: getScoreColor(score)
                }}
              >
                {score}%
              </Text>
              <Text style={{
                color: colors.textSecondary,
                fontSize: 12
              }}>
                Score
              </Text>
            </View>
          </View>
          <View style={{ marginTop: 16 }}>
            <ProgressBar percentage={score} color={color} />
          </View>
        </LinearGradient>

        {section && (
          <View style={{ gap: 12 }}>
            {/* Comment */}
            <TouchableOpacity
              style={{
                backgroundColor: colors.surfaceSecondary,
                padding: 16,
                borderRadius: 12,
                marginBottom: 16,
                borderWidth: 1,
                borderColor: colors.border,
              }}
              activeOpacity={0.7}
            >
              <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                <View
                  style={{
                    padding: 8,
                    borderRadius: 8,
                    marginRight: 12,
                    backgroundColor: iconColor + '15'
                  }}
                >
                  <Ionicons name="chatbox" size={18} color={iconColor} />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{
                    color: colors.text,
                    fontWeight: '600',
                    marginBottom: 8
                  }}>
                    Analysis Comment
                  </Text>
                  <Text style={{
                    color: colors.textSecondary,
                    lineHeight: 20
                  }}>
                    {section.comment}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>

            {/* What's Good */}
            {section.whats_good && section.whats_good.length > 0 && (
              <TouchableOpacity
                className="bg-green-50 p-4 rounded-xl border mb-4 border-green-100"
                activeOpacity={0.7}
              >
                <View className="flex-row items-start">
                  <View className="bg-green-100 p-2 rounded-lg mr-3">
                    <Ionicons name="checkmark-circle" size={18} color="#10B981" />
                  </View>
                  <View className="flex-1">
                    <Text className="text-green-800 font-semibold mb-2">
                      What's Good
                    </Text>
                    {section.whats_good.map((item, index) => (
                      <View key={index} className="flex-row items-start mb-1">
                        <View className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 mr-2" />
                        <Text className="text-green-700 text-sm flex-1 leading-5">
                          {item}
                        </Text>
                      </View>
                    ))}
                  </View>
                </View>
              </TouchableOpacity>
            )}

            {/* Needs Improvement */}
            {section.needs_improvement &&
              section.needs_improvement.length > 0 && (
                <TouchableOpacity
                  className="bg-orange-50 p-4 rounded-xl mb-4 border border-orange-100"
                  activeOpacity={0.7}
                >
                  <View className="flex-row items-start">
                    <View className="bg-orange-100 p-2 rounded-lg mr-3">
                      <Ionicons name="warning" size={18} color="#F59E0B" />
                    </View>
                    <View className="flex-1">
                      <Text className="text-orange-800 font-semibold mb-2">
                        Needs Improvement
                      </Text>
                      {section.needs_improvement.map((item, index) => (
                        <View key={index} className="flex-row items-start mb-1">
                          <View className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 mr-2" />
                          <Text className="text-orange-700 text-sm flex-1 leading-5">
                            {item}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </TouchableOpacity>
              )}

            {/* Tips for Improvement */}
            {section.tips_for_improvement &&
              section.tips_for_improvement.length > 0 && (
                <TouchableOpacity
                  className="bg-blue-50 p-4 rounded-xl border border-blue-100"
                  activeOpacity={0.7}
                >
                  <View className="flex-row items-start">
                    <View className="bg-blue-100 p-2 rounded-lg mr-3">
                      <Ionicons name="bulb" size={18} color="#3B82F6" />
                    </View>
                    <View className="flex-1">
                      <Text className="text-blue-800 font-semibold mb-2">
                        Tips for Improvement
                      </Text>
                      {section.tips_for_improvement.map((item, index) => (
                        <View key={index} className="flex-row items-start mb-1">
                          <View className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2" />
                          <Text className="text-blue-700 text-sm flex-1 leading-5">
                            {item}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </TouchableOpacity>
              )}
          </View>
        )}
      </View>
    </Card>
  );
};

export default ScoreCard;
