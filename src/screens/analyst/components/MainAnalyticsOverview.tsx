import React, { useMemo } from 'react';
import { View, Text, FlatList } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FileText, BarChart3, Award } from 'lucide-react-native';
import { AnalystResponse } from '@/services/types/analyst';
import {
  calculateAnalytics,
  calculateTrend,
  formatLargeNumber,
  getSuccessLevel,
} from '@/utils/caculateAnalystHistory';
import { useThemeColors } from '@/hooks/useThemeColors';

interface AnalyticsOverviewProps {
  myAnalysts: AnalystResponse[];
}

const AnalyticsOverview: React.FC<AnalyticsOverviewProps> = ({ myAnalysts }) => {
  const colors = useThemeColors();
  const calculatedStats = useMemo(() => {
    return calculateAnalytics(myAnalysts);
  }, [myAnalysts]);

  // Generate stats data with real values
  const statsData = useMemo(() => {
    const { totalAnalyses, averageScore, successRateText } = calculatedStats;

    return [
      {
        id: 1,
        title: 'Total Analyses',
        value: formatLargeNumber(totalAnalyses),
        icon: FileText,
        color: colors.primary,
      },
      {
        id: 2,
        title: 'Average Score',
        value: averageScore.toString(),
        icon: BarChart3,
        color: colors.success,
      },
      {
        id: 3,
        title: 'Success Rate',
        value: successRateText,
        icon: Award,
        color: colors.warning,
      },
    ];
  }, [calculatedStats, colors]);

  const renderStatsItem = ({ item, index }: { item: any; index: number }) => {
    const trend = calculateTrend(calculatedStats);
    const successLevel = getSuccessLevel(calculatedStats.successRate);

    return (
      <View className="mr-4">
        <LinearGradient
          colors={[item.color + '20', item.color + '10']}
          style={{
            borderRadius: 16,
            padding: 20,
            minWidth: 160,
            minHeight: 190,
            borderWidth: 1,
            borderColor: item.color + '30',
            justifyContent: 'space-between',
          }}
        >
          {/* Header Section */}
          <View className="flex-row items-center justify-between mb-4">
            <View
              className="w-12 h-12 rounded-full items-center justify-center"
              style={{ backgroundColor: item.color + '20' }}
            >
              <item.icon size={24} color={item.color} />
            </View>
            <View
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: item.color }}
            />
          </View>

          {/* Main Content Section */}
          <View style={{ flex: 1, justifyContent: 'center', marginBottom: 16 }}>
            <Text style={{
              color: colors.textSecondary,
              fontSize: 14,
              fontWeight: '500',
              marginBottom: 4
            }}>
              {item.title}
            </Text>
            <Text style={{
              fontSize: 30,
              fontWeight: 'bold',
              color: item.color
            }}>
              {item.value}
            </Text>
          </View>

          {/* Footer Section - Fixed height area */}
          <View style={{ minHeight: 50, justifyContent: 'flex-end' }}>
            {/* Trend indicator */}
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
              <Text style={{
                fontSize: 12,
                fontWeight: '500',
                color: trend.color === 'text-green-600' ? colors.success : colors.error
              }}>
                {trend.change} from last month
              </Text>
            </View>

            {/* Success level - only for Success Rate card */}
            {item.title === 'Success Rate' ? (
              <View className="mt-1">
                <View
                  className="px-2 py-1 rounded-full self-start"
                  style={{ backgroundColor: successLevel.bgColor }}
                >
                  <Text
                    className="text-xs font-medium"
                    style={{ color: successLevel.color }}
                  >
                    {successLevel.level}
                  </Text>
                </View>
              </View>
            ) : (
              <View style={{ height: 24 }} />
            )}
          </View>
        </LinearGradient>
      </View>
    );
  };

  return (
    <View style={{ paddingVertical: 32, backgroundColor: colors.surface }}>
      <View style={{ paddingHorizontal: 24, marginBottom: 16 }}>
        <Text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: colors.text,
          marginBottom: 8
        }}>
          Analytics Overview
        </Text>
        <Text style={{
          color: colors.text,
          opacity: 0.8,
          fontSize: 16
        }}>
          Track your resume analysis progress
        </Text>
        {/* Additional insights */}
        {calculatedStats.totalAnalyses > 0 && (
          <View style={{
            marginTop: 12,
            padding: 12,
            backgroundColor: colors.primaryLight,
            borderRadius: 8,
            borderWidth: 1,
            borderColor: colors.primary + '30'
          }}>
            <Text style={{
              color: colors.text,
              fontSize: 14,
              opacity: 0.9,
              fontWeight: '500'
            }}>
              💡 Your average score is {calculatedStats.averageScore}/100.
              {calculatedStats.averageScore < 70
                ? ' Focus on improving key areas to boost your success rate!'
                : ' Great job! Keep maintaining this quality.'}
            </Text>
          </View>
        )}
      </View>

      <FlatList
        data={statsData}
        renderItem={renderStatsItem}
        keyExtractor={(item) => item.id.toString()}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 24 }}
        decelerationRate="fast"
      />
    </View>
  );
};

export default AnalyticsOverview;