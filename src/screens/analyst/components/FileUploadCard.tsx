import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Button } from '@/components/ui';
import * as DocumentPicker from 'expo-document-picker';
import { useThemeColors } from '@/hooks/useThemeColors';

interface FileUploadCardProps {
  selectedFile: DocumentPicker.DocumentPickerResult | null;
  onUpload: () => void;
  title?: string;
  description?: string;
  formatFileSize?: (bytes: number) => string;
  disabled?: boolean;
}

export const FileUploadCard: React.FC<FileUploadCardProps> = ({
  selectedFile,
  onUpload,
  title = 'Drag and drop a file or select from your device',
  description = 'PDF, DOC, DOCX (Maximum 10MB)',
  formatFileSize,
  disabled = false,
}) => {
  const colors = useThemeColors();
  const hasFile = selectedFile?.assets && selectedFile.assets.length > 0;
  const file = hasFile ? selectedFile.assets[0] : null;

  return (
    <Card
      style={{
        borderWidth: 2,
        borderStyle: 'dashed',
        borderColor: hasFile ? '#10B981' : '#93C5FD',
        backgroundColor: hasFile ? '#F0FDF4' : colors.card,
        padding: 24,
        borderRadius: 15,
      }}
    >
      <View className="items-center">
        <View className="mb-4">
          <View
            className={`w-16 h-16 ${hasFile ? 'bg-green-100' : 'bg-blue-100'} rounded-full items-center justify-center`}
            style={{
              backgroundColor: hasFile ? '#F0FDF4' : colors.card,
            }}
          >
            <Ionicons
              name={hasFile ? 'checkmark-circle' : 'cloud-upload-outline'}
              size={32}
              color={hasFile ? '#10B981' : '#3B82F6'}
            />
          </View>
        </View>

        {hasFile && file ? (
          <View className="items-center">
            <Text className="text-green-700 font-semibold text-center mb-2 text-base">
              {file.name}
            </Text>
            <Text className="text-green-600 text-sm text-center mb-4">
              {file.size && formatFileSize
                ? formatFileSize(file.size)
                : file.size
                  ? `${(file.size / 1024 / 1024).toFixed(2)} MB`
                  : 'File selected'}
            </Text>
          </View>
        ) : (
          <>
            <Text className="text-gray-900 font-semibold text-center mb-2 text-base">
              {title}
            </Text>
            <Text className="text-gray-500 text-base text-center mb-4">
              {description}
            </Text>
          </>
        )}

        <Button
          onPress={onUpload}
          style={{
            backgroundColor: hasFile ? '#10B981' : colors.cardBackground,
            borderWidth: 1,
            borderColor: hasFile ? '#10B981' : '#93C5FD',
            paddingHorizontal: 24,
            paddingVertical: 8,
            borderRadius: 8,
          }}
          disabled={disabled}
        >
          <Text
            style={{
              color: hasFile ? colors.primaryText : colors.text,
            }}
          >
            {hasFile ? 'Change file' : 'Select file'}
          </Text>
        </Button>
      </View>
    </Card>
  );
};
