import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Linking,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { FileText, Eye, Download, ExternalLink } from 'lucide-react-native';
import { Card } from '@/components/ui/Card';
import { WebView } from 'react-native-webview';
import { useThemeColors } from '@/hooks/useThemeColors';

interface DocumentCardProps {
  type: 'resume' | 'job';
  title: string;
  date: string;
  iconColor: string;
  borderColor: string;
  textColor: string;
  fileUrl?: string;
  fileName?: string;
}

const DocumentCard: React.FC<DocumentCardProps> = ({
  type,
  title,
  date,
  iconColor,
  borderColor,
  textColor,
  fileUrl,
  fileName = 'Document.pdf',
}) => {
  const [pdfError, setPdfError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const colors = useThemeColors();

  const getIcon = () => {
    if (type === 'resume') {
      return <Ionicons name="document-text" size={22} color="white" />;
    } else {
      return <FileText size={22} color="white" />;
    }
  };

  const handleView = async () => {
    if (fileUrl) {
      try {
        const supported = await Linking.canOpenURL(fileUrl);
        if (supported) {
          await Linking.openURL(fileUrl);
        } else {
          Alert.alert('Error', 'Cannot open this file');
        }
      } catch (error) {
        console.error('Error opening file:', error);
        Alert.alert('Error', 'Failed to open file');
      }
    } else {
      Alert.alert('Error', 'File URL not available');
    }
  };

  const handleDownload = async () => {
    if (fileUrl) {
      try {
        const supported = await Linking.canOpenURL(fileUrl);
        if (supported) {
          await Linking.openURL(fileUrl);
        } else {
          Alert.alert('Error', 'Cannot download this file');
        }
      } catch (error) {
        console.error('Error downloading file:', error);
        Alert.alert('Error', 'Failed to download file');
      }
    } else {
      Alert.alert('Error', 'File URL not available');
    }
  };

  const renderPreview = () => {
    if (!fileUrl) {
      return (
        <View
          className="flex-1 items-center justify-center"
          style={{ backgroundColor: colors.background }}
        >
          <View
            className="p-4 rounded-full mb-3"
            style={{ backgroundColor: colors.cardBackground }}
          >
            <Ionicons name="document-text" size={32} color={colors.textMuted} />
          </View>
          <Text
            className=" text-sm font-medium"
            style={{ color: colors.textMuted }}
          >
            No File Available
          </Text>
          <Text className=" text-xs mt-1" style={{ color: colors.textMuted }}>
            File URL not provided
          </Text>
        </View>
      );
    }

    if (pdfError) {
      return (
        <View
          className="flex-1 items-center justify-center"
          style={{ backgroundColor: colors.background }}
        >
          <View
            className="p-4 rounded-full mb-3"
            style={{ backgroundColor: colors.cardBackground }}
          >
            <Ionicons name="alert-circle" size={32} color={colors.error} />
          </View>
          <Text
            className="text-gray-600 text-sm font-medium mb-2"
            style={{ color: colors.textMuted }}
          >
            Preview Unavailable
          </Text>
          <TouchableOpacity
            onPress={handleView}
            className="px-4 py-2 rounded-lg flex-row items-center"
          >
            <ExternalLink size={16} color="white" />
            <Text
              className=" font-medium ml-2"
              style={{ color: colors.primaryText }}
            >
              Open External
            </Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Use Google Docs Viewer for PDF preview
    const googleDocsUrl = `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(fileUrl)}`;

    return (
      <View className="flex-1 relative bg-white">
        {/* Overlay controls */}
        <View className="absolute top-2 right-2 z-20 flex-row space-x-2">
          <TouchableOpacity
            onPress={handleView}
            className="bg-black/70 px-3 py-1.5 rounded-full flex-row items-center"
          >
            <ExternalLink size={12} color="white" />
            <Text className="text-white text-xs ml-1 font-medium">Open</Text>
          </TouchableOpacity>
        </View>

        {/* WebView for PDF preview */}
        <WebView
          source={{ uri: googleDocsUrl }}
          style={{ flex: 1 }}
          onError={() => {
            setPdfError(true);
            setIsLoading(false);
          }}
          onHttpError={() => {
            setPdfError(true);
            setIsLoading(false);
          }}
          onLoadStart={() => {
            setIsLoading(true);
            setPdfError(false);
          }}
          onLoadEnd={() => {
            setIsLoading(false);
          }}
          startInLoadingState={false}
          scalesPageToFit={true}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={true}
          scrollEnabled={true}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={false}
        />

        {/* Single loading overlay - only show when actually loading */}
        {isLoading && !pdfError && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: colors.background + 'E6', // 90% opacity
              zIndex: 10,
            }}
          >
            <View
              style={{
                backgroundColor: colors.surface,
                padding: 24,
                borderRadius: 16,
                shadowColor: colors.text,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.1,
                shadowRadius: 8,
                elevation: 5,
                alignItems: 'center',
              }}
            >
              <ActivityIndicator size="large" color={colors.primary} />
              <Text
                style={{
                  color: colors.text,
                  marginTop: 12,
                  fontSize: 14,
                  fontWeight: '500',
                }}
              >
                Loading Preview
              </Text>
              <Text
                style={{
                  color: colors.textSecondary,
                  fontSize: 12,
                  marginTop: 4,
                }}
              >
                Please wait...
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <Card
      className="p-0 overflow-hidden shadow-md mt-3"
      style={{ backgroundColor: colors.card }}
    >
      {/* Header Section */}
      <View className="bg-gradient-to-r from-gray-50 to-white p-5">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            {/* Icon */}
            <View
              className={`w-12 h-12 ${iconColor} rounded-xl items-center justify-center mr-4 shadow-sm`}
            >
              {getIcon()}
            </View>

            {/* Title and Date */}
            <View className="flex-1">
              {type === 'job' ? (
                <View>
                  <Text
                    className="font-bold text-lg"
                    style={{ color: colors.text }}
                  >
                    Job
                  </Text>
                  <Text
                    className="font-bold text-lg  -mt-1"
                    style={{ color: colors.text }}
                  >
                    Description
                  </Text>
                </View>
              ) : (
                <Text
                  className="font-bold text-lg"
                  style={{ color: colors.text }}
                >
                  {title}
                </Text>
              )}
              <View className="flex-row items-center mt-1">
                <Ionicons
                  name="calendar-outline"
                  size={12}
                  color={colors.textMuted}
                />
                <Text className="text-xs text-gray-500 ml-1 font-medium">
                  {date}
                </Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View className="flex-row space-x-2">
            <TouchableOpacity
              style={{
                borderColor: borderColor,
                borderWidth: 1.5,
                backgroundColor: `${textColor}08`,
              }}
              className="px-4 py-1.5 flex-row items-center rounded-xl"
              onPress={handleView}
              disabled={!fileUrl}
            >
              <Eye size={16} color={textColor} />
              <Text
                className={`${textColor === '#2563EB' ? 'text-blue-600' : 'text-orange-600'} text-sm font-semibold ml-2`}
              >
                View
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                borderColor: borderColor,
                borderWidth: 1.5,
                backgroundColor: `${textColor}08`,
              }}
              className="px-4 py-1.5 rounded-xl ml-2 flex-row items-center"
              onPress={handleDownload}
              disabled={!fileUrl}
            >
              <Download size={16} color={textColor} />
              <Text
                className={`${textColor === '#2563EB' ? 'text-blue-600' : 'text-orange-600'} text-sm font-semibold ml-2`}
              >
                Save
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* PDF Preview Section - Increased height */}
      <View className="mx-4 mb-4 h-64 bg-gray-50 rounded-2xl border-2 border-gray-200 overflow-hidden shadow-inner">
        {renderPreview()}
      </View>

      {/* File Info Footer */}
      <View className="px-5 pb-4">
        <View className="flex-row items-center justify-between bg-gray-50 px-4 py-3 rounded-xl">
          <View className="flex-row items-center">
            <Ionicons name="document" size={16} color="#6B7280" />
            <Text className="text-gray-600 text-sm ml-2 font-medium">
              {fileName}
            </Text>
          </View>
          <View className="flex-row items-center">
            <View
              className={`w-2 h-2 ${fileUrl ? 'bg-green-500' : 'bg-red-500'} rounded-full mr-2`}
            />
            <Text
              className={`text-xs font-medium ${fileUrl ? 'text-green-600' : 'text-red-500'}`}
            >
              {fileUrl ? 'Available' : 'Unavailable'}
            </Text>
          </View>
        </View>
      </View>
    </Card>
  );
};

export default DocumentCard;
