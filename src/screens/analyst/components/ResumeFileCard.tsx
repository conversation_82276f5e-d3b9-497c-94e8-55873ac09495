import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Card, PDFPreview } from '@/components/ui';
import { useThemeColors } from '@/hooks/useThemeColors';

interface ResumeFileCardProps {
  cvFileUrl?: string;
}

export const ResumeFileCard: React.FC<ResumeFileCardProps> = ({
  cvFileUrl,
}) => {
  const colors = useThemeColors();

  return (
    <Card
      style={{
        marginBottom: 24,
        borderRadius: 16,
        marginTop: 24,
        backgroundColor: colors.surface,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.1,
        shadowRadius: 15,
        elevation: 10,
        overflow: 'hidden',
      }}
    >
      <LinearGradient
        colors={[colors.surfaceSecondary, colors.surface]}
        style={{
          padding: 24,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
        }}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              flex: 1,
            }}
          >
            <View
              style={{
                backgroundColor: colors.primaryLight,
                padding: 16,
                borderRadius: 12,
                marginRight: 16,
              }}
            >
              <Ionicons name="document-text" size={28} color={colors.primary} />
            </View>
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  fontWeight: 'bold',
                  color: colors.text,
                  fontSize: 18,
                }}
              >
                Resume File
              </Text>
              <Text
                style={{
                  fontSize: 14,
                  color: colors.textSecondary,
                  marginTop: 4,
                }}
              >
                CV Analysis • {new Date().toLocaleDateString()}
              </Text>
            </View>
          </View>
          <View
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              backgroundColor: colors.success + '20',
            }}
          >
            <Text
              style={{
                color: colors.success,
                fontSize: 14,
                fontWeight: 'bold',
              }}
            >
              Complete
            </Text>
          </View>
        </View>
      </LinearGradient>

      {/* PDF Preview */}
      <PDFPreview cvFileUrl={cvFileUrl} />
    </Card>
  );
};
