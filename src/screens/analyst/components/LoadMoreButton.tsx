import React from 'react';
import { View, Text, ActivityIndicator, TouchableOpacity } from 'react-native';
import Animated, { FadeInUp, FadeIn } from 'react-native-reanimated';
import { ChevronRight } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

interface LoadMoreButtonProps {
  loading: boolean;
  onPress: () => void;
  delay?: number;
}

export const LoadMoreButton: React.FC<LoadMoreButtonProps> = ({
  loading,
  onPress,
  delay = 0,
}) => {
  const colors = useThemeColors();

  return (
    <AnimatedTouchableOpacity
      entering={FadeInUp.delay(delay).springify()}
      onPress={onPress}
      style={{
        backgroundColor: colors.surfaceSecondary,
        borderRadius: 25,
        paddingVertical: 16,
        paddingHorizontal: 24,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 16,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 1,
        elevation: 0.7,
      }}
      activeOpacity={0.8}
      disabled={loading}
    >
      {loading ? (
        <Animated.View
          style={{ flexDirection: 'row', alignItems: 'center' }}
          entering={FadeIn}
        >
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={{
            color: colors.textSecondary,
            fontWeight: '600',
            marginLeft: 12
          }}>
            Loading...
          </Text>
        </Animated.View>
      ) : (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={{
            color: colors.primary,
            fontWeight: '600',
            marginRight: 8,
            fontSize: 16
          }}>
            Load More
          </Text>
          <ChevronRight size={16} color={colors.primary} />
        </View>
      )}
    </AnimatedTouchableOpacity>
  );
};
