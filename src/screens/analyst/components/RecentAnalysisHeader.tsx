import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Animated, {
  FadeInDown,
  FadeIn,
  SlideInRight,
  BounceIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import { useThemeColors } from '@/hooks/useThemeColors';

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

interface AnalysisHeaderProps {
  totalCount: number;
  hasMoreData: boolean;
  loading: boolean;
  searchQuery: string;
  onSeeAllPress: () => void;
}

export const AnalysisHeader: React.FC<AnalysisHeaderProps> = ({
  totalCount,
  hasMoreData,
  loading,
  searchQuery,
  onSeeAllPress,
}) => {
  const colors = useThemeColors();
  const headerScale = useSharedValue(1);

  const animatedHeaderStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: headerScale.value }],
    };
  });

  const handleHeaderPress = () => {
    headerScale.value = withSpring(
      0.98,
      { damping: 10, stiffness: 400 },
      () => {
        headerScale.value = withSpring(1, { damping: 10, stiffness: 400 });
      },
    );
  };

  return (
    <Animated.View entering={FadeInDown.delay(100).springify()}>
      <Animated.View
        className="flex-row justify-between items-center mb-8"
        style={animatedHeaderStyle}
      >
        <TouchableOpacity
          className="flex-1"
          onPress={handleHeaderPress}
          activeOpacity={0.9}
        >
          <Animated.Text
            style={{
              fontSize: 30,
              fontWeight: 'bold',
              color: colors.text,
              marginBottom: 8
            }}
            entering={FadeIn.delay(200)}
          >
            Recent Analyses
          </Animated.Text>
          <Animated.View
            style={{ flexDirection: 'row', alignItems: 'center' }}
            entering={SlideInRight.delay(300)}
          >
            <Animated.View
              style={{
                width: 48,
                height: 4,
                backgroundColor: colors.primary,
                borderRadius: 2,
                marginRight: 8
              }}
              entering={FadeIn.delay(400)}
            />
            <Text style={{
              color: colors.textSecondary,
              fontWeight: '500'
            }}>
              {totalCount} {totalCount === 1 ? 'analysis' : 'analyses'}{' '}
              available
            </Text>
          </Animated.View>
        </TouchableOpacity>
        {!searchQuery && hasMoreData && (
          <AnimatedTouchableOpacity
            entering={BounceIn.delay(500)}
            onPress={onSeeAllPress}
            disabled={loading}
            style={{
              backgroundColor: colors.surface,
              borderRadius: 16,
              paddingHorizontal: 20,
              paddingVertical: 8,
              borderWidth: 1,
              borderColor: colors.primary + '30',
              shadowColor: colors.primary,
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 4,
            }}
          >
            <Text style={{
              color: colors.primary,
              fontWeight: 'bold',
              fontSize: 14
            }}>
              See All
            </Text>
          </AnimatedTouchableOpacity>
        )}
      </Animated.View>
    </Animated.View>
  );
};
