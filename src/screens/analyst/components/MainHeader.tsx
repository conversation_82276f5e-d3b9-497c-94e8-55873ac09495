import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ImageBackground,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Upload, ArrowLeft } from 'lucide-react-native';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import { useThemeColors } from '@/hooks/useThemeColors';

interface HeaderProps {}

const Header: React.FC<HeaderProps> = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AnalysisStackParamList>>();
  const insets = useSafeAreaInsets();

  return (
    <View style={{ marginTop: -insets.top }}>
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=800&h=600&fit=crop',
        }}
        style={{ width: '100%', height: 220 + insets.top }}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(255, 165, 0, 0.6)', 'rgba(255, 140, 0, 0.6)']}
          style={{
            position: 'absolute',
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            paddingTop: insets.top,
            paddingHorizontal: 24,
            paddingBottom: 24,
          }}
        >
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="absolute left-2 bg-white/20 rounded-full p-[8px] items-center justify-center z-10"
            style={{ top: insets.top + 40 }}
            activeOpacity={0.7}
            hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
          >
            <View pointerEvents="none">
              <ArrowLeft size={20} color="white" />
            </View>
          </TouchableOpacity>

          <View className="flex-1 justify-center items-center mt-8">
            <Text className="text-white text-center text-3xl font-bold mb-2">
              Transform Your Resume
            </Text>
            <Text className="text-white/90 text-center text-lg mb-8 px-4">
              Get AI-powered feedback in seconds
            </Text>

            {/* Upload Button */}
            <TouchableOpacity
              onPress={() => navigation.navigate('UploadResume')}
              className="bg-white rounded-xl py-4 px-8 flex-row items-center justify-center shadow-lg"
              activeOpacity={0.9}
            >
              <Upload size={20} color="#6366f1" />
              <Text className="text-indigo-600 font-semibold ml-2 text-lg">
                Upload Resume
              </Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
};

export default Header;
