import React from 'react';
import { View, Text } from 'react-native';
import {
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from '@expo/vector-icons';
import { getItemContainerStyles, getTagStyles } from '@/utils/skillsColor';
import { useThemeColors } from '@/hooks/useThemeColors';

interface SkillItem {
  name: string;
  items: string[];
  status: 'good' | 'bad' | 'warning';
}

interface SkillAnalysisProps {
  skills: SkillItem[];
  recommendations: string[];
}

const SkillCard: React.FC<{
  title: string;
  items: string[];
  status: 'good' | 'bad' | 'warning';
}> = ({ title, items, status }) => {
  const colors = useThemeColors();
  const StatusIconComponent = ({
    status,
  }: {
    status: 'good' | 'bad' | 'warning';
  }) => {
    switch (status) {
      case 'good':
        return <Ionicons name="checkmark-circle" size={20} color="#10B981" />;
      case 'bad':
        return <Ionicons name="close-circle" size={20} color="#EF4444" />;
      case 'warning':
        return <Ionicons name="warning" size={20} color="#F59E0B" />;
      default:
        return null;
    }
  };

  const ItemIcon = ({
    status,
    index,
  }: {
    status: 'good' | 'bad' | 'warning';
    index: number;
  }) => {
    switch (status) {
      case 'good': {
        const goodIcons = ['star', 'trophy', 'thumbs-up', 'heart', 'medal'];
        const goodIconName = goodIcons[index % goodIcons.length] as any;
        return <Ionicons name={goodIconName} size={16} color="#10B981" />;
      }
      case 'bad': {
        const badIcons = [
          'alert-circle',
          'remove-circle',
          'warning',
          'close',
          'help-circle',
        ];
        const badIconName = badIcons[index % badIcons.length] as any;
        return <Ionicons name={badIconName} size={16} color="#DC2626" />;
      }
      case 'warning': {
        const warningIcons = [
          'build',
          'time',
          'trending-up',
          'school',
          'people',
        ];
        const warningIconName = warningIcons[
          index % warningIcons.length
        ] as any;
        return <Ionicons name={warningIconName} size={16} color="#F59E0B" />;
      }
      default: {
        return <Ionicons name="ellipse" size={16} color="#6B7280" />;
      }
    }
  };

  // Don't render if no items
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <View
      className="mb-4 p-4 rounded-lg border bg-white border-gray-200 shadow-sm"
      style={{ backgroundColor: colors.card }}
    >
      <View className="flex-row items-center mb-4">
        <StatusIconComponent status={status} />
        <Text
          className="ml-3 font-semibold text-lg"
          style={{ color: colors.text }}
        >
          {title}
        </Text>
        <View className="ml-auto bg-gray-100 px-2 py-1 rounded-full">
          <Text
            className="text-gray-600 text-xs font-medium"
            style={{ color: colors.textMuted }}
          >
            {items.length} {items.length === 1 ? 'item' : 'items'}
          </Text>
        </View>
      </View>

      {/* For Missing Skills - show as list items like Matching Points */}
      {status === 'bad' && (
        <View style={{ backgroundColor: colors.card }}>
          {items.map((item, index) => (
            <View
              key={index}
              className="flex-row items-start p-3 mb-2 bg-red-50 border border-red-200 rounded-lg"
            >
              <View className="mt-0.5">
                <ItemIcon status={status} index={index} />
              </View>
              <Text className="ml-3 text-sm font-medium text-red-700 flex-1 leading-5">
                {item}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* For Matching Points and other statuses - show as list items */}
      {status !== 'bad' && (
        <View>
          {items.map((item, index) => (
            <View
              key={index}
              style={getItemContainerStyles(status)}
              className="flex-row items-start p-3 mb-2 rounded-lg"
            >
              <View className="mt-0.5">
                <ItemIcon status={status} index={index} />
              </View>
              <Text className="ml-3 text-sm font-medium text-gray-700 flex-1 leading-5">
                {item}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

// RecommendationsCard Component
const RecommendationsCard: React.FC<{ recommendations: string[] }> = ({
  recommendations,
}) => {
  const colors = useThemeColors();
  // Don't render if no recommendations
  if (!recommendations || recommendations.length === 0) {
    return null;
  }

  return (
    <View
      className="mb-4 p-4 rounded-lg border border-gray-200 shadow-sm"
      style={{ backgroundColor: colors.card }}
    >
      <View className="flex-row items-center mb-4">
        <MaterialCommunityIcons
          name="lightbulb-variant"
          size={20}
          color={colors.primary}
        />
        <Text
          className="ml-3 font-semibold text-lg"
          style={{ color: colors.text }}
        >
          Recommendations
        </Text>
        <View className="ml-auto bg-amber-100 px-2 py-1 rounded-full">
          <Text className="text-amber-700 text-xs font-medium">
            {recommendations.length}{' '}
            {recommendations.length === 1 ? 'tip' : 'tips'}
          </Text>
        </View>
      </View>

      {recommendations.map((rec, index) => (
        <View
          key={index}
          className="flex-row items-start mb-3 p-3 bg-amber-50 rounded-xl border border-amber-200"
        >
          <View className="mt-0.5">
            <MaterialIcons name="recommend" size={16} color="#F59E0B" />
          </View>
          <Text className="ml-3 text-sm text-gray-700 flex-1 leading-5">
            {rec}
          </Text>
        </View>
      ))}
    </View>
  );
};

const SkillAnalysis: React.FC<SkillAnalysisProps> = ({
  skills,
  recommendations,
}) => {
  const colors = useThemeColors();

  return (
    <View style={{ paddingHorizontal: 16, marginBottom: 24 }}>
      {/* Skills Analysis Header Card */}
      <View
        style={{
          marginBottom: 24,
          padding: 24,
          backgroundColor: colors.surface,
          borderRadius: 12,
          borderWidth: 1,
          borderColor: colors.border,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 8,
        }}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 16,
          }}
        >
          <View
            style={{
              width: 48,
              height: 48,
              backgroundColor: colors.primaryLight,
              borderRadius: 12,
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: 16,
            }}
          >
            <MaterialCommunityIcons
              name="chart-box-outline"
              size={24}
              color={colors.primary}
            />
          </View>
          <View style={{ flex: 1 }}>
            <Text
              style={{
                fontSize: 24,
                fontWeight: 'bold',
                color: colors.text,
                marginBottom: 4,
              }}
            >
              Skills Analysis
            </Text>
            <Text
              style={{
                color: colors.textSecondary,
                fontSize: 14,
              }}
            >
              Detailed breakdown of your qualifications against job requirements
            </Text>
          </View>
        </View>

        {/* Divider */}
        <View
          style={{
            height: 1,
            backgroundColor: colors.border,
            marginBottom: 16,
          }}
        />

        {/* Stats Cards */}
        <View className="flex-row justify-between">
          {skills.map((skill, index) => (
            <View
              key={skill.name}
              className={`flex-1 p-3 rounded-lg mx-1 ${
                skill.status === 'good'
                  ? 'bg-green-50 border border-green-200'
                  : skill.status === 'warning'
                    ? 'bg-yellow-50 border border-yellow-200'
                    : 'bg-red-50 border border-red-200'
              }`}
            >
              <View className="items-center">
                <Text
                  className={`font-bold text-3xl mb-1 ${
                    skill.status === 'good'
                      ? 'text-green-600'
                      : skill.status === 'warning'
                        ? 'text-yellow-600'
                        : 'text-red-600'
                  }`}
                >
                  {skill.items.length}
                </Text>
                <Text className="text-xs text-gray-700 text-center font-medium">
                  {skill.name}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      {skills.map((skill, index) => (
        <SkillCard
          key={index}
          title={skill.name}
          items={skill.items}
          status={skill.status}
        />
      ))}

      <RecommendationsCard recommendations={recommendations} />

      {/* Summary Stats */}
      <View
        className="mt-4 p-4 rounded-lg border border-gray-200"
        style={{ backgroundColor: colors.card }}
      >
        <Text className="font-semibold text-lg" style={{ color: colors.text }}>
          Analysis Summary
        </Text>
        <View className="flex-row justify-between">
          {skills.map((skill) => (
            <View key={skill.name} className="flex-1 items-center">
              <Text className="text-sm text-gray-500 mb-1">{skill.name}</Text>
              <Text
                className={`font-bold text-xl ${
                  skill.status === 'good'
                    ? 'text-green-600'
                    : skill.status === 'warning'
                      ? 'text-yellow-600'
                      : 'text-red-600'
                }`}
              >
                {skill.items.length}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

export default SkillAnalysis;
