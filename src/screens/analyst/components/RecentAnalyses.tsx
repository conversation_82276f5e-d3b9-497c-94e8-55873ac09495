import React, { useState, useMemo } from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import { AnalystResponse } from '@/services/types/analyst';
import { extractCVInfo } from '@/utils/cvParser';
import {
  EmptyState,
  LoadingState,
  SearchBar,
  SwipeableItem,
} from '@/components/ui';
import { AnalysisCard } from './AnalysisCard';
import { LoadMoreButton } from './LoadMoreButton';
import { AnalysisHeader } from './RecentAnalysisHeader';
import { useThemeColors } from '@/hooks/useThemeColors';

interface RecentAnalysesProps {
  paginatedAnalysts: AnalystResponse[];
  allAnalysts: AnalystResponse[];
  loading: boolean;
  hasMoreData: boolean;
  loadingItemId: string | null;
  deletingItemId: string | null;
  onLoadMore: () => void;
  onAnalystPress: (analyst: AnalystResponse) => void;
  onDeleteAnalyst: (id: string) => void;
}

const RecentAnalyses: React.FC<RecentAnalysesProps> = ({
  paginatedAnalysts,
  allAnalysts,
  loading,
  hasMoreData,
  loadingItemId,
  deletingItemId,
  onLoadMore,
  onAnalystPress,
  onDeleteAnalyst,
}) => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AnalysisStackParamList>>();
  const colors = useThemeColors();
  const [searchQuery, setSearchQuery] = useState('');

  // Function to get CV info for each analyst
  const getCVInfo = (analyst: AnalystResponse) => {
    return extractCVInfo(analyst.cvText ?? '');
  };

  // Filter analysts based on search query
  const filteredAnalysts = useMemo(() => {
    if (!searchQuery.trim()) {
      return paginatedAnalysts;
    }

    const query = searchQuery.toLowerCase().trim();
    return allAnalysts.filter((analyst) => {
      const cvInfo = getCVInfo(analyst);
      const analysisType =
        analyst.agentType === 'AI_RESUME_ANALYSIS_ROLE'
          ? 'Role Analysis'
          : 'JD Analysis';

      return (
        cvInfo.name.toLowerCase().includes(query) ||
        cvInfo.role.toLowerCase().includes(query) ||
        analysisType.toLowerCase().includes(query) ||
        analyst.content.overall_score.toString().includes(query)
      );
    });
  }, [allAnalysts, paginatedAnalysts, searchQuery]);

  const handleUploadResume = () => {
    navigation.navigate('UploadResume');
  };

  const renderAnalysesContent = () => {
    // Loading state
    if (loading && paginatedAnalysts.length === 0) {
      return (
        <LoadingState
          title="Loading analyses..."
          description="Please wait while we fetch your data"
          delay={300}
        />
      );
    }

    // Empty state - no analyses
    if (paginatedAnalysts.length === 0) {
      return (
        <EmptyState
          icon={<Ionicons name="document-outline" size={40} color="#6366f1" />}
          title="No analyses yet"
          description="Upload your first resume to get started with AI-powered analysis and unlock insights"
          buttonText="Upload Resume"
          onButtonPress={handleUploadResume}
          delay={400}
        />
      );
    }

    // Show search results
    const displayAnalysts =
      searchQuery.length > 0 ? filteredAnalysts : paginatedAnalysts;

    // No search results
    if (searchQuery.length > 0 && filteredAnalysts.length === 0) {
      return (
        <EmptyState
          icon={<Ionicons name="search-outline" size={40} color="#9CA3AF" />}
          title="No results found"
          description="Try adjusting your search terms or browse all analyses below"
          buttonText="Clear Search"
          onButtonPress={() => setSearchQuery('')}
          delay={300}
        />
      );
    }

    return (
      <>
        {displayAnalysts.map((analyst, index) => {
          const isLoading = loadingItemId === analyst._id;
          const isDeleting = deletingItemId === analyst._id;

          return (
            <SwipeableItem
              key={analyst._id}
              onDelete={() => onDeleteAnalyst(analyst._id)}
              isDeleting={isDeleting}
              itemId={analyst._id}
              deleteTitle="Delete Analysis"
              deleteMessage="Are you sure you want to delete this analysis? This action cannot be undone."
            >
              <AnalysisCard
                analyst={analyst}
                index={index}
                isLoading={isLoading}
                isDeleting={isDeleting}
                onPress={onAnalystPress}
              />
            </SwipeableItem>
          );
        })}

        {/* Load More Button - Only show when not searching */}
        {!searchQuery && hasMoreData && (
          <LoadMoreButton
            loading={loading}
            onPress={onLoadMore}
            delay={displayAnalysts.length * 100}
          />
        )}
      </>
    );
  };

  return (
    <View style={{
      paddingHorizontal: 24,
      paddingVertical: 24,
      backgroundColor: colors.background
    }}>
      {/* Header Section */}
      <AnalysisHeader
        totalCount={filteredAnalysts.length}
        hasMoreData={hasMoreData}
        loading={loading}
        searchQuery={searchQuery}
        onSeeAllPress={onLoadMore}
      />

      {/* Search Bar */}
      <SearchBar
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        placeholder="Search by name, role, type, or score..."
        resultCount={
          searchQuery.length > 0 ? filteredAnalysts.length : undefined
        }
        resultLabel="result"
      />

      {/* Content */}
      {renderAnalysesContent()}
    </View>
  );
};

export default RecentAnalyses;
