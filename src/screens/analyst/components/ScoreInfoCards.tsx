import React from 'react';
import { View, Text } from 'react-native';
import { AntDesign, FontAwesome5, Feather, Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '@/hooks/useThemeColors';

interface ScoreInfoCardsProps {
  overallScore?: number;
  fitScore?: number;
  overallFeedback?: string;
  summaryComment?: string;
  verdict?: string;
}

const ScoreInfoCards: React.FC<ScoreInfoCardsProps> = ({
  overallScore = 0,
  fitScore,
  overallFeedback,
  summaryComment,
  verdict,
}) => {
  const colors = useThemeColors();

  // Function to get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 80)
      return {
        bg: 'bg-green-500',
        lightBg: 'bg-green-100',
        text: 'text-green-700',
        ring: 'ring-green-300',
        hex: colors.success,
      };
    if (score >= 60)
      return {
        bg: 'bg-yellow-500',
        lightBg: 'bg-yellow-100',
        text: 'text-yellow-700',
        ring: 'ring-yellow-300',
        hex: colors.warning,
      };
    if (score >= 40)
      return {
        bg: 'bg-orange-500',
        lightBg: 'bg-orange-100',
        text: 'text-orange-700',
        ring: 'ring-orange-300',
        hex: colors.warning,
      };
    return {
      bg: 'bg-red-500',
      lightBg: 'bg-red-100',
      text: 'text-red-700',
      ring: 'ring-red-300',
      hex: colors.error,
    };
  };

  // Function to get verdict styling
  const getVerdictStyle = (verdict: string) => {
    const lowerVerdict = verdict?.toLowerCase() || '';
    if (lowerVerdict.includes('strong') || lowerVerdict.includes('excellent')) {
      return {
        bg: 'bg-gradient-to-r from-emerald-50 to-emerald-100',
        text: 'text-emerald-700',
        border: 'border-emerald-200',
        icon: '#10B981',
      };
    }
    if (lowerVerdict.includes('good') || lowerVerdict.includes('moderate')) {
      return {
        bg: 'bg-gradient-to-r from-amber-50 to-amber-100',
        text: 'text-amber-700',
        border: 'border-amber-200',
        icon: '#F59E0B',
      };
    }
    if (lowerVerdict.includes('weak') || lowerVerdict.includes('poor')) {
      return {
        bg: 'bg-gradient-to-r from-red-50 to-red-100',
        text: 'text-red-700',
        border: 'border-red-200',
        icon: '#EF4444',
      };
    }
    return {
      bg: 'bg-gradient-to-r from-gray-50 to-gray-100',
      text: 'text-gray-700',
      border: 'border-gray-200',
      icon: '#6B7280',
    };
  };

  const scoreColor = getScoreColor(overallScore);
  const fitScoreColor = getScoreColor(fitScore || overallScore);
  const verdictStyle = getVerdictStyle(verdict || '');

  return (
    <View className="px-4 mb-4">
      {/* Overall Score Section */}
      <View
        className="mb-6 p-6 rounded-2xl shadow-lg border border-gray-100"
        style={{ backgroundColor: colors.card }}
      >
        <View className="flex-row items-center mb-6">
          <View
            className={`w-12 h-12 ${scoreColor.lightBg} rounded-xl items-center justify-center mr-4`}
          >
            <FontAwesome5 name="chart-line" size={24} color={scoreColor.hex} />
          </View>
          <View className="flex-1">
            <Text className="text-xl font-bold" style={{ color: colors.text }}>
              Overall Score
            </Text>
            <Text className="text-gray-500 text-sm mt-1">
              Performance evaluation
            </Text>
          </View>
        </View>

        <View className="flex-row items-center">
          <View className="relative">
            <View
              className={`w-20 h-20 ${scoreColor.bg} rounded-full items-center justify-center mr-6 shadow-lg`}
            >
              <Text className="text-white font-bold text-xl">
                {overallScore}%
              </Text>
            </View>
          </View>

          <View className="flex-1">
            <View className="w-full bg-gray-200 rounded-full h-3 mb-3">
              <View
                className={`${scoreColor.bg} h-3 rounded-full shadow-sm`}
                style={{ width: `${overallScore}%` }}
              />
            </View>

            <View className="flex-row items-center">
              <View
                className={`w-6 h-6 ${scoreColor.lightBg} rounded-full items-center justify-center ${scoreColor.ring} ring-2 mr-2`}
              >
                <AntDesign
                  name="star"
                  size={12}
                  color={scoreColor.bg.replace('bg-', '#')}
                />
              </View>

              <Text className={`text-base font-semibold ${scoreColor.text}`}>
                {overallScore >= 80
                  ? 'Excellent Match'
                  : overallScore >= 60
                    ? 'Good Match'
                    : overallScore >= 40
                      ? 'Fair Match'
                      : 'Needs Improvement'}
              </Text>
            </View>

            <Text className="text-gray-500 text-sm mt-1">
              {overallScore >= 80
                ? 'Outstanding performance'
                : overallScore >= 60
                  ? 'Above average performance'
                  : overallScore >= 40
                    ? 'Average performance'
                    : 'Below expectations'}
            </Text>
          </View>
        </View>
      </View>

      {/* Overall Feedback Section */}
      {overallFeedback && (
        <View
          className="mb-6 p-6 rounded-2xl shadow-lg border border-gray-100"
          style={{ backgroundColor: colors.card }}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-12 h-12 bg-blue-50 rounded-xl items-center justify-center mr-4">
              <AntDesign name="infocirlce" size={24} color="#3B82F6" />
            </View>
            <View className="flex-1">
              <Text
                className="text-xl font-bold"
                style={{ color: colors.text }}
              >
                Overall Feedback
              </Text>
              <Text
                className="text-gray-500 text-sm mt-1"
                style={{ color: colors.textMuted }}
              >
                Detailed analysis
              </Text>
            </View>
          </View>
          <View className="bg-blue-50 p-4 rounded-xl border border-blue-100">
            <Text className="text-gray-700 leading-6 text-base">
              {overallFeedback}
            </Text>
          </View>
        </View>
      )}

      {/* Summary Section */}
      {summaryComment && (
        <View
          className="mb-6 p-6 rounded-2xl shadow-lg border border-gray-100"
          style={{ backgroundColor: colors.card }}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-12 h-12 bg-indigo-50 rounded-xl items-center justify-center mr-4">
              <Ionicons name="document-text" size={24} color="#6366F1" />
            </View>
            <View className="flex-1">
              <Text
                className="text-xl font-bold"
                style={{ color: colors.text }}
              >
                Summary
              </Text>
              <Text
                className="text-gray-500 text-sm mt-1"
                style={{ color: colors.textMuted }}
              >
                Key insights
              </Text>
            </View>
          </View>
          <View className="bg-indigo-50 p-4 rounded-xl border border-indigo-100">
            <Text className="text-gray-700 leading-6 text-base">
              {summaryComment}
            </Text>
          </View>
        </View>
      )}

      {/* Fit Score Section */}
      <View
        className="mb-6 p-6 rounded-2xl shadow-lg border border-gray-100"
        style={{ backgroundColor: colors.card }}
      >
        <View className="flex-row items-center mb-6">
          <View className="w-12 h-12 bg-orange-50 rounded-xl items-center justify-center mr-4">
            <Feather name="target" size={24} color="#F97316" />
          </View>
          <View className="flex-1">
            <Text className="text-xl font-bold" style={{ color: colors.text }}>
              Fit Score
            </Text>
            <Text
              className="text-gray-500 text-sm mt-1"
              style={{ color: colors.textMuted }}
            >
              Job compatibility
            </Text>
          </View>
        </View>

        <View className="flex-row items-center justify-between">
          <View className="flex-1">
            <Text className={`text-4xl font-bold ${fitScoreColor.text} mb-2`}>
              {fitScore || overallScore}%
            </Text>
            <View className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <View
                className={`${fitScoreColor.bg} h-2 rounded-full`}
                style={{ width: `${fitScore || overallScore}%` }}
              />
            </View>
            <Text className="text-gray-500 text-sm">Compatibility Rating</Text>
          </View>

          <View className="ml-6">
            <View className="relative">
              <View
                className={`w-24 h-24 ${fitScoreColor.bg} rounded-2xl items-center justify-center shadow-lg`}
              >
                <Text className="text-white font-bold text-lg">
                  {fitScore || overallScore}%
                </Text>
              </View>
              <View
                className={`absolute -top-2 -right-2 w-8 h-8 ${fitScoreColor.lightBg} rounded-full items-center justify-center ${fitScoreColor.ring} ring-2`}
              >
                <Feather
                  name="target"
                  size={16}
                  color={fitScoreColor.bg.replace('bg-', '#')}
                />
              </View>
            </View>
          </View>
        </View>
      </View>

      {/* Verdict Section */}
      {verdict && (
        <View
          className="mb-6 p-6 rounded-2xl shadow-lg border border-gray-100"
          style={{ backgroundColor: colors.card }}
        >
          <View className="flex-row items-center mb-4">
            <View className="w-12 h-12 bg-green-50 rounded-xl items-center justify-center mr-4">
              <FontAwesome5 name="ribbon" size={24} color="#10B981" />
            </View>
            <View className="flex-1">
              <Text
                className="text-xl font-bold"
                style={{ color: colors.text }}
              >
                Verdict
              </Text>
              <Text
                className="text-gray-500 text-sm mt-1"
                style={{ color: colors.textMuted }}
              >
                Final assessment
              </Text>
            </View>
          </View>

          <View className={`mb-4 inline-flex items-center px-4 py-2`}>
            <View
              className={`flex-row items-center px-4 py-2 ${verdictStyle.bg} ${verdictStyle.border} border rounded-full`}
            >
              <FontAwesome5 name="award" size={20} color={verdictStyle.icon} />
              <Text
                className={`${verdictStyle.text} font-bold text-lg capitalize ml-3`}
              >
                {verdict}
              </Text>
            </View>
          </View>

          {/* Additional context based on score */}
          <View className="bg-gray-50 p-4 rounded-xl border border-gray-100">
            <Text className="text-gray-600 text-sm leading-5">
              💡{' '}
              {overallScore >= 80
                ? 'Strong candidate for this position with excellent qualifications'
                : overallScore >= 60
                  ? 'Good potential candidate with some areas for improvement'
                  : overallScore >= 40
                    ? 'Moderate fit - consider developing key skills and experience'
                    : 'Significant skill development needed to match position requirements'}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default ScoreInfoCards;
