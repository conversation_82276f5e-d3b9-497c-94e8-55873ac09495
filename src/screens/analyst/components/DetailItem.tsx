import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColors } from '@/hooks/useThemeColors';

interface DetailItemProps {
  icon: string;
  title: string;
  content: string | string[];
  color: string;
  iconColor: string;
}

export const DetailItem: React.FC<DetailItemProps> = ({
  icon,
  title,
  content,
  color,
  iconColor,
}) => {
  const colors = useThemeColors();

  return (
    <TouchableOpacity
      style={{
        padding: 16,
        marginVertical: 8,
        borderRadius: 12,
        borderWidth: 1,
        marginBottom: 16,
        backgroundColor: color,
        borderColor: iconColor + '30',
        shadowColor: iconColor,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}
      activeOpacity={0.7}
    >
      <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
        <View
          style={{
            padding: 12,
            borderRadius: 12,
            marginRight: 16,
            backgroundColor: iconColor + '20'
          }}
        >
          <Ionicons name={icon as any} size={20} color={iconColor} />
        </View>
        <View style={{ flex: 1 }}>
          <Text style={{
            fontWeight: 'bold',
            fontSize: 16,
            marginBottom: 12,
            color: iconColor
          }}>
            {title}
          </Text>
          {Array.isArray(content) ? (
            content.map((item, index) => (
              <View key={index} style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                marginBottom: 8
              }}>
                <View
                  style={{
                    width: 8,
                    height: 8,
                    borderRadius: 4,
                    marginTop: 6,
                    marginRight: 12,
                    backgroundColor: iconColor
                  }}
                />
                <Text style={{
                  color: colors.textSecondary,
                  fontSize: 14,
                  flex: 1,
                  lineHeight: 20
                }}>
                  {item}
                </Text>
              </View>
            ))
          ) : (
            <Text style={{
              color: colors.textSecondary,
              fontSize: 14,
              lineHeight: 20
            }}>
              {content}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};
