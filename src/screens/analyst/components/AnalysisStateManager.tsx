import React from 'react';
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import { Ionicons } from '@expo/vector-icons';
import { Button } from '@/components/ui';
import { useThemeColors } from '@/hooks/useThemeColors';

interface AnalysisStateManagerProps {
  isLoading?: boolean;
  error?: string | null;
  hasData?: boolean;
  onGoBack: () => void;
  title: string;
  subtitle?: string;
  loadingText?: string;
  loadingSubtext?: string;
  errorTitle?: string;
  noDataText?: string;
}

const AnalysisStateManager: React.FC<AnalysisStateManagerProps> = ({
  isLoading = false,
  error = null,
  hasData = true,
  onGoBack,
  title,
  subtitle,
  loadingText = 'Loading analysis...',
  loadingSubtext = 'Please wait while we process your data',
  errorTitle = 'Error loading analysis',
  noDataText = 'No analysis data found',
}) => {
  const colors = useThemeColors();

  // Header Component
  const Header = () => (
    <View
      style={{
        backgroundColor: colors.primary,
        paddingHorizontal: 24,
        paddingTop: 48,
        paddingBottom: 32,
        flexDirection: 'row',
        alignItems: 'center',
      }}
    >
      <TouchableOpacity
        onPress={onGoBack}
        style={{
          position: 'absolute',
          top: 48,
          left: 24,
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          borderRadius: 20,
          padding: 12,
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10,
        }}
        activeOpacity={0.7}
        hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
      >
        <View pointerEvents="none">
          <ArrowLeft size={20} color="white" />
        </View>
      </TouchableOpacity>
      <View
        style={{
          flex: 1,
          alignItems: 'center',
        }}
      >
        <Text
          style={{
            color: 'white',
            fontSize: 24,
            fontWeight: '600',
          }}
        >
          {title}
        </Text>
        {subtitle && (
          <Text
            style={{
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: 14,
              marginTop: 4,
            }}
          >
            {subtitle}
          </Text>
        )}
      </View>
    </View>
  );

  // Loading State
  if (isLoading) {
    return (
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        <Header />
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <View
            style={{
              backgroundColor: colors.surface,
              padding: 32,
              borderRadius: 16,
              shadowColor: colors.text,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 5,
              alignItems: 'center',
            }}
          >
            <ActivityIndicator size="large" color={colors.primary} />
            <Text
              style={{
                color: colors.text,
                marginTop: 16,
                fontWeight: '600',
                fontSize: 18,
              }}
            >
              {loadingText}
            </Text>
            <Text
              style={{
                color: colors.textSecondary,
                marginTop: 4,
                fontSize: 14,
              }}
            >
              {loadingSubtext}
            </Text>
          </View>
        </View>
      </View>
    );
  }

  // Error State
  if (error) {
    return (
      <>
        <Header />
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 24,
          }}
        >
          <View
            style={{
              backgroundColor: colors.surface,
              padding: 32,
              borderRadius: 16,
              shadowColor: colors.text,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 5,
              alignItems: 'center',
              width: '100%',
            }}
          >
            <View
              style={{
                backgroundColor: colors.error + '20',
                padding: 24,
                borderRadius: 24,
                marginBottom: 16,
              }}
            >
              <Ionicons
                name="alert-circle-outline"
                size={48}
                color={colors.error}
              />
            </View>
            <Text
              style={{
                color: colors.error,
                textAlign: 'center',
                fontSize: 20,
                fontWeight: 'bold',
                marginBottom: 8,
              }}
            >
              {errorTitle}
            </Text>
            <Text
              style={{
                color: colors.textSecondary,
                textAlign: 'center',
                marginBottom: 24,
              }}
            >
              {error}
            </Text>
            <Button
              onPress={onGoBack}
              style={{
                width: '100%',
                paddingVertical: 16,
                borderRadius: 12,
                backgroundColor: colors.primary,
                shadowColor: colors.primary,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
              }}
            >
              <Text
                style={{
                  color: 'white',
                  fontWeight: 'bold',
                  textAlign: 'center',
                }}
              >
                Go Back
              </Text>
            </Button>
          </View>
        </View>
      </>
    );
  }

  // No Data State
  if (!hasData) {
    return (
      <>
        <Header />
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            paddingHorizontal: 24,
          }}
        >
          <View
            style={{
              backgroundColor: colors.surface,
              padding: 32,
              borderRadius: 16,
              shadowColor: colors.text,
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 5,
              alignItems: 'center',
              width: '100%',
            }}
          >
            <View
              style={{
                backgroundColor: colors.backgroundSecondary,
                padding: 24,
                borderRadius: 24,
                marginBottom: 16,
              }}
            >
              <Ionicons
                name="document-outline"
                size={48}
                color={colors.textSecondary}
              />
            </View>
            <Text
              style={{
                color: colors.text,
                fontWeight: 'bold',
                fontSize: 20,
                marginBottom: 8,
              }}
            >
              No Analysis Found
            </Text>
            <Text
              style={{
                color: colors.textSecondary,
                textAlign: 'center',
                marginBottom: 24,
              }}
            >
              {noDataText}
            </Text>
            <Button
              onPress={onGoBack}
              style={{
                width: '100%',
                paddingVertical: 16,
                borderRadius: 12,
                backgroundColor: colors.primary,
                shadowColor: colors.primary,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 5,
              }}
            >
              <Text
                style={{
                  color: 'white',
                  fontWeight: 'bold',
                  textAlign: 'center',
                }}
              >
                Go Back
              </Text>
            </Button>
          </View>
        </View>
      </>
    );
  }

  // If no states match, return null
  return null;
};

export default AnalysisStateManager;
