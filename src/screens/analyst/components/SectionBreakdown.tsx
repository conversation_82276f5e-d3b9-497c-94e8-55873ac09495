import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { sectionBreakdownStyles } from '@/styles/SectionBreakdown.styles';
import {
  getIcon,
  getIconContainerStyle,
} from '@/components/ui/SectionBreakdownUtils';
import { useThemeColors } from '@/hooks/useThemeColors';
interface StatusIconProps {
  status: 'good' | 'bad' | 'warning' | string;
}

interface SectionBreakdownProps {
  title: string;
  data: Array<{
    status: 'good' | 'bad' | string;
    items: string[];
  }>;
  sectionType: 'contact' | 'experience' | 'education' | 'skills' | string;
  progressPercent?: number;
}

const StatusIcon: React.FC<StatusIconProps> = ({ status }) => {
  switch (status) {
    case 'good':
      return <Ionicons name="checkmark-circle" size={16} color="#10B981" />;
    case 'bad':
      return <Ionicons name="close-circle" size={16} color="#EF4444" />;
    case 'warning':
      return <Ionicons name="warning" size={16} color="#f59e0b" />;
    default:
      return null;
  }
};

const SectionBreakdown: React.FC<SectionBreakdownProps> = ({
  title,
  data,
  sectionType,
  progressPercent = 70,
}) => {
  const colors = useThemeColors();
  // Filter out empty sections
  const filteredData = data.filter(
    (item) => item.items && item.items.length > 0,
  );

  // Don't render if no data
  if (filteredData.length === 0) {
    return null;
  }

  // Calculate score color based on percentage
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10B981'; // green
    if (score >= 60) return '#F59E0B'; // yellow
    if (score >= 40) return '#F97316'; // orange
    return '#EF4444'; // red
  };

  const scoreColor = getScoreColor(progressPercent);

  return (
    <View
      style={[
        sectionBreakdownStyles.sectionContainer,
        { marginBottom: 16, backgroundColor: colors.card },
      ]}
    >
      {/* Header */}
      <View style={sectionBreakdownStyles.headerContainer}>
        <View
          style={[
            sectionBreakdownStyles.iconContainer,
            getIconContainerStyle(sectionType),
          ]}
        >
          {getIcon(sectionType)}
        </View>
        <View className="flex-1">
          <Text
            style={[sectionBreakdownStyles.headerTitle, { color: colors.text }]}
          >
            {title}
          </Text>
          <Text className="text-sm mt-0.5" style={{ color: colors.textMuted }}>
            {filteredData.reduce((total, item) => total + item.items.length, 0)}{' '}
            points analyzed
          </Text>
        </View>
        <View className="items-end">
          <Text className="text-2xl font-bold" style={{ color: scoreColor }}>
            {progressPercent}%
          </Text>
          <Text className="text-base text-gray-500">Score</Text>
        </View>
      </View>

      {/* Progress Bar */}
      <View
        style={[
          sectionBreakdownStyles.progressBarContainer,
          { marginBottom: 16 },
        ]}
      >
        <View className="w-full bg-gray-200 rounded-full h-2">
          <View
            style={[
              {
                height: 8,
                borderRadius: 4,
                backgroundColor: scoreColor,
                width: `${progressPercent}%`,
              },
            ]}
          />
        </View>
      </View>

      {/* Content */}
      {filteredData.map((item, index) => (
        <View key={index} className="mb-4">
          {index > 0 && <View className="h-px bg-gray-200 mb-4" />}

          <View style={sectionBreakdownStyles.statusSection}>
            <View className="flex-row items-center mb-3">
              <StatusIcon status={item.status} />
              <Text
                style={[
                  sectionBreakdownStyles.statusLabel,
                  item.status === 'good'
                    ? sectionBreakdownStyles.goodStatusLabel
                    : sectionBreakdownStyles.badStatusLabel,
                  { marginLeft: 8 },
                ]}
              >
                {item.status === 'good' ? "What's Good" : 'Needs Improvement'}
              </Text>
              <View className="ml-auto bg-gray-100 px-2 py-1 rounded-full">
                <Text className="text-gray-600 text-xs font-medium">
                  {item.items.length}
                </Text>
              </View>
            </View>

            {item.items.map((subItem, subIndex) => (
              <View
                key={subIndex}
                style={[
                  sectionBreakdownStyles.itemContainer,
                  item.status === 'good'
                    ? sectionBreakdownStyles.goodItemContainer
                    : sectionBreakdownStyles.badItemContainer,
                  { marginBottom: 8, padding: 12, borderRadius: 8 },
                ]}
              >
                <View className="mr-3 mt-0.5">
                  <StatusIcon status={item.status} />
                </View>
                <Text
                  style={[
                    sectionBreakdownStyles.itemText,
                    item.status === 'good'
                      ? sectionBreakdownStyles.goodItemText
                      : sectionBreakdownStyles.badItemText,
                    { flex: 1, lineHeight: 20 },
                  ]}
                >
                  {subItem}
                </Text>
              </View>
            ))}
          </View>
        </View>
      ))}

      {/* Section Summary */}
      <View className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
        <Text className="font-semibold text-gray-900 text-base mb-1">
          {title} Summary
        </Text>
        <View className="flex-row justify-between items-center">
          <Text className="text-sm text-gray-600">
            {filteredData.find((d) => d.status === 'good')?.items.length || 0}{' '}
            strengths,{' '}
            {filteredData.find((d) => d.status === 'bad')?.items.length || 0}{' '}
            improvements needed
          </Text>
          <View className="flex-row items-center">
            <View
              className="w-2 h-2 rounded-full mr-1"
              style={{ backgroundColor: scoreColor }}
            />
            <Text className="text-lg font-medium" style={{ color: scoreColor }}>
              {progressPercent >= 80
                ? 'Excellent'
                : progressPercent >= 60
                  ? 'Good'
                  : progressPercent >= 40
                    ? 'Fair'
                    : 'Needs Work'}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default SectionBreakdown;
