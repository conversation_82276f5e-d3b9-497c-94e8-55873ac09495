import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Card, ProgressBar } from '@/components/ui';
import { AnalystContent } from '@/services/types/analyst';
import { DetailItem } from './DetailItem';
import { useThemeColors } from '@/hooks/useThemeColors';

interface OverallAnalysisProps {
  content: AnalystContent;
}

export const OverallAnalysis: React.FC<OverallAnalysisProps> = ({
  content,
}) => {
  const colors = useThemeColors();

  const getScoreColor = (score: number): string => {
    if (score >= 80) return colors.success;
    if (score >= 60) return colors.warning;
    return colors.error;
  };

  return (
    <Card
      style={{
        marginBottom: 24,
        borderRadius: 16,
        backgroundColor: colors.surface,
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.1,
        shadowRadius: 15,
        elevation: 10,
        borderWidth: 0,
      }}
    >
      <LinearGradient
        colors={[colors.warning + '20', colors.warning + '10']}
        style={{ padding: 24, borderTopLeftRadius: 16, borderTopRightRadius: 16 }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
          <View style={{
            backgroundColor: colors.warning + '30',
            padding: 12,
            borderRadius: 12,
            marginRight: 12
          }}>
            <Ionicons name="analytics" size={28} color={colors.warning} />
          </View>
          <View>
            <Text style={{
              color: colors.text,
              fontSize: 20,
              fontWeight: 'bold'
            }}>
              Overall Analysis
            </Text>
            <Text style={{
              color: colors.warning,
              fontSize: 14
            }}>
              Complete evaluation summary
            </Text>
          </View>
        </View>
      </LinearGradient>

      <View style={{ padding: 24 }}>
        {/* Overall Score */}
        <View style={{ marginBottom: 24 }}>
          <LinearGradient
            colors={[colors.success + '10', colors.success + '05']}
            style={{
              padding: 20,
              borderRadius: 12,
              borderWidth: 1,
              borderColor: colors.success + '20'
            }}
          >
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: 16
            }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  backgroundColor: colors.success + '20',
                  padding: 12,
                  borderRadius: 20,
                  marginRight: 12
                }}>
                  <Ionicons name="star" size={24} color={colors.success} />
                </View>
                <View>
                  <Text style={{
                    color: colors.text,
                    fontWeight: 'bold',
                    fontSize: 18
                  }}>
                    Overall Score
                  </Text>
                  <Text style={{
                    color: colors.success,
                    fontSize: 14
                  }}>
                    Performance rating
                  </Text>
                </View>
              </View>
              <View style={{ alignItems: 'center' }}>
                <Text
                  style={{
                    fontSize: 30,
                    fontWeight: 'bold',
                    color: getScoreColor(content.overall_score)
                  }}
                >
                  {content.overall_score}%
                </Text>
              </View>
            </View>
            <ProgressBar
              percentage={content.overall_score}
              color={colors.success}
            />
          </LinearGradient>
        </View>

        {/* Role Fit Score */}
        {content.fit_score && (
          <View style={{ marginBottom: 24 }}>
            <LinearGradient
              colors={[colors.accent + '10', colors.accent + '05']}
              style={{
                padding: 20,
                borderRadius: 12,
                borderWidth: 1,
                borderColor: colors.accent + '20'
              }}
            >
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 16
              }}>
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <View style={{
                    backgroundColor: colors.accent + '20',
                    padding: 12,
                    borderRadius: 20,
                    marginRight: 12
                  }}>
                    <Ionicons name="people" size={24} color={colors.accent} />
                  </View>
                  <View>
                    <Text style={{
                      color: colors.text,
                      fontWeight: 'bold',
                      fontSize: 18
                    }}>
                      Role Fit Score
                    </Text>
                    <Text style={{
                      color: colors.accent,
                      fontSize: 14
                    }}>
                      Position alignment
                    </Text>
                  </View>
                </View>
                <View style={{ alignItems: 'center' }}>
                  <Text
                    style={{
                      fontSize: 30,
                      fontWeight: 'bold',
                      color: getScoreColor(content.fit_score)
                    }}
                  >
                    {content.fit_score}%
                  </Text>
                </View>
              </View>
              <ProgressBar percentage={content.fit_score} color={colors.accent} />
            </LinearGradient>
          </View>
        )}

        {/* Feedback and Summary */}
        <View style={{ gap: 12 }}>
          <DetailItem
            icon="bulb"
            title="Overall Feedback"
            content={content.overall_feedback}
            color={colors.warning + '10'}
            iconColor={colors.warning}
          />
          <DetailItem
            icon="document-text"
            title="Summary Comment"
            content={content.summary_comment}
            color={colors.warning + '10'}
            iconColor={colors.warning}
          />

          {content.verdict && (
            <DetailItem
              icon="checkmark-done"
              title="Verdict"
              content={content.verdict}
              color={colors.primary + '10'}
              iconColor={colors.primary}
            />
          )}

          {content.matching_points && content.matching_points.length > 0 && (
            <DetailItem
              icon="checkmark-circle"
              title="Matching Points"
              content={content.matching_points}
              color={colors.success + '10'}
              iconColor={colors.success}
            />
          )}

          {content.missing_skills && content.missing_skills.length > 0 && (
            <DetailItem
              icon="alert-circle"
              title="Missing Skills"
              content={content.missing_skills}
              color={colors.error + '10'}
              iconColor={colors.error}
            />
          )}

          {content.missing_experience &&
            content.missing_experience.length > 0 && (
              <DetailItem
                icon="time"
                title="Missing Experience"
                content={content.missing_experience}
                color={colors.warning + '10'}
                iconColor={colors.warning}
              />
            )}

          {content.recommendations && content.recommendations.length > 0 && (
            <DetailItem
              icon="bulb"
              title="Recommendations"
              content={content.recommendations}
              color={colors.accent + '10'}
              iconColor={colors.accent}
            />
          )}
        </View>
      </View>
    </Card>
  );
};
