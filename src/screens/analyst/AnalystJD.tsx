import React, { useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import { useNavigation, useRoute } from '@react-navigation/native';

import DocumentCard from './components/DocumentCard';
import ScoreInfoCards from './components/ScoreInfoCards';
import SectionBreakdown from './components/SectionBreakdown';
import SkillAnalysis from './components/SkillCard';
import AnalysisStateManager from './components/AnalysisStateManager';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  selectAnalystError,
  selectAnalystLoading,
  selectCurrentAnalyst,
} from '@/redux/selectors/analystSelectors';
import { getAnalystById } from '@/redux/thunks/analystThunks';
import { BottomBanner } from '@/components/ui';
import { useThemeColors } from '@/hooks/useThemeColors';
interface RouteParams {
  analystId: string;
}

const ComparisonAnalysis = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useAppDispatch();
  const colors = useThemeColors();

  const { analystId } = route.params as RouteParams;
  const currentAnalyst = useAppSelector(selectCurrentAnalyst);
  const isLoading = useAppSelector(selectAnalystLoading);
  const error = useAppSelector(selectAnalystError);

  useEffect(() => {
    if (analystId) {
      dispatch(getAnalystById(analystId));
    }
  }, [analystId, dispatch]);

  // Handle loading, error, and no data states
  if (isLoading || error || !currentAnalyst) {
    return (
      <AnalysisStateManager
        isLoading={isLoading}
        error={error}
        hasData={!!currentAnalyst}
        onGoBack={() => navigation.goBack()}
        title="Comparison Analysis"
        subtitle="CV and Job Description matching insights"
        loadingText="Loading comparison analysis..."
        loadingSubtext="Analyzing CV and job description match"
        errorTitle="Failed to load comparison"
        noDataText="We couldn't find the comparison analysis data you're looking for."
      />
    );
  }

  const { content } = currentAnalyst;

  // Transform the API data to match the component structure
  const transformedSkills = [
    {
      name: 'Matching Points',
      items: content.matching_points || [],
      status: 'good' as const,
    },
    {
      name: 'Missing Skills',
      items: content.missing_skills || [],
      status: 'bad' as const,
    },
    {
      name: 'Missing Experience',
      items: content.missing_experience || [],
      status: 'warning' as const,
    },
  ];

  const transformSectionData = (section: any) => [
    {
      status: 'good',
      items: section.whats_good ?? [],
    },
    {
      status: 'bad',
      items: section.needs_improvement ?? [],
    },
  ];

  const formatDate = (dateString?: string) => {
    if (!dateString) return new Date().toLocaleDateString('en-GB');
    return new Date(dateString).toLocaleDateString('en-GB');
  };

  const handleStartImproving = () => {
    console.log('Start improving score pressed');
  };

  return (
    <>
      {/* Header */}
      <View className="bg-blue-500 px-6 pt-12 pb-8 flex-row items-center">
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="absolute top-12 left-6 bg-white/20 rounded-full p-3 items-center justify-center z-10"
          activeOpacity={0.7}
          hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
        >
          <View pointerEvents="none">
            <ArrowLeft size={20} color="white" />
          </View>
        </TouchableOpacity>
        <View className="flex-1 items-center">
          <Text className="text-white text-2xl font-semibold">
            Comparison Analysis
          </Text>
          <Text className="text-blue-100 text-sm mt-1">
            CV and Job Description matching insights
          </Text>
        </View>
      </View>

      <ScrollView
        className="flex-1"
        style={{ backgroundColor: colors.background }}
      >
        {/* Document Cards */}
        <View className="p-4 space-y-4">
          <DocumentCard
            type="resume"
            title="Resume"
            date={formatDate()}
            iconColor="bg-blue-500"
            borderColor="#93C5FD"
            textColor="#2563EB"
            fileUrl={currentAnalyst.cvFileUrl}
            fileName="Resume.pdf"
          />

          <DocumentCard
            type="job"
            title="Job Description"
            date={formatDate()}
            iconColor="bg-orange-500"
            borderColor="#FB923C"
            textColor="#EA580C"
            fileUrl={currentAnalyst.jdFileUrl}
            fileName="Job Description.pdf"
          />
        </View>

        {/* Score and Info Cards */}
        <ScoreInfoCards
          overallScore={content.overall_score}
          fitScore={content.fit_score}
          overallFeedback={content.overall_feedback}
          summaryComment={content.summary_comment}
          verdict={content.verdict}
        />

        {/* Skills Analysis */}
        <SkillAnalysis
          skills={transformedSkills}
          recommendations={content.recommendations || []}
        />

        {/* Section Breakdowns */}
        <View className="px-4">
          <SectionBreakdown
            title="Contact Info"
            data={transformSectionData(content.sections.contact_info)}
            sectionType="contact"
            progressPercent={content.sections.contact_info.score}
          />

          <SectionBreakdown
            title="Experience"
            data={transformSectionData(content.sections.experience)}
            sectionType="experience"
            progressPercent={content.sections.experience.score}
          />

          <SectionBreakdown
            title="Education"
            data={transformSectionData(content.sections.education)}
            sectionType="education"
            progressPercent={content.sections.education.score}
          />

          <SectionBreakdown
            title="Skills"
            data={transformSectionData(content.sections.skills)}
            sectionType="skills"
            progressPercent={content.sections.skills.score}
          />
        </View>

        {/* Bottom Banner */}
        <View className="px-4">
          <BottomBanner
            title="Comparison Analysis Complete!"
            subtitle="Start improving your match score today!"
            buttonText="Start Improving Your Score"
            onButtonPress={handleStartImproving}
            iconName="analytics"
            gradientColors={['#FE7743', '#FB923C', '#F0A04B']}
          />
        </View>
      </ScrollView>
    </>
  );
};

export default ComparisonAnalysis;
