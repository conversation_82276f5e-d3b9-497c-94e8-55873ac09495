import { z } from 'zod';

export const uploadSchema = z.object({
  cvFile: z
    .any()
    .refine(
      (file) => file !== null && file?.assets && file.assets.length > 0,
      'Please upload a CV file',
    )
    .refine((file) => {
      if (!file?.assets || file.assets.length === 0) return true;
      return file.assets[0].size <= 10 * 1024 * 1024;
    }, 'CV file size must be less than 10MB'),
  jdFile: z
    .any()
    .refine(
      (file) => file !== null && file?.assets && file.assets.length > 0,
      'Please upload a JD file',
    )
    .refine((file) => {
      if (!file?.assets || file.assets.length === 0) return true;
      return file.assets[0].size <= 10 * 1024 * 1024;
    }, 'JD file size must be less than 10MB'),
});

export type UploadFormData = z.infer<typeof uploadSchema>;
