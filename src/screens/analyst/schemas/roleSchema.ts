import { z } from 'zod';

export const roleSchema = z.object({
  description: z.string().min(1, 'Please enter a job description'),
  cvFile: z
    .any()
    .refine((file) => {
      return file !== null && file?.assets && file.assets.length > 0;
    }, 'Please upload a CV file')
    .refine((file) => {
      if (!file?.assets || file.assets.length === 0) return true;
      return file.assets[0].size <= 10 * 1024 * 1024;
    }, 'File size must be less than 10MB'),
});

export type FormData = z.infer<typeof roleSchema>;
