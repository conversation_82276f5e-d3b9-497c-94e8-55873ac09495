import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Alert } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import { useDispatch, useSelector } from 'react-redux';
import * as DocumentPicker from 'expo-document-picker';
import { AppDispatch } from '@/redux/store';
import { selectAnalystLoading } from '@/redux/selectors/analystSelectors';
import { showAuthToast } from '@/utils/toastUtils';
import { createAnalystByRole } from '@/redux/thunks/analystThunks';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormData, roleSchema } from './schemas/roleSchema';
import { GradientHeader } from '@/components/ui/GradientHeader';
import { ActionButtons } from '@/components/ui/ActionButtons';
import { useFileUpload } from '@/hooks/useFileUpload';
import { FileUploadCard } from './components/FileUploadCard';
import { useThemeColors } from '@/hooks/useThemeColors';

export default function UploadCVRole() {
  const navigation =
    useNavigation<NativeStackNavigationProp<AnalysisStackParamList>>();
  const dispatch = useDispatch<AppDispatch>();
  const loading = useSelector(selectAnalystLoading);
  const colors = useThemeColors();

  const { uploadFile, formatFileSize } = useFileUpload({
    successMessage: 'File selected successfully',
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(roleSchema),
    defaultValues: {
      description: '',
      cvFile: null,
    },
  });

  const [selectedFile, setSelectedFile] =
    useState<DocumentPicker.DocumentPickerResult | null>(null);

  useFocusEffect(
    React.useCallback(() => {
      reset({
        description: '',
        cvFile: null,
      });
      setSelectedFile(null);
    }, [reset]),
  );

  useEffect(() => {
    setValue('cvFile', selectedFile);
  }, [selectedFile, setValue]);

  const handleFileUpload = async () => {
    const result = await uploadFile();
    if (result) {
      setSelectedFile(result);
    }
  };

  const handleCancel = () => {
    navigation.goBack();
  };

  const onSubmit = async (data: FormData) => {
    if (!data.cvFile?.assets?.length) {
      Alert.alert('Error', 'Please select a CV file');
      return;
    }

    try {
      const file = data.cvFile.assets[0];
      const cvFile = {
        uri: file.uri,
        type: file.mimeType ?? 'application/pdf',
        name: file.name ?? 'cv.pdf',
      };
      const credentials = {
        agentType: 'AI_RESUME_ANALYSIS_ROLE' as const,
        roleDescription: data.description.trim(),
        cvFile: cvFile as any,
      };

      const result = await dispatch(createAnalystByRole(credentials)).unwrap();
      showAuthToast.success('CV analysis successful');

      navigation.navigate('AnalystRole', {
        analysisId: result.data._id,
      });
    } catch (error: any) {
      console.error('Analysis error:', error);
      showAuthToast.error(error?.message ?? 'Unable to analyze CV');
    }
  };

  return (
    <>
      <GradientHeader
        title="Interview Based on CV"
        onBackPress={() => navigation.goBack()}
      />

      <View
        className="flex-1 px-4 py-6"
        style={{
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          marginTop: -20,
          backgroundColor: colors.background,
        }}
      >
        <View className="mb-8">
          <Text
            className="text-2xl font-bold mb-2"
            style={{ color: colors.text }}
          >
            Upload Your CV
          </Text>
          <Text
            className="text-gray-500 text-lg leading-5 mt-1"
            style={{ color: colors.textMuted }}
          >
            Enter information or upload your CV to check suitability for the
            desired position
          </Text>
        </View>

        <View className="mb-6">
          <FileUploadCard
            selectedFile={selectedFile}
            onUpload={handleFileUpload}
            formatFileSize={formatFileSize}
            disabled={loading}
          />
          {errors.cvFile && (
            <Text className="text-red-500 mt-2">
              {(errors.cvFile as any)?.message ?? 'Please upload a CV file'}
            </Text>
          )}
        </View>

        <View className="mb-8">
          <Text
            className="text-gray-900 font-semibold mb-3"
            style={{ color: colors.text }}
          >
            Desired Position
          </Text>
          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="Enter your position description (e.g., Frontend Developer, Backend Engineer, Data Scientist...)"
                className="bg-white border border-gray-200 rounded-lg px-4 py-3 text-gray-900 min-h-[100px]"
                style={{
                  backgroundColor: colors.card,
                  color: colors.text,
                }}
                multiline
                textAlignVertical="top"
                placeholderTextColor="#9CA3AF"
                editable={!loading}
              />
            )}
          />
          {errors.description && (
            <Text className="text-red-500 mt-1">
              {errors.description.message}
            </Text>
          )}
        </View>

        <ActionButtons
          onCancel={handleCancel}
          onSubmit={handleSubmit(onSubmit)}
          isLoading={loading}
          loadingText="Analyzing..."
        />
      </View>
    </>
  );
}
