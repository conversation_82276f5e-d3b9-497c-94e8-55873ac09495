import React, { useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { ArrowLeft } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AnalysisStackParamList } from '@/common/types/rootParamList';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch } from '@/redux/store';
import {
  selectAnalystLoading,
  selectCurrentAnalyst,
  selectAnalystError,
} from '@/redux/selectors/analystSelectors';
import { getAnalystById } from '@/redux/thunks/analystThunks';
import { OverallAnalysis } from './components/OverallAnalysis';
import { ScoreCard } from './components/ScoreCardRole';
import { ResumeFileCard } from './components/ResumeFileCard';
import AnalysisStateManager from './components/AnalysisStateManager';
import { BottomBanner } from '@/components/ui';
import { useThemeColors } from '@/hooks/useThemeColors';

const ResumeAnalysis = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AnalysisStackParamList>>();
  const route = useRoute();
  const dispatch = useDispatch<AppDispatch>();
  const currentAnalyst = useSelector(selectCurrentAnalyst);
  const loading = useSelector(selectAnalystLoading);
  const error = useSelector(selectAnalystError);
  const colors = useThemeColors();

  const analysisId = (route.params as any)?.analysisId;

  useEffect(() => {
    if (analysisId) {
      dispatch(getAnalystById(analysisId));
    }
  }, [analysisId, dispatch]);

  // Handle loading, error, and no data states
  if (loading || error || !currentAnalyst) {
    return (
      <AnalysisStateManager
        isLoading={loading}
        error={error}
        hasData={!!currentAnalyst}
        onGoBack={() => navigation.goBack()}
        title="Resume Analysis"
        subtitle="Comprehensive CV evaluation and insights"
        loadingText="Loading resume analysis..."
        loadingSubtext="Analyzing your CV for detailed insights"
        errorTitle="Failed to load resume analysis"
        noDataText="We couldn't find the resume analysis data you're looking for."
      />
    );
  }

  const { content } = currentAnalyst;

  const handleStartInterviewPrep = () => {
    console.log('Start interview preparation pressed');
  };

  return (
    <>
      <ScrollView
        style={{
          flex: 1,
          backgroundColor: colors.background,
        }}
      >
        <LinearGradient
          colors={['#836FFF', '#3B82F6', '#836FFF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{ paddingHorizontal: 24, paddingTop: 48, paddingBottom: 32 }}
        >
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              className="absolute top-1 left-1 bg-white/20 rounded-full p-3 items-center justify-center z-10"
              activeOpacity={0.7}
              hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
            >
              <View pointerEvents="none">
                <ArrowLeft size={20} color="white" />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center">
              <Text className="text-white text-2xl font-bold">
                Resume Analysis
              </Text>
              <Text className="text-blue-100 text-sm mt-1">
                Comprehensive CV evaluation and insights
              </Text>
            </View>
          </View>
        </LinearGradient>
        {/* Resume File Info & PDF Preview */}
        <View className="px-4">
          <ResumeFileCard cvFileUrl={currentAnalyst?.cvFileUrl} />

          {/* Overall Analysis Component */}
          <OverallAnalysis content={content} />

          {/* Score Cards */}
          <ScoreCard
            title="Contact Information"
            score={content.sections.contact_info.score}
            color={colors.success}
            iconName="person"
            iconColor={colors.success}
            section={content.sections.contact_info}
          />

          <ScoreCard
            title="Work Experience"
            score={content.sections.experience.score}
            color={colors.primary}
            iconName="briefcase"
            iconColor={colors.primary}
            section={content.sections.experience}
          />

          <ScoreCard
            title="Education"
            score={content.sections.education.score}
            color={colors.accent}
            iconName="school"
            iconColor={colors.accent}
            section={content.sections.education}
          />

          <ScoreCard
            title="Skills"
            score={content.sections.skills.score}
            color={colors.error}
            iconName="code-slash"
            iconColor={colors.error}
            section={content.sections.skills}
          />

          {/* Enhanced Bottom Banner */}
          <BottomBanner
            title="Ready for the Next Step?"
            subtitle="Get prepared for your next interview"
            buttonText="Start Interview Preparation"
            onButtonPress={handleStartInterviewPrep}
            iconName="sparkles"
            gradientColors={['#B771E5', '#3B82F6', '#60A5FA']}
          />
        </View>
      </ScrollView>
    </>
  );
};

export default ResumeAnalysis;
