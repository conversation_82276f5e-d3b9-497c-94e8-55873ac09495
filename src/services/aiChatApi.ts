import { ChatSession } from '@/common/types/ai-chat.d';
import { API_ROUTES } from '@/common/constants/router';
import { apiRequest } from './apiBase';

export const aiChatApi = {
  getChatSessions: async () => {
    const response = await apiRequest.get(API_ROUTES.AI_CHAT.SESSIONS);
    if (response.data?.statusCode === 200) {
      return response.data;
    } else {
      throw response.data;
    }
  },

  getChatSessionById: async (sessionId: string) => {
    const response = await apiRequest.get(
      API_ROUTES.AI_CHAT.SESSION_BY_ID(sessionId),
    );
    if (response.data?.statusCode === 200) {
      return response.data;
    } else {
      throw response.data;
    }
  },
  sendMessage: async (sessionId: string, message: string) => {
    try {
      const response = await apiRequest.post(
        API_ROUTES.AI_CHAT.SEND_MESSAGE(sessionId),
        { message: message },
      );

      if (response.data?.statusCode === 200) {
        return response.data;
      } else {
        throw response.data;
      }
    } catch (error) {
      throw error;
    }
  },
  deleteChatSession: async (sessionId: string) => {
    const response = await apiRequest.delete(
      API_ROUTES.AI_CHAT.DELETE_SESSION(sessionId),
    );
    if (response.data?.statusCode === 200) {
      return response.data;
    } else {
      throw response.data;
    }
  },
};
