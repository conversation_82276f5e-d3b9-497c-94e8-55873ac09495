import axios, {
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
} from 'axios';
import { ApiResponse, ApiErrorResponse } from '@/common/types/api';
import { API_CONFIG, API_ROUTES } from '@/common/constants/router';
import { tokenService } from '@/services/tokenService';
import { AlertService } from '@/services/alertService';

// Callback function to handle authentication state changes
let authStateChangeCallback: (() => void) | null = null;

export const setAuthStateChangeCallback = (callback: () => void) => {
  authStateChangeCallback = callback;
};

// Biến để theo dõi trạng thái refresh token
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// axios instance
export const apiClient = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

// Request interceptor - thêm token vào header
apiClient.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    try {
      const token = await tokenService.getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.log('Error getting token:', error);
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  },
);

// Response interceptor - xử lý refresh token tự động
apiClient.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse<ApiResponse> => {
    return response;
  },
  async (error: AxiosError): Promise<any> => {
    const originalRequest = error.config as InternalAxiosRequestConfig & {
      _retry?: boolean;
    };

    // Xử lý lỗi chung - tạo error response serializable
    const errorData = error.response?.data as any;
    const errorResponse: ApiErrorResponse = {
      statusCode: error.response?.status ?? 500,
      message: errorData?.message ?? error.message ?? 'Network Error',
      error: errorData?.error ?? 'UNKNOWN_ERROR',
      timestamp: new Date().toISOString(),
      errorCode: errorData?.errorCode,
    };

    // Nếu lỗi 401 và chưa retry, thử refresh token
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Không refresh token cho endpoint refresh-token để tránh loop
      if (originalRequest.url?.includes(API_ROUTES.AUTH.REFRESH_TOKEN)) {
        await tokenService.clearTokens();

        // Show session expired notification
        AlertService.showSessionExpired();

        // Trigger authentication state change to redirect to login
        if (authStateChangeCallback) {
          authStateChangeCallback();
        }

        // Trả về error serializable thay vì reject với AxiosError
        throw errorResponse;
      }

      originalRequest._retry = true;

      if (isRefreshing) {
        // Nếu đang refresh, đợi trong queue
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            if (originalRequest.headers) {
              originalRequest.headers.Authorization = `Bearer ${token}`;
            }
            return apiClient(originalRequest);
          })
          .catch((err) => {
            // Đảm bảo error được reject là serializable
            throw err instanceof Error
              ? {
                  statusCode: 500,
                  message: err.message,
                  error: 'REFRESH_QUEUE_ERROR',
                  timestamp: new Date().toISOString(),
                }
              : err;
          });
      }

      isRefreshing = true;

      try {
        const refreshToken = await tokenService.getRefreshToken();

        if (!refreshToken) {
          processQueue(errorResponse, null);
          await tokenService.clearTokens();

          // Show session expired notification
          AlertService.showSessionExpired();

          // Trigger authentication state change to redirect to login
          if (authStateChangeCallback) {
            authStateChangeCallback();
          }

          throw errorResponse;
        }

        // Gọi API refresh token
        const refreshResponse = await axios.post(
          `${API_CONFIG.BASE_URL}${API_ROUTES.AUTH.REFRESH_TOKEN}`,
          { refreshToken },
          {
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          },
        );
        console.log('refreshResponse', refreshResponse.data);
        if (refreshResponse.data?.statusCode === 200) {
          const { accessToken, refreshToken: newRefreshToken } =
            refreshResponse.data.data;

          // Lưu token mới
          await tokenService.saveTokens(accessToken, newRefreshToken);

          // Cập nhật header cho request gốc
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          }

          processQueue(null, accessToken);

          // Thực hiện lại request gốc
          return apiClient(originalRequest);
        } else {
          throw new Error('Refresh token failed');
        }
      } catch (refreshError) {
        console.log('Refresh token failed:', refreshError);
        processQueue(refreshError, null);
        await tokenService.clearTokens();

        // Show session expired notification
        AlertService.showSessionExpired();

        // Trigger authentication state change to redirect to login
        if (authStateChangeCallback) {
          authStateChangeCallback();
        }

        throw errorResponse;
      } finally {
        isRefreshing = false;
      }
    }

    // Các lỗi khác
    if (error.response?.status === 401) {
      await tokenService.clearTokens();

      // Show session expired notification
      AlertService.showSessionExpired();

      // Trigger authentication state change to redirect to login
      if (authStateChangeCallback) {
        authStateChangeCallback();
      }
    }

    // Luôn throw error serializable thay vì AxiosError
    throw errorResponse;
  },
);

export const apiRequest = {
  get: <T = any>(url: string, config = {}) =>
    apiClient.get<ApiResponse<T>>(url, config),

  post: <T = any>(url: string, data = {}, config = {}) =>
    apiClient.post<ApiResponse<T>>(url, data, config),

  put: <T = any>(url: string, data = {}, config = {}) =>
    apiClient.put<ApiResponse<T>>(url, data, config),

  patch: <T = any>(url: string, data = {}, config = {}) =>
    apiClient.patch<ApiResponse<T>>(url, data, config),

  delete: <T = any>(url: string, config = {}) =>
    apiClient.delete<ApiResponse<T>>(url, config),
};

export default apiClient;
