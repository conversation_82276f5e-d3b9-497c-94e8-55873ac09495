import { apiClient } from './apiBase';
import { 
  ResponseRoadmapAiDto, 
  CreateRoadmapByRoleRequest, 
  CreateRoadmapByJdRequest,
  ApiResponse
} from '@/common/types/career-roadmap';

export const careerRoadmapApi = {
  getMyRoadmaps: async (): Promise<ResponseRoadmapAiDto[]> => {
    try {
      const response = await apiClient.get<ApiResponse<ResponseRoadmapAiDto[]>>('/roadmap-ai/my-roadmap-ai');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching my roadmaps:', error);
      throw error;
    }
  },

  getRoadmapById: async (id: string): Promise<ResponseRoadmapAiDto> => {
    try {
      const response = await apiClient.get<ApiResponse<ResponseRoadmapAiDto>>(`/roadmap-ai/roadmap-ai/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching roadmap by id:', error);
      throw error;
    }
  },
  createRoadmapByRole: async (data: CreateRoadmapByRoleRequest): Promise<ResponseRoadmapAiDto> => {
    try {
      const formData = new FormData();
      formData.append('roleDescription', data.roleDescription);
      
      if (data.cvFile) {
        formData.append('cvFile', {
          uri: data.cvFile.uri,
          type: data.cvFile.mimeType || 'application/pdf',
          name: data.cvFile.name || 'cv.pdf',
        } as any);
      }
      
      formData.append('agentType', 'AI_ROADMAP_AI_ROLE');

      console.log('Sending formData for role:', {
        roleDescription: data.roleDescription,
        cvFile: data.cvFile,
      });

      const response = await apiClient.post<ApiResponse<ResponseRoadmapAiDto>>(
        '/roadmap-ai/roadmap-ai-by-role-description',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data.data;    } catch (error: any) {
      console.error('Error creating roadmap by role:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    }
  },
  createRoadmapByJd: async (data: CreateRoadmapByJdRequest): Promise<ResponseRoadmapAiDto> => {
    try {
      const formData = new FormData();
      
      if (data.cvFile) {
        formData.append('cvFile', {
          uri: data.cvFile.uri,
          type: data.cvFile.mimeType || 'application/pdf',
          name: data.cvFile.name || 'cv.pdf',
        } as any);
      }
      
      if (data.jdFile) {
        formData.append('jdFile', {
          uri: data.jdFile.uri,
          type: data.jdFile.mimeType || 'application/pdf',
          name: data.jdFile.name || 'jd.pdf',
        } as any);
      }
      
      formData.append('agentType', 'AI_ROADMAP_AI_JD');

      console.log('Sending formData for JD:', {
        cvFile: data.cvFile,
        jdFile: data.jdFile,
      });

      const response = await apiClient.post<ApiResponse<ResponseRoadmapAiDto>>(
        '/roadmap-ai/roadmap-ai-by-jd-description',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data.data;
    } catch (error: any) {
      console.error('Error creating roadmap by JD:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      throw error;
    }
  },

  deleteRoadmap: async (id: string): Promise<void> => {
    try {
      await apiClient.delete(`/roadmap-ai/${id}`);
    } catch (error) {
      console.error('Error deleting roadmap:', error);
      throw error;
    }
  },

  getAllRoadmaps: async (): Promise<ResponseRoadmapAiDto[]> => {
    try {
      const response = await apiClient.get<ApiResponse<ResponseRoadmapAiDto[]>>('/roadmap-ai/all-roadmap-ai');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching all roadmaps:', error);
      throw error;
    }
  },
};
