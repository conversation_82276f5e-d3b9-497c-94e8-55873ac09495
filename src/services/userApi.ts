import { API_ROUTES } from '@/common/constants/router';
import { apiRequest, apiClient } from './apiBase';
import { userService } from './tokenService';
import { isApiSuccess } from '@/common/types/api';
import { User } from '@/redux/types/userType';

export interface UpdateProfileRequest {
  firstName: string;
  lastName: string;
  phone?: string;
  address?: string;
  bio?: string;
  dateOfBirth?: string;
}

export interface UploadAvatarResponse {
  avatarUrl: string;
}

export const userApi = {
  getProfile: async () => {
    let userProfile = await userService.getUserProfile();
    if (userProfile && Object.keys(userProfile).length > 0) {
      return userProfile as User;
    }
    const response = await apiRequest.get(API_ROUTES.USER.PROFILE);
    if (isApiSuccess(response.data)) {
      await userService.saveUserProfile(response.data.data);
      return response.data.data as User;
    } else {
      throw response.data;
    }
  },
  updateProfile: async (profileData: UpdateProfileRequest) => {
    const response = await apiRequest.put(
      API_ROUTES.USER.UPDATE_PROFILE,
      profileData,
    );
    if (isApiSuccess(response.data)) {
      const userProfile = await userService.getUserProfile();
      await userService.saveUserProfile({
        ...userProfile,
        ...response.data.data,
      });
    }
    console.log('Profile updated with data:', profileData);

    return response.data;
  },

  uploadAvatar: async (imageUri: string) => {
    console.log('Uploading avatar with URI:', imageUri);
    const formData = new FormData();

    // Create file object for React Native
    const file = {
      uri: imageUri,
      type: 'image/png',
      name: 'avatar.png',
    } as any;

    formData.append('file', file);
    console.log('FormData prepared, making API call...');

    const response = await apiClient.post<{ data: UploadAvatarResponse }>(
      API_ROUTES.USER.UPLOAD_AVATAR,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    console.log(123123123, response);

    if (isApiSuccess(response as any)) {
      console.log(123123, response.data);
      const userProfile = await userService.getUserProfile();
      await userService.saveUserProfile({
        ...userProfile,
        avatar: response.data.data.avatarUrl,
      });
    }

    console.log('Avatar upload response:', response.data);
    return response.data;
  },
};
