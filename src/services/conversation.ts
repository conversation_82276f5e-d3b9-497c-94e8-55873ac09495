import { API_CONFIG, API_ROUTES } from '@/common/constants/router';
import { apiRequest } from './apiBase';
import { ApiResponse } from '@/common/types/api';
import {
  ConversationRequest,
  ConversationResponse,
} from './types/conversation';
import { tokenService } from './tokenService';

export const conversationApi = {
  getMyConversations: async (): Promise<
    ApiResponse<ConversationResponse[]>
  > => {
    const response = await apiRequest.get(
      API_ROUTES.CONVERSATION.GET_MY_CONVERSATION,
    );

    const apiResponse = response.data as ApiResponse<ConversationResponse[]>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
  getConversationById: async (
    conversationId: string,
  ): Promise<ApiResponse<ConversationResponse>> => {
    const response = await apiRequest.get(
      API_ROUTES.CONVERSATION.GET_CONVERSATION_BY_ID(conversationId),
    );

    const apiResponse = response.data as ApiResponse<ConversationResponse>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
  deleteConversation: async (conversationId: string): Promise<ApiResponse> => {
    const response = await apiRequest.delete(
      API_ROUTES.CONVERSATION.DELETE_CONVERSATION(conversationId),
    );

    const apiResponse = response.data as ApiResponse<void>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
  createConversation: async (
    body: ConversationRequest,
  ): Promise<ApiResponse<ConversationResponse>> => {
    const response = await apiRequest.post(
      API_ROUTES.CONVERSATION.CREATE_CONVERSATION,
      body,
    );

    const apiResponse = response.data as ApiResponse<ConversationResponse>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
};
