export interface AnalystRoleCredentials {
  agentType: 'AI_RESUME_ANALYSIS_ROLE';
  roleDescription: string;
  cvFile: File;
}

export interface AnalystJDCredentials {
  agentType: 'AI_RESUME_ANALYSIS_JD';
  cvFile: File;
  jdFile: File;
}
export interface AnalystContent {
  overall_score: number;
  fit_score?: number;
  overall_feedback: string;
  summary_comment: string;
  verdict?: string;
  matching_points?: string[];
  missing_skills?: string[];
  missing_experience?: string[];
  recommendations?: string[];
  sections: {
    contact_info: AnalystSection;
    experience: AnalystSection;
    education: AnalystSection;
    skills: AnalystSection;
  };
}

export interface AnalystSection {
  score: number;
  comment: string;
  tips_for_improvement: string[];
  whats_good: string[];
  needs_improvement: string[];
}

export interface AnalystResponse {
  _id: string;
  userId: string;
  content: AnalystContent;
  agentType: 'AI_RESUME_ANALYSIS_ROLE' | 'AI_RESUME_ANALYSIS_JD';
  cvFileUrl: string;
  roleDescription?: string;
  jdFileUrl?: string;
  cvText?: string;
}

export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data: T;
}
