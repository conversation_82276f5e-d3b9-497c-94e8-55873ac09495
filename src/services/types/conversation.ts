export interface Conversation {
  role: string;
  content: string;
}

export interface PerQuestionFeedback {
  question: string;
  userAnswer: string;
  feedback: string;
  rating: number;
}

export interface SkillRating {
  technicalSkills: number;
  communication: number;
  problemSolving: number;
  experience: number;
}

export interface Summary {
  overallFeedback: string;
  skillsRating: SkillRating;
  recommendationLevel: string;
  recommendationMsg: string;
}

export interface Feedback {
  perQuestionFeedback: PerQuestionFeedback[];
  summary: Summary;
}

export interface ConversationResponse {
  _id: string;
  userId: string;
  interviewId: string;
  conversationLog: Conversation[];
  feedback: Feedback;
  interviewDuration: string;
  createdAt: string;
}

export interface ConversationRequest {
  interviewId: string;
  conversationLog: Conversation[];
  interviewDuration: string;
}
