import { InterviewTypeEnum } from '@/common/enums/interviewQuestion.enum';
import { ConversationResponse } from './conversation';

export interface InterviewQuestionSessionResponse {
  _id: string;
  userId: string;
  jobPosition: string;
  interviewDuration: string;
  interviewType: InterviewTypeEnum[];
  jobDescriptions: string;
  totalQuestion: number;
  interviewQuestions: InterviewQuestion[];
  conversations: ConversationResponse[];
  jdFileUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface InterviewQuestion {
  question: string;
  type: InterviewTypeEnum;
  estimated_time_minutes: number;
  tip: string;
}
