import { API_ROUTES } from '@/common/constants/router';
import { ApiResponse } from '@/common/types/api';
import { apiRequest } from './apiBase';
import { InterviewQuestionSessionResponse } from './types/interview';
import { tokenService } from './tokenService';
import { API_CONFIG } from '@/common/constants/router';

export const interviewQuestionApi = {
  getMyInterviewQuestion: async (): Promise<
    ApiResponse<InterviewQuestionSessionResponse[]>
  > => {
    const response = await apiRequest.get(
      API_ROUTES.INTERVIEW_QUESTION.GET_MY_INTERVIEW_QUESTION,
    );

    const apiResponse = response.data as ApiResponse<
      InterviewQuestionSessionResponse[]
    >;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
  getInterviewQuestionById: async (
    interviewId: string,
  ): Promise<ApiResponse<InterviewQuestionSessionResponse>> => {
    const response = await apiRequest.get(
      API_ROUTES.INTERVIEW_QUESTION.GET_INTERVIEW_QUESTION_BY_ID.replace(
        ':id',
        interviewId,
      ),
    );

    const apiResponse =
      response.data as ApiResponse<InterviewQuestionSessionResponse>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
  createInterviewQuestion: async (
    formData: FormData,
  ): Promise<ApiResponse<InterviewQuestionSessionResponse>> => {
    const response = await apiRequest.post(
      API_ROUTES.INTERVIEW_QUESTION.CREATE,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    const result =
      response.data as ApiResponse<InterviewQuestionSessionResponse>;

    if (result?.statusCode === 201 || result?.statusCode === 200) {
      return result;
    } else {
      throw result;
    }
  },
  deleteInterviewQuestion: async (
    interviewId: string,
  ): Promise<ApiResponse<void>> => {
    const response = await apiRequest.delete(
      API_ROUTES.INTERVIEW_QUESTION.DELETE.replace(':id', interviewId),
    );

    const apiResponse = response.data as ApiResponse<void>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
};
