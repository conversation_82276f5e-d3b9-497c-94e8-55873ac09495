import { API_ROUTES } from '@/common/constants/router';
import { apiRequest } from './apiBase';
import {
  AnalystJDCredentials,
  AnalystResponse,
  AnalystRoleCredentials,
  ApiResponse,
} from './types/analyst';

export const analystApi = {
  createAnalystByRole: async (
    credentials: AnalystRoleCredentials,
  ): Promise<ApiResponse<AnalystResponse>> => {
    const formData = new FormData();
    formData.append('agentType', credentials.agentType);
    formData.append('roleDescription', credentials.roleDescription);
    formData.append('cvFile', credentials.cvFile as any);

    const response = await apiRequest.post(
      API_ROUTES.ANALYST.ANALYST_ROLE,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000,
      },
    );

    const apiResponse = response.data as ApiResponse<AnalystResponse>;

    if (apiResponse?.statusCode === 201 || apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },

  createAnalystByJD: async (
    credentials: AnalystJDCredentials,
  ): Promise<ApiResponse<AnalystResponse>> => {
    const formData = new FormData();
    formData.append('agentType', credentials.agentType);
    formData.append('cvFile', credentials.cvFile);
    formData.append('jdFile', credentials.jdFile);

    const response = await apiRequest.post(
      API_ROUTES.ANALYST.ANALYST_JD,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 60000,
      },
    );

    const apiResponse = response.data as ApiResponse<AnalystResponse>;

    if (apiResponse?.statusCode === 201 || apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },

  getAnalystById: async (id: string): Promise<ApiResponse<AnalystResponse>> => {
    const response = await apiRequest.get(
      API_ROUTES.ANALYST.ANALYST_BY_ID.replace(':id', id),
    );

    const apiResponse = response.data as ApiResponse<AnalystResponse>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },

  getMyAnalysts: async (): Promise<ApiResponse<AnalystResponse[]>> => {
    const response = await apiRequest.get(API_ROUTES.ANALYST.MY_ANALYST);

    const apiResponse = response.data as ApiResponse<AnalystResponse[]>;

    if (apiResponse?.statusCode === 200) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },

  deleteAnalyst: async (id: string): Promise<ApiResponse<void>> => {
    const response = await apiRequest.delete(
      API_ROUTES.ANALYST.DELETE_ANALYST.replace(':id', id),
    );

    const apiResponse = response.data as ApiResponse<void>;

    if (apiResponse?.statusCode === 200 || apiResponse?.statusCode === 204) {
      return apiResponse;
    } else {
      throw apiResponse;
    }
  },
};
