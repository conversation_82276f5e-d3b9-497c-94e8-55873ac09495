import { Alert, Platform, ToastAndroid } from 'react-native';

export const AlertService = {
  showSessionExpired: () => {
    if (Platform.OS === 'android') {
      ToastAndroid.show('Bạn đã hết phiên đăng nhập', ToastAndroid.LONG);
    } else {
      Alert.alert(
        'Phiên đăng nhập hết hạn',
        'Bạn đã hết phiên đăng nhập. Vui lòng đăng nhập lại.',
        [
          {
            text: 'OK',
            style: 'default',
          },
        ],
        { cancelable: false },
      );
    }
  },

  showError: (title: string, message: string) => {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.LONG);
    } else {
      Alert.alert(title, message, [{ text: 'OK', style: 'default' }]);
    }
  },

  showSuccess: (message: string) => {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    } else {
      Alert.alert('Thành công', message, [{ text: 'OK', style: 'default' }]);
    }
  },
};

export default AlertService;
