// src/services/navigationService.ts
import { createNavigationContainerRef } from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

export const NavigationService = {
  navigate: (name: string, params?: any) => {
    if (navigationRef.isReady()) {
      (navigationRef as any).navigate(name, params);
    }
  },

  reset: (routeName: string) => {
    if (navigationRef.isReady()) {
      try {
        console.log('NavigationService: Attempting to reset to:', routeName);
        navigationRef.reset({
          index: 0,
          routes: [{ name: routeName as never }],
        });
      } catch (error) {
        console.error('NavigationService: Reset failed:', error);
        // Fallback: try to navigate instead of reset
        try {
          navigationRef.navigate(routeName as never);
        } catch (navigateError) {
          console.error(
            'NavigationService: Navigate fallback also failed:',
            navigateError,
          );
        }
      }
    } else {
      console.warn(
        'NavigationService: Navigation not ready for reset to:',
        routeName,
      );
    }
  },

  goBack: () => {
    if (navigationRef.isReady()) {
      navigationRef.goBack();
    }
  },

  getCurrentRoute: () => {
    if (navigationRef.isReady()) {
      return navigationRef.getCurrentRoute();
    }
    return null;
  },
};

export default NavigationService;
