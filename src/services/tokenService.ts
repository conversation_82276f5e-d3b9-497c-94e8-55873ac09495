import * as SecureStore from 'expo-secure-store';
import { APP_KEYS, TOKEN_KEYS, USER_KEYS } from '@/common/constants/router';
import { User } from '@/redux/types/userType';

export const tokenService = {
  saveTokens: async (
    accessToken: string,
    refreshToken: string,
  ): Promise<void> => {
    await SecureStore.setItemAsync(TOKEN_KEYS.ACCESS_TOKEN, accessToken);
    await SecureStore.setItemAsync(TOKEN_KEYS.REFRESH_TOKEN, refreshToken);
  },

  getAccessToken: async (): Promise<string | null> => {
    return await SecureStore.getItemAsync(TOKEN_KEYS.ACCESS_TOKEN);
  },

  getRefreshToken: async (): Promise<string | null> => {
    return await SecureStore.getItemAsync(TOKEN_KEYS.REFRESH_TOKEN);
  },

  clearTokens: async (): Promise<void> => {
    await SecureStore.deleteItemAsync(TOKEN_KEYS.ACCESS_TOKEN);
    await SecureStore.deleteItemAsync(TOKEN_KEYS.REFRESH_TOKEN);
    await userService.clearUserProfile();
  },

  hasTokens: async (): Promise<boolean> => {
    const accessToken = await SecureStore.getItemAsync(TOKEN_KEYS.ACCESS_TOKEN);
    return Boolean(accessToken);
  },
};

export const appService = {
  setIsFirstLaunch: async (isFirstLaunch: string): Promise<void> => {
    await SecureStore.setItemAsync(APP_KEYS.IS_FIRST_LAUNCH, isFirstLaunch);
  },

  getIsFirstLaunch: async (): Promise<boolean> => {
    const isFirstLaunch = await SecureStore.getItemAsync(
      APP_KEYS.IS_FIRST_LAUNCH,
    );
    if (!isFirstLaunch) {
      return true;
    }
    return isFirstLaunch === 'true';
  },
};

export const userService = {
  getUserProfile: async (): Promise<User> => {
    const userProfile = await SecureStore.getItemAsync(USER_KEYS.USER_PROFILE);
    return JSON.parse(userProfile || '{}') as User;
  },

  saveUserProfile: async (userProfile: User): Promise<void> => {
    SecureStore.setItemAsync(
      USER_KEYS.USER_PROFILE,
      JSON.stringify(userProfile),
    );
  },

  clearUserProfile: async (): Promise<void> => {
    await SecureStore.deleteItemAsync(USER_KEYS.USER_PROFILE);
  },
};
