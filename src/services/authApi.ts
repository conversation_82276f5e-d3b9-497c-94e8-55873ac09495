import { apiRequest } from '@/services/apiBase';
import { isApiSuccess } from '@/common/types/api';
import { API_ROUTES } from '@/common/constants/router';
import {
  CreateOtpCredentials,
  ForgotPasswordCredentials,
  LoginCredentials,
  RegisterCredentials,
  VerifyOtpCredentials,
} from './types/auth';

export const authApi = {
  login: async (credentials: LoginCredentials) => {
    const response = await apiRequest.post(API_ROUTES.AUTH.LOGIN, credentials);
    if (isApiSuccess(response.data)) {
      return response.data;
    } else {
      throw response.data;
    }
  },
  register: async (credentials: RegisterCredentials) => {
    const { confirmPassword, termsAgreement, ...apiPayload } = credentials;

    const response = await apiRequest.post(
      API_ROUTES.AUTH.REGISTER,
      apiPayload,
    );
    if (
      response.data?.statusCode === 201 ||
      response.data?.statusCode === 200
    ) {
      return response.data;
    } else {
      throw response.data;
    }
  },
  createOtp: async (credentials: CreateOtpCredentials) => {
    const response = await apiRequest.post(
      API_ROUTES.AUTH.CREATE_OTP,
      credentials,
    );

    if (
      response.data?.statusCode === 201 ||
      response.data?.statusCode === 200
    ) {
      return response.data;
    } else {
      throw response.data;
    }
  },
  verifyOtp: async (credentials: VerifyOtpCredentials) => {
    const response = await apiRequest.post(
      API_ROUTES.AUTH.VERIFY_OTP,
      credentials,
    );
    if (
      response.data?.statusCode === 201 ||
      response.data?.statusCode === 200
    ) {
      return response.data;
    } else {
      throw response.data;
    }
  },
  forgotPassword: async (credentials: ForgotPasswordCredentials) => {
    const response = await apiRequest.post(
      API_ROUTES.AUTH.FORGOT_PASSWORD,
      credentials,
    );
    if (
      response.data?.statusCode === 201 ||
      response.data?.statusCode === 200
    ) {
      return response.data;
    } else {
      throw response.data;
    }
  },
};
