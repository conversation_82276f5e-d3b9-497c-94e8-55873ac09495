import { registerAs } from '@nestjs/config';
import validateConfig from '@utils/validate-config';
import { IsString, IsUrl } from 'class-validator';

class EnvironmentVariablesValidator {
  @IsUrl()
  @IsString()
  DEFAULT_AVATAR_URL: string;
}

export default registerAs('defaultAvatar', () => {
  console.log('DEFAULT_AVATAR_URL from env:', process.env.DEFAULT_AVATAR_URL);

  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    url: process.env.DEFAULT_AVATAR_URL,
  };
});
