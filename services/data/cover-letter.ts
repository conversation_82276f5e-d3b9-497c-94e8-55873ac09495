import { Briefcase, Crown, Filter, Minimize2, Palette, Shirt, Sparkles } from "lucide-react";
// Mock data for templates

export const templates = [
  {
    id: 1,
    name: "Executive Pro",
    tags: ["Professional", "Formal"],
    image:
      "https://www.topcv.vn/images/cover-letter/screenshots/thumbs/vi/basic_3.png?v=1.0.1",
    categories: ["professional", "formal"],
    popular: true,
    color: "blue",
  },
  {
    id: 2,
    name: "Creative Spark",
    tags: ["Creative", "Colorful"],
    image:
      "https://www.topcv.vn/images/cover-letter/screenshots/thumbs/vi/color_1.png?v=1.0.1",
    categories: ["creative", "colorful"],
    popular: false,
    color: "orange",
  },
  {
    id: 3,
    name: "Modern Minimalist",
    tags: ["Simple", "Elegant"],
    image:
      "https://www.topcv.vn/images/cover-letter/screenshots/thumbs/vi/dev_2.png?v=1.0.10",
    categories: ["simple", "elegant"],
    popular: true,
    color: "gray",
  },
  //   {
  //     id: 4,
  //     name: "Fresh Graduate",
  //     tags: ["Simple", "Professional"],
  //     image:
  //       "https://images.pexels.com/photos/1181406/pexels-photo-1181406.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["simple", "professional"],
  //     popular: false,
  //     color: "green",
  //   },
  //   {
  //     id: 5,
  //     name: "Tech Innovator",
  //     tags: ["Creative", "Professional"],
  //     image:
  //       "https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["creative", "professional"],
  //     popular: true,
  //     color: "purple",
  //   },
  //   {
  //     id: 6,
  //     name: "Classic Elegance",
  //     tags: ["Elegant", "Formal"],
  //     image:
  //       "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["elegant", "formal"],
  //     popular: false,
  //     color: "yellow",
  //   },
  //   {
  //     id: 7,
  //     name: "Bold Statement",
  //     tags: ["Colorful", "Creative"],
  //     image:
  //       "https://images.pexels.com/photos/1181241/pexels-photo-1181241.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["colorful", "creative"],
  //     popular: true,
  //     color: "red",
  //   },
  //   {
  //     id: 8,
  //     name: "Soft Professional",
  //     tags: ["Elegant", "Professional"],
  //     image:
  //       "https://images.pexels.com/photos/1181304/pexels-photo-1181304.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["elegant", "professional"],
  //     popular: false,
  //     color: "pink",
  //   },
  //   {
  //     id: 9,
  //     name: "Formal Business",
  //     tags: ["Formal", "Professional"],
  //     image:
  //       "https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["formal", "professional"],
  //     popular: true,
  //     color: "navy",
  //   },
  //   {
  //     id: 10,
  //     name: "Vibrant Creative",
  //     tags: ["Colorful", "Creative"],
  //     image:
  //       "https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["colorful", "creative"],
  //     popular: false,
  //     color: "rainbow",
  //   },
  //   {
  //     id: 11,
  //     name: "Clean Simple",
  //     tags: ["Simple"],
  //     image:
  //       "https://images.pexels.com/photos/1181316/pexels-photo-1181316.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["simple"],
  //     popular: true,
  //     color: "white",
  //   },
  //   {
  //     id: 12,
  //     name: "Royal Elegance",
  //     tags: ["Elegant", "Formal"],
  //     image:
  //       "https://images.pexels.com/photos/1181435/pexels-photo-1181435.jpeg?auto=compress&cs=tinysrgb&w=600",
  //     categories: ["elegant", "formal"],
  //     popular: false,
  //     color: "gold",
  //   },
];

export const filterOptions = [
  { label: "All", value: "all", icon: Filter },
  { label: "Simple", value: "simple", icon: Minimize2 },
  { label: "Creative", value: "creative", icon: Palette },
  { label: "Professional", value: "professional", icon: Briefcase },
  { label: "Formal", value: "formal", icon: Crown },
  { label: "Elegant", value: "elegant", icon: Sparkles },
  { label: "Colorful", value: "colorful", icon: Shirt },
];