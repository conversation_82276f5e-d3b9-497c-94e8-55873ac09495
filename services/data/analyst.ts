// analyst role
export const mockAnalysisDataJD = {
  overallScore: 85,
  overallFeedback:
    "Excellent match! Your profile strongly aligns with the job requirements. With some minor improvements in cloud technologies, you'll be a perfect candidate.",
  summaryComment:
    "The candidate demonstrates strong technical expertise in frontend development with React and TypeScript. Backend experience with Node.js is solid. Cloud experience needs enhancement to meet all requirements.",
  fitScore: 82,
  verdict: "Highly Recommended",
  matchingPoints: [
    "5+ years of React development experience",
    "Strong TypeScript and JavaScript skills",
    "Proven Node.js backend development",
    "Experience with REST APIs and database design",
    "Git version control proficiency",
    "Agile development methodology experience",
  ],
  missingSkills: [
    "Kubernetes orchestration",
    "Docker Swarm",
    "GraphQL implementation",
    "Jenkins CI/CD",
    "AWS Lambda functions",
    "Microservices architecture",
  ],
  missingExperience: [
    "Large-scale system architecture (500k+ users)",
    "Team leadership experience (5+ developers)",
    "Performance optimization for high-traffic applications",
    "Security audit and compliance experience",
  ],
  recommendations: [
    {
      priority: "high",
      title: "Cloud Platform Certification",
      description:
        "Obtain AWS Solutions Architect certification to strengthen cloud expertise",
      timeframe: "2-3 months",
    },
    {
      priority: "medium",
      title: "Microservices Project",
      description: "Build a personal project using microservices architecture",
      timeframe: "1-2 months",
    },
    {
      priority: "low",
      title: "GraphQL Integration",
      description: "Learn GraphQL and implement it in an existing project",
      timeframe: "3-4 weeks",
    },
  ],
  contactInfo: {
    name: "John Doe",
    email: "<EMAIL>",
    phone: "+****************",
    location: "San Francisco, CA",
    linkedin: "linkedin.com/in/johndoe",
  },
  experience: [
    {
      title: "Senior Frontend Developer",
      company: "TechCorp Inc.",
      duration: "2021 - Present",
      location: "San Francisco, CA",
      highlights: [
        "Led development of React-based dashboard serving 100k+ users",
        "Implemented TypeScript migration improving code quality by 40%",
        "Mentored 3 junior developers",
      ],
    },
    {
      title: "Full Stack Developer",
      company: "StartupXYZ",
      duration: "2019 - 2021",
      location: "Remote",
      highlights: [
        "Built complete e-commerce platform using MERN stack",
        "Optimized API performance reducing response time by 60%",
        "Implemented automated testing increasing coverage to 85%",
      ],
    },
  ],
  education: [
    {
      degree: "Bachelor of Science in Computer Science",
      school: "University of California, Berkeley",
      year: "2019",
      gpa: "3.8/4.0",
      relevant: [
        "Data Structures",
        "Algorithms",
        "Software Engineering",
        "Database Systems",
      ],
    },
  ],
  skills: {
    programming: ["JavaScript", "TypeScript", "Python", "Java"],
    frontend: ["React", "Vue.js", "HTML5", "CSS3", "Tailwind CSS"],
    backend: ["Node.js", "Express.js", "Django", "PostgreSQL", "MongoDB"],
    tools: ["Git", "Docker", "Webpack", "Jest", "Postman"],
    cloud: ["AWS EC2", "AWS S3", "Heroku", "Netlify"],
  },
};

// analyst role
export const mockAnalysisDataRole = {
  overallScore: 85,
  fitScore: 78,
  overallFeedback:
    "Your resume shows strong technical skills and relevant experience. Consider highlighting more quantifiable achievements and adding industry-specific keywords to improve ATS compatibility.",
  summaryComment:
    "Well-structured resume with good technical depth. Strong candidate for senior developer roles with room for optimization in presentation and keyword alignment.",
  contactInfo: {
    name: "Alex Chen",
    email: "<EMAIL>",
    phone: "+****************",
    location: "San Francisco, CA",
  },
  experience: [
    {
      title: "Senior Frontend Developer",
      company: "TechCorp Inc.",
      duration: "2022 - Present",
      description:
        "Led development of React-based dashboard serving 50K+ users. Improved performance by 40% through code optimization and implemented CI/CD pipeline reducing deployment time by 60%.",
    },
    {
      title: "Frontend Developer",
      company: "StartupXYZ",
      duration: "2020 - 2022",
      description:
        "Built responsive web applications using React and TypeScript. Collaborated with design team to implement pixel-perfect UI components and integrated REST APIs for data visualization.",
    },
    {
      title: "Junior Web Developer",
      company: "Digital Agency Pro",
      duration: "2019 - 2020",
      description:
        "Developed client websites using HTML, CSS, and JavaScript. Maintained WordPress sites and implemented SEO best practices resulting in 25% increase in organic traffic.",
    },
  ],
  education: {
    degree: "Bachelor of Science in Computer Science",
    school: "University of California, Berkeley",
    graduationDate: "2019",
    gpa: "3.7/4.0",
  },
  skills: [
    "React",
    "TypeScript",
    "JavaScript",
    "Node.js",
    "Python",
    "AWS",
    "Docker",
    "Git",
    "CI/CD",
    "REST APIs",
    "GraphQL",
    "MongoDB",
    "PostgreSQL",
    "Tailwind CSS",
    "Next.js",
    "Express.js",
  ],
};
