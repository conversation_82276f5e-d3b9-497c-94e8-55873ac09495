import { BotMessageSquare, FileText, Map, Mic } from "lucide-react";

export const tools = [
  {
    id: 1,
    title: "AI Resume Analyzer",
    description:
      "Get instant AI-powered insights on your CV's strengths and areas for improvement with detailed recommendations",
    icon: FileText,
    color: "from-blue-500 via-cyan-500 to-teal-500",
    hoverColor: "hover:from-blue-600 hover:via-cyan-600 hover:to-teal-600",
    buttonText: "Analyze CV",
    bgPattern: "bg-blue-50",
  },
  {
    id: 2,
    title: "AI Career Q&A Chat",
    description:
      "Chat with an AI to explore career fit, upload job posts, and get match scores based on your qualifications",
    icon: BotMessageSquare,
    color: "from-purple-500 via-pink-500 to-rose-500",
    hoverColor: "hover:from-purple-600 hover:via-pink-600 hover:to-rose-600",
    buttonText: "Scan Job",
    bgPattern: "bg-purple-50",
  },
  {
    id: 3,
    title: "Career Roadmap Generator",
    description:
      "Generate a custom career path with AI suggestions to sharpen your profile for your ideal roles and goals",
    icon: Map,
    color: "from-orange-500 via-amber-500 to-yellow-500",
    hoverColor: "hover:from-orange-600 hover:via-amber-600 hover:to-yellow-600",
    buttonText: "Get Suggestions",
    bgPattern: "bg-orange-50",
  },
  {
    id: 4,
    title: "AI Mock Interview",
    description:
      "Simulate job interviews with AI-driven questions for your desired roles and receive expert-level feedback",
    icon: Mic,
    color: "from-green-500 via-emerald-500 to-teal-500",
    hoverColor: "hover:from-green-600 hover:via-emerald-600 hover:to-teal-600",
    buttonText: "Start Interview",
    bgPattern: "bg-green-50",
  },
];