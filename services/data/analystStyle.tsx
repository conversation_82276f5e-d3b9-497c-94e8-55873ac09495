import {
  AlertCircle,
  Clock,
  Star,
  Zap,
  User,
  Briefcase,
  GraduationCap,
  Code,
} from "lucide-react";

export const getScoreColor = (score: any) => {
  if (score >= 80) return "from-green-500 to-emerald-500";
  if (score >= 60) return "from-yellow-500 to-orange-500";
  return "from-red-500 to-pink-500";
};

export const getVerdictColor = (verdict: any) => {
  switch (verdict) {
    case "Highly Recommended":
      return "bg-green-100 text-green-800 border-green-200";
    case "Recommended":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "Consider":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    default:
      return "bg-red-100 text-red-800 border-red-200";
  }
};

export const getPriorityIcon = (priority: any) => {
  switch (priority) {
    case "high":
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    case "medium":
      return <Clock className="w-4 h-4 text-yellow-500" />;
    case "low":
      return <Zap className="w-4 h-4 text-blue-500" />;
    default:
      return <Star className="w-4 h-4 text-gray-500" />;
  }
};

export const getSectionIcon = (sectionName: string) => {
  switch (sectionName) {
    case "contact_info":
      return <User className="w-6 h-6 text-blue-500" />;
    case "experience":
      return <Briefcase className="w-6 h-6 text-green-500" />;
    case "education":
      return <GraduationCap className="w-6 h-6 text-purple-500" />;
    case "skills":
      return <Code className="w-6 h-6 text-indigo-500" />;
    default:
      return <User className="w-6 h-6 text-blue-500" />;
  }
};

export const getScoreColors = (score: number) => {
  if (score >= 80) return "text-green-600 bg-green-50";
  if (score >= 60) return "text-orange-600 bg-orange-50";
  return "text-red-600 bg-red-50";
};
