import { InterviewQuestionSessionType } from "@/common/types/interview-question";
import { BuilderType } from "../types/interview-question";

export const createQuestionInterview = (builder: BuilderType) =>
  builder.mutation<InterviewQuestionSessionType, FormData>({
    query: (formdata: FormData) => ({
      url: `/interview/generate-question-interview`,
      method: "POST",
      body: formdata,
    }),
    invalidatesTags: ["InterviewQuestion"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
