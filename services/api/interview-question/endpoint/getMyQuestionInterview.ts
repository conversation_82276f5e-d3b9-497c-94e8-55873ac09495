import { BuilderType } from "../types/interview-question";
import { InterviewQuestionSessionType } from "@/common/types/interview-question";

export const getMyInterviewQuestion = (builder: BuilderType) =>
  builder.query<InterviewQuestionSessionType[], void>({
    query: () => "/interview/get-my-question-interview",
    providesTags: ["InterviewQuestion"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
