import { InterviewQuestionSessionType } from "@/common/types/interview-question";
import { BuilderType } from "../types/interview-question";

export const getQuestionInterviewById = (builder: BuilderType) =>
  builder.query<InterviewQuestionSessionType, string>({
    query: (interviewId) =>
      `/interview/get-question-interview-by-id/${interviewId}`,
    providesTags: ["InterviewQuestion"],
    transformResponse: (response: any): InterviewQuestionSessionType => {
      return (response?.data ?? response) as InterviewQuestionSessionType;
    },
  });
