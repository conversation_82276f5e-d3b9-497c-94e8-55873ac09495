import {
  BaseQueryFn,
  <PERSON>tchA<PERSON>s,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/query";

// Define BaseQueryFn type
export type InterviewQuestionBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
>;

// Define builder typeNextRequest
export type BuilderType = EndpointBuilder<
  InterviewQuestionBaseQueryFn,
  "InterviewQuestion",
  "interviewQuestionApi"
>;
