import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../auth/baseQuery";
import { getMyInterviewQuestion } from "./endpoint/getMyQuestionInterview";
import { getQuestionInterviewById } from "./endpoint/getQuestionInterviewById";
import { deleteQuestionInterviewById } from "./endpoint/deleteQuestionInterview";
import { createQuestionInterview } from "./endpoint/createQuestionInterview";

export const interviewQuestionApi = createApi({
  reducerPath: "interviewQuestionApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["InterviewQuestion"],
  endpoints: (builder) => ({
    getMyQuestionInterview: getMyInterviewQuestion(builder),
    getQuestionInterviewById: getQuestionInterviewById(builder),
    deleteQuestionInterviewById: deleteQuestionInterviewById(builder),
    createQuestionInterview: createQuestionInterview(builder),
  }),
});

export const {
  useGetMyQuestionInterviewQuery,
  useGetQuestionInterviewByIdQuery,
  useDeleteQuestionInterviewByIdMutation,
  useCreateQuestionInterviewMutation,
} = interviewQuestionApi;
