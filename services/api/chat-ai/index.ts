import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../auth/baseQuery";
import { geMySessionsEndpoint } from "./endpoints/getMySessions";
import { getSessionByIdEndpoint } from "./endpoints/getSessionById";
import { sendMessageEndpoint } from "./endpoints/sendMessage";
import { deleteSessionByIdEndpoint } from "./endpoints/deleteSession";

export const chatAiApi = createApi({
  reducerPath: "chatAiApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["ChatAI"],
  endpoints: (builder) => ({
    getMySessions: geMySessionsEndpoint(builder),
    getSessionById: getSessionByIdEndpoint(builder),
    sendMessage: sendMessageEndpoint(builder),
    deleteSessionById: deleteSessionByIdEndpoint(builder),
  }),
});

export const {
  useGetMySessionsQuery,
  useGetSessionByIdQuery,
  useSendMessageMutation,
  useDeleteSessionByIdMutation,
} = chatAiApi;
