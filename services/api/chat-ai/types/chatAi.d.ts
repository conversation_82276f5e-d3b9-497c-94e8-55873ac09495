import {
  BaseQueryFn,
  <PERSON>tchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/dist/query/endpointDefinitions";

// Define BaseQueryFn type
export type ChatAiBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
>;

// Define builder typeNextRequest
export type BuilderType = EndpointBuilder<ChatAiBaseQueryFn, "ChatAI", "chatAiApi">;
