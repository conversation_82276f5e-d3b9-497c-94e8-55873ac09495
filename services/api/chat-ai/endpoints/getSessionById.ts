import { ChatSession } from "@/common/types/chat-ai";
import { BuilderType } from "../types/chatAi";
import {
  createNewSession,
  setCurrentSession,
} from "@/redux/slices/chatAiSlice";

export const getSessionByIdEndpoint = (builder: BuilderType) =>
  builder.query<ChatSession[], void>({
    query: (sessionId: string) => `/chat-ai/chat-session/${sessionId}`,
    providesTags: ["ChatAI"],
    transformResponse: (response: any) => {
      if (response.data === null) return null;
      return response?.data ?? response;
    },
    async onQueryStarted(sessionId: string, { dispatch, queryFulfilled }: any) {
      try {
        const { data: session } = await queryFulfilled;
        if (session) {
          dispatch(setCurrentSession(session as ChatSession));
        } else {
          dispatch(
            createNewSession({
              _id: sessionId,
              sessionId,
              content: [],
              loading: false,
              name: "New Chat Session",
            })
          );
        }
      } catch (error) {
        console.error("Error fetching my sessions:", error);
      }
    },
  });
