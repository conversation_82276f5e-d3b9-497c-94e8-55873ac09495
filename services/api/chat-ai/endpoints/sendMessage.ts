import {
  setLoadingChatSession,
  setMessageChatSession,
} from "@/redux/slices/chatAiSlice";
import { BuilderType } from "../types/chatAi";

export const sendMessageEndpoint = (builder: BuilderType) =>
  builder.mutation<
    { message: string; role: string },
    { sessionId: string; message: string }
  >({
    query: ({
      sessionId,
      message,
    }: {
      sessionId: string;
      message: string;
    }) => ({
      url: `/chat-ai/chat-session/${sessionId}`,
      method: "POST",
      body: { message },
    }),
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
    async onQueryStarted(
      arg: { sessionId: string; message: string },
      { dispatch, queryFulfilled }: any
    ) {
      try {
        const { sessionId, message } = arg;
        const userMessage = {
          content: message,
          role: "user",
          timestamp: new Date().toISOString(),
        };
        dispatch(setLoadingChatSession(true));
        dispatch(
          setMessageChatSession({
            sessionId,
            message: userMessage,
          })
        );

        // get response from server
        const { data: response } = await queryFulfilled;

        dispatch(setLoadingChatSession(false));
        dispatch(
          setMessageChatSession({
            sessionId,
            message: response,
          })
        );
      } catch (error) {
        console.error("Error sending message:", error);
      }
    },
  });
