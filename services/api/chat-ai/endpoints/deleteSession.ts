import { BuilderType } from "../types/chatAi";

export const deleteSessionByIdEndpoint = (builder: BuilderType) =>
  builder.mutation<void, string>({
    query: (sessionId: string) => ({
      url: `/chat-ai/chat-session/${sessionId}`,
      method: "DELETE",
    }),
    invalidatesTags: ["ChatAI"],
    transformResponse: (response: any) => {
      if (response.data === null) return null;
      return response?.data ?? response;
    },
    async onQueryStarted({ queryFulfilled }: any) {
      try {
        await queryFulfilled;
      } catch (error) {
        console.error("Error fetching my sessions:", error);
      }
    },
  });
