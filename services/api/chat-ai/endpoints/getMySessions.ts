import { ChatSession } from "@/common/types/chat-ai";
import { BuilderType } from "../types/chatAi";
import { setMySessions } from "@/redux/slices/chatAiSlice";

export const geMySessionsEndpoint = (builder: BuilderType) =>
  builder.query<ChatSession[], void>({
    query: () => "/chat-ai/get-my-sessions",
    providesTags: ["ChatAI"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
    async onQueryStarted(arg: void, { dispatch, queryFulfilled }: any) {
      try {
        const { data: sessions } = await queryFulfilled;
        dispatch(setMySessions(sessions));
      } catch (error) {
        console.error("Error fetching my sessions:", error);
      }
    },
  });
