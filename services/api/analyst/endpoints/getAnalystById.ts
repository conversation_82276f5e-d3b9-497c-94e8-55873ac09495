import { EndpointBuilder } from "@reduxjs/toolkit/query";
import { AnalystByIdResponse } from "../types/getAnalyst";

export const getAnalystById = (
  builder: EndpointBuilder<any, "Analysis", "analystApi">
) =>
  builder.query<AnalystByIdResponse, string>({
    query: (id: string) => `/analyst/analyst/${id}`,
    providesTags: (result: any, error: any, id: string) => [
      { type: "Analysis", id },
    ],
  });
