import { AnalystRequest, AnalystResponse } from "../types/analystRole";
import { BuilderType } from "../types/baseQuery";

export const analyzeByRoleEndpoint = (builder: BuilderType) => ({
  analyzeByRole: builder.mutation<AnalystResponse, AnalystRequest>({
    query: ({ agentType, roleDescription, cvFile }: AnalystRequest) => {
      const formData = new FormData();
      formData.append("agentType", agentType);
      formData.append("roleDescription", roleDescription);
      formData.append("cvFile", cvFile);

      return {
        url: "/analyst/analyst-by-role-description",
        method: "POST",
        body: formData,
      };
    },
    invalidatesTags: ["Analysis"],
  }),
});
