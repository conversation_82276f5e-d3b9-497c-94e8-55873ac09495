import { AnalystJDRequest, AnalystResponse } from "../types/analystJD";
import { BuilderType } from "../types/baseQuery";

export const analyzeByJDEndpoint = (builder: BuilderType) => ({
  analyzeByJD: builder.mutation<AnalystResponse, AnalystJDRequest>({
    query: ({ agentType, cvFile, jdFile }: AnalystJDRequest) => {
      const formData = new FormData();
      formData.append("agentType", agentType);
      formData.append("cvFile", cvFile);
      formData.append("jdFile", jdFile);

      return {
        url: "/analyst/analyst-by-jd-description",
        method: "POST",
        body: formData,
      };
    },
    invalidatesTags: ["Analysis"],
  }),
});
