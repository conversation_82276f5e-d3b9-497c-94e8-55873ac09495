export interface AnalystContent {
  overall_score: number;
  fit_score: number;
  overall_feedback: string;
  summary_comment: string;
  verdict: string;
  matching_points: string[];
  missing_skills: string[];
  missing_experience: string[];
  recommendations: string[];
  sections: {
    contact_info: {
      score: number;
      comment: string;
      tips_for_improvement: string[];
      whats_good: string[];
      needs_improvement: string[];
    };
    experience: {
      score: number;
      comment: string;
      tips_for_improvement: string[];
      whats_good: string[];
      needs_improvement: string[];
    };
    education: {
      score: number;
      comment: string;
      tips_for_improvement: string[];
      whats_good: string[];
      needs_improvement: string[];
    };
    skills: {
      score: number;
      comment: string;
      tips_for_improvement: string[];
      whats_good: string[];
      needs_improvement: string[];
    };
  };
}

export interface Analyst {
  _id: string;
  userId: string;
  content: AnalystContent;
  agentType: string;
  cvFileUrl: string;
  cvFileName?: string;
  cvFileSize?: number;
  cvFileType?: string;
  jdFileUrl?: string;
  jdFileName?: string;
  jdFileSize?: number;
  jdFileType?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface MyAnalystResponse {
  statusCode: number;
  message: string;
  data: Analyst[];
}

export interface AnalystByIdResponse {
  statusCode: number;
  message: string;
  data: Analyst;
}
