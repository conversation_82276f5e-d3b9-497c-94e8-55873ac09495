export interface AnalystRequest {
  agentType: string;
  roleDescription: string;
  cvFile: File;
}

export interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  location: string;
}

export interface Experience {
  title: string;
  company: string;
  duration: string;
  description: string;
}

export interface Education {
  degree: string;
  school: string;
  graduationDate: string;
  gpa: string;
}

export interface SectionAnalysis {
  score: number;
  comment: string;
  tips_for_improvement: string[];
  whats_good: string[];
  needs_improvement: string[];
}

export interface AnalysisContent {
  overall_score: number;
  fit_score: number;
  overall_feedback: string;
  summary_comment: string;
  sections: {
    contact_info: SectionAnalysis;
    experience: SectionAnalysis;
    education: SectionAnalysis;
    skills: SectionAnalysis;
  };
}

export interface AnalystResponse {
  statusCode: number;
  message: string;
  data: {
    _id: string;
    userId: string;
    content: AnalysisContent;
    agentType: string;
    roleDescription: string;
  };
}
