export interface AnalystRequest {
  agentType: "AI_RESUME_ANALYSIS_ROLE";
  roleDescription: string;
  cvFile: File;
}

export interface AnalystJDRequest {
  agentType: "AI_RESUME_ANALYSIS_JD";
  cvFile: File;
  jdFile: File;
}

export interface AnalystResponse {
  statusCode: number;
  message: string;
  data: {
    _id: string;
    userId: string;
    content: {
      overall_score: number;
      overall_feedback: string;
      summary_comment: string;
      fit_score: number;
      verdict: string;
      matching_points: string[];
      missing_skills: string[];
      missing_experience: string[];
      recommendations: string[];
      sections?: {
        contact_info: {
          score: number;
          comment: string;
          tips_for_improvement: string[];
          whats_good: string[];
          needs_improvement: string[];
        };
        experience: {
          score: number;
          comment: string;
          tips_for_improvement: string[];
          whats_good: string[];
          needs_improvement: string[];
        };
        education: {
          score: number;
          comment: string;
          tips_for_improvement: string[];
          whats_good: string[];
          needs_improvement: string[];
        };
        skills: {
          score: number;
          comment: string;
          tips_for_improvement: string[];
          whats_good: string[];
          needs_improvement: string[];
        };
      };
    };
    agentType: string;
  };
}
