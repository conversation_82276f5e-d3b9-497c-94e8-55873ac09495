import {
  BaseQueryFn,
  <PERSON>tchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/dist/query/endpointDefinitions";

// Define BaseQueryFn type
export type AnalystBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError,
  {},
  FetchBaseQueryMeta
>;

// Define builder typeNextRequest
export type BuilderType = EndpointBuilder<
  AnalystBaseQueryFn,
  "Analysis",
  "analystApi"
>;
