import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQuery } from "./baseQuery";
import { analyzeByRoleEndpoint } from "./endpoints/analyzeByRole";
import { analyzeByJDEndpoint } from "./endpoints/analyzeByJD";
import { getMyAnalysts } from "./endpoints/getMyAnalysts";
import { getAnalystById } from "./endpoints/getAnalystById";

export const analystApi = createApi({
  reducerPath: "analystApi",
  baseQuery,
  tagTypes: ["Analysis"],
  endpoints: (builder) => ({
    analyzeByRole: analyzeByRoleEndpoint(builder).analyzeByRole,
    analyzeByJD: analyzeByJDEndpoint(builder).analyzeByJD,
    getMyAnalysts: getMyAnalysts(builder),
    getAnalystById: getAnalystById(builder),
  }),
});

export const {
  useAnalyzeByRoleMutation,
  useAnalyzeByJDMutation,
  useGetMyAnalystsQuery,
  useGetAnalystByIdQuery,
} = analystApi;
