import {
  BaseQueryFn,
  <PERSON>tchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/query";

// Define BaseQueryFn type
export type ConversationBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
>;

// Define builder typeNextRequest
export type BuilderType = EndpointBuilder<
  ConversationBaseQueryFn,
  "Conversation",
  "conversationApi"
>;
