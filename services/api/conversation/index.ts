import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../auth/baseQuery";
import { getMyConversation } from "./endpoints/getMyConversation";
import { getConversationById } from "./endpoints/getConversationById";
import { createConversation } from "./endpoints/createConversation";
export const conversationApi = createApi({
  reducerPath: "conversationApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Conversation"],
  endpoints: (builder) => ({
    getMyConversation: getMyConversation(builder),
    getConversationById: getConversationById(builder),
    createConversation: createConversation(builder),
  }),
});

export const {
  useGetMyConversationQuery,
  useGetConversationByIdQuery,
  useCreateConversationMutation,
} = conversationApi;
