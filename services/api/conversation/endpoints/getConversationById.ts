import { ConversationResponse } from "@/common/types/conversation";
import { BuilderType } from "../types/conversation";

export const getConversationById = (builder: BuilderType) =>
  builder.query<ConversationResponse, string>({
    query: (id) => `/conversation/${id}`,
    providesTags: ["Conversation"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
