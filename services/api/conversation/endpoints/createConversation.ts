import {
  ConversationRequest,
  ConversationResponse,
} from "@/common/types/conversation";
import { BuilderType } from "../types/conversation";

export const createConversation = (builder: BuilderType) =>
  builder.mutation<ConversationResponse, ConversationRequest>({
    query: (conversation) => ({
      url: "/conversation",
      method: "POST",
      body: conversation,
    }),
    invalidatesTags: ["Conversation"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
