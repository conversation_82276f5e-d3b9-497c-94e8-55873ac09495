import {
  BaseQueryFn,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/dist/query/endpointDefinitions";

// Define BaseQueryFn type
export type CoverLetterBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
>;

// Define builder type
export type BuilderType = EndpointBuilder<
  CoverLetterBaseQueryFn,
  "CoverLetter",
  "coverLetterAI"
>;

// API Response wrapper
export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data: T;
}

// Cover Letter Content Structure
export interface CoverLetterHeader {
  fullName: string;
  targetPosition: string;
  profileImage: string;
}

export interface CoverLetterContactInfo {
  dateOfBirth: string;
  phoneNumber: string;
  email: string;
  address: string;
  linkedin: string;
}

export interface CoverLetterRecipientInfo {
  hiringManagerName: string;
  hiringManagerTitle: string;
  companyName: string;
  companyAddress: string;
  applicationDate: string;
  jobTitle: string;
}

export interface CoverLetterContent {
  header: CoverLetterHeader;
  contactInfo: CoverLetterContactInfo;
  recipientInfo: CoverLetterRecipientInfo;
  greetingLine: string;
  openingParagraph: string;
  experienceAchievements: string;
  skillsStrengths: string;
  closingParagraph: string;
  signature: string;
}

// Cover Letter Entity
export interface CoverLetter {
  _id: string;
  userId: string;
  content: CoverLetterContent;
  agentType: "AI_COVER_LETTER_ROLE" | "AI_COVER_LETTER_JD";
  cvFileUrl?: string;
  jdFileUrl?: string;
  roleDescription?: string;
  language: "english" | "vietnamese";
  templateId?: string; // Template identifier (temp1, temp2, temp3)
  coverLetterFileUrl?: string;

  // 🆕 GLOBAL UI STATE (áp dụng cho toàn template)
  fontSize?: string;
  fontFamily?: string;
  lineHeight?: string;
  currentTheme?: string;

  // 🆕 PER-SECTION FORMATTING (áp dụng cho từng section)
  sectionFormatting?: Record<
    string,
    {
      textAlign?: "left" | "center" | "right" | "justify";
      isBold?: boolean;
      isItalic?: boolean;
      isUnderline?: boolean;
      isHidden?: boolean;
    }
  >;

  // 🆕 DETAILED CHARACTER-LEVEL FORMATTING (Lựa chọn 2)
  detailedFormatting?: Record<
    string, // field name (greetingLine, openingParagraph, experienceAchievements, skillsStrengths, closingParagraph, jobTitle, etc.)
    Array<{
      start: number; // vị trí ký tự bắt đầu (0-based index)
      end: number; // vị trí ký tự kết thúc (exclusive)
      bold?: boolean; // có bold không
      italic?: boolean; // có italic không
      underline?: boolean; // có underline không
    }>
  >;

  createdAt: string;
  updatedAt: string;
}

// Cover Letter Evaluation Types
export interface CoverLetterSectionEvaluation {
  score: number;
  feedback: string;
  suggestions: string[];
}

export interface CoverLetterEvaluation {
  overallScore: number;
  overallFeedback: string;
  sections: {
    header: CoverLetterSectionEvaluation;
    contactInfo: CoverLetterSectionEvaluation;
    recipientInfo: CoverLetterSectionEvaluation;
    greeting: CoverLetterSectionEvaluation;
    opening: CoverLetterSectionEvaluation;
    body: CoverLetterSectionEvaluation;
    closing: CoverLetterSectionEvaluation;
    signature: CoverLetterSectionEvaluation;
  };
  improvements: string[];
  strengths: string[];
}

// Request types
export interface CreateCoverLetterByRoleRequest {
  agentType: "AI_COVER_LETTER_ROLE";
  roleDescription: string;
  language: "english" | "vietnamese";
  cvFile: File;
}

export interface CreateCoverLetterByJdRequest {
  agentType: "AI_COVER_LETTER_JD";
  language: "english" | "vietnamese";
  cvFile: File;
  jdFile: File;
}

export interface UpdateCoverLetterRequest {
  fullName: string;
  targetPosition: string;
  profileImage?: string;
  dateOfBirth: string;
  phoneNumber: string;
  email: string;
  address: string;
  linkedin: string;
  hiringManagerName: string;
  hiringManagerTitle: string;
  companyName: string;
  companyAddress: string;
  applicationDate: string;
  jobTitle: string;
  greetingLine: string;
  openingParagraph: string;
  experienceAchievements: string;
  skillsStrengths: string;
  closingParagraph: string;
  signature: string;
  templateId?: string; // Template identifier (temp1, temp2, temp3)
  coverLetterFileUrl?: string;
  hiddenSections?: string[]; // Array of hidden section names

  // 🆕 GLOBAL UI STATE (áp dụng cho toàn template)
  fontSize?: string;
  fontFamily?: string;
  lineHeight?: string;
  currentTheme?: string;

  // 🆕 PER-SECTION FORMATTING (áp dụng cho từng section)
  sectionFormatting?: Record<
    string,
    {
      textAlign?: "left" | "center" | "right" | "justify";
      isBold?: boolean;
      isItalic?: boolean;
      isUnderline?: boolean;
      isHidden?: boolean;
    }
  >;

  // 🆕 DETAILED CHARACTER-LEVEL FORMATTING (Lựa chọn 2)
  detailedFormatting?: Record<
    string, // field name
    Array<{
      start: number; // vị trí ký tự bắt đầu (0-based index)
      end: number; // vị trí ký tự kết thúc (exclusive)
      bold?: boolean; // có bold không
      italic?: boolean; // có italic không
      underline?: boolean; // có underline không
    }>
  >;
}

// Response types
export type CreateCoverLetterByRoleResponse = ApiResponse<CoverLetter>;
export type CreateCoverLetterByJdResponse = ApiResponse<CoverLetter>;
export type GetCoverLetterByIdResponse = ApiResponse<CoverLetter>;
export type GetMyCoverLettersResponse = ApiResponse<CoverLetter[]>;
export type UpdateCoverLetterResponse = ApiResponse<CoverLetter>;
export type UploadAvatarResponse = ApiResponse<{ profileImage: string }>;
export type DeleteCoverLetterResponse = ApiResponse<null>;
export type EvaluateCoverLetterResponse = ApiResponse<CoverLetterEvaluation>;
export type GetCoverLetterEvaluationResponse =
  ApiResponse<CoverLetterEvaluation>;
