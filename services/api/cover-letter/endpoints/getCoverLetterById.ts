import {
  BuilderType,
  GetCoverLetterByIdResponse,
} from "../types/cover-letter.d";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

export const getCoverLetterByIdEndpoint = (builder: BuilderType) =>
  builder.query<GetCoverLetterByIdResponse, string>({
    query: (id: string) => ({
      url: `/cover-letter/cover-letter/${id}`,
      method: "GET",
    }),
    providesTags: (
      result: GetCoverLetterByIdResponse | undefined,
      error: FetchBaseQueryError | undefined,
      id: string
    ) => [{ type: "CoverLetter", id }],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
