import {
  BuilderType,
  UpdateCoverLetterRequest,
  UpdateCoverLetterResponse,
} from "../types/cover-letter.d";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

export const updateCoverLetterEndpoint = (builder: BuilderType) =>
  builder.mutation<
    UpdateCoverLetterResponse,
    { id: string; data: UpdateCoverLetterRequest }
  >({
    query: ({ id, data }: { id: string; data: UpdateCoverLetterRequest }) => ({
      url: `/cover-letter/cover-letter/${id}`,
      method: "PUT",
      body: data,
    }),
    invalidatesTags: (
      result: UpdateCoverLetterResponse | undefined,
      error: FetchBaseQueryError | undefined,
      { id }: { id: string; data: UpdateCoverLetterRequest }
    ) => [{ type: "CoverLetter", id }],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
