import { BuilderType } from "../types/cover-letter.d";

export interface ImageKitAuthResponse {
  token: string;
  expire: number;
  signature: string;
}

export const getImageKitAuthEndpoint = (builder: BuilderType) =>
  builder.query<ImageKitAuthResponse, void>({
    query: () => ({
      url: "/imagekit/auth",
      method: "GET",
    }),
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
