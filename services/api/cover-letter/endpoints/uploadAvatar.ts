import { BuilderType, UploadAvatarResponse } from "../types/cover-letter.d";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

export const uploadAvatarEndpoint = (builder: BuilderType) =>
  builder.mutation<UploadAvatarResponse, { id: string; formData: FormData }>({
    query: ({ id, formData }: { id: string; formData: FormData }) => ({
      url: `/cover-letter/upload-avatar/${id}`,
      method: "POST",
      body: formData,
    }),
    invalidatesTags: (
      result: UploadAvatarResponse | undefined,
      error: FetchBaseQueryError | undefined,
      { id }: { id: string; formData: FormData }
    ) => [{ type: "CoverLetter", id }],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
