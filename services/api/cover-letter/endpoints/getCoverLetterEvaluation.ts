import {
  BuilderType,
  GetCoverLetterEvaluationResponse,
} from "../types/cover-letter.d";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

export const getCoverLetterEvaluationEndpoint = (builder: BuilderType) =>
  builder.query<GetCoverLetterEvaluationResponse, string>({
    query: (id: string) => ({
      url: `/cover-letter/evaluate/${id}`,
      method: "GET",
    }),
    providesTags: (
      result: GetCoverLetterEvaluationResponse | undefined,
      error: FetchBaseQueryError | undefined,
      id: string
    ) => [{ type: "CoverLetter", id: "EVALUATION" }],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
