import {
  BuilderType,
  GetMyCoverLettersResponse,
} from "../types/cover-letter.d";

export const getMyCoverLettersEndpoint = (builder: BuilderType) =>
  builder.query<GetMyCoverLettersResponse, void>({
    query: () => ({
      url: "/cover-letter/my-cover-letter",
      method: "GET",
    }),
    providesTags: ["CoverLetter"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
