import { BuilderType } from "../types/cover-letter.d";
import { CreateCoverLetterByJdResponse, CreateCoverLetterByJdRequest } from "../types/cover-letter.d";

export const createCoverLetterByJdEndpoint = (builder: BuilderType) =>
  builder.mutation<CreateCoverLetterByJdResponse, FormData>({
    query: (formData: FormData) => ({
      url: "/cover-letter/cover-letter-by-jd-description",
      method: "POST",
      body: formData,
    }),
    invalidatesTags: ["CoverLetter"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
