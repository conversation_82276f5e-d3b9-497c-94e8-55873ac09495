import { BuilderType } from "../types/cover-letter.d";
import { CreateCoverLetterByRoleResponse, CreateCoverLetterByRoleRequest } from "../types/cover-letter.d";

export const createCoverLetterByRoleEndpoint = (builder: BuilderType) =>
  builder.mutation<CreateCoverLetterByRoleResponse, FormData>({
    query: (formData: FormData) => ({
      url: "/cover-letter/cover-letter-by-role-description",
      method: "POST",
      body: formData,
    }),
    invalidatesTags: ["CoverLetter"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
