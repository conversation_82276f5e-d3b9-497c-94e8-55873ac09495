import {
  BuilderType,
  EvaluateCoverLetterResponse,
} from "../types/cover-letter.d";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";

export const evaluateCoverLetterEndpoint = (builder: BuilderType) =>
  builder.mutation<EvaluateCoverLetterResponse, string>({
    query: (id: string) => ({
      url: `/cover-letter/evaluate/${id}`,
      method: "POST",
    }),
    invalidatesTags: (
      result: EvaluateCoverLetterResponse | undefined,
      error: FetchBaseQueryError | undefined,
      id: string
    ) => [{ type: "CoverLetter", id: "EVALUATION" }],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
