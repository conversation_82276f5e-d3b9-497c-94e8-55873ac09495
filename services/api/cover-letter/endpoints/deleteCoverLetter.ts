import { BuilderType, DeleteCoverLetterResponse } from "../types/cover-letter.d";

export const deleteCoverLetterEndpoint = (builder: BuilderType) =>
  builder.mutation<DeleteCoverLetterResponse, string>({
    query: (id: string) => ({
      url: `/cover-letter/${id}`,
      method: "DELETE",
    }),
    invalidatesTags: ["CoverLetter"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
