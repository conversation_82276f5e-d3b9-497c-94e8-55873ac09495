import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../auth/baseQuery";
import { createCoverLetterByRoleEndpoint } from "./endpoints/createCoverLetterByRole";
import { createCoverLetterByJdEndpoint } from "./endpoints/createCoverLetterByJd";
import { getCoverLetterByIdEndpoint } from "./endpoints/getCoverLetterById";
import { getMyCoverLettersEndpoint } from "./endpoints/getMyCoverLetters";
import { uploadAvatarEndpoint } from "./endpoints/uploadAvatar";
import { updateCoverLetterEndpoint } from "./endpoints/updateCoverLetter";
import { deleteCoverLetterEndpoint } from "./endpoints/deleteCoverLetter";
import { getImageKitAuthEndpoint } from "./endpoints/getImageKitAuth";
import { evaluateCoverLetterEndpoint } from "./endpoints/evaluateCoverLetter";
import { getCoverLetterEvaluationEndpoint } from "./endpoints/getCoverLetterEvaluation";

export const coverLetterAI = createApi({
  reducerPath: "coverLetterAI",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["CoverLetter"],
  endpoints: (builder) => ({
    createCoverLetterByRole: createCoverLetterByRoleEndpoint(builder),
    createCoverLetterByJd: createCoverLetterByJdEndpoint(builder),
    getCoverLetterById: getCoverLetterByIdEndpoint(builder),
    getMyCoverLetters: getMyCoverLettersEndpoint(builder),
    uploadAvatar: uploadAvatarEndpoint(builder),
    updateCoverLetter: updateCoverLetterEndpoint(builder),
    deleteCoverLetter: deleteCoverLetterEndpoint(builder),
    getImageKitAuth: getImageKitAuthEndpoint(builder),
    evaluateCoverLetter: evaluateCoverLetterEndpoint(builder),
    getCoverLetterEvaluation: getCoverLetterEvaluationEndpoint(builder),
  }),
});

export const {
  useCreateCoverLetterByRoleMutation,
  useCreateCoverLetterByJdMutation,
  useGetCoverLetterByIdQuery,
  useGetMyCoverLettersQuery,
  useUploadAvatarMutation,
  useUpdateCoverLetterMutation,
  useDeleteCoverLetterMutation,
  useGetImageKitAuthQuery,
  useEvaluateCoverLetterMutation,
  useGetCoverLetterEvaluationQuery,
} = coverLetterAI;
