import { AiAgentRoadmapAiType } from "@/common/enums/agentType.enum";

export interface CreateRoadmapByRoleParams {
  roleDescription: string;
  cvFile: File;
  agentType?: AiAgentRoadmapAiType;
}

export interface CreateRoadmapByJdParams {
  cvFile: File;
  jdFile: File;
  agentType?: AiAgentRoadmapAiType;
}

export const createRoadmapByRoleFormData = (
  params: CreateRoadmapByRoleParams
): FormData => {
  const formData = new FormData();
  formData.append("roleDescription", params.roleDescription);
  formData.append("cvFile", params.cvFile);
  formData.append(
    "agentType",
    params.agentType || AiAgentRoadmapAiType.AI_ROADMAP_AI_ROLE
  );
  return formData;
};

export const createRoadmapByJdFormData = (
  params: CreateRoadmapByJdParams
): FormData => {
  const formData = new FormData();
  formData.append("cvFile", params.cvFile);
  formData.append("jdFile", params.jdFile);
  formData.append(
    "agentType",
    params.agentType || AiAgentRoadmapAiType.AI_ROADMAP_AI_JD
  );
  return formData;
};

export interface ApiResponse<T> {
  statusCode: number;
  message: string;
  data: T;
}
