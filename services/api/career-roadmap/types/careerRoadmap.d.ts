import {
  BaseQueryFn,
  <PERSON>tchA<PERSON><PERSON>,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/dist/query/endpointDefinitions";

// Define BaseQueryFn type
export type CareerRoadmapBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
>;

// Define builder type
export type BuilderType = EndpointBuilder<CareerRoadmapBaseQueryFn, "CareerRoadmap", "careerRoadmapApi">;

// Request types
export interface CreateRoadmapByRoleRequest {
  roleDescription: string;
  cvFile: File;
}

export interface CreateRoadmapByJdRequest {
  cvFile: File;
  jdFile: File;
}
