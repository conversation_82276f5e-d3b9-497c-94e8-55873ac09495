import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../auth/baseQuery";
import { createRoadmapByRoleEndpoint } from "./endpoints/createRoadmapByRole";
import { createRoadmapByJdEndpoint } from "./endpoints/createRoadmapByJd";
import { getRoadmapByIdEndpoint } from "./endpoints/getRoadmapById";
import { getMyRoadmapsEndpoint } from "./endpoints/getMyRoadmaps";
import { getAllRoadmapsEndpoint } from "./endpoints/getAllRoadmaps";
import { deleteRoadmapEndpoint } from "./endpoints/deleteRoadmap";

export const careerRoadmapApi = createApi({
  reducerPath: "careerRoadmapApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["CareerRoadmap"],
  endpoints: (builder) => ({
    createRoadmapByRole: createRoadmapByRoleEndpoint(builder),
    createRoadmapByJd: createRoadmapByJdEndpoint(builder),
    getRoadmapById: getRoadmapByIdEndpoint(builder),
    getMyRoadmaps: getMyRoadmapsEndpoint(builder),
    getAllRoadmaps: getAllRoadmapsEndpoint(builder),
    deleteRoadmap: deleteRoadmapEndpoint(builder),
  }),
});

export const {
  useCreateRoadmapByRoleMutation,
  useCreateRoadmapByJdMutation,
  useGetRoadmapByIdQuery,
  useGetMyRoadmapsQuery,
  useGetAllRoadmapsQuery,
  useDeleteRoadmapMutation,
} = careerRoadmapApi;
