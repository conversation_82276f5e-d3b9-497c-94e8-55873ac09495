import { BuilderType } from "../types/careerRoadmap.d";
import { ResponseRoadmapAiDto } from "@/common/types/career-roadmap";

export const getRoadmapByIdEndpoint = (builder: BuilderType) =>
  builder.query<ResponseRoadmapAiDto, string>({
    query: (id: string) => `/roadmap-ai/roadmap-ai/${id}`,
    providesTags: (_result: any, _error: any, id: string) => [{ type: "CareerRoadmap", id }],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
