import { BuilderType } from "../types/careerRoadmap.d";
import { ResponseRoadmapAiByRoleDescriptionDto } from "@/common/types/career-roadmap";

export const createRoadmapByRoleEndpoint = (builder: BuilderType) =>
  builder.mutation<ResponseRoadmapAiByRoleDescriptionDto, FormData>({
    query: (formData: FormData) => ({
      url: "/roadmap-ai/roadmap-ai-by-role-description",
      method: "POST",
      body: formData,
    }),
    invalidatesTags: ["CareerRoadmap"],    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
