import { BuilderType } from "../types/careerRoadmap.d";
import { ResponseRoadmapAiDto } from "@/common/types/career-roadmap";

export const getMyRoadmapsEndpoint = (builder: BuilderType) =>
  builder.query<ResponseRoadmapAiDto[], void>({
    query: () => "/roadmap-ai/my-roadmap-ai",
    providesTags: ["CareerRoadmap"],    transformResponse: (response: any) => {
      const data = response?.data ?? response;
      return Array.isArray(data) ? data : [];
    },
  });
