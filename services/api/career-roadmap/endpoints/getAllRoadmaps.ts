import { BuilderType } from "../types/careerRoadmap.d";
import { ResponseRoadmapAiDto } from "@/common/types/career-roadmap";

export const getAllRoadmapsEndpoint = (builder: BuilderType) =>
  builder.query<ResponseRoadmapAiDto[], void>({
    query: () => "/roadmap-ai/all-roadmap-ai",
    providesTags: ["CareerRoadmap"],    transformResponse: (response: any) => {
      const data = response?.data ?? response;
      return Array.isArray(data) ? data : [];
    },
  });
