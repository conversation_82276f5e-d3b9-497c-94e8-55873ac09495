import { BuilderType } from "../types/careerRoadmap.d";
import { ResponseRoadmapAiByJdDescriptionDto } from "@/common/types/career-roadmap";

export const createRoadmapByJdEndpoint = (builder: BuilderType) =>
  builder.mutation<ResponseRoadmapAiByJdDescriptionDto, FormData>({
    query: (formData: FormData) => ({
      url: "/roadmap-ai/roadmap-ai-by-jd-description",
      method: "POST",
      body: formData,
    }),
    invalidatesTags: ["CareerRoadmap"],    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
