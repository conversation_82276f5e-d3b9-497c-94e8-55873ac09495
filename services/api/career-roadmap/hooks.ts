// Temporary fix for RTK Query hook export issue

import { careerRoadmapApi } from './index';

// Re-export the hooks correctly
export const useGetMyRoadmapsQuery = careerRoadmapApi.endpoints.getMyRoadmaps.useQuery;
export const useGetRoadmapByIdQuery = careerRoadmapApi.endpoints.getRoadmapById.useQuery;
export const useGetAllRoadmapsQuery = careerRoadmapApi.endpoints.getAllRoadmaps.useQuery;
export const useCreateRoadmapByRoleMutation = careerRoadmapApi.endpoints.createRoadmapByRole.useMutation;
export const useCreateRoadmapByJdMutation = careerRoadmapApi.endpoints.createRoadmapByJd.useMutation;
export const useDeleteRoadmapMutation = careerRoadmapApi.endpoints.deleteRoadmap.useMutation;
