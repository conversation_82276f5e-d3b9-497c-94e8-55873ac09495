import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./baseQuery";
import { getMeEndpoint } from "./endpoints/getMe";
import { loginEndpoint } from "./endpoints/login";
import { registerEndpoint } from "./endpoints/register";
import { refreshTokenEndpoint } from "./endpoints/refreshToken";
import { createOtpEndpoint } from "./endpoints/createOtp";
import { verifyOtpEndpoint } from "./endpoints/verifyOtp";
import { forgotPasswordEndpoint } from "./endpoints/forgotPassword";
import { changePasswordEndpoint } from "./endpoints/changePassword";

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["User"],
  endpoints: (builder) => ({
    getMe: getMeEndpoint(builder),
    login: loginEndpoint(builder),
    register: registerEndpoint(builder),
    refreshToken: refreshTokenEndpoint(builder),
    createOtp: createOtpEndpoint(builder),
    verifyOtp: verifyOtpEndpoint(builder),
    forgotPassword: forgotPasswordEndpoint(builder),
    changePassword: changePasswordEndpoint(builder),
  }),
});

export const {
  useGetMeQuery,
  useLoginMutation,
  useRegisterMutation,
  useRefreshTokenMutation,
  useCreateOtpMutation,
  useVerifyOtpMutation,
  useForgotPasswordMutation,
  useChangePasswordMutation,
} = authApi;
