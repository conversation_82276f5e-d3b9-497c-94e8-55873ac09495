import { User } from "@/common/types/user";
import { setCredentials } from "@/redux/slices/authSlice";
import { RootState } from "@/redux/store";
import { BuilderType } from "../types/auth";

export const getMeEndpoint = (builder: BuilderType) =>
  builder.query<User, void>({
    query: () => "/users/profile",
    providesTags: ["User"],
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
    async onQueryStarted(
      arg: void,
      { dispatch, queryFulfilled, getState }: any
    ) {
      try {
        const { data: user } = await queryFulfilled;
        const currentState = (getState() as RootState).auth;

        if (user && currentState.accessToken && currentState.refreshToken) {
          dispatch(
            setCredentials({
              user: user,
              accessToken: currentState.accessToken,
              refreshToken: currentState.refreshToken,
            })
          );
        }
      } catch (error) {
        console.error("Error in getMe onQueryStarted:", error);
      }
    },
  });
