import { BuilderType } from "../types/auth";

export const loginEndpoint = (builder: BuilderType) =>
  builder.mutation<
    { user: any; accessToken: string; refreshToken: string },
    { email: string; password: string }
  >({
    query: (credentials: any) => ({
      url: "/auth/login",
      method: "POST",
      body: credentials,
    }),
    transformResponse: (response: any) => {
      console.log("Login API response:", response);
      return response?.data ?? response;
    },
  });
