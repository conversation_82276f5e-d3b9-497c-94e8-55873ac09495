import { BuilderType } from "../types/auth";

export const registerEndpoint = (builder: BuilderType) =>
  builder.mutation<
    { user: any; accessToken: string; refreshToken: string },
    { email: string; password: string; firstName: string; lastName: string }
  >({
    query: (userData: any) => ({
      url: "/auth/register",
      method: "POST",
      body: userData,
    }),
    transformResponse: (response: any) => {
      return response?.data ?? response;
    },
  });
