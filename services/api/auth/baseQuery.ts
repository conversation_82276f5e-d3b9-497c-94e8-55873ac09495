import { RootState } from "@/redux/store";
import {
  fetchBaseQuery,
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import { logout, updateAccessToken } from "@/redux/slices/authSlice";
import { isTokenExpiringSoon, isPageActive } from "./utils/tokenUtils";

const baseQuery = fetchBaseQuery({
  baseUrl: `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1`,
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).auth.accessToken;
    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

export const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;
  const { accessToken, refreshToken } = state.auth;

  // Only refresh token if page is active and token is about to expire
  if (
    accessToken &&
    isTokenExpiringSoon(accessToken) &&
    refreshToken &&
    isPageActive()
  ) {
    try {
      const refreshResult = await baseQuery(
        {
          url: "/auth/refresh-token",
          method: "POST",
          body: { refreshToken },
        },
        api,
        extraOptions
      );

      if (refreshResult.data) {
        const responseData =
          (refreshResult.data as any)?.data ?? refreshResult.data;
        const { accessToken: newAccessToken } = responseData as {
          accessToken: string;
        };
        api.dispatch(updateAccessToken(newAccessToken));
      } else {
        api.dispatch(logout());
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      }
    } catch (error) {
      console.error("Error refreshing token:", error);
      api.dispatch(logout());
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
  } else if (
    accessToken &&
    isTokenExpiringSoon(accessToken) &&
    !isPageActive()
  ) {
    api.dispatch(logout());
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
  }

  let result = await baseQuery(args, api, extraOptions);

  if (result.error && result.error.status === 401) {
    if (refreshToken && isPageActive()) {
      const refreshResult = await baseQuery(
        {
          url: "/auth/refresh-token",
          method: "POST",
          body: { refreshToken },
        },
        api,
        extraOptions
      );

      if (refreshResult.data) {
        const responseData =
          (refreshResult.data as any)?.data ?? refreshResult.data;
        const { accessToken: newAccessToken } = responseData as {
          accessToken: string;
        };
        api.dispatch(updateAccessToken(newAccessToken));
        result = await baseQuery(args, api, extraOptions);
      } else {
        api.dispatch(logout());
        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      }
    } else {
      api.dispatch(logout());
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
  }

  return result;
};
