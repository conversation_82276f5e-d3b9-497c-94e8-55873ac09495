import {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/dist/query/endpointDefinitions";

// Define BaseQueryFn type
export type AuthBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
>;

// Define builder typeNextRequest
export type BuilderType = EndpointBuilder<AuthBaseQueryFn, "User", "authApi">;
