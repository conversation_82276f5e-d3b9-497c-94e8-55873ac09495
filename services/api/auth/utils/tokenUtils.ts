export const TOKEN_REFRESH_THRESHOLD = 60000; // 1 phút

export const isTokenExpiringSoon = (token: string | null): boolean => {
  if (!token) return true;

  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const expiry = payload.exp * 1000;
    const now = Date.now();
    return expiry - now <= TOKEN_REFRESH_THRESHOLD;
  } catch {
    return true;
  }
};

// Check user is active
export const isPageActive = (): boolean => {
  if (typeof window === "undefined") return false; 
  return document.visibilityState === "visible";
};