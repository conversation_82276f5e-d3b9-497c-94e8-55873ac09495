import {
  Base<PERSON>ueryFn,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";
import { EndpointBuilder } from "@reduxjs/toolkit/dist/query/endpointDefinitions";

// Define BaseQueryFn type
export type ProfileBaseQueryFn = BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
>;

// Define builder type
export type BuilderType = EndpointBuilder<ProfileBaseQueryFn, "Profile", "profileApi">;

// Profile update request interface
export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  bio?: string;
  phone?: string;
  dateOfBirth?: string;
  address?: string;
}

// Upload avatar response interface
export interface UploadAvatarResponse {
  statusCode: number;
  message: string;
  data: {
    avatarUrl: string;
  };
}

// Profile API response interface
export interface ProfileResponse {
  statusCode: number;
  message: string;
  data: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    role?: string;
    bio?: string;
    avatar?: string;
    phone?: string;
    dateOfBirth?: string;
    address?: string;
    createdAt?: string;
    updatedAt?: string;
  };
}
