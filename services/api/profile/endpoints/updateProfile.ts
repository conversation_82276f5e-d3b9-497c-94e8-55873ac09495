import { User } from "@/common/types/user";
import { BuilderType, UpdateProfileRequest } from "../types/profile";

export const updateProfileEndpoint = (builder: BuilderType) =>
    builder.mutation<User, UpdateProfileRequest>({
        query: (profileData: UpdateProfileRequest) => ({
            url: "/profiles",
            method: "PUT",
            body: profileData,
        }),
        invalidatesTags: ["Profile"],
        transformResponse: (response: any) => {
            return response?.data ?? response;
        },
    });
