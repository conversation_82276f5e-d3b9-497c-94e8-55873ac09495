import { BuilderType, UploadAvatarResponse } from "../types/profile";

export const uploadAvatarEndpoint = (builder: BuilderType) =>
    builder.mutation<UploadAvatarResponse, FormData>({
        query: (formData: FormData) => ({
            url: "/profiles/upload-avatar",
            method: "POST",
            body: formData,
        }),
        invalidatesTags: ["Profile"],
        transformResponse: (response: any) => {
            return response;
        },
    });
