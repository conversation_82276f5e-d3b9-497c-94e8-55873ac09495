import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./baseQuery";
import { getProfileEndpoint } from "./endpoints/getProfile";
import { updateProfileEndpoint } from "./endpoints/updateProfile";
import { uploadAvatarEndpoint } from "./endpoints/uploadAvatar";

export const profileApi = createApi({
    reducerPath: "profileApi",
    baseQuery: baseQueryWithReauth,
    tagTypes: ["Profile"],
    endpoints: (builder) => ({
        getProfile: getProfileEndpoint(builder),
        updateProfile: updateProfileEndpoint(builder),
        uploadAvatar: uploadAvatarEndpoint(builder),
        deleteAvatar: builder.mutation({
            query: (id: string) => ({
                url: `/profiles/avatar/${id}`,
                method: "DELETE",
            }),
            invalidatesTags: ["Profile"],
        }),
    }),
});

export const {
    useGetProfileQuery,
    useUpdateProfileMutation,
    useUploadAvatarMutation,
    useDeleteAvatarMutation,
} = profileApi;
