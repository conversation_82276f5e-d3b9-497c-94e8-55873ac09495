"use client";

import React from "react";
import TestimonialsCarousel from "@/components/TestimonialsCarousel";
import {
    Navigation,
    HeroSection,
    FeaturesSection,
    PricingSection,
    Footer
} from "@/features/landing/components";




export default function Home() {
    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white overflow-hidden">
            {/* Background Effects */}
            <div className="fixed inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-0 right-0 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse transform translate-x-1/2 -translate-y-1/2"></div>
                <div className="absolute bottom-0 left-0 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse transform -translate-x-1/2 translate-y-1/2"></div>
                <div className="absolute top-40 left-1/2 w-60 h-60 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse transform -translate-x-1/2"></div>
            </div>

            <main className="relative z-10 space-y-12 sm:space-y-16 md:space-y-20 lg:space-y-24 xl:space-y-32 w-full max-w-full">
                {/* Navigation */}
                <Navigation />
                {/* Hero Section */}
                <HeroSection />

                {/* Features Section */}
                <FeaturesSection />

                {/* Testimonials & Pricing Section - Closer spacing */}
                <div className="space-y-8 sm:space-y-10 md:space-y-12">
                    {/* Testimonials Section */}
                    <TestimonialsCarousel />
                    {/* Pricing Section */}
                    <PricingSection />
                </div>
                {/* Footer */}
                <Footer />
            </main>
        </div>
    );
}
