"use client";

import { useRouter } from "next/navigation";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { toast } from "sonner";

import { useRegisterMutation } from "@/services/api/auth";
import RegisterForm from "@/features/auth/pages/RegisterForm";
import { RegisterFormData } from "@/features/auth/schemas/registerSchema";

export default function RegisterPage() {
  const router = useRouter();
  const [register, { isLoading }] = useRegisterMutation();

  const onSubmit = async (data: RegisterFormData) => {
    try {
      const { confirmPassword, ...registerData } = data;
      console.log(confirmPassword);
      await register(registerData).unwrap();

      toast.success("Đăng ký thành công!");
      router.replace("/login");
    } catch (error: any) {
      toast.error(error?.data?.message ?? "Đăng ký thất bại");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="space-y-1 pb-4">
          <CardTitle className="text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Đăng ký
          </CardTitle>
          <CardDescription className="text-center text-muted-foreground">
            Tạo tài khoản mới để bắt đầu hành trình của bạn
          </CardDescription>
        </CardHeader>
        <RegisterForm onSubmit={onSubmit} isLoading={isLoading} />
      </Card>
    </div>
  );
}
