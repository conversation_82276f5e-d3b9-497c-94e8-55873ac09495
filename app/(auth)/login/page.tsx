"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { toast } from "sonner";
import { RootState } from "@/redux/store";
import { setCredentials } from "@/redux/slices/authSlice";
import { useLoginMutation, useGetMeQuery } from "@/services/api/auth";
import LoginForm from "@/features/auth/pages/LoginForm";
import LoadingSpinner from "@/features/auth/components/LoadingSpinner";
import { useGoogleAuthCallback } from "@/features/auth/hooks/useGoogleAuthCallback";

export default function LoginPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [login, { isLoading }] = useLoginMutation();

  const { isAuthenticated, authLoaded, user, accessToken } = useSelector(
    (state: RootState) => state.auth
  );

  // Custom hook for Google OAuth
  const { isProcessingGoogleAuth, isLoadingUser } = useGoogleAuthCallback();

  // Get user info after login
  const { data: userData, isLoading: isLoadingUserData } = useGetMeQuery(
    undefined,
    {
      skip: !accessToken || !!user || !isAuthenticated,
    }
  );

  // Update user data when received
  useEffect(() => {
    if (userData && isAuthenticated && accessToken) {
      dispatch(
        setCredentials({
          user: userData,
          accessToken,
          refreshToken: localStorage.getItem("refreshToken") ?? "",
        })
      );
    }
  }, [userData, isAuthenticated, accessToken, dispatch]);

  // Redirect to dashboard if authenticated
  useEffect(() => {
    if (isProcessingGoogleAuth || isLoadingUserData) {
      return;
    }

    if (isAuthenticated && authLoaded && user) {
      router.push("/dashboard");
    }
  }, [
    isAuthenticated,
    user,
    authLoaded,
    router,
    isProcessingGoogleAuth,
    isLoadingUserData,
  ]);

  const onSubmit = async (data: { email: string; password: string }) => {
    try {
      const result = await login(data).unwrap();
      const { accessToken, refreshToken } = result.data ?? result;

      if (!accessToken || !refreshToken) {
        throw new Error("No tokens received from server");
      }

      localStorage.setItem("accessToken", accessToken);
      localStorage.setItem("refreshToken", refreshToken);

      dispatch(
        setCredentials({
          user: null,
          accessToken,
          refreshToken,
        })
      );

      toast.success("Đăng nhập thành công!");
    } catch (error: any) {
      console.error("Login error:", error);
      toast.error(error?.data?.message ?? "Đăng nhập thất bại");
    }
  };

  if (
    isLoadingUser ||
    isProcessingGoogleAuth ||
    (isAuthenticated && !user && isLoadingUserData)
  ) {
    return <LoadingSpinner message={"Đang tải thông tin user..."} />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md shadow-xl border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="space-y-1 pb-4">
          <CardTitle className="text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            Đăng nhập
          </CardTitle>
          <CardDescription className="text-center text-muted-foreground">
            Chào mừng bạn trở lại! Vui lòng đăng nhập vào tài khoản của bạn
          </CardDescription>
        </CardHeader>
        <LoginForm onSubmit={onSubmit} isLoading={isLoading} />
      </Card>
    </div>
  );
}
