"use client";

import React from "react";
import { useParams, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Calendar,
  FileText,
  Target,
  Lightbulb,
  CheckCircle2,
  Clock,
  Sparkles,
} from "lucide-react";
import { useGetRoadmapByIdQuery } from "@/services/api/career-roadmap/hooks";
import { AiAgentRoadmapAiType } from "@/common/enums/agentType.enum";
import RoadmapVisualization from "@/features/career-roadmap/components/RoadmapVisualization";
import ImagePreview from "@/features/career-roadmap/components/ImagePreview";

export default function RoadmapDetailPage() {
  const params = useParams();
  const router = useRouter();
  const roadmapId = params.id as string;
  const { data: roadmap, isLoading, error } = useGetRoadmapByIdQuery(roadmapId);
  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };  const getAgentTypeBadge = (type: AiAgentRoadmapAiType) => {
    switch (type) {
      case AiAgentRoadmapAiType.AI_ROADMAP_AI_ROLE:
      case "AI_ROADMAP_AI_ROLE":
        return (
          <Badge className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-none">
            <Sparkles className="w-3 h-3 mr-1" />
            Role Based AI
          </Badge>
        );
      case AiAgentRoadmapAiType.AI_ROADMAP_AI_JD:
      case "AI_ROADMAP_AI_JD":
        return (
          <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-none">
            <FileText className="w-3 h-3 mr-1" />
            Job Description AI
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="border-gray-300">
            Unknown ({type})
          </Badge>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="animate-pulse space-y-6"
          >
            <div className="h-8 bg-gray-200 rounded-xl w-1/4"></div>
            <div className="h-32 bg-gray-200 rounded-xl"></div>
            <div className="h-64 bg-gray-200 rounded-xl"></div>
          </motion.div>
        </div>
      </div>
    );
  }

  if (error || !roadmap) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="text-center py-16 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardContent className="space-y-6">
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <FileText className="w-8 h-8 text-red-600" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-gray-900">
                    Roadmap not found
                  </h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    The roadmap does not exist or has been deleted.
                  </p>
                </div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={() => router.push("/career-roadmap")}
                    className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white shadow-lg"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Go back
                  </Button>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto px-4 py-8 space-y-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {/* Header Section */}
          <div className="flex items-start justify-between">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                onClick={() => router.push("/career-roadmap")}
                className="flex items-center gap-2 bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <ArrowLeft className="w-4 h-4" />
                Go back
              </Button>
            </motion.div>
            {getAgentTypeBadge(roadmap.agentType)}
          </div>

          {/* Title Section */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {roadmap.content.roadmapTitle ||
                roadmap.jobPosition ||
                "AI Career Roadmap"}
            </h1>

            {roadmap.content?.description && (
              <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {roadmap.content.description}
              </p>
            )}

            <div className="flex flex-wrap justify-center gap-4">
              {roadmap.createdAt && (
                <div className="flex items-center gap-2 px-3 py-1 bg-white/70 rounded-full text-sm text-gray-600">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  <span>Created {formatDate(roadmap.createdAt)}</span>
                </div>
              )}
              {roadmap.content?.duration && (
                <div className="flex items-center gap-2 px-3 py-1 bg-white/70 rounded-full text-sm text-gray-600">
                  <Clock className="w-4 h-4 text-green-500" />
                  <span>{roadmap.content.duration}</span>
                </div>
              )}
              {roadmap.content?.skills &&
                Array.isArray(roadmap.content.skills) && (
                  <div className="flex items-center gap-2 px-3 py-1 bg-white/70 rounded-full text-sm text-gray-600">
                    <Lightbulb className="w-4 h-4 text-yellow-500" />
                    <span>{roadmap.content.skills.length} skills</span>
                  </div>
                )}
            </div>
          </div>
        </motion.div>        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.1 }}
          className="space-y-8"
        >
          {/* Visual Roadmap */}
          {roadmap.content?.initialNodes &&
            roadmap.content.initialNodes.length > 0 && (
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Target className="w-4 h-4 text-blue-600" />
                    </div>
                    Visual Roadmap
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-xl p-4">
                    <RoadmapVisualization
                      nodes={roadmap.content.initialNodes}
                      edges={roadmap.content.initialEdges}
                      title=""
                      className="w-full h-full"
                    />
                  </div>
                </CardContent>
              </Card>
            )}

          {/* Overview and Skills */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {roadmap.content?.summary && (
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Target className="w-4 h-4 text-blue-600" />
                    </div>
                    Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-200 rounded-xl p-4">
                    <p className="text-blue-800 leading-relaxed">
                      {typeof roadmap.content.summary === "string"
                        ? roadmap.content.summary
                        : JSON.stringify(roadmap.content.summary, null, 2)}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {roadmap.content?.skills &&
              Array.isArray(roadmap.content.skills) && (
                <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <Lightbulb className="w-4 h-4 text-yellow-600" />
                      </div>
                      Required Skills
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {roadmap.content.skills.map(
                        (skill: string, index: number) => (
                          <Badge
                            key={index}
                            className="bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200 hover:from-yellow-200 hover:to-orange-200 transition-all duration-200"
                          >
                            {skill}
                          </Badge>
                        )
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
          </div>          {/* Learning Path */}
          {roadmap.content?.steps && Array.isArray(roadmap.content.steps) && (
            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <CheckCircle2 className="w-4 h-4 text-emerald-600" />
                  </div>
                  Detailed Learning Path
                </CardTitle>
                <CardDescription>
                  {roadmap.content.steps.length} steps to achieve your goal
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {roadmap.content.steps.map((step: any, index: number) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex gap-4 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl hover:from-blue-50 hover:to-cyan-50 transition-all duration-300"
                    >
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-full flex items-center justify-center font-semibold text-sm shadow-lg">
                          {index + 1}
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-2">
                          {typeof step === "string"
                            ? step
                            : step.title || `Step ${index + 1}`}
                        </h4>
                        {typeof step === "object" && step.description && (
                          <p className="text-gray-700 mb-3 leading-relaxed">
                            {step.description}
                          </p>
                        )}
                        {typeof step === "object" && step.duration && (
                          <div className="flex items-center gap-2 text-sm text-blue-600 bg-blue-100 px-3 py-1 rounded-full w-fit">
                            <Clock className="w-4 h-4" />
                            {step.duration}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* File Information */}
          <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-xl">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <FileText className="w-4 h-4 text-green-600" />
                </div>
                File Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-4">
                <ImagePreview
                  cvFileUrl={roadmap.cvFileUrl}
                  jdFileUrl={roadmap.jdFileUrl}
                  className="w-full"
                  compact={false}
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
