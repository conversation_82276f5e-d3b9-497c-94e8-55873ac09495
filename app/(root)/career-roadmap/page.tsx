"use client";

import { useState, useMemo } from "react";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
    Plus,
    FileText,
    Target,
    Brain,
    ChevronLeft,
    ChevronRight,
    Sparkles,
    ArrowRight,
} from "lucide-react";
import {
    useGetMyRoadmapsQuery,
    useDeleteRoadmapMutation,
} from "@/services/api/career-roadmap/hooks";
import { CreateRoadmapModal } from "@/features/career-roadmap/components/CreateRoadmapModal";
import { RoadmapCard } from "@/features/career-roadmap/components/RoadmapCard";
import { RoadmapSkeleton } from "@/features/career-roadmap/components/RoadmapSkeleton";
import DeleteRoadmapDialog from "@/features/career-roadmap/components/DeleteRoadmapDialog";
import { ResponseRoadmapAiDto } from "@/common/types/career-roadmap";

// Simple animation variants for framer motion
const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            delayChildren: 0.1,
            staggerChildren: 0.1,
        },
    },
};

const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
        y: 0,
        opacity: 1,
        transition: {
            duration: 0.5,
        },
    },
};

export default function CareerRoadmapPage() {
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const ITEMS_PER_PAGE = 6;

    const [deleteDialog, setDeleteDialog] = useState<{
        open: boolean;
        roadmap: ResponseRoadmapAiDto | null;
    }>({
        open: false,
        roadmap: null,
    });    const { data: roadmaps, isLoading, refetch } = useGetMyRoadmapsQuery();
    const safeRoadmaps = useMemo(() => roadmaps || [], [roadmaps]);
    const safeIsLoading = isLoading || false;

    // Pagination logic
    const paginationData = useMemo(() => {
        const totalItems = safeRoadmaps.length;
        const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = startIndex + ITEMS_PER_PAGE;
        const currentItems = safeRoadmaps.slice(startIndex, endIndex);

        return {
            currentItems,
            totalPages,
            totalItems,
            hasNextPage: currentPage < totalPages,
            hasPrevPage: currentPage > 1,
        };
    }, [safeRoadmaps, currentPage]);

    const [deleteRoadmap, { isLoading: isDeleting }] =
        useDeleteRoadmapMutation();

    const handleDeleteRoadmap = async (id: string) => {
        const roadmapToDelete = safeRoadmaps.find(
            (r: ResponseRoadmapAiDto) => r._id === id
        );
        if (roadmapToDelete) {
            setDeleteDialog({
                open: true,
                roadmap: roadmapToDelete,
            });
        }
    };
    const handleConfirmDelete = async () => {
        if (deleteDialog.roadmap) {
            try {
                await deleteRoadmap(deleteDialog.roadmap._id).unwrap();
                toast.success("Roadmap deleted successfully!", {
                    description: "The roadmap has been permanently removed.",
                });
                // Reset to page 1 if current page would be empty after deletion
                const newTotalItems = paginationData.totalItems - 1;
                const newTotalPages = Math.ceil(newTotalItems / ITEMS_PER_PAGE);
                if (currentPage > newTotalPages && newTotalPages > 0) {
                    setCurrentPage(newTotalPages);
                }
                refetch();
            } catch (error) {
                console.error("Error deleting roadmap:", error);
                toast.error("Failed to delete roadmap", {
                    description:
                        "Please try again or contact support if the problem persists.",
                });
            } finally {
                setDeleteDialog({ open: false, roadmap: null });
            }
        }
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        // Scroll to top when changing pages
        window.scrollTo({ top: 0, behavior: "smooth" });
    };

    const handlePrevPage = () => {
        if (paginationData.hasPrevPage) {
            handlePageChange(currentPage - 1);
        }
    };

    const handleNextPage = () => {
        if (paginationData.hasNextPage) {
            handlePageChange(currentPage + 1);
        }
    };
    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50"
        >
            <div className="container mx-auto px-4 py-8 space-y-8">
                {/* Hero Section */}
                <motion.div variants={itemVariants} className="space-y-4">
                    <div className="text-center space-y-4">
                        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Career Roadmap
                        </h1>
                        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                            Create personalized career development roadmaps
                            based on your CV and target job descriptions
                        </p>
                    </div>
                    <div className="flex justify-center">
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Button
                                onClick={() => setIsCreateModalOpen(true)}
                                className="h-14 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                <Sparkles className="mr-2 h-5 w-5" />
                                Create New Roadmap
                                <ArrowRight className="ml-2 h-5 w-5" />
                            </Button>
                        </motion.div>
                    </div>
                </motion.div>
                {/* Metrics Cards */}
                <motion.div
                    variants={itemVariants}
                    className="grid grid-cols-1 md:grid-cols-3 gap-6"
                >
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-600">
                                Total Roadmaps
                            </CardTitle>
                            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                <FileText className="h-4 w-4 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-gray-900">
                                {safeRoadmaps?.length || 0}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                                Roadmaps created
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-600">
                                Active Goals
                            </CardTitle>
                            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                                <Target className="h-4 w-4 text-green-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-gray-900">
                                {safeRoadmaps?.filter(
                                    (r: ResponseRoadmapAiDto) =>
                                        r.content?.initialNodes?.length > 0
                                ).length || 0}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                                With detailed roadmaps
                            </p>
                        </CardContent>
                    </Card>

                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium text-gray-600">
                                AI Generated
                            </CardTitle>
                            <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                                <Brain className="h-4 w-4 text-purple-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-gray-900">
                                {safeRoadmaps?.length || 0}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                                Generated by AI
                            </p>
                        </CardContent>
                    </Card>
                </motion.div>{" "}
                {/* Roadmaps Section */}
                <motion.div variants={itemVariants} className="space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold text-gray-900">
                            Your Roadmaps
                        </h2>
                        {safeRoadmaps && safeRoadmaps.length > 0 && (
                            <div className="flex items-center gap-4">
                                <Badge
                                    variant="secondary"
                                    className="bg-blue-100 text-blue-700"
                                >
                                    {paginationData.totalItems} total roadmaps
                                </Badge>
                                {paginationData.totalPages > 1 && (
                                    <div className="text-sm text-gray-500">
                                        Page {currentPage} of{" "}
                                        {paginationData.totalPages}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>{" "}
                    {safeIsLoading ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {[...Array(6)].map((_, i) => (
                                <RoadmapSkeleton key={i} />
                            ))}
                        </div>
                    ) : safeRoadmaps && safeRoadmaps.length > 0 ? (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {paginationData.currentItems.map(
                                    (roadmap: ResponseRoadmapAiDto) => (
                                        <RoadmapCard
                                            key={roadmap._id}
                                            roadmap={roadmap}
                                            onDelete={handleDeleteRoadmap}
                                        />
                                    )
                                )}
                            </div>{" "}
                            {/* Pagination Controls */}
                            {paginationData.totalPages > 1 && (
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.3, delay: 0.2 }}
                                    className="space-y-8 mt-12"
                                >
                                    {/* Info Section */}
                                    <div className="flex items-center justify-center">
                                        <div className="px-6 py-3 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 shadow-lg">
                                            <div className="text-sm text-gray-600 font-medium">
                                                Showing{" "}
                                                <span className="text-blue-600 font-semibold">
                                                    {(currentPage - 1) *
                                                        ITEMS_PER_PAGE +
                                                        1}
                                                </span>{" "}
                                                to{" "}
                                                <span className="text-blue-600 font-semibold">
                                                    {Math.min(
                                                        currentPage *
                                                            ITEMS_PER_PAGE,
                                                        paginationData.totalItems
                                                    )}
                                                </span>{" "}
                                                of{" "}
                                                <span className="text-blue-600 font-semibold">
                                                    {paginationData.totalItems}
                                                </span>{" "}
                                                roadmaps
                                            </div>
                                        </div>
                                    </div>

                                    {/* Navigation Controls */}
                                    <div className="flex items-center justify-center">
                                        <div className="flex items-center gap-3 p-3 bg-white/80 backdrop-blur-sm rounded-3xl border border-gray-200/60 shadow-lg">
                                            {/* Previous Button */}
                                            <motion.div
                                                whileHover={{
                                                    scale: paginationData.hasPrevPage
                                                        ? 1.05
                                                        : 1,
                                                }}
                                                whileTap={{
                                                    scale: paginationData.hasPrevPage
                                                        ? 0.95
                                                        : 1,
                                                }}
                                            >
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={handlePrevPage}
                                                    disabled={
                                                        !paginationData.hasPrevPage
                                                    }
                                                    className={`gap-2 h-11 px-5 rounded-2xl font-medium transition-all duration-300 ${
                                                        paginationData.hasPrevPage
                                                            ? "hover:bg-gradient-to-r hover:from-blue-500 hover:to-cyan-500 hover:text-white hover:shadow-lg text-gray-700"
                                                            : "text-gray-400 cursor-not-allowed opacity-50"
                                                    }`}
                                                >
                                                    <ChevronLeft className="w-4 h-4" />
                                                    <span className="hidden sm:inline">
                                                        Previous
                                                    </span>
                                                </Button>
                                            </motion.div>
                                            {/* Page Numbers */}
                                            <div className="flex items-center gap-1 sm:gap-2 mx-1 sm:mx-2">
                                                {(() => {
                                                    const maxVisiblePages = 5; // Default to 5, responsive will be handled by CSS
                                                    const totalPages =
                                                        paginationData.totalPages;

                                                    if (
                                                        totalPages <=
                                                        maxVisiblePages
                                                    ) {
                                                        return Array.from(
                                                            {
                                                                length: totalPages,
                                                            },
                                                            (_, i) => i + 1
                                                        ).map((page) => (
                                                            <motion.div
                                                                key={page}
                                                                whileHover={{
                                                                    scale: 1.1,
                                                                }}
                                                                whileTap={{
                                                                    scale: 0.9,
                                                                }}
                                                                className={
                                                                    page > 3
                                                                        ? "hidden sm:block"
                                                                        : ""
                                                                } // Hide some pages on mobile
                                                            >
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() =>
                                                                        handlePageChange(
                                                                            page
                                                                        )
                                                                    }
                                                                    className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                                                        currentPage ===
                                                                        page
                                                                            ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                                            : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                                                    }`}
                                                                >
                                                                    {page}
                                                                </Button>
                                                            </motion.div>
                                                        ));
                                                    }

                                                    const pages = [];

                                                    // First page
                                                    pages.push(
                                                        <motion.div
                                                            key={1}
                                                            whileHover={{
                                                                scale: 1.1,
                                                            }}
                                                            whileTap={{
                                                                scale: 0.9,
                                                            }}
                                                        >
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() =>
                                                                    handlePageChange(
                                                                        1
                                                                    )
                                                                }
                                                                className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                                                    currentPage ===
                                                                    1
                                                                        ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                                        : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                                                }`}
                                                            >
                                                                1
                                                            </Button>
                                                        </motion.div>
                                                    );

                                                    // Left ellipsis
                                                    if (currentPage > 3) {
                                                        pages.push(
                                                            <div
                                                                key="ellipsis1"
                                                                className="flex items-center justify-center w-10 h-10 sm:w-11 sm:h-11"
                                                            >
                                                                <span className="text-gray-400 font-medium text-sm sm:text-lg">
                                                                    •••
                                                                </span>
                                                            </div>
                                                        );
                                                    }

                                                    // Middle pages
                                                    const start = Math.max(
                                                        2,
                                                        currentPage - 1
                                                    );
                                                    const end = Math.min(
                                                        totalPages - 1,
                                                        currentPage + 1
                                                    );

                                                    for (
                                                        let i = start;
                                                        i <= end;
                                                        i++
                                                    ) {
                                                        if (
                                                            i !== 1 &&
                                                            i !== totalPages
                                                        ) {
                                                            pages.push(
                                                                <motion.div
                                                                    key={i}
                                                                    whileHover={{
                                                                        scale: 1.1,
                                                                    }}
                                                                    whileTap={{
                                                                        scale: 0.9,
                                                                    }}
                                                                >
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={() =>
                                                                            handlePageChange(
                                                                                i
                                                                            )
                                                                        }
                                                                        className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                                                            currentPage ===
                                                                            i
                                                                                ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                                                : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                                                        }`}
                                                                    >
                                                                        {i}
                                                                    </Button>
                                                                </motion.div>
                                                            );
                                                        }
                                                    }

                                                    // Right ellipsis
                                                    if (
                                                        currentPage <
                                                        totalPages - 2
                                                    ) {
                                                        pages.push(
                                                            <div
                                                                key="ellipsis2"
                                                                className="flex items-center justify-center w-10 h-10 sm:w-11 sm:h-11"
                                                            >
                                                                <span className="text-gray-400 font-medium text-sm sm:text-lg">
                                                                    •••
                                                                </span>
                                                            </div>
                                                        );
                                                    }

                                                    // Last page
                                                    if (totalPages > 1) {
                                                        pages.push(
                                                            <motion.div
                                                                key={totalPages}
                                                                whileHover={{
                                                                    scale: 1.1,
                                                                }}
                                                                whileTap={{
                                                                    scale: 0.9,
                                                                }}
                                                            >
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() =>
                                                                        handlePageChange(
                                                                            totalPages
                                                                        )
                                                                    }
                                                                    className={`w-10 h-10 sm:w-11 sm:h-11 p-0 rounded-xl font-semibold transition-all duration-300 ${
                                                                        currentPage ===
                                                                        totalPages
                                                                            ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:shadow-xl hover:from-blue-700 hover:to-cyan-700"
                                                                            : "hover:bg-gradient-to-r hover:from-blue-100 hover:to-cyan-100 hover:text-blue-700 text-gray-600"
                                                                    }`}
                                                                >
                                                                    {totalPages}
                                                                </Button>
                                                            </motion.div>
                                                        );
                                                    }

                                                    return pages;
                                                })()}
                                            </div>

                                            {/* Next Button */}
                                            <motion.div
                                                whileHover={{
                                                    scale: paginationData.hasNextPage
                                                        ? 1.05
                                                        : 1,
                                                }}
                                                whileTap={{
                                                    scale: paginationData.hasNextPage
                                                        ? 0.95
                                                        : 1,
                                                }}
                                            >
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={handleNextPage}
                                                    disabled={
                                                        !paginationData.hasNextPage
                                                    }
                                                    className={`gap-2 h-11 px-5 rounded-2xl font-medium transition-all duration-300 ${
                                                        paginationData.hasNextPage
                                                            ? "hover:bg-gradient-to-r hover:from-blue-500 hover:to-cyan-500 hover:text-white hover:shadow-lg text-gray-700"
                                                            : "text-gray-400 cursor-not-allowed opacity-50"
                                                    }`}
                                                >
                                                    <span className="hidden sm:inline">
                                                        Next
                                                    </span>
                                                    <ChevronRight className="w-4 h-4" />
                                                </Button>
                                            </motion.div>
                                        </div>
                                    </div>
                                </motion.div>
                            )}
                        </>
                    ) : (
                        <Card className="text-center py-16 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                            <CardContent className="space-y-6">
                                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                                    <FileText className="w-8 h-8 text-blue-600" />
                                </div>
                                <div className="space-y-2">
                                    <h3 className="text-xl font-semibold text-gray-900">
                                        No roadmaps yet
                                    </h3>
                                    <p className="text-gray-500 max-w-md mx-auto">
                                        You haven&apos;t created any roadmaps
                                        yet. Let&apos;s start creating your
                                        first roadmap!
                                    </p>
                                </div>
                                <motion.div
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                >
                                    <Button
                                        onClick={() =>
                                            setIsCreateModalOpen(true)
                                        }
                                        className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white shadow-lg"
                                    >
                                        <Plus className="w-4 h-4 mr-2" />
                                        Create First Roadmap
                                    </Button>
                                </motion.div>
                            </CardContent>
                        </Card>
                    )}
                </motion.div>
            </div>

            {/* Modals */}
            <CreateRoadmapModal
                isOpen={isCreateModalOpen}
                onClose={() => setIsCreateModalOpen(false)}
                onSuccess={() => {
                    setIsCreateModalOpen(false);
                    setCurrentPage(1);
                    refetch();
                }}
            />
            <DeleteRoadmapDialog
                open={deleteDialog.open}
                onOpenChange={(open: boolean) =>
                    setDeleteDialog({
                        open,
                        roadmap: open ? deleteDialog.roadmap : null,
                    })
                }
                roadmap={deleteDialog.roadmap}
                onConfirm={handleConfirmDelete}
                isDeleting={isDeleting}
            />
        </motion.div>
    );
}
