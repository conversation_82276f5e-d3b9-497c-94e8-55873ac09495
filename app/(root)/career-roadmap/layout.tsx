"use client";

import AuthGuard from "@/layouts/auth/AuthGuard";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/DashboardSidebar";
import { Header } from "@/components/DashboardHeader_K";

interface CareerRoadmapLayoutProps {
  children: React.ReactNode;
}

export default function CareerRoadmapLayout({ children }: CareerRoadmapLayoutProps) {
  return (
    <AuthGuard>
      <SidebarProvider>
        <AppSidebar />
        <div className="flex-1">
          <Header />
          {children}
        </div>
      </SidebarProvider>
    </AuthGuard>
  );
}
