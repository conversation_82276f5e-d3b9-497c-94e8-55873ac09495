"use client";

import { useParams, useSearchParams } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import CoverLetterTemp1 from "@/features/cover-letter/components/CoverLetterTemp1";
import CoverLetterTemp2 from "@/features/cover-letter/components/CoverLetterTemp2";
import CoverLetterTemp3 from "@/features/cover-letter/components/CoverLetterTemp3";

export default function CoverLetterTemplatePage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const coverLetterId = params.id as string;
  const templateParam = searchParams.get("template");

  // Get selected template from URL parameter or Redux state
  const selectedTemplate = useSelector(
    (state: RootState) => state.coverLetter.selectedTemplate
  );

  const template = templateParam || selectedTemplate;

  const renderTemplate = () => {
    switch (template) {
      case "temp1":
        return (
          <CoverLetterTemp1 coverLetterId={coverLetterId} templateId="temp1" />
        );
      case "temp2":
        return (
          <CoverLetterTemp2 coverLetterId={coverLetterId} templateId="temp2" />
        );
      case "temp3":
        return (
          <CoverLetterTemp3 coverLetterId={coverLetterId} templateId="temp3" />
        );
      default:
        return (
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="text-center">
              <p className="text-gray-600">Template not found</p>
            </div>
          </div>
        );
    }
  };

  return <div className="min-h-screen bg-gray-50">{renderTemplate()}</div>;
}
