"use client";

import React from "react";
import { useParams } from "next/navigation";
import {
  useGetCoverLetterEvaluationQuery,
  useGetCoverLetterByIdQuery,
} from "@/services/api/cover-letter";
import CoverLetterEvaluation from "@/features/cover-letter/components/CoverLetterEvaluation";
import { Loader2, AlertCircle } from "lucide-react";

const CoverLetterEvaluationPage = () => {
  const params = useParams();
  const coverLetterId = params?.id as string;

  const {
    data: evaluationData,
    isLoading: isLoadingEvaluation,
    error: evaluationError,
  } = useGetCoverLetterEvaluationQuery(coverLetterId, {
    skip: !coverLetterId,
  });

  const {
    data: coverLetterData,
    isLoading: isLoadingCoverLetter,
    error: coverLetterError,
  } = useGetCoverLetterByIdQuery(coverLetterId, {
    skip: !coverLetterId,
  });

  if (!coverLetterId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Invalid Cover Letter ID
          </h1>
          <p className="text-gray-600">Please check the URL and try again.</p>
        </div>
      </div>
    );
  }

  if (isLoadingEvaluation || isLoadingCoverLetter) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-blue-600 mx-auto mb-4 animate-spin" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Loading evaluation results...
          </h1>
          <p className="text-gray-600">Please wait a moment.</p>
        </div>
      </div>
    );
  }

  if (evaluationError || coverLetterError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            An error occurred
          </h1>
          <p className="text-gray-600 mb-4">
            Unable to load evaluation results. Please try again later.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!evaluationData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            No evaluation results yet
          </h1>
          <p className="text-gray-600">
            This cover letter has not been evaluated yet. Please perform an
            evaluation first.
          </p>
        </div>
      </div>
    );
  }

  return (
    <CoverLetterEvaluation
      evaluation={evaluationData}
      coverLetterId={coverLetterId}
      pdfUrl={coverLetterData?.coverLetterFileUrl}
    />
  );
};

export default CoverLetterEvaluationPage;
