"use client";

import { useEffect } from "react";
import CoverLetterModal from "@/features/cover-letter/components/CoverLetterModal";
import DeleteConfirmDialog from "@/features/cover-letter/components/DeleteConfirmDialog";
import CoverLetterDashboardHeader from "@/features/cover-letter/components/CoverLetterDashboardHeader";
import CoverLetterStatsCards from "@/features/cover-letter/components/CoverLetterStatsCards";
import PopularTemplates from "@/features/cover-letter/components/PopularTemplates";
import CoverLetterHistory from "@/features/cover-letter/components/CoverLetterHistory";
import CoverLetterQuickTips from "@/features/cover-letter/components/CoverLetterQuickTips";
import RecentActivity from "@/features/cover-letter/components/RecentActivity";
import { useCoverLetterDashboard } from "@/features/cover-letter/hooks/useCoverLetterDashboard";
import {
  mockTemplates,
  tips,
} from "@/features/cover-letter/constants/dashboardData";

export default function CoverLetterDashboard() {
  const {
    // State
    searchTerm,
    languageFilter,
    analystFilter,
    coverLetterModalOpen,
    deleteConfirm,

    // Data
    recentCoverLetters,
    filteredCoverLetters,
    stats,

    // Loading states
    isLoadingRecent,
    isDeleting,

    // Errors
    recentError,

    // Actions
    setSearchTerm,
    setLanguageFilter,
    setAnalystFilter,
    setCoverLetterModalOpen,
    handleDeleteConfirm,
    handleDeleteCancel,
    handleViewCoverLetter,
    handleEditCoverLetter,
    handleDownloadCoverLetter,
    handleViewFeedback,
    refetchCoverLetters,
  } = useCoverLetterDashboard();

  // Refetch data when page becomes visible (user returns from template page)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refetchCoverLetters();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Also refetch when component mounts
    refetchCoverLetters();

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [refetchCoverLetters]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header Section */}
        <CoverLetterDashboardHeader
          onCreateClick={() => setCoverLetterModalOpen(true)}
        />

        {/* Stats Cards */}
        <CoverLetterStatsCards stats={stats} />

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="xl:col-span-3 space-y-8">
            {/* Templates Section */}
            <PopularTemplates templates={mockTemplates} />

            {/* Cover Letter History */}
            <CoverLetterHistory
              coverLetters={recentCoverLetters ?? []}
              filteredCoverLetters={filteredCoverLetters}
              searchTerm={searchTerm}
              languageFilter={languageFilter}
              analystFilter={analystFilter}
              isLoadingRecent={isLoadingRecent}
              recentError={recentError}
              onSearchChange={setSearchTerm}
              onLanguageFilterChange={setLanguageFilter}
              onAnalystFilterChange={setAnalystFilter}
              onViewCoverLetter={handleViewCoverLetter}
              onEditCoverLetter={handleEditCoverLetter}
              onDownloadCoverLetter={handleDownloadCoverLetter}
              onViewFeedback={handleViewFeedback}
            />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Tips */}
            <CoverLetterQuickTips tips={tips} />

            {/* Recent Activity */}
            <RecentActivity />
          </div>
        </div>
      </div>

      <CoverLetterModal
        open={coverLetterModalOpen}
        onOpenChange={setCoverLetterModalOpen}
      />

      <DeleteConfirmDialog
        isOpen={deleteConfirm.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title={deleteConfirm.coverLetterTitle}
        isDeleting={isDeleting}
      />
    </div>
  );
}
