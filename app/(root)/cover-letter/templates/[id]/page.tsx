"use client";
import { useSearchParams } from "next/navigation";
import CoverLetterTemp1 from "@/features/cover-letter/components/CoverLetterTemp1";
import CoverLetterTemp2 from "@/features/cover-letter/components/CoverLetterTemp2";
import CoverLetterTemp3 from "@/features/cover-letter/components/CoverLetterTemp3";
import { useGetCoverLetterByIdQuery } from "@/services/api/cover-letter";

interface CoverLetterPageProps {
  params: { id: string };
}

const CoverLetterPage = ({ params }: CoverLetterPageProps) => {
  const { id } = params;
  const searchParams = useSearchParams();
  const template = searchParams.get("template") || "temp1"; // Default to temp1

  const {
    data: coverLetterData,
    isLoading,
    error,
  } = useGetCoverLetterByIdQuery(id);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading cover letter...</p>
        </div>
      </div>
    );
  }

  if (error || !coverLetterData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
          <p className="text-gray-600">
            Failed to load cover letter. Please try again.
          </p>
        </div>
      </div>
    );
  }

  // Render the appropriate template based on the template parameter
  if (template === "temp1") {
    return (
      <CoverLetterTemp1 coverLetterData={coverLetterData} coverLetterId={id} />
    );
  }

  if (template === "temp2") {
    return (
      <CoverLetterTemp2 coverLetterData={coverLetterData} coverLetterId={id} />
    );
  }

  if (template === "temp3") {
    return (
      <CoverLetterTemp3 coverLetterData={coverLetterData} coverLetterId={id} />
    );
  }

  // Default to temp1 if template parameter is invalid
  return (
    <CoverLetterTemp1 coverLetterData={coverLetterData} coverLetterId={id} />
  );
};

export default CoverLetterPage;
