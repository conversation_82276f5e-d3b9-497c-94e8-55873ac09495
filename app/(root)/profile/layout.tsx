import AuthGuard from "@/layouts/auth/AuthGuard";
import DashboardLayout from "../dashboard/layout";
import { AppSidebar } from "@/components/DashboardSidebar";
import { Header } from "@/components/DashboardHeader_K";

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard>
      <DashboardLayout>
        <AppSidebar />
        <div className="flex-1">
          <Header />
          {children}
        </div>
      </DashboardLayout>
    </AuthGuard>
  );
}
