"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { Eye, Upload, CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/features/dashboard/hooks/useAuth";
import ChangePasswordModal from "@/features/auth/components/ChangePasswordModal";
import CVAnalyzerModal from "@/features/dashboard/components/DashboardCVAnalyze";
import { useProfile } from "@/features/profile/hooks/useProfile";
import ProfileCard from "@/features/profile/components/ProfileCard";
import ProfileEditDialog from "@/features/profile/components/ProfileEditDialog";
import ProfileInfoCard from "@/features/profile/components/ProfileInfoCard";
import { useRouter } from "next/navigation";
const ProfilePage = () => {
  const router = useRouter();
  const { handleLogout } = useAuth();
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [cvAnalyzerOpen, setCvAnalyzerOpen] = useState(false);
  const {
    profileData,
    isLoading,
    isDialogOpen,
    setIsDialogOpen,
    isUploading,
    isUpdating,
    isDeleting,
    fileInputRef,
    handleSave,
    handleAvatarUpload,
    handleDeleteAvatar,
  } = useProfile();

  const handleChangePassword = () => {
    setShowChangePassword(true);
  };

  const handleChangePasswordSuccess = () => {
    console.log("Password changed successfully");
    setShowChangePassword(false);
    handleLogout();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        Loading...
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs defaultValue="profile" className="w-full">
            <TabsContent value="profile" className="mt-0">
              <div className="py-6 sm:py-8">
                <div className="grid grid-cols-1 gap-6 sm:gap-8">
                  {/* Profile Card */}
                  <ProfileCard
                    profileData={profileData}
                    isUploading={isUploading}
                    isDeleting={isDeleting}
                    onAvatarUpload={handleAvatarUpload}
                    onDeleteAvatar={handleDeleteAvatar}
                    onEditProfile={() => setIsDialogOpen(true)}
                    onChangePassword={handleChangePassword}
                    onLogout={handleLogout}
                    fileInputRef={fileInputRef}
                  />

                  {/* Profile Edit Dialog */}
                  <ProfileEditDialog
                    isOpen={isDialogOpen}
                    onOpenChange={setIsDialogOpen}
                    onSave={handleSave}
                    isUpdating={isUpdating}
                    profileData={profileData}
                  />

                  {/* Profile Information Card */}
                  <ProfileInfoCard profileData={profileData} />

                  {/* Other Sections */}
                  <div className="space-y-6">
                    {/* Resume & Cover Letter */}
                    <Card>
                      <CardHeader>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                          <CardTitle>Resume & Cover Letter</CardTitle>
                          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full sm:w-auto"
                              onClick={() => setCvAnalyzerOpen(true)} // Open CVAnalyzerModal
                            >
                              <Upload className="h-4 w-4 mr-1" />
                              Update Resume
                            </Button>
                            <Button
                              size="sm"
                              className="w-full sm:w-auto"
                              onClick={() => router.push("/analyst")}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View Analysis
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <p className="text-sm text-muted-foreground">
                            Last uploaded: May 15, 2023
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Analysis:{" "}
                            <span className="text-green-600 font-medium">
                              Complete
                            </span>
                          </p>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Professional Plan */}
                    <Card>
                      <CardHeader>
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                          <CardTitle>Professional Plan</CardTitle>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full sm:w-auto"
                          >
                            <CreditCard className="h-4 w-4 mr-1" />
                            Manage Billing
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                            <span className="text-sm text-muted-foreground">
                              Coaching sessions used
                            </span>
                            <span className="text-sm font-medium">7/10</span>
                          </div>
                          <Progress value={70} className="h-2" />
                          <p className="text-xs text-muted-foreground">
                            3 sessions remaining this month
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <ChangePasswordModal
        open={showChangePassword}
        onOpenChange={setShowChangePassword}
        onSuccess={handleChangePasswordSuccess}
      />
      <CVAnalyzerModal open={cvAnalyzerOpen} onOpenChange={setCvAnalyzerOpen} />
    </div>
  );
};

export default ProfilePage;
