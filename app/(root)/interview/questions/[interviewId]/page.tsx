"use client";
import { <PERSON><PERSON> } from "@/components/DashboardHeader_K";
import { AppSidebar } from "@/components/DashboardSidebar";
import InterviewQuestion from "@/features/interview/components/InterviewQuestion";

const InterviewQuestionPage = () => {
  return (
    <>
      <AppSidebar />
      <div className="flex-1 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
        <Header />
        <main className="flex-1 p-4 sm:p-6 lg:p-8 space-y-6">
          <InterviewQuestion />
        </main>
      </div>
    </>
  );
};

export default InterviewQuestionPage;
