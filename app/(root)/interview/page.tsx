"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Head<PERSON> } from "@/components/DashboardHeader_K";
import { AppSidebar } from "@/components/DashboardSidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MockInterviewDialog } from "@/features/interview/components/MockInterviewDialog";
import { <PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";
import InterviewSessions from "@/features/interview/components/InterviewSession";
import FeedbackSessions from "@/features/interview/components/FeedbackSession";
import InterviewFooter from "@/features/interview/components/InterviewFooter";
import InterviewLoading from "@/components/InterviewLoading";
import { useGetMyConversationQuery } from "@/services/api/conversation";
import {
  interviewPageVariants,
  buttonVariants,
} from "@/styles/animations/interview.variants";

export default function MockInterviewPage() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleGenerateInterview = () => {
    setIsDialogOpen(true);
  };

  const {
    data: myInterviewQuestions,
    isLoading,
    isError,
  } = useGetMyConversationQuery();

  if (isLoading) {
    return (
      <>
        <AppSidebar />
        <InterviewLoading />
      </>
    );
  }

  return (
    <>
      <AppSidebar />
      <div className="flex-1 min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Header />
        <motion.main
          variants={interviewPageVariants}
          initial="hidden"
          animate="visible"
          className="flex-1 space-y-8 p-8"
        >
          <motion.div
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
            className="flex w-5"
          >
            <Button
              className="h-14 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
              onClick={handleGenerateInterview}
            >
              <Sparkles className="mr-2 h-5 w-5" />
              Generate Interview Questions
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </motion.div>

          <InterviewSessions />

          <FeedbackSessions
            headerTitle="Recent Feedbacks"
            isLoading={false}
            isError={isError}
            data={myInterviewQuestions ?? []}
          />

          <MockInterviewDialog
            open={isDialogOpen}
            onOpenChange={setIsDialogOpen}
          />
        </motion.main>

        <InterviewFooter />
      </div>
    </>
  );
}
