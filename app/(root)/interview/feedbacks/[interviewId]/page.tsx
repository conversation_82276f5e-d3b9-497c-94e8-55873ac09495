"use client";

import { Head<PERSON> } from "@/components/DashboardHeader_K";
import { AppSidebar } from "@/components/DashboardSidebar";
import { ChatSection } from "@/features/interviewStart/components/ChatSection";
import { FeedbackSection } from "@/features/interviewStart/components/FeedbackSection";
import React from "react";
import { useParams } from "next/navigation";
import { useGetConversationByIdQuery } from "@/services/api/conversation";
import { Feedback } from "@/common/types/conversation";
import InterviewLoading from "@/components/InterviewLoading";

export default function FeedbackSession() {
  const { interviewId } = useParams();
  const { data: conversation, isLoading } = useGetConversationByIdQuery(
    interviewId as string
  );

  console.log(conversation);

  return (
    <>
      <AppSidebar />
      <div className="flex-1 min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex flex-col relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-indigo-200/20 to-blue-200/20 rounded-full blur-3xl"></div>
        </div>

        <Header />
        {isLoading ? (
          <InterviewLoading />
        ) : (
          <div className="flex flex-col gap-8 px-6 py-6 relative z-10">
            {/* Chat Section */}
            <div className="flex-1">
              <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-1 shadow-xl border border-white/20">
                <ChatSection
                  messages={
                    conversation?.conversationLog.map((log) => ({
                      sender: log.role as "you" | "user" | "assistant",
                      content: log.content,
                      isComplete: true,
                    })) || []
                  }
                  isInterviewing={false}
                />
              </div>
            </div>

            {/* Feedback Section */}
            <div className="flex-1 min-w-0">
              <div className="sticky top-6">
                <FeedbackSection
                  feedback={conversation?.feedback as Feedback}
                  isLoading={isLoading}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
