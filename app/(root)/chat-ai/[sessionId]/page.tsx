import { SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/features/chat-ai/components/AppSidebar";
import ChatMain from "@/features/chat-ai/components/ChatMain";
import React from "react";

const ChatPage = () => {
  return (
    <>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <div className="flex-1">
            <h1 className="text-lg font-semibold">AI LearnVox Assistant</h1>
          </div>
        </header>
        <ChatMain />
      </SidebarInset>
    </>
  );
};

export default ChatPage;
