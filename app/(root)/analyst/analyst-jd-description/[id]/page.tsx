"use client";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { useGetAnalystByIdQuery } from "@/services/api/analyst";

import AnalystDocumentPreviews from "@/features/analyst/components/AnalystDocumentPreviews";
import AnalystSection from "@/features/analyst/components/AnalystSection";
import AuthGuard from "@/layouts/auth/AuthGuard";

export default function JDAnalysisDetailPage() {
  const [mounted, setMounted] = useState(false);
  const params = useParams();
  const id = params.id as string;

  const {
    data: analysisData,
    isLoading,
    error,
  } = useGetAnalystByIdQuery(id, {
    skip: !id,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (analysisData?.data && typeof window !== "undefined") {
      sessionStorage.setItem("analysisResult", JSON.stringify(analysisData));

      sessionStorage.setItem(
        "analysisData",
        JSON.stringify({
          analysisType: "jd",
          cvFileName: analysisData.data.cvFileName ?? "Resume.pdf",
          cvFileSize: analysisData.data.cvFileSize ?? 0,
          cvFileType: analysisData.data.cvFileType ?? "application/pdf",
          jdFileName: analysisData.data.jdFileName ?? "Job Description.pdf",
          jdFileSize: analysisData.data.jdFileSize ?? 0,
          jdFileType: analysisData.data.jdFileType ?? "application/pdf",
          timestamp: analysisData.data.createdAt
            ? new Date(analysisData.data.createdAt).getTime()
            : new Date().getTime(),
        })
      );

      if (analysisData.data.cvFileUrl) {
        sessionStorage.setItem("cvFileUrl", analysisData.data.cvFileUrl);
      }
      if (analysisData.data?.jdFileUrl) {
        sessionStorage.setItem("jdFileUrl", analysisData.data?.jdFileUrl);
      }
    }
  }, [analysisData]);

  if (!mounted) return null;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading analysis...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="flex items-center justify-center h-screen">
          <div className="text-center">
            <p className="text-red-600">
              Error loading analysis. Please try again.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Document Previews - Side by Side */}
          <AnalystDocumentPreviews />

          {/* Analysis Sections */}
          <AnalystSection />
        </div>
      </div>
    </AuthGuard>
  );
}
