"use client";

import { <PERSON><PERSON> } from "@/components/DashboardHeader_K";
import { AppSidebar } from "@/components/DashboardSidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import AuthGuard from "@/layouts/auth/AuthGuard";

export default function AnalystLayout({
  children,
}: {
  readonly children: React.ReactNode;
}) {
  return (
    <AuthGuard>
      <SidebarProvider>
        <AppSidebar />
        <div className="flex-1">
          <Header />
          {children}
        </div>
      </SidebarProvider>
    </AuthGuard>
  );
}
