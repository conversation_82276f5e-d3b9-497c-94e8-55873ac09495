"use client";
import { useParams } from "next/navigation";
import { motion } from "framer-motion";
import { User, Briefcase, GraduationCap, Code } from "lucide-react";
import { useGetAnalystByIdQuery } from "@/services/api/analyst";
import { useAnalysis } from "@/features/analyst/hooks/useAnalysis";
import { ErrorState, LoadingState } from "@/components/LoadingErrorStates";
import ResumePreview from "@/features/analyst/components/ResumePreview";
import OverallAnalysis from "@/features/analyst/components/OverallAnalysis";
import SectionAnalysis from "@/features/analyst/components/SectionAnalysis";
import FooterBanner from "@/components/FooterBanner";
import {
  containerVariant,
  itemVariant,
} from "@/styles/animations/analystRole.variants";
import { handleDownloadFile, handleViewFile } from "@/lib/fileUtils";

export default function AnalystByRolePage() {
  const params = useParams();
  const analysisId = params.id as string;

  const {
    data: analysisResult,
    error,
    isLoading,
    isError,
  } = useGetAnalystByIdQuery(analysisId, {
    skip: !analysisId,
  });

  const { mounted, getFileName } = useAnalysis(analysisResult?.data);

  if (!mounted || isLoading) {
    return <LoadingState />;
  }

  if (isError || !analysisResult) {
    return <ErrorState error={error} analysisId={analysisId} />;
  }

  const { data: analysisData } = analysisResult;
  const { content } = analysisData;

  // const roleDescription =
  //   analysisData.roleDescription ??
  //   analysisData.jdDescription ??
  //   analysisMetadata?.roleDescription ??
  //   "Analysis Report";

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50">
      {/* Header */}
      {/* <AnalysisHeader roleDescription={roleDescription} /> */}

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Resume Preview - Pass both URLs */}
          <ResumePreview
            cvFileUrl={analysisData.cvFileUrl}
            serverCvFileUrl={analysisData.cvFileUrl}
            fileName={getFileName(analysisData.cvFileUrl)}
            overallScore={content.overall_score}
            fitScore={content.fit_score}
            onViewPdf={() => handleViewFile(analysisData.cvFileUrl)}
            onDownloadPdf={() =>
              handleDownloadFile(
                analysisData.cvFileUrl,
                getFileName(analysisData.cvFileUrl)
              )
            }
          />

          {/* Analysis Results */}
          <motion.div
            variants={containerVariant}
            initial="hidden"
            animate="visible"
            className="lg:col-span-2 space-y-6"
          >
            {/* Overall Analysis */}
            <OverallAnalysis content={content} variants={itemVariant} />

            {/* Section Analysis */}
            {content.sections.contact_info && (
              <SectionAnalysis
                title="Contact Information"
                icon={<User className="w-5 h-5 text-green-500" />}
                analysis={content.sections.contact_info}
                gradientFrom="from-green-50"
                gradientTo="to-emerald-50"
                variants={itemVariant}
              />
            )}

            {content.sections.experience && (
              <SectionAnalysis
                title="Work Experience"
                icon={<Briefcase className="w-5 h-5 text-blue-500" />}
                analysis={content.sections.experience}
                gradientFrom="from-blue-50"
                gradientTo="to-cyan-50"
                variants={itemVariant}
              />
            )}

            {content.sections.education && (
              <SectionAnalysis
                title="Education"
                icon={<GraduationCap className="w-5 h-5 text-purple-500" />}
                analysis={content.sections.education}
                gradientFrom="from-purple-50"
                gradientTo="to-pink-50"
                variants={itemVariant}
              />
            )}

            {content.sections.skills && (
              <SectionAnalysis
                title="Skills"
                icon={<Code className="w-5 h-5 text-orange-500" />}
                analysis={content.sections.skills}
                gradientFrom="from-orange-50"
                gradientTo="to-red-50"
                variants={itemVariant}
              />
            )}
          </motion.div>
        </div>

        {/* Footer Banner */}
        <FooterBanner />
      </div>
    </div>
  );
}
