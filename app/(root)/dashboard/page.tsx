"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Header } from "@/components/DashboardHeader_K";
import { AppSidebar } from "@/components/DashboardSidebar";
import { motion } from "framer-motion";
import {
  MessageSquare,
  FileText,
  Compass,
  FileEdit,
  Upload,
  Rocket,
  TrendingUp,
  Clock,
  Award
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import CVAnalyzerModal from "@/features/dashboard/components/DashboardCVAnalyze";
import { useAuth } from "@/features/dashboard/hooks/useAuth";

const aiTools = [
  {
    id: 1,
    title: "Mock Interview",
    description: "Help you prepare for interviews in minutes",
    icon: <MessageSquare className="h-6 w-6" />,
    href: "/interview",
    gradient: "from-blue-500 to-cyan-500",
    bgGradient: "from-blue-50 to-cyan-50",
    iconColor: "text-blue-600",
    iconHoverColor: "group-hover:text-blue-700",
  },
  {
    id: 2,
    title: "Resume Analyzer",
    description: "Get detailed feedback and suggestions for your resume",
    icon: <FileText className="h-6 w-6" />,
    href: "/analyst",
    gradient: "from-green-500 to-emerald-500",
    bgGradient: "from-green-50 to-emerald-50",
    iconColor: "text-green-600",
    iconHoverColor: "group-hover:text-green-700",
  },
  {
    id: 3,
    title: "Career Roadmap",
    description: "Plan your career path with AI-powered guidance",
    icon: <Compass className="h-6 w-6" />,
    href: "/career-roadmap",
    gradient: "from-purple-500 to-violet-500",
    bgGradient: "from-purple-50 to-violet-50",
    iconColor: "text-purple-600",
    iconHoverColor: "group-hover:text-purple-700",
  },
  {
    id: 4,
    title: "Cover Letter AI",
    description: "Generate personalized cover letters in minutes",
    icon: <FileEdit className="h-6 w-6" />,
    href: "/cover-letter",
    gradient: "from-orange-500 to-red-500",
    bgGradient: "from-orange-50 to-red-50",
    iconColor: "text-orange-600",
    iconHoverColor: "group-hover:text-orange-700",
  },
];

const stats = {
  totalTools: 147,
  resumesAnalyzed: 24,
  chatSessions: 89,
  creditsUsed: 75,
  achievements: [
    { label: "Tools Used", value: 147, icon: TrendingUp, change: "+12%" },
    { label: "Resumes Analyzed", value: 24, icon: FileText, change: "+8%" },
    { label: "Chat Sessions", value: 89, icon: MessageSquare, change: "+24%" },
    { label: "Credits Used", value: "75%", icon: Award, change: "250 left" },
  ],
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      delayChildren: 0.1,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

export default function DashboardPage() {
  const router = useRouter();
  const [cvAnalyzerOpen, setCvAnalyzerOpen] = useState(false);

  const handleToolClick = (link: string) => {
    router.push(link);
  };
  const { user } = useAuth();

  return (
    <>
      <AppSidebar />
      <div className="flex-1">
        <Header />
        <motion.main 
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50"
        >
          <div className="space-y-8 p-8">
            {/* Welcome Section */}
            <motion.div variants={itemVariants} className="space-y-4">
              <div className="space-y-2">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Welcome back, {user?.firstName} {user?.lastName}!
                </h1>
                <p className="text-lg text-gray-600">
                  Let&apos;s accelerate your career journey with AI-powered tools
                </p>
              </div>
              <div className="flex gap-4">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button className="gap-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white shadow-lg">
                    <Rocket className="h-4 w-4" />
                    Explore Tools
                  </Button>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button 
                    variant="outline" 
                    className="gap-2 border-gray-300 hover:border-blue-300 hover:bg-blue-50"
                    onClick={() => setCvAnalyzerOpen(true)}
                  >
                    <Upload className="h-4 w-4" />
                    Upload Resume
                  </Button>
                </motion.div>
              </div>
            </motion.div>

            {/* Stats Overview */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.achievements.map((stat) => (
                <Card key={stat.label} className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <stat.icon className="h-4 w-4 text-blue-600" />
                          </div>
                          <span className="text-sm font-medium text-gray-600">{stat.label}</span>
                        </div>
                        <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                        <div className="text-xs text-green-600 font-medium">{stat.change}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </motion.div>

            {/* AI Tools Grid */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">AI-Powered Tools</h2>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                </motion.div>
              </div>
              
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {aiTools.map((tool) => (
                  <motion.div
                    key={tool.title}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300 cursor-pointer group">                      <CardHeader className="pb-4">
                        <div className={`mb-4 flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r ${tool.bgGradient} group-hover:scale-110 transition-all duration-300`}>
                          <div className={`${tool.iconColor} ${tool.iconHoverColor} transition-colors`}>
                            {tool.icon}
                          </div>
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {tool.title}
                        </h3>
                        <p className="text-gray-600">{tool.description}</p>
                      </CardHeader>
                      <CardContent>
                        <Button 
                          onClick={() => handleToolClick(tool.href)}
                          className={`w-full bg-gradient-to-r ${tool.gradient} hover:opacity-90 text-white shadow-lg group-hover:shadow-xl transition-all duration-300`}
                        >
                          Try Now
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Usage Overview */}
            <motion.div variants={itemVariants}>
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-900">AI Usage Overview</h2>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-500">This month</span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-600">Monthly Usage</div>
                      <div className="text-sm font-semibold">750/1000 credits</div>
                    </div>
                    <div className="space-y-2">
                      <Progress value={75} className="h-3 bg-gray-200" />
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">You&apos;ve used 75% of your free limit.</span>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button 
                            variant="link" 
                            className="h-auto p-0 text-blue-600 hover:text-blue-700 font-medium"
                          >
                            Upgrade to Pro
                          </Button>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.main>
      </div>

      <CVAnalyzerModal open={cvAnalyzerOpen} onOpenChange={setCvAnalyzerOpen} />
    </>
  );
}
