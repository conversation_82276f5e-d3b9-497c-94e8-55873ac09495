import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/sonner";
import ReduxProvider from "@/redux/provider";
import "../styles/globals.css";
import { ConfirmProvider } from "@/providers/ConfirmModalProvider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "LearnVoxAI",
  description: "LearnVoxAI - AI Interview Assistant",
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
    other: [
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        url: "/favicon-32x32.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        url: "/favicon-16x16.png",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: {
  readonly children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ReduxProvider>
          <ConfirmProvider>{children}</ConfirmProvider>
          <Toaster />
        </ReduxProvider>
      </body>
    </html>
  );
}
