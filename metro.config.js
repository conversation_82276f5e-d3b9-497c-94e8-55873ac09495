const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

const config = getDefaultConfig(__dirname);

//Alias react-native-webrtc → @daily-co/react-native-webrtc
config.resolver.alias = {
  ...config.resolver.alias,
  'react-native-webrtc': path.resolve(
    __dirname,
    'node_modules/@daily-co/react-native-webrtc',
  ),
};

module.exports = withNativeWind(config, { input: './global.css' });
