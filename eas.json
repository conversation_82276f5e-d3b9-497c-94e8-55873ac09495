{"cli": {"appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "env": {"NPM_CONFIG_LEGACY_PEER_DEPS": "true", "NODE_OPTIONS": "--max-old-space-size=4096"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "env": {"EXPO_USE_LEGACY_INSTALLER": "1", "NPM_CONFIG_LEGACY_PEER_DEPS": "true", "NODE_OPTIONS": "--max-old-space-size=4096", "SKIP_BUNDLING": "0", "NO_FLIPPER": "1"}}, "ios": {"env": {"NPM_CONFIG_LEGACY_PEER_DEPS": "true", "NODE_OPTIONS": "--max-old-space-size=4096"}}}, "production": {"ios": {"env": {"NPM_CONFIG_LEGACY_PEER_DEPS": "true", "NODE_OPTIONS": "--max-old-space-size=4096"}}, "android": {"buildType": "apk", "env": {"EXPO_USE_LEGACY_INSTALLER": "1", "NPM_CONFIG_LEGACY_PEER_DEPS": "true", "NODE_OPTIONS": "--max-old-space-size=4096", "SKIP_BUNDLING": "0", "NO_FLIPPER": "1"}}}}}