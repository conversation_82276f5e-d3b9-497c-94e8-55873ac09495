version: '3.8'

services:
  learn_vox_be: 
    container_name: learn_vox_be
    env_file:
      - .env.${NODE_ENV:-dev}
    build:
      context: .
      dockerfile: Dockerfile
      target:  prod
    command: npm run start:dev -- --legacy-watch
    ports:
      - '${APP_PORT}:${APP_PORT}'
    volumes:
      - ./:/usr/src/app
      - /usr/src/app/node_modules
    restart: unless-stopped
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
