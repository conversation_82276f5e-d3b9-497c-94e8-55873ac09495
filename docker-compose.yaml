version: '3.8'

services:
  postgres:
    image: postgres:17
    container_name: postgres_17_container
    env_file:
      - .env.${NODE_ENV:-dev}
    environment:
      POSTGRES_USER: ${POSTGRE_DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${POSTGRE_DATABASE_PASSWORD}
      POSTGRES_DB: ${POSTGRE_DATABASE_NAME}
    ports:
      - '5433:${POSTGRE_DATABASE_PORT}'
      # - '${POSTGRE_DATABASE_PORT}:${POSTGRE_DATABASE_PORT}'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: always
    networks:
      - app-network

  mongo:
    image: mongo:latest
    container_name: mongo_container
    env_file:
      - .env.${NODE_ENV:-dev}
    ports:
      - '27018:${MONGO_DATABASE_PORT}'
      # - '${MONGO_DATABASE_PORT}:${MONGO_DATABASE_PORT}'
    environment:
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE_NAME}
    volumes:
      - mongo_data:/data/db
    restart: always
    networks:
      - app-network

  redis:
    image: redis:latest
    container_name: redis_container
    env_file:
      - .env.${NODE_ENV:-dev}
    ports:
      - '${REDIS_DATABASE_PORT}:${REDIS_DATABASE_PORT}'
    volumes:
      - redis_data:/data
    restart: always
    networks:
      - app-network

  learn_vox_be: 
    container_name: learn_vox_be
    env_file:
      - .env.${NODE_ENV:-dev}
    build:
      context: .
      dockerfile: Dockerfile
      target:  dev
    command: npm run start:dev -- --legacy-watch
    ports:
      - '${APP_PORT}:${APP_PORT}'
    depends_on:
      - postgres
    volumes:
      - ./:/usr/src/app
      - /usr/src/app/node_modules
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres_data:
  mongo_data:
  redis_data:

networks:
  app-network:
    driver: bridge
