name: EAS Build APK

on:
  push:
    branches:
      - develop

jobs:
  build-android:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20
          
      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EAS_ACCESS_TOKEN }}
          
      - name: Install dependencies
        run: npm install --legacy-peer-deps
        
      - name: Build Android APK
        run: eas build --platform android --profile production --non-interactive
