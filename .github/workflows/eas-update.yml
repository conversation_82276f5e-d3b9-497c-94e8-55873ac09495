name: EAS Update

on:
  push:
    branches:
      - develop

jobs:
  eas-update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20
          
      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EAS_ACCESS_TOKEN }}
          
      - name: Install dependencies
        run: npm install --legacy-peer-deps
        
      - name: Run EAS Update
        run: eas update --branch develop --non-interactive --message "CI Update from develop"