export interface ProfileFormData {
    firstName: string;
    lastName: string;
    bio: string;
    phone: string;
    dateOfBirth: string;
    address: string;
}

export interface ProfileCardProps {
    profileData?: any;
    isUploading: boolean;
    isDeleting: boolean;
    onAvatarUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
    onDeleteAvatar: () => void;
    onEditProfile: () => void;
    onChangePassword: () => void;
    onLogout: () => void;
    fileInputRef: React.RefObject<HTMLInputElement>;
}

export interface ProfileEditDialogProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    formData: ProfileFormData;
    onInputChange: (field: keyof ProfileFormData, value: string) => void;
    onSave: () => void;
    isUpdating: boolean;
    profileData?: any;
}

export interface ProfileInfoCardProps {
    profileData?: any;
}
