"use client";

import { useState, useRef } from "react";
import { toast } from "sonner";
import {
    useGetProfileQuery,
    useUpdateProfileMutation,
    useUploadAvatarMutation,
    useDeleteAvatarMutation,
} from "@/services/api/profile";
import { ProfileFormData } from "../schemas/profileSchema";

export const useProfile = () => {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isUploading, setIsUploading] = useState(false);

    // API hooks
    const {
        data: profileData,
        isLoading,
        refetch,
    } = useGetProfileQuery(undefined, {
        refetchOnMountOrArgChange: true,
    });
    const [updateProfile, { isLoading: isUpdating }] =
        useUpdateProfileMutation();
    const [uploadAvatar] = useUploadAvatarMutation();
    const [deleteAvatar, { isLoading: isDeleting }] = useDeleteAvatarMutation();

    const handleSave = async (data: ProfileFormData) => {
        try {
            await updateProfile(data).unwrap();
            toast.success("Profile updated successfully!");
            setIsDialogOpen(false);
            refetch();
        } catch (error) {
            console.error("Failed to update profile:", error);
            toast.error("Failed to update profile. Please try again.");
        }
    };

    const handleAvatarUpload = async (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Validate file type
        if (!file.type.startsWith("image/")) {
            toast.error("Please select a valid image file");
            return;
        }

        // Validate file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            toast.error("File size must be less than 5MB");
            return;
        }

        setIsUploading(true);
        try {
            const formData = new FormData();
            formData.append("file", file);

            await uploadAvatar(formData).unwrap();
            toast.success("Avatar uploaded successfully!");
            refetch();
        } catch (error) {
            toast.error("Failed to upload avatar. Please try again.");
        } finally {
            setIsUploading(false);
            // Reset file input
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
        }
    };

    const handleDeleteAvatar = async () => {
        if (!profileData?.id) {
            toast.error("User ID not found");
            return;
        }

        try {
            await deleteAvatar(profileData.id.toString()).unwrap();
            toast.success("Avatar deleted successfully!");
            refetch();
        } catch (error) {
            toast.error("This is the default avatar that cannot be deleted.");
        }
    };

    return {
        profileData,
        isLoading,
        isDialogOpen,
        setIsDialogOpen,
        isUploading,
        isUpdating,
        isDeleting,
        fileInputRef,
        handleSave,
        handleAvatarUpload,
        handleDeleteAvatar,
    };
};
