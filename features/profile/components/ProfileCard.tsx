"use client";

import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { CreditCard, Edit, LogOut } from "lucide-react";
import { ProfileCardProps } from "../profile";

import AvatarModal from "./AvatarModal";

const ProfileCard: React.FC<ProfileCardProps> = ({
  profileData,
  isUploading,
  isDeleting,
  onAvatarUpload,
  onDeleteAvatar,
  onEditProfile,
  onChangePassword,
  onLogout,
  fileInputRef,
}) => {
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  const handleAvatarClick = () => {
    setShowAvatarModal(true);
  };


  return (
    <Card>
      <CardContent className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div className="flex flex-col items-start space-y-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Avatar
                  className="h-12 w-12 sm:h-16 sm:w-16 cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={handleAvatarClick}
                >
                  {profileData?.avatar && profileData.avatar.trim() !== "" ? (
                    <AvatarImage src={profileData.avatar} alt="Profile" />
                  ) : (
                    <AvatarFallback className="text-base sm:text-lg">
                      {profileData?.firstName?.charAt(0).toUpperCase() || "?"}
                      {profileData?.lastName?.charAt(0).toUpperCase() || "?"}
                    </AvatarFallback>
                  )}
                </Avatar>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={onAvatarUpload}
                  className="hidden"
                />
              </div>
              <div>
                <h2 className="text-lg sm:text-xl font-semibold">
                  {profileData?.firstName || "No name"}{" "}
                  {profileData?.lastName || ""}
                </h2>
                <p className="text-sm text-muted-foreground">
                  ({profileData?.email})
                </p>
                <Badge
                  variant="secondary"
                  className="mt-2 bg-purple-100 text-purple-800 text-xs sm:text-sm"
                >
                  {profileData?.role || "USER"}
                </Badge>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="w-full sm:w-auto"
              onClick={onEditProfile}
            >
              <Edit className="h-4 w-4 mr-1" />
              Edit Profile
            </Button>
          </div>
          <div className="flex flex-col sm:flex-row sm:ml-auto space-y-2 sm:space-y-0 sm:space-x-2">
            <Button
              size="sm"
              onClick={onChangePassword}
              className="w-full sm:w-auto"
            >
              <CreditCard className="h-4 w-4 mr-1" />
              Change Password
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onLogout}
              className="text-red-600 w-full sm:w-auto"
            >
              <LogOut className="h-4 w-4 mr-1" />
              Logout
            </Button>
          </div>
        </div>
      </CardContent>

      {/* Avatar Modal */}
      <AvatarModal
        open={showAvatarModal}
        onOpenChange={setShowAvatarModal}
        profileData={profileData}
        isUploading={isUploading}
        isDeleting={isDeleting}
        onAvatarUpload={onAvatarUpload}
        onDeleteAvatar={onDeleteAvatar}
        fileInputRef={fileInputRef}
      />


    </Card>
  );
};

export default ProfileCard;
