"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { ProfileInfoCardProps } from "../profile";

const ProfileInfoCard: React.FC<ProfileInfoCardProps> = ({ profileData }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Bio
            </label>
            <p className="font-medium text-sm sm:text-base">
              {profileData?.bio || "No bio available"}
            </p>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Phone
            </label>
            <p className="font-medium text-sm sm:text-base">
              {profileData?.phone || "No phone number"}
            </p>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Date of Birth
            </label>
            <p className="font-medium text-sm sm:text-base">
              {profileData?.dateOfBirth
                ? new Date(profileData.dateOfBirth).toLocaleDateString()
                : "Not specified"}
            </p>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Address
            </label>
            <p className="font-medium text-sm sm:text-base">
              {profileData?.address || "No address"}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileInfoCard;
