"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { profileSchema, ProfileFormData } from "../schemas/profileSchema";

interface ProfileEditDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (data: ProfileFormData) => void;
  isUpdating: boolean;
  profileData?: any;
}

const ProfileEditDialog: React.FC<ProfileEditDialogProps> = ({
  isOpen,
  onOpenChange,
  onSave,
  isUpdating,
  profileData,
}) => {
  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      bio: "",
      phone: "",
      dateOfBirth: "",
      address: "",
    },
  });

  const { handleSubmit, reset, setValue } = form;

  // Update form when profileData changes
  useEffect(() => {
    if (profileData && isOpen) {
      setValue("firstName", profileData.firstName || "");
      setValue("lastName", profileData.lastName || "");
      setValue("bio", profileData.bio || "");
      setValue("phone", profileData.phone || "");
      setValue("dateOfBirth", profileData.dateOfBirth || "");
      setValue("address", profileData.address || "");
    }
  }, [profileData, isOpen, setValue]);

  const onSubmit = (data: ProfileFormData) => {
    onSave(data);
  };

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-[90vw] max-w-md sm:max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 py-4">
              {/* First Name */}
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-1 sm:grid-cols-4 items-start gap-4 space-y-0">
                    <FormLabel className="text-left sm:text-right sm:pt-2">
                      First Name *
                    </FormLabel>
                    <div className="col-span-1 sm:col-span-3">
                      <FormControl>
                        <Input placeholder="Enter your first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
              {/* Last Name */}
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-1 sm:grid-cols-4 items-start gap-4 space-y-0">
                    <FormLabel className="text-left sm:text-right sm:pt-2">
                      Last Name *
                    </FormLabel>
                    <div className="col-span-1 sm:col-span-3">
                      <FormControl>
                        <Input placeholder="Enter your last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              {/* Email (Read-only) */}
              <FormItem className="grid grid-cols-1 sm:grid-cols-4 items-center gap-4 space-y-0">
                <FormLabel className="text-left sm:text-right">
                  Email
                </FormLabel>
                <div className="col-span-1 sm:col-span-3">
                  <Input
                    value={profileData?.email || ""}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
              </FormItem>

              {/* Bio */}
              <FormField
                control={form.control}
                name="bio"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-1 sm:grid-cols-4 items-start gap-4 space-y-0">
                    <FormLabel className="text-left sm:text-right sm:pt-2">
                      Bio
                    </FormLabel>
                    <div className="col-span-1 sm:col-span-3">
                      <FormControl>
                        <Textarea
                          placeholder="Tell us about yourself..."
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              {/* Phone */}
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-1 sm:grid-cols-4 items-start gap-4 space-y-0">
                    <FormLabel className="text-left sm:text-right sm:pt-2">
                      Phone
                    </FormLabel>
                    <div className="col-span-1 sm:col-span-3">
                      <FormControl>
                        <Input placeholder="+84 123 456 789" {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
              {/* Date of Birth */}
              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-1 sm:grid-cols-4 items-start gap-4 space-y-0">
                    <FormLabel className="text-left sm:text-right sm:pt-2">
                      Date of Birth
                    </FormLabel>
                    <div className="col-span-1 sm:col-span-3">
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              {/* Address */}
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem className="grid grid-cols-1 sm:grid-cols-4 items-start gap-4 space-y-0">
                    <FormLabel className="text-left sm:text-right sm:pt-2">
                      Address
                    </FormLabel>
                    <div className="col-span-1 sm:col-span-3">
                      <FormControl>
                        <Input placeholder="Your address..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isUpdating}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isUpdating}
                className="flex-1"
              >
                {isUpdating ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileEditDialog;
