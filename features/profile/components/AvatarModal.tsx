"use client";

import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Upload, Trash2 } from "lucide-react";
import DeleteAvatarDialog from "./DeleteAvatarDialog";

interface AvatarModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  profileData?: any;
  isUploading: boolean;
  isDeleting: boolean;
  onAvatarUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDeleteAvatar: () => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
}

const AvatarModal: React.FC<AvatarModalProps> = ({
  open,
  onOpenChange,
  profileData,
  isUploading,
  isDeleting,
  onAvatarUpload,
  onDeleteAvatar,
  fileInputRef,
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const hasRealAvatar = profileData?.avatar &&
    profileData.avatar.trim() !== "" &&
    !profileData.avatar.toLowerCase().includes("/avatar_");

  const getInitials = () => {
    if (profileData?.firstName && profileData?.lastName) {
      return `${profileData.firstName.charAt(0)}${profileData.lastName.charAt(0)}`.toUpperCase();
    }
    return profileData?.email?.charAt(0).toUpperCase() || "U";
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="w-[90vw] max-w-md">
          <div className="text-center">
            <h2 className="text-lg font-semibold mb-4">Profile Avatar</h2>
          </div>

          <div className="flex flex-col items-center space-y-6 py-6">
            {/* Large Avatar Display */}
            <div className="relative">
              <Avatar className="h-32 w-32 border-4 border-gray-200">
                {profileData?.avatar && profileData.avatar.trim() !== "" ? (
                  <AvatarImage
                    src={profileData.avatar}
                    alt="Profile Avatar"
                    className="object-cover"
                  />
                ) : (
                  <AvatarFallback className="text-2xl font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {getInitials()}
                  </AvatarFallback>
                )}
              </Avatar>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 w-full">
              <Button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading || isDeleting}
                className="flex-1"
                variant="outline"
              >
                <Upload className="h-4 w-4 mr-2" />
                {isUploading ? "Uploading..." : "Change Avatar"}
              </Button>

              {hasRealAvatar && (
                <Button
                  onClick={handleDeleteClick}
                  disabled={isUploading || isDeleting}
                  variant="outline"
                  className="flex-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {isDeleting ? "Deleting..." : "Remove"}
                </Button>
              )}
            </div>

            {/* File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={onAvatarUpload}
              className="hidden"
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <DeleteAvatarDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        onConfirm={onDeleteAvatar}
        isDeleting={isDeleting}
      />
    </>
  );
};

export default AvatarModal;
