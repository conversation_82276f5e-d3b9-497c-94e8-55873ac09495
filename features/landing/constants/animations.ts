import { easeInOut, cubicBezier } from "framer-motion";

export const fadeInLeft = {
  hidden: { opacity: 0, x: -60 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.6, ease: easeInOut },
  },
};

export const fadeInRight = {
  hidden: { opacity: 0, x: 60 },
  visible: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.6, ease: easeInOut },
  },
};

export const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

export const staggerItem = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 },
  },
};

export const bounceAnimation = {
  y: [0, -10, 0],
  transition: {
    duration: 2,
    repeat: Infinity,
    repeatType: "reverse" as const,
    ease: cubicBezier(0.42, 0, 0.58, 1),
  },
};

export const gradientAnimation = {
  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
  transition: {
    duration: 3,
    repeat: Infinity,
    ease: easeInOut,
  },
};

export const floatingAnimation = {
  y: [0, -15, 0],
  transition: {
    duration: 4,
    repeat: Infinity,
    ease: cubicBezier(0.25, 0.46, 0.45, 0.94),
  },
};

export const rotateAnimation = {
  rotate: [0, 5, -5, 0],
  transition: {
    duration: 2,
    repeat: Infinity,
    repeatDelay: 3,
    ease: cubicBezier(0.25, 0.46, 0.45, 0.94),
  },
};
