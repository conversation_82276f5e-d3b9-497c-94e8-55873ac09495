import { MessageSquare, FileText, Rocket } from "lucide-react";

export const navigationItems = ["Features", "Pricing", "About", "Resources"];

export const heroFeatures = [
  "24/7 Career Guidance",
  "Smart Resume Analysis", 
  "Personalized Career Roadmaps",
  "AI Cover Letter Generator",
];

export const features = [
  {
    icon: MessageSquare,
    title: "AI Chat Assistant",
    desc: "Get instant answers to your career questions from our advanced AI career coach",
    color: "from-cyan-500 to-blue-600",
  },
  {
    icon: FileText,
    title: "Resume Analyzer",
    desc: "Receive detailed feedback and suggestions to improve your resume",
    color: "from-purple-500 to-pink-600",
  },
  {
    icon: Rocket,
    title: "Career Roadmap",
    desc: "Get a personalized career development plan based on your goals",
    color: "from-green-500 to-teal-600",
  },
  {
    icon: FileText,
    title: "Cover Letter Builder",
    desc: "Generate tailored cover letters that stand out to employers",
    color: "from-orange-500 to-red-600",
  },
];

export const pricingPlans = [
  {
    plan: "Free",
    price: "$0",
    popular: false,
    features: [
      "Basic AI Chat",
      "Resume Analysis (1/month)",
      "Career Resources",
      "Community Access",
    ],
  },
  {
    plan: "Pro",
    price: "$29",
    popular: true,
    features: [
      "Unlimited AI Chat",
      "Advanced Resume Analysis",
      "Career Roadmap Generator",
      "Cover Letter Builder",
      "Priority Support",
      "Interview Preparation",
    ],
  },
  {
    plan: "Enterprise",
    price: "$99",
    popular: false,
    features: [
      "All Pro Features",
      "Custom AI Training",
      "Team Management",
      "API Access",
      "Dedicated Support",
      "Custom Integrations",
    ],
  },
];

export const footerSections = [
  {
    title: "LearnVoxAI",
    content:
      "LearnVox AI is a web-based platform that helps job seekers simulate interviews and evaluate the compatibility of their resumes (CVs) with specific job descriptions (JDs) using AI.",
  },
  {
    title: "Product",
    content: [
      "Features",
      "Pricing",
      "Enterprise",
      "Case Studies",
    ],
  },
  {
    title: "Resources",
    content: [
      "Blog",
      "Documentation",
      "Community",
      "Support",
    ],
  },
  {
    title: "Legal",
    content: ["Privacy", "Terms", "Security"],
  },
];
