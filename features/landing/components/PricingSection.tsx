"use client";

import React from "react";
import { motion } from "framer-motion";
import { Check } from "lucide-react";
import Link from "next/link";
import { pricingPlans } from "../constants/data";
import { staggerContainer, staggerItem } from "../constants/animations";

export default function PricingSection() {
  return (
    <div className="px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <motion.section
        className="bg-slate-800/50 backdrop-blur-sm rounded-2xl sm:rounded-3xl p-6 sm:p-8 md:p-10 lg:p-12 xl:p-16 space-y-6 sm:space-y-8 lg:space-y-10 xl:space-y-12 border border-purple-500/20 w-full"
        initial={{ opacity: 0, scale: 0.95 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        {/* Section Header */}
        <div className="text-center space-y-4">
          <motion.h2
            className="text-3xl sm:text-4xl font-bold text-white"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Simple, Transparent Pricing
          </motion.h2>

          <motion.p
            className="text-gray-300 text-lg sm:text-xl max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
          >
            Choose the plan that&apos;s right for you
          </motion.p>
        </div>

        {/* Pricing Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8"
          variants={staggerContainer}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {pricingPlans.map((pricing, index) => (
            <motion.div
              key={pricing.plan}
              variants={staggerItem}
              whileHover={{
                scale: 1.05,
                y: -10,
                transition: { duration: 0.3 },
              }}
              className="cursor-pointer"
            >
              <div
                className={`bg-slate-700/50 backdrop-blur-sm border rounded-2xl sm:rounded-3xl p-6 sm:p-8 flex flex-col space-y-4 sm:space-y-6 h-full hover:bg-slate-700/70 transition-all duration-300 relative overflow-hidden ${pricing.popular
                  ? "border-purple-500 shadow-lg shadow-purple-500/25"
                  : "border-slate-600/50 hover:border-purple-500/50"
                  }`}
              >
                {/* Popular Badge */}
                {pricing.popular && (
                  <motion.div
                    className="absolute top-0 right-0 bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-4 sm:px-6 py-1 sm:py-2 text-xs sm:text-sm font-bold rounded-bl-xl"
                    initial={{ x: 100 }}
                    animate={{ x: 0 }}
                    transition={{
                      delay: 0.5,
                      duration: 0.5,
                    }}
                  >
                    Most Popular
                  </motion.div>
                )}

                {/* Plan Name */}
                <div className="text-base sm:text-lg font-bold text-gray-400">
                  {pricing.plan}
                </div>

                {/* Price */}
                <motion.div
                  className="text-4xl sm:text-5xl font-bold text-white"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 10,
                    delay: index * 0.1,
                  }}
                  viewport={{ once: true }}
                >
                  {pricing.price}
                  <span className="text-lg sm:text-xl font-normal text-gray-400">
                    /month
                  </span>
                </motion.div>

                {/* Features List */}
                <ul className="space-y-3 sm:space-y-4 text-gray-300 flex-1 text-base sm:text-lg">
                  {pricing.features.map((feature, featureIndex) => (
                    <motion.li
                      key={feature}
                      className="flex items-start gap-3"
                      initial={{
                        opacity: 0,
                        x: -20,
                      }}
                      whileInView={{
                        opacity: 1,
                        x: 0,
                      }}
                      transition={{
                        delay: index * 0.1 + featureIndex * 0.05,
                        duration: 0.3,
                      }}
                      viewport={{ once: true }}
                    >
                      <Check className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400 flex-shrink-0 mt-0.5" />
                      <span className="flex-1">{feature}</span>
                    </motion.li>
                  ))}
                </ul>

                {/* CTA Button */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link href="/dashboard">
                    <button
                      className={`w-full py-3 sm:py-4 px-4 sm:px-6 rounded-xl sm:rounded-2xl font-bold text-base sm:text-lg transition-all duration-300 ${pricing.popular
                        ? "bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white shadow-lg"
                        : "bg-slate-600 hover:bg-slate-500 text-white border-2 border-slate-500 hover:border-purple-500"
                        }`}
                    >
                      Get Started
                    </button>
                  </Link>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </motion.section>
    </div>
  );
}
