"use client";

import React from "react";
import { motion } from "framer-motion";
import { footerSections } from "../constants/data";
import { staggerContainer, staggerItem } from "../constants/animations";

export default function Footer() {
  return (
    <motion.footer
      className="border-t border-slate-700 mt-16 sm:mt-24 lg:mt-32 pt-12 sm:pt-16 text-gray-400 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
    >
      {/* Footer Content Grid */}
      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12 mb-8 sm:mb-12"
        variants={staggerContainer}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {footerSections.map((section) => (
          <motion.div
            key={section.title}
            variants={staggerItem}
            className="space-y-4"
          >
            <h4 className="font-bold text-white text-lg">
              {section.title}
            </h4>

            {typeof section.content === "string" ? (
              <p className="text-gray-400 leading-relaxed text-sm sm:text-base">
                {section.content}
              </p>
            ) : (
              <ul className="space-y-2 sm:space-y-3">
                {section.content.map((item) => (
                  <motion.li
                    key={item}
                    whileHover={{
                      x: 5,
                      transition: {
                        duration: 0.2,
                      },
                    }}
                    className="cursor-pointer hover:text-cyan-400 transition-colors text-sm sm:text-base"
                  >
                    {item}
                  </motion.li>
                ))}
              </ul>
            )}
          </motion.div>
        ))}
      </motion.div>

      {/* Copyright */}
      <motion.div
        className="text-center border-t border-slate-700 pt-3 sm:pt-4 text-sm sm:text-base lg:text-lg mb-2"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        &copy; 2025 LearnVoxAI. All rights reserved.
      </motion.div>
    </motion.footer>
  );
}
