"use client";

import React from "react";
import { motion } from "framer-motion";
import { Check } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import AI_homepage from "@/assets/images/AI_homepage.png";
import { heroFeatures } from "../constants/data";
import {
  fadeInLeft,
  fadeInRight,
  staggerContainer,
  staggerItem,
  bounceAnimation,
  floatingAnimation
} from "../constants/animations";

export default function HeroSection() {
  return (
    <section className="flex flex-col lg:flex-row items-center gap-8 sm:gap-12 lg:gap-16 xl:gap-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
      {/* Left Content */}
      <motion.div
        className="w-full lg:w-1/2 space-y-6 sm:space-y-8 lg:space-y-10 text-center lg:text-left"
        variants={fadeInLeft}
        initial="hidden"
        animate="visible"
      >
        {/* Main Heading */}
        <motion.h1
          className="text-4xl sm:text-5xl lg:text-6xl font-extrabold leading-tight"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
        >
          <motion.span
            className="bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
            animate={{
              backgroundPosition: [
                "0% 50%",
                "100% 50%",
                "0% 50%",
              ],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: [0.25, 0.46, 0.45, 0.94],
            }}
            style={{ backgroundSize: "200% 200%" }}
          >
            Your AI-Powered Career Coach
          </motion.span>
        </motion.h1>

        {/* Description */}
        <motion.p
          className="text-lg sm:text-xl text-gray-300 leading-relaxed max-w-2xl mx-auto lg:mx-0"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          Transform your career journey with personalized AI
          guidance. Get expert advice, analyze your resume,
          and plan your career path - all powered by advanced
          AI.
        </motion.p>

        {/* Feature List */}
        <motion.ul
          className="space-y-3 sm:space-y-4 text-gray-300 text-base sm:text-lg max-w-md mx-auto lg:mx-0"
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
        >
          {heroFeatures.map((item, index) => (
            <motion.li
              key={item}
              className="flex items-center gap-3 sm:gap-4"
              variants={staggerItem}
              whileHover={{
                x: 10,
                transition: { duration: 0.2 },
              }}
            >
              <motion.div
                animate={bounceAnimation}
                style={{
                  animationDelay: `${index * 0.1}s`,
                }}
              >
                <Check className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-400 flex-shrink-0" />
              </motion.div>
              <span className="flex-1">{item}</span>
            </motion.li>
          ))}
        </motion.ul>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="pt-4"
        >
          <button className="px-6 sm:px-8 md:px-12 py-3 sm:py-4 md:py-5 bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white font-bold text-base sm:text-lg md:text-xl rounded-2xl shadow-2xl transition-all duration-300 w-full sm:w-auto">
            <Link href="/dashboard">Start Free Trial</Link>
          </button>
        </motion.div>
      </motion.div>

      {/* Right Content - Hero Image */}
      <motion.div
        className="w-full lg:w-1/2 mt-8 lg:mt-0"
        variants={fadeInRight}
        initial="hidden"
        animate="visible"
      >
        <motion.div
          animate={floatingAnimation}
          className="relative"
        >
          <div className="relative w-full h-64 sm:h-80 lg:h-96 rounded-2xl sm:rounded-3xl shadow-2xl overflow-hidden border-2 border-purple-500/30">
            <Image
              src={AI_homepage.src}
              alt="Team collaboration and career growth"
              className="w-full h-full object-cover"
              width={800}
              height={600}
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/50 to-transparent"></div>
          </div>
        </motion.div>
      </motion.div>
    </section>
  );
}
