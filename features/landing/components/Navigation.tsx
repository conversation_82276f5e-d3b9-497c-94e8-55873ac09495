"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Menu, X } from "lucide-react";
import Link from "next/link";
import { navigationItems } from "../constants/data";
import { staggerContainer, staggerItem, gradientAnimation } from "../constants/animations";

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <motion.nav
      className="relative flex items-center justify-between py-3 sm:py-4 md:py-6 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Logo */}
      <motion.div
        className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent flex-shrink-0"
        style={{
          backgroundSize: "200% 200%",
        }}
        animate={gradientAnimation}
      >
        LearnVoxAI
      </motion.div>

      {/* Desktop Navigation - Hidden on mobile and small tablets */}
      <motion.ul
        className="hidden xl:flex space-x-6 lg:space-x-8 xl:space-x-12 font-medium text-gray-300 text-sm lg:text-base xl:text-lg"
        variants={staggerContainer}
        initial="hidden"
        animate="visible"
      >
        {navigationItems.map((item) => (
          <motion.li
            key={item}
            className="hover:text-cyan-400 cursor-pointer transition-colors"
            variants={staggerItem}
            whileHover={{ scale: 1.1, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            {item}
          </motion.li>
        ))}
      </motion.ul>

      {/* Get Started Button - Show from small screens up */}
      <motion.div
        className="hidden sm:flex flex-shrink-0"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.3, duration: 0.6 }}
      >
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <button className="px-4 sm:px-6 lg:px-8 py-2 sm:py-2.5 lg:py-3 bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white font-semibold rounded-lg lg:rounded-xl shadow-lg transition-all duration-300 text-sm sm:text-base lg:text-lg whitespace-nowrap">
            <Link href="/dashboard">Get started</Link>
          </button>
        </motion.div>
      </motion.div>

      {/* Mobile Menu Button - Show on small screens */}
      <motion.button
        className="sm:hidden p-2 text-gray-300 hover:text-white transition-colors border border-gray-600 rounded-lg"
        onClick={toggleMenu}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
      </motion.button>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          className="absolute top-full left-0 right-0 bg-slate-900/98 backdrop-blur-md border-t border-slate-700 block sm:hidden z-[9999] shadow-2xl"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <div className="px-4 py-6 space-y-6">
            {/* Mobile Navigation Items */}
            <div className="space-y-3">
              {navigationItems.length > 0 ? (
                navigationItems.map((item) => (
                  <motion.div
                    key={item}
                    className="text-gray-300 hover:text-cyan-400 cursor-pointer transition-colors py-3 border-b border-slate-700/50 text-lg font-medium"
                    whileHover={{ x: 10 }}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item}
                  </motion.div>
                ))
              ) : (
                <div className="text-gray-400">No navigation items</div>
              )}
            </div>

            {/* Mobile Get Started Button */}
            <div className="pt-4 border-t border-slate-700/50">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Link href="/dashboard" className="block w-full">
                  <button
                    className="w-full px-6 py-4 bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-300 text-lg"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Get started
                  </button>
                </Link>
              </motion.div>
            </div>
          </div>
        </motion.div>
      )}
    </motion.nav>
  );
}
