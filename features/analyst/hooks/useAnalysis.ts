"use client";
import { useState, useEffect } from "react";

export const useAnalysis = (analysisData?: any) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const analysisMetadata = analysisData
    ? {
        roleDescription:
          analysisData.roleDescription ?? analysisData.jdDescription,
        analysisType: analysisData.roleDescription ? "role" : "jd",
      }
    : null;

  const cvFileUrl = analysisData?.cvFileUrl ?? null;

  const getFileName = (url: string) => {
    if (!url) return "Resume.pdf";
    try {
      const urlParts = url.split("/");
      const fileName = urlParts[urlParts.length - 1];
      return decodeURIComponent(fileName);
    } catch {
      return "Resume.pdf";
    }
  };

  return {
    analysisMetadata,
    cvFileUrl,
    mounted,
    getFileName,
  };
};
