import { useState, useEffect } from "react";
import { AnalysisData } from "../analyst";
import { AnalystByIdResponse } from "@/services/api/analyst/types/getAnalyst";

interface UseAnalysisDataReturn {
  analysisData: AnalysisData | null;
  analysisResult: AnalystByIdResponse | null;
  isLoading: boolean;
}

export const useAnalysisData = (): UseAnalysisDataReturn => {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [analysisResult, setAnalysisResult] =
    useState<AnalystByIdResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedAnalysisData = sessionStorage.getItem("analysisData");
      const storedAnalysisResult = sessionStorage.getItem("analysisResult");

      if (storedAnalysisData) {
        setAnalysisData(JSON.parse(storedAnalysisData));
      }

      if (storedAnalysisResult) {
        setAnalysisResult(JSON.parse(storedAnalysisResult));
      }

      setIsLoading(false);
    }
  }, []);

  return {
    analysisData,
    analysisResult,
    isLoading,
  };
};
