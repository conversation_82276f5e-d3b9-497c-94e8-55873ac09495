"use client";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  useAnalyzeByRoleMutation,
  useAnalyzeByJDMutation,
} from "@/services/api/analyst";
import { AnalysisType } from "../analyst";

export function useAnalysisLogic() {
  const router = useRouter();
  const [analyzeByRole, { isLoading: isAnalyzingRole }] =
    useAnalyzeByRoleMutation();
  const [analyzeByJD, { isLoading: isAnalyzingJD }] = useAnalyzeByJDMutation();

  const isAnalyzing = isAnalyzingRole || isAnalyzingJD;

  const isFormValid = (
    analysisType: AnalysisType,
    roleDescription: string,
    cvFile: File | null,
    jdFile: File | null
  ) => {
    if (analysisType === "role") {
      return roleDescription.trim() && cvFile;
    }
    if (analysisType === "jd") {
      return cvFile && jdFile;
    }
    return false;
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };

  const handleRoleAnalysis = async (
    roleDescription: string,
    cvFile: File,
    onSuccess: () => void
  ) => {
    try {
      const result = await analyzeByRole({
        agentType: "AI_RESUME_ANALYSIS_ROLE",
        roleDescription: roleDescription.trim(),
        cvFile: cvFile,
      }).unwrap();

      // Convert file to base64 for storage
      const cvFileBase64 = await fileToBase64(cvFile);
      const cvFileUrl = URL.createObjectURL(cvFile);

      // Store analysis result and file data
      if (typeof window !== "undefined") {
        sessionStorage.setItem("analysisResult", JSON.stringify(result));
        sessionStorage.setItem(
          "analysisData",
          JSON.stringify({
            analysisType: "role",
            roleDescription,
            cvFileName: cvFile.name,
            cvFileSize: cvFile.size,
            cvFileType: cvFile.type,
            timestamp: Date.now(),
          })
        );

        // Store file data for PDF viewing
        sessionStorage.setItem("cvFileBase64", cvFileBase64);
        sessionStorage.setItem("cvFileUrl", cvFileUrl);
      }

      const analysisId = result.data._id;
      router.push(`/analyst/analyst-role-description/${analysisId}`);
      onSuccess();
      toast.success("Analysis completed successfully!");
    } catch (error: any) {
      console.error("Analysis failed:", error);
      toast.error(error?.data?.message ?? "Analysis failed. Please try again.");
    }
  };

  const handleJDAnalysis = async (
    cvFile: File,
    jdFile: File,
    onSuccess: () => void
  ) => {
    try {
      const result = await analyzeByJD({
        agentType: "AI_RESUME_ANALYSIS_JD",
        cvFile: cvFile,
        jdFile: jdFile,
      }).unwrap();

      // Convert files to base64 and create URLs for storage
      const cvFileBase64 = await fileToBase64(cvFile);
      const jdFileBase64 = await fileToBase64(jdFile);
      const cvFileUrl = URL.createObjectURL(cvFile);
      const jdFileUrl = URL.createObjectURL(jdFile);

      // Store analysis result and file data
      if (typeof window !== "undefined") {
        sessionStorage.setItem("analysisResult", JSON.stringify(result));
        sessionStorage.setItem(
          "analysisData",
          JSON.stringify({
            analysisType: "jd",
            cvFileName: cvFile.name,
            cvFileSize: cvFile.size,
            cvFileType: cvFile.type,
            jdFileName: jdFile.name,
            jdFileSize: jdFile.size,
            jdFileType: jdFile.type,
            timestamp: Date.now(),
          })
        );

        // Store file data for PDF viewing
        sessionStorage.setItem("cvFileBase64", cvFileBase64);
        sessionStorage.setItem("jdFileBase64", jdFileBase64);
        sessionStorage.setItem("cvFileUrl", cvFileUrl);
        sessionStorage.setItem("jdFileUrl", jdFileUrl);
      }

      const analysisId = result.data._id;
      router.push(`/analyst/analyst-jd-description/${analysisId}`);
      onSuccess();
      toast.success("JD Analysis completed successfully!");
    } catch (error: any) {
      console.error("Analysis failed:", error);
      toast.error(error?.data?.message ?? "Analysis failed. Please try again.");
    }
  };

  const handleAnalyze = async (
    analysisType: AnalysisType,
    roleDescription: string,
    cvFile: File | null,
    jdFile: File | null,
    onSuccess: () => void
  ) => {
    if (!isFormValid(analysisType, roleDescription, cvFile, jdFile)) return;

    if (analysisType === "role" && cvFile && roleDescription.trim()) {
      await handleRoleAnalysis(roleDescription, cvFile, onSuccess);
    } else if (analysisType === "jd" && cvFile && jdFile) {
      await handleJDAnalysis(cvFile, jdFile, onSuccess);
    }
  };

  return {
    isAnalyzing,
    isFormValid,
    handleAnalyze,
  };
}
