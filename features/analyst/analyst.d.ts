import { AnalystResponse } from "@/services/api/analyst/types/analystJD";

export interface CVAnalyzerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export type AnalysisType = "role" | "jd" | "";

export interface FileUploadAreaProps {
  type: "cv" | "jd";
  label: string;
  file: File | null;
  onFileSelect: (file: File) => void;
  onDragOver: (e: React.DragEvent, type: string) => void;
  onDrop: (e: React.DragEvent, type: "cv" | "jd") => void;
}

export interface AnalysisTypeSelectProps {
  analysisType: AnalysisType;
  onAnalysisTypeChange: (value: AnalysisType) => void;
}

export interface RoleAnalysisFormProps {
  roleDescription: string;
  onRoleDescriptionChange: (value: string) => void;
  cvFile: File | null;
  onFileUpload: (file: File | null, type: "cv" | "jd") => void;
  onDragOver: (e: React.DragEvent, type: string) => void;
  onDrop: (e: React.DragEvent, type: "cv" | "jd") => void;
}

export interface JDAnalysisFormProps {
  cvFile: File | null;
  jdFile: File | null;
  onFileUpload: (file: File | null, type: "cv" | "jd") => void;
  onDragOver: (e: React.DragEvent, type: string) => void;
  onDrop: (e: React.DragEvent, type: "cv" | "jd") => void;
}

export interface AnalysisModalHeaderProps {}

export interface AnalysisModalActionsProps {
  isAnalyzing: boolean;
  isFormValid: boolean;
  onAnalyze: () => void;
  onClose: () => void;
}

// Analyst Section
export interface AnalysisContent {
  overall_score: number;
  overall_feedback: string;
  summary_comment: string;
  fit_score: number;
  verdict: string;
  matching_points: string[];
  missing_skills: string[];
  missing_experience: string[];
  recommendations: string[];
  sections?: {
    contact_info: SectionData;
    experience: SectionData;
    education: SectionData;
    skills: SectionData;
  };
}

export interface SectionData {
  score: number;
  comment: string;
  tips_for_improvement: string[];
  whats_good: string[];
  needs_improvement: string[];
}

export interface AnalysisResult {
  statusCode: number;
  message: string;
  data: {
    _id: string;
    userId: string;
    content: AnalysisContent;
    agentType: string;
    cvFileUrl: string;
    cvFileName?: string;
    cvFileSize?: number;
    cvFileType?: string;
    jdFileUrl?: string;
    jdFileName?: string;
    jdFileSize?: number;
    jdFileType?: string;
    createdAt?: string;
    updatedAt?: string;
  };
}

export interface AnalysisData {
  analysisType: "role" | "jd";
  cvFileName: string;
  cvFileSize: number;
  cvFileType: string;
  jdFileName?: string;
  jdFileSize?: number;
  jdFileType?: string;
  roleDescription?: string;
  timestamp: number;
}

export interface DocumentPreviewProps {
  analysisData: AnalysisData;
  analysisResult: AnalystResponse | null;
}

export interface AnalysisMetadata {
  analysisType: string;
  roleDescription: string;
  cvFileName: string;
  cvFileSize: number;
  cvFileType: string;
  timestamp: number;
}

export interface SectionAnalysis {
  score: number;
  comment: string;
  whats_good?: string[];
  needs_improvement?: string[];
  tips_for_improvement?: string[];
}

export interface AnalysisContent {
  overall_score: number;
  fit_score?: number;
  overall_feedback: string;
  summary_comment?: string;
  sections: {
    contact_info?: SectionAnalysis;
    experience?: SectionAnalysis;
    education?: SectionAnalysis;
    skills?: SectionAnalysis;
  };
}
