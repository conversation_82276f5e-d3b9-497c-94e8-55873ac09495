"use client";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { FileUploadArea } from "./FileUploadArea";
import { RoleAnalysisFormProps } from "../analyst";

export function RoleAnalysisForm({
  roleDescription,
  onRoleDescriptionChange,
  cvFile,
  onFileUpload,
  onDragOver,
  onDrop,
}: Readonly<RoleAnalysisFormProps>) {
  return (
    <motion.div
      key="role"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6 p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl border border-purple-100">
        <h3 className="text-lg font-semibold text-purple-800 flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-purple-500"></div>
          <span>Role-Based Analysis</span>
        </h3>

        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700">
            Role Description
          </Label>
          <Input
            placeholder="Enter the role you're targeting (e.g., Senior Frontend Developer, Product Manager...)"
            value={roleDescription}
            onChange={(e) => onRoleDescriptionChange(e.target.value)}
            className="h-12 border-2 border-slate-200 focus:border-purple-400 rounded-xl"
          />
        </div>

        <FileUploadArea
          type="cv"
          label="Upload Your Resume"
          file={cvFile}
          onFileSelect={(file) => onFileUpload(file, "cv")}
          onDragOver={onDragOver}
          onDrop={onDrop}
        />
      </div>
    </motion.div>
  );
}
