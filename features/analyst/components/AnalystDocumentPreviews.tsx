"use client";

import { motion } from "framer-motion";
import { FileText, Target } from "lucide-react";
import React from "react";
import { useAnalysisData } from "../hooks/useAnalysisData";
import LoadingSkeleton from "@/components/LoadingSkeletonJD";
import PDFStyles from "@/components/PDFStyles";
import DocumentCard from "@/components/DocumentCard";
import { handleDownloadFile, handleViewFile } from "@/lib/fileUtils";
import { JD_COLOR_SCHEME, RESUME_COLOR_SCHEME } from "@/common/constants/color";

const AnalystDocumentPreviews = () => {
  const { analysisData, analysisResult, isLoading } = useAnalysisData();

  const cvFileUrl = analysisResult?.data?.cvFileUrl ?? "";
  const jdFileUrl = analysisResult?.data?.jdFileUrl ?? "";

  if (isLoading || !analysisData) {
    return <LoadingSkeleton />;
  }

  return (
    <>
      <PDFStyles />

      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={`grid grid-cols-1 ${
          analysisData.analysisType === "jd"
            ? "lg:grid-cols-2"
            : "lg:grid-cols-1 max-w-4xl mx-auto"
        } gap-8 mb-8`}
      >
        {/* Resume Preview */}
        <DocumentCard
          title="Resume"
          fileName={analysisData.cvFileName}
          fileSize={analysisData.cvFileSize}
          timestamp={analysisData.timestamp}
          fileUrl={cvFileUrl}
          icon={FileText}
          bgGradient="bg-gradient-to-br from-blue-50 via-white to-indigo-50"
          borderColor="border border-blue-200/50"
          onViewFile={handleViewFile}
          onDownloadFile={handleDownloadFile}
          colorScheme={RESUME_COLOR_SCHEME}
          roleDescription={analysisData.roleDescription}
          showRoleDescription={analysisData.analysisType === "role"}
        />

        {/* Job Description Preview - Only show for JD analysis type */}
        {analysisData.analysisType === "jd" && analysisData.jdFileName && (
          <DocumentCard
            title="Job Description"
            fileName={analysisData.jdFileName}
            fileSize={analysisData.jdFileSize ?? 0}
            timestamp={analysisData.timestamp}
            fileUrl={jdFileUrl}
            icon={Target}
            bgGradient="bg-gradient-to-br from-orange-50 via-white to-amber-50"
            borderColor="border border-orange-200/50"
            onViewFile={handleViewFile}
            onDownloadFile={handleDownloadFile}
            colorScheme={JD_COLOR_SCHEME}
          />
        )}
      </motion.div>
    </>
  );
};

export default AnalystDocumentPreviews;
