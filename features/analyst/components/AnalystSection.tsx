"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import {
  containerVariants,
  itemVariants,
} from "@/styles/animations/analystRole.variants";

import AnalystFooter from "./AnalystFooter";
import { AnalysisContent, AnalysisResult } from "../analyst";
import LoadingSkeleton from "@/components/LoadingSkeleton";
import NoDataMessage from "@/components/NoDataMessage";
import OverallScoreCard from "@/components/OverallScoreCard";
import FeedbackCard from "@/components/FeedbackCard";
import SummarySection from "@/components/SummarySection";
import MatchingPoints from "@/components/MatchingPoints";
import MissingSkills from "@/components/MissingSkills";
import MissingExperience from "@/components/MissingExperience";
import Recommendations from "@/components/Recommendations";
import SectionAnalysis from "@/components/SectionAnalysis";

const AnalystSection = () => {
  const [analysisResult, setAnalysisResult] = useState<AnalysisContent | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedResult = sessionStorage.getItem("analysisResult");
      if (storedResult) {
        try {
          const parsed: AnalysisResult = JSON.parse(storedResult);
          setAnalysisResult(parsed.data.content);
        } catch (error) {
          console.error("Error parsing analysis result:", error);
        }
      }
      setLoading(false);
    }
  }, []);

  if (loading) {
    return <LoadingSkeleton />;
  }

  if (!analysisResult) {
    return <NoDataMessage />;
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Section 1: Overall Score & Feedback */}
      <motion.div
        variants={itemVariants}
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
      >
        <OverallScoreCard score={analysisResult.overall_score} />
        <FeedbackCard feedback={analysisResult.overall_feedback} />
      </motion.div>

      {/* Section 2: Summary, Fit Score, Verdict */}
      <motion.div variants={itemVariants}>
        <SummarySection
          summaryComment={analysisResult.summary_comment}
          fitScore={analysisResult.fit_score}
          verdict={analysisResult.verdict}
        />
      </motion.div>

      {/* Section 3: Matching Points */}
      <motion.div variants={itemVariants}>
        <MatchingPoints points={analysisResult.matching_points} />
      </motion.div>

      {/* Section 4: Missing Skills */}
      <motion.div variants={itemVariants}>
        <MissingSkills skills={analysisResult.missing_skills} />
      </motion.div>

      {/* Section 5: Missing Experience */}
      <motion.div variants={itemVariants}>
        <MissingExperience experience={analysisResult.missing_experience} />
      </motion.div>

      {/* Section 6: Recommendations */}
      <motion.div variants={itemVariants}>
        <Recommendations recommendations={analysisResult.recommendations} />
      </motion.div>

      {/* Detailed Sections Analysis - Only show if sections data exists */}
      {analysisResult.sections && (
        <motion.div variants={itemVariants}>
          <SectionAnalysis sections={analysisResult.sections} />
        </motion.div>
      )}

      {/* Footer Banner */}
      <AnalystFooter />
    </motion.div>
  );
};

export default AnalystSection;
