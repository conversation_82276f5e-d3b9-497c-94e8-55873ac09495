"use client";
import { motion } from "framer-motion";
import { FileUploadArea } from "./FileUploadArea";
import { JDAnalysisFormProps } from "../analyst";

export function JDAnalysisForm({
  cvFile,
  jdFile,
  onFileUpload,
  onDragOver,
  onDrop,
}: Readonly<JDAnalysisFormProps>) {
  return (
    <motion.div
      key="jd"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6 p-6 bg-gradient-to-br from-orange-50 to-amber-50 rounded-2xl border border-orange-100">
        <h3 className="text-lg font-semibold text-orange-800 flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
          <span>Job Description Comparison</span>
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FileUploadArea
            type="cv"
            label="Upload Your Resume"
            file={cvFile}
            onFileSelect={(file) => onFileUpload(file, "cv")}
            onDragOver={onDragOver}
            onDrop={onDrop}
          />

          <FileUploadArea
            type="jd"
            label="Upload Job Description"
            file={jdFile}
            onFileSelect={(file) => onFileUpload(file, "jd")}
            onDragOver={onDragOver}
            onDrop={onDrop}
          />
        </div>
      </div>
    </motion.div>
  );
}
