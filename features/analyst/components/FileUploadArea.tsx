"use client";
import { useState, memo } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Upload, Check, X } from "lucide-react";
import { motion } from "framer-motion";
import { FileUploadAreaProps } from "../analyst";

export const FileUploadArea = memo(
  ({
    type,
    label,
    file,
    onFileSelect,
    onDragOver,
    onDrop,
  }: FileUploadAreaProps) => {
    const [isDragOver, setIsDragOver] = useState(false);

    const handleDragEnter = (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(true);
    };

    const handleDragLeave = (e: React.DragEvent) => {
      e.preventDefault();
      if (!e.currentTarget.contains(e.relatedTarget as Node)) {
        setIsDragOver(false);
      }
    };

    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      onDragOver(e, type);
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      onDrop(e, type);
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-2"
      >
        <Label className="text-sm font-medium text-slate-700">{label}</Label>
        <motion.div
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          className={`relative border-2 border-dashed rounded-xl p-6 transition-all duration-300 cursor-pointer ${
            isDragOver
              ? "border-blue-500 bg-blue-50 scale-105"
              : file
              ? "border-emerald-400 bg-emerald-50"
              : "border-slate-300 hover:border-blue-400 hover:bg-slate-50"
          }`}
          onClick={() => document.getElementById(`file-${type}`)?.click()}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          <input
            id={`file-${type}`}
            type="file"
            accept=".pdf"
            className="hidden"
            onChange={(e) =>
              e.target.files?.[0] && onFileSelect(e.target.files[0])
            }
          />

          <div className="flex flex-col items-center text-center">
            {file ? (
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                className="contents"
              >
                <div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center mb-3">
                  <Check className="w-6 h-6 text-emerald-600" />
                </div>
                <p className="text-sm font-medium text-emerald-700">
                  {file.name}
                </p>
                <p className="text-xs text-emerald-600 mt-1">
                  File uploaded successfully
                </p>
              </motion.div>
            ) : isDragOver ? (
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                className="contents"
              >
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                  <Upload className="w-6 h-6 text-blue-600 animate-bounce" />
                </div>
                <p className="text-sm font-medium text-blue-700 mb-1">
                  Drop your PDF here
                </p>
                <p className="text-xs text-blue-600">
                  Release to upload the file
                </p>
              </motion.div>
            ) : (
              <>
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-3">
                  <Upload className="w-6 h-6 text-blue-600" />
                </div>
                <p className="text-sm font-medium text-slate-700 mb-1">
                  Drag & drop your PDF here
                </p>
                <p className="text-xs text-slate-500">
                  or click to browse files
                </p>
              </>
            )}
          </div>

          {file && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 h-8 w-8 p-0 hover:bg-red-100"
              onClick={(e) => {
                e.stopPropagation();
                onFileSelect(null as any);
              }}
            >
              <X className="w-4 h-4 text-red-500" />
            </Button>
          )}

          {isDragOver && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="absolute inset-0 bg-blue-500/10 rounded-xl border-2 border-blue-500 border-dashed"
            />
          )}
        </motion.div>
      </motion.div>
    );
  }
);

FileUploadArea.displayName = "FileUploadArea";
