import { motion } from "framer-motion";
import { CheckCircle, MessageSquare } from "lucide-react";

const AnalystFooter = () => {
  return (
    <div>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="mt-12 p-8 bg-gradient-to-r from-orange-600 via-amber-600 to-yellow-600 rounded-2xl text-white text-center"
      >
        <div className="max-w-2xl mx-auto space-y-4">
          <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mx-auto">
            <MessageSquare className="w-8 h-8" />
          </div>
          <h3 className="text-2xl font-bold">Comparison Analysis Complete!</h3>
          <p className="text-orange-100 text-lg">
            Your resume has been thoroughly compared with the job description.
            Ready to ace your interview?
          </p>
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-orange-600 hover:bg-orange-50 font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center space-x-2 mx-auto"
            >
              <CheckCircle className="w-5 h-5" />
              <span>Start Interview Preparation</span>
            </motion.button>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default AnalystFooter;
