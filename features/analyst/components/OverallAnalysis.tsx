"use client";
import { motion } from "framer-motion";
import { Star } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { AnalysisContent } from "../analyst";

interface OverallAnalysisProps {
  content: AnalysisContent;
  variants: any;
}

export default function OverallAnalysis({
  content,
  variants,
}: Readonly<OverallAnalysisProps>) {
  return (
    <motion.div variants={variants}>
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="w-5 h-5 text-yellow-500" />
            <span>Overall Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-slate-700">
                  Overall Score
                </span>
                <span className="text-2xl font-bold text-blue-600">
                  {content.overall_score}%
                </span>
              </div>
              <Progress value={content.overall_score} className="h-3" />
            </div>
            {content.fit_score !== undefined && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-slate-700">
                    Role Fit Score
                  </span>
                  <span className="text-2xl font-bold text-purple-600">
                    {content.fit_score}%
                  </span>
                </div>
                <Progress value={content.fit_score} className="h-3" />
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-xl border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-2">
                Overall Feedback
              </h4>
              <p className="text-blue-700 text-sm leading-relaxed">
                {content.overall_feedback}
              </p>
            </div>
            {content.summary_comment && (
              <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                <h4 className="font-semibold text-purple-800 mb-2">
                  Summary Comment
                </h4>
                <p className="text-purple-700 text-sm leading-relaxed">
                  {content.summary_comment}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
