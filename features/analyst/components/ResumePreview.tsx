"use client";
import { motion } from "framer-motion";
import {
  FileText,
  Star,
  Target,
  CheckCircle,
  Eye,
  Download,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface ResumePreviewProps {
  cvFileUrl: string | null;
  serverCvFileUrl?: string;
  fileName: string;
  overallScore: number;
  fitScore?: number;
  onViewPdf: () => void;
  onDownloadPdf: () => void;
}

export default function ResumePreview({
  cvFileUrl,
  serverCvFileUrl,
  fileName,
  overallScore,
  fitScore,
  onViewPdf,
  onDownloadPdf,
}: Readonly<ResumePreviewProps>) {
  const displayUrl = serverCvFileUrl || cvFileUrl;

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="lg:col-span-1"
    >
      <Card className="h-fit sticky top-24 border-0 shadow-2xl bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                <FileText className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-slate-800">
                  Resume File
                </h3>
                <p className="text-xs text-slate-500">AI-Powered Insights</p>
              </div>
            </div>
            <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 shadow-md">
              {displayUrl ? "Active" : "Pending"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* PDF Preview Container */}
          <div className="relative group">
            <div className="aspect-[3/4] bg-gradient-to-br from-slate-100 via-blue-50 to-purple-50 rounded-2xl overflow-hidden border border-slate-200/50 shadow-inner">
              {displayUrl ? (
                <div className="w-full h-full relative">
                  <iframe
                    src={`${displayUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
                    className="w-full h-full rounded-2xl"
                    title="Resume Preview"
                    style={{
                      border: "none",
                      background: "transparent",
                    }}
                    onError={(e) => {
                      console.error("PDF iframe failed to load:", displayUrl);
                      // Fallback: hide iframe and show placeholder
                      (e.target as HTMLIFrameElement).style.display = "none";
                    }}
                  />

                  {/* Hover overlay */}
                  <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/30 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-b-2xl flex items-end justify-center pb-4 pointer-events-none">
                    <div className="flex space-x-2 pointer-events-auto">
                      <Button
                        size="sm"
                        onClick={onViewPdf}
                        className="bg-white/95 hover:bg-white text-slate-700 shadow-lg border-0 backdrop-blur-sm"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={onDownloadPdf}
                        className="bg-white/95 hover:bg-white text-slate-700 border-slate-200 shadow-lg backdrop-blur-sm"
                      >
                        <Download className="w-4 h-4 mr-1" />
                        Download
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mx-auto shadow-lg">
                      <FileText className="w-10 h-10 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-slate-700">
                        {fileName || "Resume.pdf"}
                      </p>
                      <p className="text-xs text-slate-500 mt-1">
                        PDF Preview Loading...
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg z-20">
              <CheckCircle className="w-3 h-3 text-white" />
            </div>
          </div>

          {/* Score Cards */}
          <div className="space-y-4">
            {/* Overall Score Card */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 p-6 text-white shadow-xl">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <Star className="w-4 h-4" />
                    </div>
                    <span className="text-sm font-medium text-blue-100">
                      Overall Score
                    </span>
                  </div>
                  <span className="text-3xl font-bold">{overallScore}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2.5">
                  <div
                    className="bg-white rounded-full h-2.5 transition-all duration-700"
                    style={{ width: `${overallScore}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Role Fit Score Card */}
            {fitScore !== undefined && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-purple-500 via-purple-600 to-pink-600 p-6 text-white shadow-xl">
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"></div>
                <div className="relative">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                        <Target className="w-4 h-4" />
                      </div>
                      <span className="text-sm font-medium text-purple-100">
                        Role Fit Score
                      </span>
                    </div>
                    <span className="text-3xl font-bold">{fitScore}%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2.5">
                    <div
                      className="bg-white rounded-full h-2.5 transition-all duration-700"
                      style={{ width: `${fitScore}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
