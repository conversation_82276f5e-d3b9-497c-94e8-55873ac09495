"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { useRouter } from "next/navigation";
import CVAnalyzerModal from "@/features/dashboard/components/DashboardCVAnalyze";
import { useGetMyAnalystsQuery } from "@/services/api/analyst";
import HeroSection from "@/components/HeroSection";
import MetricsCards from "@/components/MetricsCards";
import RecentAnalyses from "@/components/RecentAnalyses";
import QuickTips from "@/components/QuickTips";
import AIAssistant from "@/components/AIAssistant";
import FeaturesSection from "@/components/FeaturesSection";
import {
  containerVariants,
  itemVariants,
} from "@/styles/animations/mainAnalyst.variants";

const ResumeAnalyzer = () => {
  const router = useRouter();
  const [showAllHistory, setShowAllHistory] = useState(false);
  const [cvAnalyzerOpen, setCvAnalyzerOpen] = useState(false);

  const {
    data: analystsData,
    error,
    isLoading,
    refetch,
  } = useGetMyAnalystsQuery();

  const analysts = analystsData?.data || [];

  const displayedAnalysts = showAllHistory ? analysts : analysts.slice(0, 4);

  const handleViewReport = (
    analystId: string,
    analysisType?: "role" | "jd"
  ) => {
    if (analysisType === "jd") {
      router.push(`/analyst/analyst-jd-description/${analystId}`);
    } else {
      router.push(`/analyst/analyst-role-description/${analystId}`);
    }
  };

  const totalAnalyses = analysts.length;
  const averageScore =
    analysts.length > 0
      ? Math.round(
          analysts.reduce(
            (sum, analyst) => sum + analyst.content.overall_score,
            0
          ) / analysts.length
        )
      : 0;

  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const thisMonthCount = analysts.filter((analyst) => {
    if (!analyst.createdAt) return false;
    const analystDate = new Date(analyst.createdAt);
    return (
      analystDate.getMonth() === currentMonth &&
      analystDate.getFullYear() === currentYear
    );
  }).length;

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50"
      >
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Hero Section */}
          <HeroSection
            onUploadClick={() => setCvAnalyzerOpen(true)}
            variants={itemVariants}
          />

          {/* Metrics Section */}
          <MetricsCards
            totalAnalyses={totalAnalyses}
            averageScore={averageScore}
            thisMonthCount={thisMonthCount}
            variants={itemVariants}
          />

          {/* Recent History */}
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <RecentAnalyses
                analysts={analysts}
                displayedAnalysts={displayedAnalysts}
                isLoading={isLoading}
                error={error}
                showAllHistory={showAllHistory}
                onShowAllHistory={setShowAllHistory}
                onViewReport={handleViewReport} // Updated function
                onRefetch={refetch}
              />
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <QuickTips />
              <AIAssistant />
            </div>
          </div>

          {/* Features Section */}
          <FeaturesSection variants={itemVariants} />
        </div>
      </motion.div>

      <CVAnalyzerModal open={cvAnalyzerOpen} onOpenChange={setCvAnalyzerOpen} />
    </>
  );
};

export default ResumeAnalyzer;
