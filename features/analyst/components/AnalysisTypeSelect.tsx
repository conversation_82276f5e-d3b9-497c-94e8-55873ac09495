"use client";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { motion } from "framer-motion";
import { AnalysisTypeSelectProps } from "../analyst";

export function AnalysisTypeSelect({
  analysisType,
  onAnalysisTypeChange,
}: Readonly<AnalysisTypeSelectProps>) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-3"
    >
      <div className="flex items-center space-x-2">
        <Label className="text-base font-semibold text-slate-800">
          Choose Analysis Type
        </Label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="w-4 h-4 text-slate-400" />
            </TooltipTrigger>
            <TooltipContent>
              <p className="max-w-xs">
                Select how you want to analyze your resume: based on a specific
                role or compared with a job description
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Select value={analysisType} onValueChange={onAnalysisTypeChange}>
        <SelectTrigger className="w-full h-12 border-2 border-slate-200 focus:border-blue-400 rounded-xl">
          <SelectValue placeholder="Select analysis type..." />
        </SelectTrigger>
        <SelectContent className="bg-white/95 backdrop-blur-xl border border-slate-200 shadow-xl">
          <SelectItem value="role">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full bg-purple-500"></div>
              <span>Phân tích theo vai trò (Analyze based on role)</span>
            </div>
          </SelectItem>
          <SelectItem value="jd">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 rounded-full bg-orange-500"></div>
              <span>So sánh với JD (Compare with Job Description)</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </motion.div>
  );
}
