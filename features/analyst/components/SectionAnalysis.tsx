"use client";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>cle,
  T<PERSON><PERSON><PERSON>p,
  <PERSON>hum<PERSON><PERSON>p,
  <PERSON><PERSON><PERSON>riangle,
} from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { SectionAnalysis as SectionAnalysisType } from "../analyst";

interface SectionAnalysisProps {
  title: string;
  icon: React.ReactNode;
  analysis: SectionAnalysisType;
  gradientFrom: string;
  gradientTo: string;
  variants: any;
}

export default function SectionAnalysis({
  title,
  icon,
  analysis,
  gradientFrom,
  gradientTo,
  variants,
}: Readonly<SectionAnalysisProps>) {
  return (
    <motion.div variants={variants}>
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {icon}
              <span>{title}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-blue-600">
                {analysis.score}%
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Progress value={analysis.score} className="h-3" />

          <div
            className={`p-4 bg-gradient-to-br ${gradientFrom} ${gradientTo} rounded-xl border border-blue-200`}
          >
            <h4 className="font-semibold text-blue-800 mb-2">
              Analysis Comment
            </h4>
            <p className="text-blue-700 text-sm leading-relaxed">
              {analysis.comment}
            </p>
          </div>

          {analysis.whats_good && analysis.whats_good.length > 0 && (
            <div className="p-4 bg-green-50 rounded-xl border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <ThumbsUp className="w-4 h-4 text-green-600" />
                <h4 className="font-semibold text-green-800">What's Good</h4>
              </div>
              <ul className="space-y-1">
                {analysis.whats_good.map((item: string, index: number) => (
                  <li
                    key={index}
                    className="text-green-700 text-sm flex items-start space-x-2"
                  >
                    <CheckCircle className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {analysis.needs_improvement &&
            analysis.needs_improvement.length > 0 && (
              <div className="p-4 bg-amber-50 rounded-xl border border-amber-200">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-amber-600" />
                  <h4 className="font-semibold text-amber-800">
                    Needs Improvement
                  </h4>
                </div>
                <ul className="space-y-1">
                  {analysis.needs_improvement.map(
                    (item: string, index: number) => (
                      <li
                        key={index}
                        className="text-amber-700 text-sm flex items-start space-x-2"
                      >
                        <AlertCircle className="w-3 h-3 text-amber-500 mt-0.5 flex-shrink-0" />
                        <span>{item}</span>
                      </li>
                    )
                  )}
                </ul>
              </div>
            )}

          {analysis.tips_for_improvement &&
            analysis.tips_for_improvement.length > 0 && (
              <div className="p-4 bg-purple-50 rounded-xl border border-purple-200">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-purple-600" />
                  <h4 className="font-semibold text-purple-800">
                    Tips for Improvement
                  </h4>
                </div>
                <ul className="space-y-1">
                  {analysis.tips_for_improvement.map(
                    (tip: string, index: number) => (
                      <li
                        key={index}
                        className="text-purple-700 text-sm flex items-start space-x-2"
                      >
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                        <span>{tip}</span>
                      </li>
                    )
                  )}
                </ul>
              </div>
            )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
