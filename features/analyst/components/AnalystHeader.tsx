"use client";
import { motion } from "framer-motion";
import {  Target } from "lucide-react";

const Header = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white/80 backdrop-blur-xl border-b border-slate-200 sticky top-0 z-10"
    >
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
                className="w-10 h-10 rounded-xl bg-gradient-to-br from-orange-500 to-amber-500 flex items-center justify-center"
              >
                <Target className="w-5 h-5 text-white" />
              </motion.div>
              <div>
                <h1 className="text-xl font-bold text-slate-800">
                  Job Analysis Results
                </h1>
                <p className="text-sm text-slate-600">
                  Comprehensive Resume Analysis
                </p>
              </div>
            </div>
          </div>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.3, type: "spring" }}
            className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium"
          >
            Analysis Complete
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default Header;
