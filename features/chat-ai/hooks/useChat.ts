import {
  useDeleteSessionByIdMutation,
  useGetMySessionsQuery,
  useGetSessionByIdQuery,
  useSendMessageMutation,
} from "@/services/api/chat-ai";
import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";

export const useChat = () => {
  const router = useRouter();
  const params = useParams();

  const sessionIdFromUrl = params?.sessionId as string;

  useGetMySessionsQuery();
  const { refetch } = useGetSessionByIdQuery(sessionIdFromUrl, {
    skip: !sessionIdFromUrl,
  });

  const [sendMessage] = useSendMessageMutation();
  const [deleteSessionById] = useDeleteSessionByIdMutation();

  // set current session from url
  useEffect(() => {
    // refetch current session
    refetch();
  }, [sessionIdFromUrl]);

  const handleSessionClick = (sessionId: string) => {
    router.push(`/chat-ai/${sessionId}`);
  };

  const handleSubmit = (message: string): void => {
    if (!message.trim()) return;
    sendMessage({ sessionId: sessionIdFromUrl, message });
  };

  const removeSession = (sessionId: string) => {
    deleteSessionById(sessionId);
  };

  const handleNewChat = () => {
    const newSessionId = uuidv4();
    router.push(`/chat-ai/${newSessionId}`);
  };

  return {
    handleSubmit,
    handleSessionClick,
    removeSession,
    handleNewChat,
  };
};

export default useChat;
