"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MessageSquare, Sparkles, Lightbulb, HelpCircle } from "lucide-react";

const ChatSuggestions = ({
  setMessage,
}: {
  setMessage: (message: string) => void;
}) => {
  const suggestions = [
    {
      icon: <Sparkles className="h-5 w-5" />,
      title: "Tell me about your services",
      description: "Learn more about what we offer",
    },
    {
      icon: <Lightbulb className="h-5 w-5" />,
      title: "How can I get started?",
      description: "Begin your journey with us",
    },
    {
      icon: <MessageSquare className="h-5 w-5" />,
      title: "What are your pricing plans?",
      description: "Explore our flexible options",
    },
    {
      icon: <HelpCircle className="h-5 w-5" />,
      title: "Can you help me with a specific problem?",
      description: "Let's solve it together",
    },
  ];

  return (
    <div className="flex-1 flex items-center justify-center p-4">
      <div className="max-w-3xl w-full space-y-8">
        <div className="text-center space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            How can I help you today?
          </h2>
          <p className="text-muted-foreground">
            Choose a suggestion or type your own message
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {suggestions.map((suggestion, index) => (
            <Card
              key={index}
              className="hover:bg-accent transition-colors cursor-pointer"
            >
              <CardContent className="p-6">
                <Button
                  variant="ghost"
                  className="w-full h-full flex flex-col items-center justify-center gap-2 p-4"
                  onClick={() => setMessage(suggestion.title)}
                >
                  <div className="text-primary">{suggestion.icon}</div>
                  <div className="text-center">
                    <h3 className="font-semibold">{suggestion.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {suggestion.description}
                    </p>
                  </div>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChatSuggestions;
