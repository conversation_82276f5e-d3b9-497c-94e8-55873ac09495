"use client";

import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import ReactMarkdown from "react-markdown";
import { ChatMessageContent } from "@/common/types/chat-ai";
import { Loader } from "lucide-react";

const Message = ({ message }: { message: ChatMessageContent }) => {
  const isUser = message.role === "user";

  return (
    <div className={cn("flex gap-3", isUser ? "justify-end" : "justify-start")}>
      {!isUser && (
        <Avatar className="h-8 w-8">
          <AvatarImage src="/bot-avatar.png" alt="AI Assistant" />
          <AvatarFallback>AI</AvatarFallback>
        </Avatar>
      )}
      <div
        className={cn(
          "max-w-[70%] rounded-lg p-3",
          isUser ? "bg-primary text-primary-foreground" : "bg-muted"
        )}
      >
        {message?.loading ? (
          <div className="flex gap-2 items-center justify-start">
            <Loader className="animate-spin" />
            <p>Thinking...</p>
          </div>
        ) : (
          <>
            <ReactMarkdown>{message.content}</ReactMarkdown>
            <span className="text-xs opacity-70 mt-1 block">
              {new Date(message?.timestamp || "").toLocaleTimeString()}
            </span>
          </>
        )}
      </div>
      {isUser && (
        <Avatar className="h-8 w-8">
          <AvatarImage src="/user-avatar.png" alt="User" />
          <AvatarFallback>U</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};

export default Message;
