"use client";
import React, { useState } from "react";
import ChatSuggestions from "./ChatSuggestions";
import ChatMessages from "./ChatMessages";
import ChatInput from "./ChatInput";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

const ChatMain = () => {
  const { currentSession } = useSelector((state: RootState) => state.chatAi);
  const [message, setMessage] = useState("");

  const hasMessages = currentSession?.content?.length || 0 > 0;

  return (
    <div className="flex flex-1 flex-col h-full">
      <div className="flex-1 overflow-hidden">
        {hasMessages ? (
          <ChatMessages />
        ) : (
          <ChatSuggestions setMessage={setMessage} />
        )}
      </div>
      <ChatInput message={message} setMessage={setMessage} />
    </div>
  );
};

export default ChatMain;
