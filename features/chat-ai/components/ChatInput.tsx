"use client";

import { Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useChat } from "../hooks/useChat";

const ChatInput = ({
  message,
  setMessage,
}: {
  message: string;
  setMessage: (message: string) => void;
}) => {
  const { handleSubmit } = useChat();

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(message);
    setMessage("");
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
  };

  return (
    <div className="border-t p-4">
      <form onSubmit={onSubmit} className="flex gap-2">
        <Textarea
          value={message}
          onChange={handleMessageChange}
          placeholder="Type your message..."
          className="min-h-[60px] resize-none"
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              handleSubmit(message);
              setMessage("");
            }
          }}
        />
        <Button
          type="submit"
          size="icon"
          className="h-[60px] w-[60px]"
          disabled={!message.trim()}
        >
          <Send className="h-4 w-4" />
        </Button>
      </form>
    </div>
  );
};

export default ChatInput;
