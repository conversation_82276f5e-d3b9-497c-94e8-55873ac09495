"use client";

import React, { useEffect, useRef } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import Message from "./Message";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";

const ChatMessages = () => {
  const bottomRef = useRef<HTMLDivElement | null>(null);
  const { currentSession } = useSelector((state: RootState) => state.chatAi);

  const loadingMessage = {
    role: "assistant",
    content: "Thinking...",
    loading: true,
  };

  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [currentSession?.content, currentSession?.loading]);

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-4">
        {currentSession?.content?.map((message, index) => (
          <Message key={index} message={message} />
        ))}
        {currentSession?.loading && <Message message={loadingMessage} />}
        <div ref={bottomRef} />
      </div>
    </ScrollArea>
  );
};

export default ChatMessages;
