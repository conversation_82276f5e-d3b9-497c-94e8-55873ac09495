import {
  ConversationRequest,
  ConversationResponse,
} from "@/common/types/conversation";
import { useCreateConversationMutation } from "@/services/api/conversation";

export const useCreateConversation = () => {
  const [createConversation, { isLoading }] = useCreateConversationMutation();

  const createConversationHandler = async (
    conversation: ConversationRequest
  ): Promise<ConversationResponse> => {
    console.log("💬 Conversation:", conversation);
    const response = await createConversation(conversation);
    return response.data as ConversationResponse;
  };

  return {
    createConversation,
    isLoading,
    createConversationHandler,
  };
};
