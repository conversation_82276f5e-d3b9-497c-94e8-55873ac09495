import { InterviewQuestionSessionType } from "@/common/types/interview-question";
import { useVapi } from "@/hooks/useVapi";
import { useGetQuestionInterviewByIdQuery } from "@/services/api/interview-question";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useCreateConversation } from "./useCreateConversation";
import { ConversationRequest } from "@/common/types/conversation";
import { distinctTime } from "@/lib/timer";
import { RootState } from "@/redux/store";
import { useSelector } from "react-redux";

export interface Message {
  id?: number;
  sender: "you" | "user" | "assistant";
  content: string;
  timestamp?: string;
  isComplete?: boolean;
}
export const useInterviewStart = () => {
  const {
    startCall,
    endCall,
    assistantSpeech,
    userSpeech,
    conversationLog,
    isCallActive,
    converstationLatest,
  } = useVapi();

  const { user } = useSelector((state: RootState) => state.auth);

  const { createConversationHand<PERSON>, isLoading } = useCreateConversation();

  const { interviewId } = useParams();
  const router = useRouter();

  const { data: interviewQuestion, isLoading: isLoadingFetch } =
    useGetQuestionInterviewByIdQuery(interviewId as string);
  const durationInMinutes = parseInt(
    interviewQuestion?.interviewDuration || "0"
  );

  const [inputMessage, setInputMessage] = useState("");
  const [isMuted, setIsMuted] = useState(false);
  const [isInterviewing, setIsInterviewing] = useState<boolean>(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [viewFeedbackAnalysis, setViewFeedbackAnalysis] = useState(false);
  const [timer, setTimer] = useState(durationInMinutes * 60);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (interviewQuestion?.interviewDuration) {
      const durationInMinutes = parseInt(
        interviewQuestion.interviewDuration,
        10
      );
      setTimer(durationInMinutes * 60);
    }
  }, [interviewQuestion]);

  useEffect(() => {
    if (isInterviewing) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      if (timer <= 0) {
        endCall();
        handleFeedbackAnalysis();
      }
      return () => clearInterval(interval);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timer, isInterviewing]);

  const handleToggleCall = () => {
    setIsInterviewing(!isInterviewing);
    if (isInterviewing) {
      endCall();
    } else {
      setMessages([]);
      startCall(
        interviewQuestion as InterviewQuestionSessionType,
        user?.firstName + " " + user?.lastName || user?.email
      );
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleFeedbackAnalysis = async () => {
    const startTime =
      parseInt(interviewQuestion?.interviewDuration || "0") * 60;
    const conversationRequest: ConversationRequest = {
      interviewId: interviewId as string,
      interviewDuration: distinctTime(startTime, timer),
      conversationLog: (converstationLatest as any[])
        .filter((conv) => conv.role !== "system")
        .map((conv) => ({
          role: conv.role,
          content: conv.content || conv.message,
        })),
    };
    const response = await createConversationHandler(conversationRequest);
    router.push(`/interview/feedbacks/${response._id}`);
  };

  useEffect(() => {
    if (conversationLog.length > 0) {
      const vapiMessages = conversationLog.map((log, index) => ({
        id: index + 1,
        sender: log.role === "assistant" ? "assistant" : "you",
        content: log.message,
        timestamp: log.timestamp,
        isComplete: log.isComplete,
      }));

      console.log("💬 VAPI Messages:", vapiMessages);

      setMessages(vapiMessages as Message[]);
    }
  }, [conversationLog]);

  useEffect(() => {
    setIsInterviewing(isCallActive);
  }, [isCallActive]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (converstationLatest?.length > 0 && !isInterviewing) {
      setViewFeedbackAnalysis(true);
    }
  }, [converstationLatest, isInterviewing]);

  return {
    interviewQuestion,
    handleToggleCall,
    inputMessage,
    setInputMessage,
    isMuted,
    setIsMuted,
    messages,
    setMessages,
    messagesEndRef,
    isInterviewing,
    assistantSpeech,
    userSpeech,
    converstationLatest,
    viewFeedbackAnalysis,
    handleFeedbackAnalysis,
    isLoading,
    isLoadingFetch,
    timer,
  };
};
