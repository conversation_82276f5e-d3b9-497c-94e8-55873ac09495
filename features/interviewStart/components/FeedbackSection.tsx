import { Feedback } from "@/common/types/conversation";
import SkeletonFeedback from "./SkeletonFeedback";
import SkillsGrid from "./SkillGrid";
import QuestionCard from "./QuestionCard";

export function FeedbackSection({
  feedback,
  isLoading,
}: {
  feedback: Feedback;
  isLoading?: boolean;
}) {
  if (isLoading) {
    return <SkeletonFeedback />;
  }

  if (!feedback) {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-12 border border-gray-200/50 text-center">
        <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center text-4xl">
          📋
        </div>
        <h3 className="text-2xl font-bold text-gray-800 mb-4">
          No Feedback Yet
        </h3>
        <p className="text-gray-600 text-lg leading-relaxed">
          Complete your interview to see detailed feedback and analysis.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Overall Feedback */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-gray-200/50 hover:shadow-2xl transition-all duration-300">
        <div className="flex items-center gap-4 mb-6">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white text-xl">
            💡
          </div>
          <h3 className="text-2xl font-bold text-gray-900">Overall Feedback</h3>
        </div>
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
          <p className="text-gray-800 leading-relaxed text-lg">
            {feedback.summary.overallFeedback}
          </p>
        </div>
      </div>

      {/* Recommendation */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-gray-200/50 hover:shadow-2xl transition-all duration-300">
        <div className="flex items-center gap-4 mb-6">
          <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center text-white text-xl">
            🎯
          </div>
          <h3 className="text-2xl font-bold text-gray-900">Recommendation</h3>
        </div>

        <div className="mb-8">
          <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-6 border border-emerald-100">
            <div className="flex items-center justify-between mb-4">
              <span className="text-lg font-semibold text-gray-700">
                Assessment Level:
              </span>
              <span className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-500 text-white rounded-full text-lg font-bold shadow-lg">
                {feedback.summary.recommendationLevel}
              </span>
            </div>
            <div className="bg-white/70 rounded-lg p-4 border border-emerald-200">
              <p className="text-gray-800 text-lg italic font-medium">
                &quot;{feedback.summary.recommendationMsg}&quot;
              </p>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white text-sm">
              📊
            </div>
            <h4 className="text-xl font-bold text-gray-900">
              Skills Assessment
            </h4>
          </div>
          <SkillsGrid skills={feedback.summary.skillsRating} />
        </div>
      </div>

      {/* Question Analysis */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-gray-200/50 hover:shadow-2xl transition-all duration-300">
        <div className="flex items-center gap-4 mb-8">
          <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white text-xl">
            🔍
          </div>
          <h3 className="text-2xl font-bold text-gray-900">
            Question Analysis
          </h3>
        </div>
        <div className="space-y-6">
          {feedback.perQuestionFeedback.map((item, idx) => (
            <QuestionCard key={idx} item={item} index={idx} />
          ))}
        </div>
      </div>
    </div>
  );
}
