import React from "react";
import SkeletonCard from "./SkeletonCard";
import SkeletonRating from "./SkeletonRating";

const SkeletonFeedback = () => (
  <div className="space-y-6">
    <SkeletonCard />
    <SkeletonCard />
    <div className="bg-white rounded-lg shadow-sm p-6 border">
      <div className="animate-pulse mb-4">
        <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
      </div>
      <SkeletonRating />
    </div>
    <div className="space-y-4">
      {[1, 2, 3].map((i) => (
        <SkeletonCard key={i} />
      ))}
    </div>
  </div>
);

export default SkeletonFeedback;
