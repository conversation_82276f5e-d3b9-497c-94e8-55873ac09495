import React from "react";
import RatingBadge from "./RatingBadge";

const QuestionCard = ({ item, index }: { item: any; index: number }) => (
  <div className="group bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 border border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
    {/* Question Header */}
    <div className="flex justify-between items-start mb-6">
      <div className="flex-1">
        <div className="flex items-center gap-4 mb-3">
          <span className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-sm font-bold px-4 py-2 rounded-full shadow-lg">
            Q{index + 1}
          </span>
          <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
          <span className="text-sm text-gray-500 font-medium">
            Interview Question
          </span>
        </div>
        <h4 className="font-bold text-xl text-gray-900 leading-relaxed group-hover:text-indigo-700 transition-colors duration-300">
          {item.question}
        </h4>
      </div>
      <div className="ml-6 transform group-hover:scale-110 transition-transform duration-300">
        <RatingBadge rating={item.rating} maxRating={10} />
      </div>
    </div>

    {/* Answer and Feedback Sections */}
    <div className="space-y-6">
      {/* User Answer */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border-l-4 border-blue-400 shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white text-sm">
            👤
          </div>
          <span className="font-bold text-blue-900 text-lg">Your Answer</span>
        </div>
        <div className="bg-white/70 rounded-lg p-4 border border-blue-200">
          <p className="text-blue-800 leading-relaxed italic">
            &quot;{item.userAnswer}&quot;
          </p>
        </div>
      </div>

      {/* AI Feedback */}
      <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-6 border-l-4 border-emerald-400 shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center text-white text-sm">
            🤖
          </div>
          <span className="font-bold text-emerald-900 text-lg">
            AI Feedback
          </span>
        </div>
        <div className="bg-white/70 rounded-lg p-4 border border-emerald-200">
          <p className="text-emerald-800 leading-relaxed">{item.feedback}</p>
        </div>
      </div>
    </div>

    {/* Decorative Elements */}
    <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-20 transition-opacity duration-300">
      <div className="w-16 h-16 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full blur-xl"></div>
    </div>
  </div>
);

export default QuestionCard;
