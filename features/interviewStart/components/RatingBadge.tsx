import React from "react";

const RatingBadge = ({
  rating,
  maxRating = 10,
}: {
  rating: number;
  maxRating?: number;
}) => {
  const percentage = (rating / maxRating) * 100;

  let gradientClass = "from-gray-400 to-gray-500";
  const textClass = "text-white";
  let shadowClass = "shadow-lg";

  if (percentage >= 80) {
    gradientClass = "from-emerald-400 to-green-500";
    shadowClass = "shadow-lg shadow-green-200";
  } else if (percentage >= 60) {
    gradientClass = "from-blue-400 to-indigo-500";
    shadowClass = "shadow-lg shadow-blue-200";
  } else if (percentage >= 40) {
    gradientClass = "from-yellow-400 to-orange-500";
    shadowClass = "shadow-lg shadow-yellow-200";
  } else if (percentage > 0) {
    gradientClass = "from-red-400 to-pink-500";
    shadowClass = "shadow-lg shadow-red-200";
  }

  return (
    <div className="relative inline-block group">
      <div
        className={`bg-gradient-to-r ${gradientClass} px-6 py-3 rounded-full ${textClass} font-bold text-lg ${shadowClass} transform transition-all duration-300 hover:scale-110 hover:shadow-xl border-2 border-white/30`}
      >
        <span className="flex items-center gap-2">
          {rating}/{maxRating}
        </span>
      </div>

      {/* Tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
        {percentage >= 80
          ? "Excellent!"
          : percentage >= 60
          ? "Good Job!"
          : percentage >= 40
          ? "Keep Improving!"
          : "Needs Work"}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
      </div>
    </div>
  );
};

export default RatingBadge;
