import React from "react";
import RatingBadge from "./RatingBadge";

const SkillsGrid = ({ skills }: { skills: any }) => {
  const skillItems = [
    {
      label: "Technical Skills",
      value: skills.technicalSkills,
      icon: "⚡",
      gradient: "from-blue-500 to-cyan-500",
    },
    {
      label: "Communication",
      value: skills.communication,
      icon: "💬",
      gradient: "from-green-500 to-emerald-500",
    },
    {
      label: "Problem Solving",
      value: skills.problemSolving,
      icon: "🧩",
      gradient: "from-purple-500 to-pink-500",
    },
    {
      label: "Experience",
      value: skills.experience,
      icon: "🏆",
      gradient: "from-orange-500 to-red-500",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {skillItems.map((skill) => (
        <div
          key={skill.label}
          className="group bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-200 hover:border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div
                className={`w-10 h-10 bg-gradient-to-br ${skill.gradient} rounded-lg flex items-center justify-center text-white text-lg shadow-lg group-hover:scale-110 transition-transform duration-300`}
              >
                {skill.icon}
              </div>
              <div>
                <div className="font-semibold text-gray-800 text-lg">
                  {skill.label}
                </div>
                <div className="text-sm text-gray-500">Assessment Score</div>
              </div>
            </div>
          </div>
          <div className="flex justify-center">
            <RatingBadge rating={skill.value} />
          </div>

          {/* Progress bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
              <div
                className={`h-full bg-gradient-to-r ${skill.gradient} rounded-full transition-all duration-1000 ease-out`}
                style={{ width: `${(skill.value / 10) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SkillsGrid;
