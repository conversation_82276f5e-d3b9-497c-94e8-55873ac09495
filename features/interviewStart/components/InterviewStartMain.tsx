import { ChatSection } from "@/features/interviewStart/components/ChatSection";
import { VideoSection } from "@/features/interviewStart/components/VideoSection";
import React from "react";
import { useInterviewStart } from "../hooks/useInterviewStart";
import { Message } from "../hooks/useInterviewStart";
import { Button } from "@/components/ui/button";
import InterviewLoading from "@/components/InterviewLoading";
import { formatTime } from "@/lib/timer";

const InterviewStartMain = () => {
  const {
    interviewQuestion,
    handleToggleCall,
    isMuted,
    setIsMuted,
    messages,
    messagesEndRef,
    isInterviewing,
    assistantSpeech,
    userSpeech,
    viewFeedbackAnalysis,
    handleFeedbackAnalysis,
    isLoading,
    isLoadingFetch,
    timer,
  } = useInterviewStart();

  if (isLoadingFetch) {
    return <InterviewLoading />;
  }

  return (
    <div className="flex-1 space-y-8 p-8">
      <div className="bg-white border border-gray-200 shadow-md px-6 py-4 flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            {interviewQuestion?.jobPosition}
          </h2>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-right">
            <div className="text-sm text-gray-500">Time Remaining</div>
            <div className="text-lg font-bold text-gray-900">
              {formatTime(timer)}
            </div>
          </div>
        </div>
      </div>
      <div className="grid md:grid-cols-2 xs:grid-cols-1 gap-4">
        <div className="flex flex-col gap-4">
          <VideoSection
            isMuted={isMuted}
            setIsMuted={setIsMuted}
            handleToggleCall={handleToggleCall}
            isInterviewing={isInterviewing}
          />
          <Button
            onClick={handleFeedbackAnalysis}
            disabled={!viewFeedbackAnalysis || isLoading}
            className="w-1/3 self-center bg-blue-500"
          >
            {isLoading ? "Generating..." : "Feedback Analysis"}
          </Button>
        </div>
        <ChatSection
          messages={messages as Message[]}
          messagesEndRef={messagesEndRef}
          assistantSpeech={assistantSpeech}
          userSpeech={userSpeech}
          isInterviewing={isInterviewing}
        />
      </div>
    </div>
  );
};

export default InterviewStartMain;
