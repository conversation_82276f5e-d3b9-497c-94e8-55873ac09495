"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off, <PERSON>Off, Phone } from "lucide-react";
import Image from "next/image";
import AI_homepage from "@/assets/images/AI_homepage.png";
import { useCamera } from "@/hooks/useCamera";
import { cn } from "@/lib/utils";

interface VideoSectionProps {
  isMuted: boolean;
  setIsMuted: React.Dispatch<React.SetStateAction<boolean>>;
  handleToggleCall: () => void;
  isInterviewing: boolean;
}

export function VideoSection({
  isMuted,
  setIsMuted,
  handleToggleCall,
  isInterviewing,
}: VideoSectionProps) {
  const { isCameraOn, videoRef, toggleCamera, isLoading, videoKey } =
    useCamera();
  return (
    <div className="flex flex-col gap-4">
      {/* Main Video Container - Relative positioning for stacking */}
      <div className="relative aspect-video bg-white rounded-lg overflow-hidden border-2 border-gray-300 h-[50vh]">
        {/* Interviewer Video - Main/Background video */}
        <div className="w-full h-full">
          <div className="w-full h-full flex items-center justify-center">
            <div className="relative rounded-full p-1 animate-pulseRing">
              <Image
                src={AI_homepage.src}
                alt="Interviewer"
                className="object-contain rounded-full"
                width={100}
                height={100}
              />
            </div>
          </div>
          <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
            Interviewer
          </div>
        </div>

        {/* Your Video - Small overlay in bottom right corner */}
        <div className="absolute bottom-4 right-4 w-48 aspect-video bg-gray-900 rounded-lg overflow-hidden border-2 border-white shadow-lg">
          {isCameraOn ? (
            <video
              key={videoKey}
              autoPlay
              playsInline
              muted
              ref={videoRef}
              width="100%"
              height="100%"
              style={{ objectFit: "cover" }}
            />
          ) : (
            <div className="w-full h-full bg-gray-700 flex items-center justify-center">
              <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                U
              </div>
            </div>
          )}
          <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
            You
          </div>
        </div>
      </div>

      {/* Control Buttons - Inside video at bottom center */}
      <div className="flex justify-center gap-4">
        {/* Camera Button */}
        <button
          onClick={toggleCamera}
          disabled={isLoading}
          className={`p-3 rounded-full ${
            isCameraOn
              ? "bg-gray-700 bg-opacity-70"
              : "bg-red-500 bg-opacity-90"
          } text-white hover:bg-opacity-90 transition-colors backdrop-blur-sm`}
          aria-label={isCameraOn ? "Turn off camera" : "Turn on camera"}
        >
          {isCameraOn ? <Camera size={20} /> : <CameraOff size={20} />}
        </button>

        {/* Audio Button */}
        <button
          onClick={() => setIsMuted(!isMuted)}
          className={`p-3 rounded-full ${
            isMuted ? "bg-red-500 bg-opacity-90" : "bg-gray-700 bg-opacity-70"
          } text-white hover:bg-opacity-90 transition-colors backdrop-blur-sm`}
          aria-label={isMuted ? "Unmute microphone" : "Mute microphone"}
        >
          {isMuted ? <MicOff size={20} /> : <Mic size={20} />}
        </button>

        {/* End Call Button */}
        <button
          className={cn(
            "p-3  bg-opacity-90 text-white rounded-full hover:bg-opacity-100 transition-colors backdrop-blur-sm",
            isInterviewing
              ? "bg-red-500 hover:bg-red-600"
              : "bg-green-500 hover:bg-green-600"
          )}
          aria-label={isInterviewing ? "End call" : "Start call"}
          onClick={handleToggleCall}
        >
          {isInterviewing ? <PhoneOff size={20} /> : <Phone size={20} />}
        </button>
      </div>
    </div>
  );
}
