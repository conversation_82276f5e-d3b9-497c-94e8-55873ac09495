"use client";
import React from "react";
import { Message } from "../hooks/useInterviewStart";

interface ChatSectionProps {
  messages: Message[];
  messagesEndRef?: React.RefObject<HTMLDivElement>;
  assistantSpeech?: string;
  userSpeech?: string;
  isInterviewing?: boolean;
}

export function ChatSection({
  messages,
  messagesEndRef,
  isInterviewing = false,
}: ChatSectionProps) {
  return (
    <div className="flex-1 flex flex-col bg-white/90 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-2xl overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6 text-white">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-white/30 rounded-full animate-pulse"></div>
            <h3 className="font-bold text-lg">💬 Interview Conversation</h3>
          </div>
          <div
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              isInterviewing
                ? "bg-green-400/20 text-green-100 border border-green-300/30"
                : "bg-white/20 text-gray-100 border border-white/30"
            }`}
          >
            {isInterviewing ? (
              <span className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                🎤 AI Active
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                📝 Review Mode
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6 min-h-[400px] max-h-[500px] bg-gradient-to-b from-gray-50/50 to-white/80">
        {messages.length === 0 && isInterviewing && (
          <div className="text-center text-gray-500 py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center">
              <div className="animate-spin">⚡</div>
            </div>
            <div className="animate-pulse text-lg font-medium">
              Waiting for AI to start the interview...
            </div>
          </div>
        )}

        {messages.length === 0 && !isInterviewing && (
          <div className="text-center text-gray-500 py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center text-2xl">
              🚀
            </div>
            <div className="text-lg font-medium text-gray-700 mb-2">
              Interview Complete!
            </div>
            <p className="text-gray-500">
              Review your conversation and feedback below
            </p>
          </div>
        )}

        {messages.map((message, index) => (
          <div
            key={message.id}
            className={`flex ${
              message.sender === "you" || message.sender === "user"
                ? "justify-end"
                : "justify-start"
            } transform transition-all duration-300 ease-out animate-in slide-in-from-bottom-2`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div
              className={`max-w-[80%] px-6 py-4 rounded-2xl shadow-lg transition-all duration-200 hover:shadow-xl ${
                message.sender === "you" || message.sender === "user"
                  ? "bg-gradient-to-br from-indigo-500 to-purple-600 text-white ml-4"
                  : "bg-white border border-gray-200 text-gray-800 mr-4"
              }`}
            >
              {/* Message content */}
              <p className="whitespace-pre-wrap leading-relaxed">
                {message.content}
                {!message.isComplete && (
                  <span className="inline-block w-2 h-5 bg-current opacity-60 animate-pulse ml-1 align-bottom">
                    |
                  </span>
                )}
              </p>

              {/* Message metadata */}
              <div className="flex items-center justify-between mt-3 pt-2 border-t border-current/20">
                <div className="flex items-center gap-2">
                  <span
                    className={`text-xs font-medium ${
                      message.sender === "you" || message.sender === "user"
                        ? "text-white/80"
                        : "text-gray-500"
                    }`}
                  >
                    {message.sender === "you" || message.sender === "user"
                      ? "You"
                      : "AI Interviewer"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span
                    className={`text-xs ${
                      message.sender === "you" || message.sender === "user"
                        ? "text-white/70"
                        : "text-gray-400"
                    }`}
                  >
                    {message.timestamp}
                  </span>
                  {!message.isComplete && (
                    <span
                      className={`text-xs ${
                        message.sender === "you" || message.sender === "user"
                          ? "text-white/70"
                          : "text-gray-400"
                      }`}
                    >
                      typing...
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}
