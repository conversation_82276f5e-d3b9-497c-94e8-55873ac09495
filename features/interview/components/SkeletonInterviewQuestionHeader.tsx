import { Skeleton } from "@/components/ui/skeleton";

export const InterviewQuestionHeaderSkeleton = () => {
  return (
    <div className="relative bg-white rounded-2xl border-2 border-gray-100 p-4 sm:p-6 lg:p-8 shadow-xl overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-48 h-48 sm:w-64 sm:h-64 bg-gradient-to-br from-blue-100 to-purple-200 rounded-full blur-3xl opacity-30 -translate-y-24 sm:-translate-y-32 translate-x-24 sm:-translate-x-32"></div>
      <div className="absolute bottom-0 left-0 w-32 h-32 sm:w-48 sm:h-48 bg-gradient-to-br from-pink-100 to-yellow-100 rounded-full blur-3xl opacity-30 translate-y-16 sm:translate-y-24 -translate-x-16 sm:-translate-x-24"></div>

      <div className="relative z-10">
        {/* Header section */}
        <div className="flex flex-col sm:flex-row items-center sm:items-start justify-between gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="text-center sm:text-left">
            {/* Icon and title */}
            <div className="flex items-center justify-center sm:justify-start space-x-3 mb-3">
              <Skeleton className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl" />
              <Skeleton className="h-8 sm:h-10 w-48 sm:w-64" />
            </div>
            {/* Subtitle */}
            <Skeleton className="h-4 sm:h-5 w-64 sm:w-80 mx-auto sm:mx-0" />
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4">
            <Skeleton className="h-10 sm:h-12 w-48 sm:w-56 rounded-full" />
            <Skeleton className="h-12 sm:h-14 w-40 sm:w-48 rounded-xl" />
          </div>
        </div>

        {/* Cards grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="relative bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl p-4 sm:p-6 shadow-lg"
            >
              <div className="flex items-center space-x-3 sm:space-x-4 mb-3">
                <Skeleton className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg" />
                <Skeleton className="h-5 sm:h-6 w-16 sm:w-20" />
              </div>
              <Skeleton className="h-6 sm:h-7 w-3/4" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
