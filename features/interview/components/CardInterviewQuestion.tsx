"use client";
import { InterviewQuestionSessionType } from "@/common/types/interview-question";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { getTimeAgo } from "@/lib/timer";
import {
  ArrowR<PERSON>,
  ClipboardList,
  Clock,
  MessageSquare,
  Trash2,
} from "lucide-react";
import React from "react";
import { useInterviewQuestion } from "../hooks/useInterviewQuestion";

const CardInterviewQuestion = ({
  interview,
  index,
}: {
  interview: InterviewQuestionSessionType;
  index: number;
}) => {
  const { naviteToQuestionInterviewById, handleDeleteInterViewQuestion } =
    useInterviewQuestion();

  return (
    <Card
      key={index}
      className="bg-gradient-to-br from-blue-50 to-cyan-100 border-none rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 relative"
    >
      {/* Delete button */}
      <button
        onClick={() => handleDeleteInterViewQuestion(interview._id)}
        className="absolute top-3 right-3 w-8 h-8 bg-red-400 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 z-10"
        aria-label="Delete interview"
      >
        <Trash2 className="h-4 w-4" />
      </button>
      <CardContent className="p-6">
        <div className="text-center mb-4">
          <div className="w-10 h-10 bg-cyan-200 rounded-full flex items-center justify-center mx-auto mb-2">
            <MessageSquare className="h-5 w-5 text-cyan-600" />
          </div>
          <h3 className="font-semibold text-slate-700">
            {interview.jobPosition}
          </h3>
          <p className="text-xs text-slate-600">
            Created {getTimeAgo(interview.createdAt)}
          </p>
        </div>

        <div className="space-y-3 mb-4">
          <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
            <MessageSquare className="h-3 w-3 text-cyan-600" />
            <span className="text-sm text-slate-700">
              {interview.totalQuestion} Questions
            </span>
          </div>
          <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
            <ClipboardList className="h-3 w-3 text-cyan-600" />
            <span className="text-sm text-slate-700">
              {interview.interviewType.join(", ")}
            </span>
          </div>
          <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
            <Clock className="h-3 w-3 text-cyan-600" />
            <span className="text-sm text-slate-700">
              {interview.interviewDuration} minutes
            </span>
          </div>
        </div>

        <Button
          className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-xl py-2 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300"
          onClick={() => naviteToQuestionInterviewById(interview._id)}
        >
          <ArrowRight className="mr-2 h-3 w-3" />
          View Interview
        </Button>
      </CardContent>
    </Card>
  );
};

export default CardInterviewQuestion;
