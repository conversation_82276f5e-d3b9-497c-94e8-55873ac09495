"use client";
import { useGetQuestionInterviewByIdQuery } from "@/services/api/interview-question";
import { useParams } from "next/navigation";
import InterviewQuestionHeader from "./InterviewQuestionHeader";
import { InterviewQuestionSessionType } from "@/common/types/interview-question";
import CardQuestion from "./CardQuestion";
import { InterviewQuestionHeaderSkeleton } from "./SkeletonInterviewQuestionHeader";
import { CardQuestionSkeleton } from "./SkeletonCardQuestion";
import FeedbackSessions from "./FeedbackSession";

const InterviewQuestion = () => {
  const { interviewId } = useParams();

  const {
    data: questionsInterview,
    isError,
    isLoading,
  } = useGetQuestionInterviewByIdQuery(interviewId as string);

  console.log(questionsInterview);

  return (
    <>
      {isLoading || isError ? (
        <>
          <InterviewQuestionHeaderSkeleton />
          <CardQuestionSkeleton />
        </>
      ) : (
        <div className="flex flex-col gap-7">
          {/* Header */}
          <InterviewQuestionHeader
            questionsInterview={
              questionsInterview as InterviewQuestionSessionType
            }
          />
          {/* Questions */}
          <div className="space-y-4 sm:space-y-6 mb-7">
            {questionsInterview?.interviewQuestions.map((question, index) => (
              <CardQuestion key={index} question={question} index={index} />
            ))}
          </div>

          <div className="mb-7">
            <FeedbackSessions
              headerTitle="HistoryFeedbacks"
              isLoading={isLoading}
              isError={isError}
              data={questionsInterview?.conversations || []}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default InterviewQuestion;
