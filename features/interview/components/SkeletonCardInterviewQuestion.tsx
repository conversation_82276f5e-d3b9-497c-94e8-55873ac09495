"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export const CardInterviewQuestionSkeleton = () => {
  return (
    <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 border-none rounded-2xl shadow-lg">
      <CardContent className="p-6">
        {/* Center icon and title section */}
        <div className="text-center mb-4">
          <Skeleton className="w-10 h-10 rounded-full mx-auto mb-2" />
          <Skeleton className="h-6 w-3/4 mx-auto mb-1" />
          <Skeleton className="h-3 w-1/2 mx-auto" />
        </div>

        {/* Metadata rows */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
            <Skeleton className="h-3 w-3 rounded-sm" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
            <Skeleton className="h-3 w-3 rounded-sm" />
            <Skeleton className="h-4 w-16" />
          </div>
          <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
            <Skeleton className="h-3 w-3 rounded-sm" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        {/* Button */}
        <Skeleton className="h-9 w-full rounded-xl" />
      </CardContent>
    </Card>
  );
};
