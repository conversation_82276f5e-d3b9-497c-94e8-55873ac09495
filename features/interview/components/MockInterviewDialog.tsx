"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Form, FormField } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { mockInterviewSchema } from "../schemas/interview-question.schema";
import { z } from "zod";
import { interviewTypes } from "@/common/constants/interviewTypeData";
import { FormInputField } from "@/components/FormInputField";
import { FormSelectField } from "@/components/FormSelectField";
import { FormToggleGroupField } from "@/components/FormToggleGroupField";
import { FormFileUploadField } from "@/components/FormSingleUploadField";
import { useCreateInterviewQuestion } from "../hooks/useCreateInterviewQuestion";

interface MockInterviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export type MockInterviewFormData = z.infer<typeof mockInterviewSchema>;

const durationOptions = [
  { value: "10min", label: "10 minutes" },
  { value: "15min", label: "15 minutes" },
  { value: "20min", label: "20 minutes" },
  { value: "25min", label: "25 minutes" },
  { value: "30min", label: "30 minutes" },
];

const interviewTypeOptions = interviewTypes
  .filter((type) => type.name && type.icon)
  .map((type) => ({
    value: type.name.toLowerCase(),
    label: type.name,
    icon: type.icon,
  }));

export function MockInterviewDialog({
  open,
  onOpenChange,
}: MockInterviewDialogProps) {
  const form = useForm<MockInterviewFormData>({
    resolver: zodResolver(mockInterviewSchema),
    defaultValues: {
      jobPosition: "",
      interviewDuration: "",
      interviewType: [],
      jdFile: undefined,
    },
  });

  const { handleCreateInterviewQuestion, isLoading, handleClose } =
    useCreateInterviewQuestion(form, onOpenChange);

  const onSubmit = (data: MockInterviewFormData) => {
    handleCreateInterviewQuestion(data);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent
        className="sm:max-w-[600px] max-h-[90vh] p-0 flex flex-col"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader className="p-6 pb-0 flex-shrink-0">
          <div className="flex justify-between items-start">
            <div>
              <DialogTitle className="text-2xl font-bold">
                Generate a Mock Interview Session
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Fill in the details below to create a personalized mock
                interview experience.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col flex-1 min-h-0"
          >
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* Job Position */}
              <FormField
                control={form.control}
                name="jobPosition"
                render={({ field }) => (
                  <FormInputField
                    field={field}
                    label="Job Position"
                    placeholder="e.g. Frontend Developer"
                    required
                  />
                )}
              />

              {/* Interview Duration */}
              <FormField
                control={form.control}
                name="interviewDuration"
                render={({ field }) => (
                  <FormSelectField
                    field={field}
                    label="Interview Duration"
                    placeholder="Select Duration"
                    options={durationOptions}
                    required
                  />
                )}
              />

              {/* Interview Type */}
              <FormField
                control={form.control}
                name="interviewType"
                render={({ field }) => (
                  <FormToggleGroupField
                    field={field}
                    label="Interview Type"
                    options={interviewTypeOptions}
                    type="multiple"
                    required
                    description="Select one or more interview types"
                  />
                )}
              />

              {/* File Upload */}
              <FormField
                control={form.control}
                name="jdFile"
                render={({ field }) => (
                  <FormFileUploadField
                    field={field}
                    label="Upload JD File (PDF)"
                    accept="application/pdf"
                    maxSize={5}
                    description="Upload a job description to get more personalized questions"
                    dragDropText="Drag and drop your PDF here or click to browse"
                  />
                )}
              />
            </div>

            <DialogFooter className="p-6 pt-0">
              <div className="flex gap-3 w-full">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={
                    isLoading ||
                    !form.formState.isValid ||
                    !form.getValues("jdFile")
                  }
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Generating...</span>
                    </div>
                  ) : (
                    "Generate Questions"
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
