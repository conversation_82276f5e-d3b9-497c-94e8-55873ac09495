"use client";
import {
  <PERSON><PERSON><PERSON>,
  ChevronRight,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Target,
  Timer,
  User,
} from "lucide-react";
import React from "react";
import SubCardInterviewQuestion from "./SubCardInterviewQuestion";
import { InterviewQuestionSessionType } from "@/common/types/interview-question";
import { useInterviewQuestion } from "../hooks/useInterviewQuestion";

const InterviewQuestionHeader = ({
  questionsInterview,
}: {
  questionsInterview: InterviewQuestionSessionType;
}) => {
  const { handleStartInterview } = useInterviewQuestion();

  return (
    <div className="relative bg-white rounded-2xl border-2 border-gray-100 p-4 sm:p-6 lg:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 overflow-hidden">
      <div className="absolute top-0 right-0 w-48 h-48 sm:w-64 sm:h-64 bg-gradient-to-br from-blue-100 to-purple-200 rounded-full blur-3xl opacity-30 -translate-y-24 sm:-translate-y-32 translate-x-24 sm:-translate-x-32"></div>
      <div className="absolute bottom-0 left-0 w-32 h-32 sm:w-48 sm:h-48 bg-gradient-to-br from-pink-100 to-yellow-100 rounded-full blur-3xl opacity-30 translate-y-16 sm:translate-y-24 -translate-x-16 sm:-translate-x-24"></div>
      <div className="relative z-10">
        <div className="flex flex-col sm:flex-row items-center sm:items-start justify-between gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="text-center sm:text-left">
            <div className="flex items-center justify-center sm:justify-start space-x-3 mb-3">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </div>
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
                Interview Questions
              </h1>
            </div>
            <p className="text-sm sm:text-base lg:text-lg text-gray-600 font-medium max-w-md">
              Prepare for your{" "}
              <span className="text-blue-600 font-semibold">
                {questionsInterview?.jobPosition}
              </span>{" "}
              interview with confidence
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4">
            <div className="flex items-center space-x-2 sm:space-x-3 bg-gradient-to-r from-blue-500 to-purple-600 px-4 sm:px-6 py-2 sm:py-3 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300">
              <Timer className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="text-sm sm:text-base font-bold">
                {questionsInterview?.interviewDuration} minutes total
              </span>
            </div>
            <button
              className="group relative bg-gradient-to-r from-pink-500 via-red-500 to-orange-500 hover:from-pink-600 hover:via-red-600 hover:to-orange-600 text-white font-bold py-4 px-10 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1"
              aria-label="Start Interview"
              role="button"
              onClick={() => handleStartInterview(questionsInterview._id)}
            >
              <div className="absolute inset-0 bg-gradient-to-r rounded-xl blur opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center space-x-2 sm:space-x-3">
                <PlayIcon className="w-5 h-5 sm:w-6 sm:h-6 group-hover:scale-125 transition-transform duration-200" />
                <span className="text-base sm:text-lg">Start Interview</span>
                <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-2 transition-transform duration-200" />
              </div>
            </button>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          <SubCardInterviewQuestion
            icon={<User className="w-5 h-5 sm:w-6 sm:h-6 text-white" />}
            title="Role"
            content={questionsInterview?.jobPosition}
            className="from-blue-500 via-blue-600 to-indigo-600"
          />
          <SubCardInterviewQuestion
            icon={<BookOpen className="w-5 h-5 sm:w-6 sm:h-6 text-white" />}
            title="Questions"
            content={`${questionsInterview?.totalQuestion} Questions`}
            className=" from-emerald-500 via-green-500 to-teal-600"
          />
          <SubCardInterviewQuestion
            icon={<Target className="w-5 h-5 sm:w-6 sm:h-6 text-white" />}
            title="Types"
            content={questionsInterview?.interviewType.join(", ")}
            className=" from-purple-500 via-pink-500 to-red-500"
          />
        </div>
      </div>
    </div>
  );
};

export default InterviewQuestionHeader;
