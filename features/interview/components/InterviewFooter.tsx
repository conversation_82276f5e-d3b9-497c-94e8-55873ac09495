"use client";

import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import {
  Lightbulb,
  Target,
  Clock,
  TrendingUp,
  BookOpen,
  Users,
  Award,
  ArrowRight,
} from "lucide-react";
import {
  footerContainerVariants,
  footerItemVariants,
  footerStatsVariants,
  footerCardVariants,
  footerHoverEffects,
} from "@/styles/animations/footer.variants";

const hints = [
  {
    icon: <Lightbulb className="h-5 w-5" />,
    title: "Practice Makes Perfect",
    description:
      "Regular mock interviews help improve your confidence and performance.",
  },
  {
    icon: <Target className="h-5 w-5" />,
    title: "Focus on Key Skills",
    description:
      "Identify and strengthen your weak areas through targeted practice.",
  },
  {
    icon: <Clock className="h-5 w-5" />,
    title: "Time Management",
    description:
      "Practice answering questions within the allocated time frame.",
  },
  {
    icon: <TrendingUp className="h-5 w-5" />,
    title: "Track Progress",
    description:
      "Monitor your improvement through feedback scores and recommendations.",
  },
  {
    icon: <BookOpen className="h-5 w-5" />,
    title: "Study Job Requirements",
    description:
      "Tailor your answers to match the specific job description provided.",
  },
  {
    icon: <Users className="h-5 w-5" />,
    title: "Behavioral Questions",
    description:
      "Use the STAR method (Situation, Task, Action, Result) for behavioral questions.",
  },
];

const stats = [
  { number: "95%", label: "Success Rate", icon: <Award className="h-6 w-6" /> },
  {
    number: "1000+",
    label: "Interviews Completed",
    icon: <Users className="h-6 w-6" />,
  },
  {
    number: "50+",
    label: "Companies Supported",
    icon: <BookOpen className="h-6 w-6" />,
  },
  {
    number: "4.8/5",
    label: "User Rating",
    icon: <TrendingUp className="h-6 w-6" />,
  },
];

export default function InterviewFooter() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });

  return (
    <motion.footer
      ref={ref}
      variants={footerContainerVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      className="bg-gradient-to-br from-slate-50 to-blue-50 border-t border-slate-200 mt-16"
    >
      <div className="max-w-7xl mx-auto px-8 py-12">
        {/* Stats Section */}
        <motion.div
          variants={footerItemVariants}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              variants={footerStatsVariants}
              custom={index}
              className="text-center p-4 bg-white rounded-xl shadow-sm border border-slate-100 hover:shadow-md transition-shadow"
              whileHover={footerHoverEffects.statsCard}
            >
              <div className="flex justify-center text-blue-600 mb-2">
                {stat.icon}
              </div>
              <div className="text-2xl font-bold text-slate-800 mb-1">
                {stat.number}
              </div>
              <div className="text-sm text-slate-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Tips Section */}
        <motion.div variants={footerItemVariants} className="mb-8">
          <motion.div
            variants={footerItemVariants}
            className="text-center mb-8"
          >
            <h3 className="text-2xl font-bold text-slate-800 mb-2">
              Interview Success Tips
            </h3>
            <p className="text-slate-600">
              Expert advice to help you ace your next interview
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {hints.map((hint, index) => (
              <motion.div
                key={index}
                variants={footerCardVariants}
                custom={index}
                className="bg-white p-6 rounded-xl shadow-sm border border-slate-100 hover:shadow-md transition-all duration-300 group"
                whileHover={footerHoverEffects.tipCard}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 p-2 bg-blue-100 rounded-lg text-blue-600 group-hover:bg-blue-200 transition-colors">
                    {hint.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-slate-800 mb-2">
                      {hint.title}
                    </h4>
                    <p className="text-sm text-slate-600 leading-relaxed">
                      {hint.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          variants={footerItemVariants}
          className="text-center bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl p-8 text-white"
        >
          <h3 className="text-xl font-bold mb-2">
            Ready to Start Your Interview Journey?
          </h3>
          <p className="text-blue-100 mb-6">
            Join thousands of candidates who have improved their interview
            skills with our AI-powered platform
          </p>
          <motion.button
            whileHover={footerHoverEffects.button}
            whileTap={footerHoverEffects.buttonTap}
            className="inline-flex items-center px-6 py-3 bg-white text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
          >
            Get Started Today
            <ArrowRight className="ml-2 h-4 w-4" />
          </motion.button>
        </motion.div>

        {/* Footer Bottom */}
        <motion.div
          variants={footerItemVariants}
          className="text-center pt-8 mt-8 border-t border-slate-200"
        >
          <p className="text-slate-500 text-sm">
            © 2024 LearnVox. Empowering your career journey with AI-driven
            interview preparation.
          </p>
        </motion.div>
      </div>
    </motion.footer>
  );
}
