import { cn } from "@/lib/utils";
import React from "react";

export type SubCardInterviewQuestionProps = {
  icon?: React.ReactNode;
  title?: string;
  content?: string;
  className?: string;
};

const SubCardInterviewQuestion = ({
  icon,
  title,
  content,
  className,
}: SubCardInterviewQuestionProps) => {
  return (
    <div
      className={cn(
        "relative bg-gradient-to-br p-4 sm:p-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 overflow-hidden",
        className
      )}
    >
      <div className="absolute top-0 right-0 w-24 h-24 sm:w-32 sm:h-32 bg-white/10 rounded-full blur-2xl"></div>
      <div className="relative z-10">
        <div className="flex items-center space-x-3 sm:space-x-4 mb-3 sm:mb-4">
          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
            {icon}
          </div>
          <div className="text-xs font-bold text-blue-100 uppercase tracking-wider">
            {title}
          </div>
        </div>
        <div className="text-lg sm:text-xl lg:text-2xl font-bold text-white">
          {content}
        </div>
      </div>
    </div>
  );
};

export default SubCardInterviewQuestion;
