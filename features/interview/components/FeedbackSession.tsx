"use client";

import { useState, useMemo } from "react";
import { Eye } from "lucide-react";
import { motion } from "framer-motion";
import { ConversationResponse } from "@/common/types/conversation";
import CommonTable, {
  TableColumn,
  TableAction,
} from "@/components/CommonTable";
import SearchComponent from "@/components/SearchComponent";
import { useFeedbackSession } from "../hooks/useFeedbackSession";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { tableVariants } from "@/styles/animations/interview.variants";

export default function FeedbackSessions({
  headerTitle,
  isLoading,
  data: myInterviewQuestions,
  isError,
}: {
  headerTitle: string;
  isLoading: boolean;
  isError: boolean;
  data: ConversationResponse[];
}) {
  const [searchQuery, setSearchQuery] = useState("");
  const user = useSelector((state: RootState) => state.auth.user);

  const {
    currentPage,
    itemsPerPage,
    handleViewFeedback,
    calculateScore,
    handlePageChange,
  } = useFeedbackSession();

  // Filter data based on search
  const filteredData = useMemo(() => {
    if (!myInterviewQuestions) return [];

    return myInterviewQuestions.filter((item) => {
      const matchesSearch =
        searchQuery === "" ||
        item.feedback?.summary?.recommendationLevel
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        item.feedback?.summary?.recommendationMsg
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        user?.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user?.lastName?.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesSearch;
    });
  }, [myInterviewQuestions, searchQuery, user]);

  // Định nghĩa các cột cho table
  const columns: TableColumn<ConversationResponse>[] = [
    {
      key: "userId",
      label: "Candidate Name",
      render: () => `${user?.firstName} ${user?.lastName}` || "Unknown User",
    },
    {
      key: "interviewDuration",
      label: "Interview Duration",
      render: (value: any, row: ConversationResponse) =>
        `${row.interviewDuration} minutes` || "N/A",
    },
    {
      key: "feedback.summary.recommendationLevel",
      label: "Recommendation",
      render: (value: any, row: ConversationResponse) => (
        <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
          {row.feedback?.summary?.recommendationLevel || "Interview"}
        </span>
      ),
    },
    {
      key: "feedback.summary.recommendationMsg",
      label: "Recommendation Message",
      render: (value: any, row: ConversationResponse) =>
        row.feedback?.summary?.recommendationMsg || "N/A",
    },
    {
      key: "score",
      label: "Score",
      render: (_: any, row: ConversationResponse) => {
        const score = calculateScore(row);
        return (
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              score >= 70
                ? "bg-yellow-100 text-yellow-800"
                : score >= 60
                ? "bg-orange-100 text-orange-800"
                : "bg-red-100 text-red-700"
            }`}
          >
            {score}/10
          </span>
        );
      },
    },
  ];

  // Định nghĩa các actions
  const actions: TableAction<ConversationResponse>[] = [
    {
      label: "View Feedback",
      onClick: (row: ConversationResponse) => handleViewFeedback(row._id),
      variant: "ghost",
      icon: <Eye className="w-4 h-4" />,
      className: "text-indigo-600 hover:underline",
    },
  ];

  return (
    <motion.div
      variants={tableVariants}
      initial="hidden"
      animate="visible"
      className="space-y-4"
    >
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-slate-700">{headerTitle}</h2>
        <SearchComponent
          placeholder="Search feedbacks..."
          onSearch={setSearchQuery}
          searchValue={searchQuery}
          className="w-80"
        />
      </div>

      {!isLoading && !isError && filteredData.length === 0 && searchQuery && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-4"
        >
          <p className="text-slate-500">
            No feedbacks found matching &quot;{searchQuery}&quot;
          </p>
        </motion.div>
      )}

      <CommonTable<ConversationResponse>
        data={filteredData || []}
        columns={columns}
        actions={actions}
        isLoading={isLoading}
        isError={isError}
        emptyMessage="Chưa có phản hồi nào"
        errorMessage="Có lỗi xảy ra khi tải dữ liệu phản hồi"
        pagination={{
          enabled: true,
          pageSize: itemsPerPage,
          currentPage: currentPage,
          onPageChange: handlePageChange,
        }}
      />
    </motion.div>
  );
}
