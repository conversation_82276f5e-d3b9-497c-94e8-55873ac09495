import { Clock, Lightbulb, Zap } from "lucide-react";
import React from "react";
import { InterviewQuestion } from "@/common/types/interview-question";
import { unifiedColors } from "@/styles/customeStyle/interview-question.style";
import { interviewTypes } from "@/common/constants/interviewTypeData";

const CardQuestion = ({
  question,
  index,
}: {
  question: InterviewQuestion;
  index: number;
}) => {
  const matchInterviewType = (type: string) => {
    return interviewTypes.find(
      (interviewType) => interviewType.name.toUpperCase() === type.toUpperCase()
    );
  };
  const IconComponent = matchInterviewType(question?.type)?.icon || Zap;
  const color =
    matchInterviewType(question?.type)?.color ||
    "bg-gradient-to-br from-blue-500 to-purple-500";

  return (
    <div
      className={`relative border border-gray-200 bg-white rounded-2xl shadow-xl hover:shadow-2xl group overflow-hidden`}
    >
      <div className="relative z-10 p-4">
        <div className="flex flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 mb-4 text-lg sm:text-xl">
          <div
            className={`w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br ${unifiedColors.accentColor.from} ${unifiedColors.accentColor.to} rounded-lg flex items-center justify-center text-white shadow-lg`}
          >
            {index + 1}
          </div>
          <div className="flex items-center gap-2 sm:gap-4">
            <span
              className={`px-2 py-2 rounded-lg text-xs sm:text-sm font-bold ${color} text-white`}
            >
              <div className="flex items-center space-x-2 sm:space-x-3">
                <IconComponent className="w-4 h-4 sm:w-5 sm:h-5" />
                <span>{question.type}</span>
              </div>
            </span>
            <div className="flex items-center space-x-1 sm:space-x-2 text-gray-600 bg-gray-100 px-2 py-2 text-xs sm:text-sm rounded-lg font-semibold">
              <Clock className="w-4 h-4 sm:w-5 sm:h-5" />
              <span>{question.estimated_time_minutes} mins</span>
            </div>
          </div>
        </div>
        <h3 className="text-md lg:text-lg font-bold text-gray-900 mb-2">
          {question.question}
        </h3>
        <div className="relative border border-amber-200 rounded-lg p-2 shadow-lg overflow-hidden">
          <div className="relative z-10 flex items-start space-x-3 sm:space-x-4">
            <div>
              <h4 className="font-bold text-amber-800 text-sm sm:text-base mb-2 sm:mb-3 tracking-wide flex items-center space-x-1 sm:space-x-2">
                <Lightbulb className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-600" />
                <span>Tips:</span>
              </h4>
              <p className="text-amber-800 text-xs sm:text-sm leading-relaxed font-medium">
                {question.tip}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardQuestion;
