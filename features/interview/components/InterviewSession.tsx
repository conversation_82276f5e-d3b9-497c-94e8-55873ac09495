"use client";

import { useState, useMemo } from "react";
import { motion } from "framer-motion";
import { useGetMyQuestionInterviewQuery } from "@/services/api/interview-question";
import CardInterviewQuestion from "./CardInterviewQuestion";
import { CardInterviewQuestionSkeleton } from "./SkeletonCardInterviewQuestion";
import SearchComponent from "@/components/SearchComponent";
import {
  interviewSectionVariants,
  cardVariants,
} from "@/styles/animations/interview.variants";

export default function InterviewSessions() {
  const [searchQuery, setSearchQuery] = useState("");

  const {
    data: myInterviewQuestions,
    isLoading,
    isError,
  } = useGetMyQuestionInterviewQuery();

  // Filter và search logic
  const filteredQuestions = useMemo(() => {
    if (!myInterviewQuestions) return [];

    return myInterviewQuestions.filter((interview) => {
      // Search filter
      const matchesSearch =
        searchQuery === "" ||
        interview.jobPosition
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        interview.jobDescriptions
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase());

      return matchesSearch;
    });
  }, [myInterviewQuestions, searchQuery]);

  return (
    <motion.div
      variants={interviewSectionVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold text-slate-700">
          Your Interview Sessions
        </h2>
        <p className="text-slate-600">
          Review and continue your previous interview sessions.
        </p>

        <SearchComponent
          placeholder="Search interview sessions..."
          onSearch={setSearchQuery}
          searchValue={searchQuery}
          className="mb-6"
        />
      </div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        variants={{
          visible: {
            transition: {
              staggerChildren: 0.1,
            },
          },
        }}
      >
        {isLoading || isError
          ? Array.from({ length: 3 }).map((_, index) => (
              <motion.div
                key={index}
                variants={cardVariants}
                initial="hidden"
                animate="visible"
              >
                <CardInterviewQuestionSkeleton />
              </motion.div>
            ))
          : filteredQuestions?.map((interview, index) => (
              <motion.div
                key={index}
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
              >
                <CardInterviewQuestion interview={interview} index={index} />
              </motion.div>
            ))}
      </motion.div>

      {!isLoading &&
        !isError &&
        filteredQuestions.length === 0 &&
        searchQuery && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-8"
          >
            <p className="text-slate-500">
              No interview sessions found matching &quot;{searchQuery}&quot;
            </p>
          </motion.div>
        )}
    </motion.div>
  );
}
