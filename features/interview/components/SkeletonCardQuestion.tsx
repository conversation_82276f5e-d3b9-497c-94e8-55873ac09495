import { Skeleton } from "@/components/ui/skeleton";

export const CardQuestionSkeleton = () => {
  return (
    <div className="relative border border-gray-200 bg-white rounded-2xl shadow-xl overflow-hidden">
      <div className="relative z-10 p-4">
        {/* Header with number and badges */}
        <div className="flex flex-row items-start sm:items-center justify-between gap-4 sm:gap-6 mb-4">
          <Skeleton className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg" />
          <div className="flex items-center gap-2 sm:gap-4">
            <Skeleton className="h-8 sm:h-10 w-20 sm:w-24 rounded-lg" />
            <Skeleton className="h-8 sm:h-10 w-16 sm:w-20 rounded-lg" />
          </div>
        </div>

        {/* Question text */}
        <div className="mb-4">
          <Skeleton className="h-5 sm:h-6 w-full mb-2" />
          <Skeleton className="h-5 sm:h-6 w-3/4" />
        </div>

        {/* Tips section */}
        <div className="border border-amber-200 rounded-lg p-2 shadow-lg">
          <div className="flex items-start space-x-3 sm:space-x-4">
            <div className="w-full">
              <div className="flex items-center space-x-1 sm:space-x-2 mb-2 sm:mb-3">
                <Skeleton className="w-5 h-5 sm:w-6 sm:h-6 rounded" />
                <Skeleton className="h-4 sm:h-5 w-12" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-3 sm:h-4 w-full" />
                <Skeleton className="h-3 sm:h-4 w-5/6" />
                <Skeleton className="h-3 sm:h-4 w-2/3" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
