import { useCreateQuestionInterviewMutation } from "@/services/api/interview-question";
import { UseFormReturn } from "react-hook-form";
import { MockInterviewFormData } from "../components/MockInterviewDialog";
import { useRouter } from "next/navigation";

export const useCreateInterviewQuestion = (
  form: UseFormReturn<any>,
  onOpenChange: (open: boolean) => void
) => {
  const router = useRouter();

  const [createQuestionInterview, { isLoading }] =
    useCreateQuestionInterviewMutation();

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  const handleCreateInterviewQuestion = async (data: MockInterviewFormData) => {
    const formData = new FormData();
    formData.append("jobPosition", data.jobPosition);
    formData.append("interviewDuration", data.interviewDuration);
    data.interviewType.forEach((type) => {
      formData.append("interviewType", type);
    });
    if (data.jdFile instanceof File) {
      formData.append("jdFile", data.jdFile);
    }

    const response = await createQuestionInterview(formData);
    if (response.data) {
      router.push(`/interview/questions/${response.data._id}`);
    }

    handleClose();
  };

  return { handleCreateInterviewQuestion, isLoading, handleClose };
};
