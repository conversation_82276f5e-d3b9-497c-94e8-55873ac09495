import { useConfirm } from "@/providers/ConfirmModalProvider";
import { useDeleteQuestionInterviewByIdMutation } from "@/services/api/interview-question";
import { useRouter } from "next/navigation";
export const useInterviewQuestion = () => {
  const router = useRouter();
  const [deleteInterViewQuestion] = useDeleteQuestionInterviewByIdMutation();
  const { confirmAsync } = useConfirm();

  const handleDeleteInterViewQuestion = async (interviewId: string) => {
    await confirmAsync(() => deleteInterViewQuestion(interviewId), {
      title: "Delete Interview Question",
      description: "This action cannot be undone.",
      confirmText: "Delete",
      cancelText: "Cancel",
      variant: "destructive",
      loadingText: "Deleting...",
    });
  };

  const naviteToQuestionInterviewById = (interviewId: string) => {
    router.push(`/interview/questions/${interviewId}`);
  };

  const handleStartInterview = (interviewId: string) => {
    router.push(`/interview/start/${interviewId}`);
  };

  return {
    handleDeleteInterViewQuestion,
    naviteToQuestionInterviewById,
    handleStartInterview,
  };
};
