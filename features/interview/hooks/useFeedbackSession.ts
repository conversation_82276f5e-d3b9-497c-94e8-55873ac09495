import { ConversationResponse } from "@/common/types/conversation";
import { useRouter } from "next/navigation";
import { useState } from "react";

export const useFeedbackSession = () => {
  const router = useRouter();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const handleViewFeedback = (interviewId: string) => {
    router.push(`/interview/feedbacks/${interviewId}`);
  };

  const calculateScore = (conversation: ConversationResponse): number => {
    if (!conversation.feedback?.summary?.skillsRating) return 0;

    const skills = conversation.feedback.summary.skillsRating;
    const average =
      (skills.technicalSkills +
        skills.communication +
        skills.problemSolving +
        skills.experience) /
      4;

    return Math.round(average);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return {
    currentPage,
    itemsPerPage,
    handleViewFeedback,
    calculateScore,
    handlePageChange,
  };
};
