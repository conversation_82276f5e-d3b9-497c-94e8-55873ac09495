export interface CoverLetterModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export type CoverLetterLanguage = "Vietnamese" | "English";
export type CoverLetterAnalysisType = "role" | "jd" | "";

export interface CoverLetterFormData {
  analysisType: CoverLetterAnalysisType;
  roleDescription: string;
  language: CoverLetterLanguage;
  cvFile: File | null;
  jdFile: File | null;
}

export interface CoverLetterAnalysisTypeSelectProps {
  analysisType: CoverLetterAnalysisType;
  onAnalysisTypeChange: (value: CoverLetterAnalysisType) => void;
}

export interface CoverLetterRoleFormProps {
  roleDescription: string;
  onRoleDescriptionChange: (value: string) => void;
  language: CoverLetterLanguage;
  onLanguageChange: (value: CoverLetterLanguage) => void;
  cvFile: File | null;
  onFileUpload: (file: File | null, type: "cv" | "jd") => void;
  onDragOver: (e: React.DragEvent, type: string) => void;
  onDrop: (e: React.DragEvent, type: "cv" | "jd") => void;
}

export interface CoverLetterJDFormProps {
  language: CoverLetterLanguage;
  onLanguageChange: (value: CoverLetterLanguage) => void;
  cvFile: File | null;
  jdFile: File | null;
  onFileUpload: (file: File | null, type: "cv" | "jd") => void;
  onDragOver: (e: React.DragEvent, type: string) => void;
  onDrop: (e: React.DragEvent, type: "cv" | "jd") => void;
}

export interface CoverLetterModalHeaderProps {}

export interface CoverLetterModalActionsProps {
  isGenerating: boolean;
  isFormValid: boolean;
  onGenerate: () => void;
  onClose: () => void;
}
