import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { CoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";
import { COVER_LETTER_LABELS } from "../constants/labels";
import {
  DetailedFormatRange,
  DetailedFormattingData,
} from "@/lib/detailedFormattingUtils";

export const useCoverLetterState = (
  coverLetterData?: CoverLetter,
  propCoverLetterId?: string
) => {
  const router = useRouter();

  const isVietnamese = coverLetterData?.language === "vietnamese";
  const labels = isVietnamese ? COVER_LETTER_LABELS.vi : COVER_LETTER_LABELS.en;

  const coverLetterId = propCoverLetterId || coverLetterData?._id || "";
  const [editingField, setEditingField] = useState<string | null>(null);
  const [headerColor, setHeaderColor] = useState("#F4E8F8");
  const [contactColor, setContactColor] = useState("#F4E8F8");
  const [currentTheme, setCurrentTheme] = useState(
    () => coverLetterData?.currentTheme || "blue"
  );
  const [showToolbar, setShowToolbar] = useState<string | null>(null);
  const [fontSize, setFontSize] = useState(
    () => coverLetterData?.fontSize || "M"
  );
  const [fontFamily, setFontFamily] = useState(
    () => coverLetterData?.fontFamily || "Roboto"
  );
  const [lineHeight, setLineHeight] = useState(
    () => coverLetterData?.lineHeight || "1.5"
  );
  const [textAlign, setTextAlign] = useState("left");
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [showAddSectionsModal, setShowAddSectionsModal] = useState(false);

  const [sectionFormatting, setSectionFormatting] = useState(() => {
    const data = coverLetterData?.sectionFormatting || {};
    const deepCopy: Record<string, any> = {};
    Object.keys(data).forEach((key) => {
      deepCopy[key] = { ...(data[key] || {}) };
    });
    return deepCopy;
  });

  const [detailedFormatting, setDetailedFormatting] =
    useState<DetailedFormattingData>(() => {
      return coverLetterData?.detailedFormatting || {};
    });
  useEffect(() => {
    if (coverLetterData) {
      const theme = coverLetterData.currentTheme || "blue";
      setCurrentTheme(theme);
      setFontSize(coverLetterData.fontSize || "M");
      setFontFamily(coverLetterData.fontFamily || "Roboto");
      setLineHeight(coverLetterData.lineHeight || "1.5");

      // Update theme colors based on saved theme
      updateThemeColors(theme);

      // Deep copy sectionFormatting to avoid readonly issues
      const data = coverLetterData.sectionFormatting || {};
      const deepCopy: Record<string, any> = {};
      Object.keys(data).forEach((key) => {
        deepCopy[key] = { ...(data[key] || {}) };
      });
      setSectionFormatting(deepCopy);

      // Update detailed formatting
      setDetailedFormatting(coverLetterData.detailedFormatting || {});
    }
  }, [coverLetterData]);

  // Function to update theme colors
  const updateThemeColors = (theme: string) => {
    let headerColor = "#F4E8F8"; // default blue
    let contactColor = "#F4E8F8"; // default blue

    switch (theme) {
      case "green":
        headerColor = "#E8F5E8";
        contactColor = "#E8F5E8";
        break;
      case "yellow":
        headerColor = "#FFF8E1";
        contactColor = "#FFF8E1";
        break;
      case "red":
        headerColor = "#FFE8E8";
        contactColor = "#FFE8E8";
        break;
      // blue and default cases use the default values
    }

    setHeaderColor(headerColor);
    setContactColor(contactColor);
  };

  // Update colors when theme changes
  useEffect(() => {
    updateThemeColors(currentTheme);
  }, [currentTheme]);

  // Get current section formatting
  const getCurrentSectionFormatting = (sectionId: string) => {
    if (!sectionFormatting || typeof sectionFormatting !== "object") {
      return {
        textAlign: "left" as const,
        isBold: false,
        isItalic: false,
        isUnderline: false,
        isHidden: false,
      };
    }

    return (
      sectionFormatting[sectionId] || {
        textAlign: "left" as const,
        isBold: false,
        isItalic: false,
        isUnderline: false,
        isHidden: false,
      }
    );
  };

  // Update section formatting
  const updateSectionFormatting = (
    sectionId: string,
    formatting: Partial<{
      textAlign: "left" | "center" | "right" | "justify";
      isBold: boolean;
      isItalic: boolean;
      isUnderline: boolean;
      isHidden: boolean;
    }>
  ) => {
    setSectionFormatting((prev) => {
      const currentFormatting = getCurrentSectionFormatting(sectionId);
      return {
        ...prev,
        [sectionId]: {
          ...currentFormatting,
          ...formatting,
        },
      };
    });
  };

  // Update current section formatting based on active field
  useEffect(() => {
    if (editingField) {
      const currentFormatting = getCurrentSectionFormatting(editingField);
      setTextAlign(currentFormatting.textAlign || "left");
      setIsBold(currentFormatting.isBold || false);
      setIsItalic(currentFormatting.isItalic || false);
      setIsUnderline(currentFormatting.isUnderline || false);
    }
  }, [editingField, sectionFormatting]);

  // Save formatting when leaving edit mode
  const prevEditingField = useRef<string | null>(null);

  useEffect(() => {
    // If we were editing a field and now we're not, save formatting
    if (prevEditingField.current && !editingField) {
      const fieldToSave = prevEditingField.current;
      const fieldDetailedFormatting = detailedFormatting[fieldToSave] || [];

      // Only save section-level bold/italic/underline if there's no detailed formatting
      // If there's detailed formatting, only save textAlign
      if (fieldDetailedFormatting.length === 0) {
        // No detailed formatting - save section formatting
        updateSectionFormatting(fieldToSave, {
          textAlign: textAlign as "left" | "center" | "right" | "justify",
          isBold,
          isItalic,
          isUnderline,
        });
      } else {
        // Has detailed formatting - only save textAlign, clear section bold/italic/underline
        updateSectionFormatting(fieldToSave, {
          textAlign: textAlign as "left" | "center" | "right" | "justify",
          isBold: false,
          isItalic: false,
          isUnderline: false,
        });
      }
    }

    prevEditingField.current = editingField;
  }, [
    editingField,
    textAlign,
    isBold,
    isItalic,
    isUnderline,
    detailedFormatting,
  ]);

  // Change template handler
  const handleChangeTemplate = () => {
    if (coverLetterId) {
      router.push(`/cover-letter/templates?id=${coverLetterId}`);
    }
  };

  // Handle per-section formatting changes
  const handleTextAlignChange = (align: string) => {
    const validAlign = align as "left" | "center" | "right" | "justify";
    setTextAlign(validAlign);
    if (editingField) {
      updateSectionFormatting(editingField, { textAlign: validAlign });
    }
  };

  // Handlers for bold/italic/underline state (for saving to database)
  const handleBoldToggle = () => {
    const newBoldState = !isBold;
    setIsBold(newBoldState);
    if (editingField) {
      updateSectionFormatting(editingField, { isBold: newBoldState });
    }
  };

  const handleItalicToggle = () => {
    const newItalicState = !isItalic;
    setIsItalic(newItalicState);
    if (editingField) {
      updateSectionFormatting(editingField, { isItalic: newItalicState });
    }
  };

  const handleUnderlineToggle = () => {
    const newUnderlineState = !isUnderline;
    setIsUnderline(newUnderlineState);
    if (editingField) {
      updateSectionFormatting(editingField, { isUnderline: newUnderlineState });
    }
  };

  // Detailed formatting functions
  const updateDetailedFormatting = (
    field: string,
    ranges: DetailedFormatRange[]
  ) => {
    setDetailedFormatting((prev) => ({
      ...prev,
      [field]: ranges,
    }));

    // If detailed formatting is being set, clear section formatting for this field
    if (ranges.length > 0) {
      setSectionFormatting((prevSection) => {
        const updated = { ...prevSection };
        if (updated[field]) {
          updated[field] = {
            ...updated[field],
            isBold: false,
            isItalic: false,
            isUnderline: false,
          };
        }
        return updated;
      });
    }
  };

  const getDetailedFormatting = (field: string): DetailedFormatRange[] => {
    return detailedFormatting[field] || [];
  };

  const clearDetailedFormatting = (field: string) => {
    setDetailedFormatting((prev) => {
      const updated = { ...prev };
      delete updated[field];
      return updated;
    });
  };

  const clearSectionFormatting = (field: string) => {
    setSectionFormatting((prev) => {
      const updated = { ...prev };
      if (updated[field]) {
        // Clear bold, italic, underline but keep textAlign and isHidden
        updated[field] = {
          ...updated[field],
          isBold: false,
          isItalic: false,
          isUnderline: false,
        };
      }
      return updated;
    });
  };

  const resetToolbarStates = () => {
    setIsBold(false);
    setIsItalic(false);
    setIsUnderline(false);
  };

  return {
    isVietnamese,
    labels,
    coverLetterId,
    editingField,
    setEditingField,
    headerColor,
    setHeaderColor,
    contactColor,
    setContactColor,
    currentTheme,
    setCurrentTheme,
    showToolbar,
    setShowToolbar,
    fontSize,
    setFontSize,
    fontFamily,
    setFontFamily,
    lineHeight,
    setLineHeight,
    textAlign,
    setTextAlign,
    isBold,
    setIsBold,
    isItalic,
    setIsItalic,
    isUnderline,
    setIsUnderline,
    showAddSectionsModal,
    setShowAddSectionsModal,
    handleChangeTemplate,
    // Per-section formatting
    sectionFormatting,
    setSectionFormatting,
    getCurrentSectionFormatting,
    updateSectionFormatting,
    handleTextAlignChange,
    handleBoldToggle,
    handleItalicToggle,
    handleUnderlineToggle,
    // Detailed formatting
    detailedFormatting,
    updateDetailedFormatting,
    getDetailedFormatting,
    clearDetailedFormatting,
    clearSectionFormatting,
    resetToolbarStates,
  };
};
