import { useSelector } from "react-redux";
import { useSearchParams } from "next/navigation";
import { RootState } from "@/redux/store";
import { useGetCoverLetterByIdQuery } from "@/services/api/cover-letter";

export const useCoverLetterData = (overrideId?: string) => {
  const searchParams = useSearchParams();
  const currentCoverLetterId = useSelector(
    (state: RootState) => state.coverLetter.currentCoverLetterId
  );

  // Priority: overrideId > URL param > Redux state
  const coverLetterIdFromUrl = searchParams?.get("id");
  const effectiveId =
    overrideId || coverLetterIdFromUrl || currentCoverLetterId;

  const {
    data: coverLetterData,
    isLoading,
    error,
  } = useGetCoverLetterByIdQuery(effectiveId || "", { skip: !effectiveId });

  return {
    coverLetterData,
    isLoading,
    error,
    coverLetterId: effectiveId,
  };
};
