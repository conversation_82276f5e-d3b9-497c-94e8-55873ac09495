import { useCallback, useState } from "react";
import { useUpdateCoverLetterMutation } from "@/services/api/cover-letter";
import { FormData } from "@/common/types/cover-letter";
import { UpdateCoverLetterRequest } from "@/services/api/cover-letter/types/cover-letter.d";
import { usePDFGeneration } from "./usePDFGeneration";
import { DetailedFormattingData } from "@/lib/detailedFormattingUtils";

interface SaveState {
  fontSize: string;
  fontFamily: string;
  lineHeight: string;
  currentTheme: string;
  sectionFormatting: Record<string, any>;
  hiddenSections: Set<string>;
  detailedFormatting?: DetailedFormattingData;
}

export const useCoverLetterSave = (
  coverLetterId: string,
  templateId?: string
) => {
  const [updateCoverLetter, { isLoading, error }] =
    useUpdateCoverLetterMutation();
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [coverLetterFileUrl, setCoverLetterFileUrl] = useState("");
  const {
    generateAndUploadPDF,
    isLoading: isPDFLoading,
    error: pdfError,
  } = usePDFGeneration();

  const mapFormDataToUpdateRequest = (
    formData: FormData,
    saveState?: SaveState
  ): UpdateCoverLetterRequest => {
    const updateRequest: UpdateCoverLetterRequest = {
      fullName: formData.fullName,
      targetPosition: formData.targetPosition,
      profileImage: formData.profileImage,
      dateOfBirth: formData.dateOfBirth,
      phoneNumber: formData.phoneNumber,
      email: formData.emailAddress,
      address: formData.address,
      linkedin: formData.linkedin,
      hiringManagerName: formData.hiringManagerName,
      hiringManagerTitle: formData.hiringManagerTitle,
      companyName: formData.companyName,
      companyAddress: formData.companyAddress,
      applicationDate: formData.applicationDate,
      jobTitle: formData.jobTitle,
      greetingLine: formData.greeting,
      openingParagraph: formData.openingParagraph,
      experienceAchievements: formData.experienceSection,
      skillsStrengths: formData.skillsSection,
      closingParagraph: formData.closingParagraph,
      signature: `${formData.signatureClosing}\n${formData.signatureName}`,
      templateId: templateId,
    };

    if (saveState) {
      updateRequest.fontSize = saveState.fontSize;
      updateRequest.fontFamily = saveState.fontFamily;
      updateRequest.lineHeight = saveState.lineHeight;
      updateRequest.currentTheme = saveState.currentTheme;
      updateRequest.sectionFormatting = saveState.sectionFormatting;

      const hiddenSectionNames = Array.from(saveState.hiddenSections);
      updateRequest.hiddenSections = hiddenSectionNames;

      const updatedSectionFormatting: Record<string, any> = {};
      Object.keys(saveState.sectionFormatting || {}).forEach((key) => {
        updatedSectionFormatting[key] = { ...saveState.sectionFormatting[key] };
      });

      const allPossibleSections = [
        "greeting",
        "openingParagraph",
        "experienceSection",
        "skillsSection",
        "closingParagraph",
      ];
      allPossibleSections.forEach((sectionName) => {
        if (!updatedSectionFormatting[sectionName]) {
          updatedSectionFormatting[sectionName] = {};
        }
        updatedSectionFormatting[sectionName].isHidden =
          hiddenSectionNames.includes(sectionName);
      });

      updateRequest.sectionFormatting = updatedSectionFormatting;

      // Add detailed formatting if provided
      if (saveState.detailedFormatting) {
        updateRequest.detailedFormatting = saveState.detailedFormatting;
      }
    }

    return updateRequest;
  };

  const handleSave = useCallback(
    async (formData: FormData, saveState?: SaveState) => {
      if (!coverLetterId) {
        console.error("Cover letter ID is required for saving");
        return;
      }

      try {
        let pdfUrl = "";

        if (templateId) {
          try {
            const pdfOptions = saveState
              ? {
                  fontSize: saveState.fontSize,
                  fontFamily: saveState.fontFamily,
                  lineHeight: saveState.lineHeight,
                  currentTheme: saveState.currentTheme,
                  hiddenSections: saveState.hiddenSections,
                  sectionFormatting: saveState.sectionFormatting,
                  detailedFormatting: saveState.detailedFormatting,
                }
              : undefined;

            pdfUrl = await generateAndUploadPDF(
              templateId,
              coverLetterId,
              formData.fullName,
              pdfOptions
            );
          } catch (pdfError) {
            console.error("PDF generation failed:", pdfError);
          }
        }

        const updateData = mapFormDataToUpdateRequest(formData, saveState);
        if (pdfUrl) {
          updateData.coverLetterFileUrl = pdfUrl;
        }
        if (templateId) {
          updateData.templateId = templateId;
        }

        const result = await updateCoverLetter({
          id: coverLetterId,
          data: updateData,
        }).unwrap();

        const finalPdfUrl = result.coverLetterFileUrl || pdfUrl;
        setCoverLetterFileUrl(finalPdfUrl);
        setShowSuccessModal(true);

        return result;
      } catch (error) {
        throw error;
      }
    },
    [updateCoverLetter, coverLetterId, templateId, generateAndUploadPDF]
  );

  const closeSuccessModal = useCallback(() => {
    setShowSuccessModal(false);
    setCoverLetterFileUrl("");
  }, []);

  const handleEdit = useCallback(() => {
    setShowSuccessModal(false);
  }, []);

  return {
    handleSave,
    isLoading: isLoading || isPDFLoading,
    error: error || pdfError,
    showSuccessModal,
    coverLetterFileUrl,
    closeSuccessModal,
    handleEdit,
  };
};
