import { useCallback } from "react";
import { useUploadAvatarMutation } from "@/services/api/cover-letter";

export const useAvatarUpload = (
  coverLetterId: string,
  onImageUpdate?: (imageUrl: string) => void
) => {
  const [uploadAvatar, { isLoading, error }] = useUploadAvatarMutation();

  const handleAvatarUpload = useCallback(
    async (file: File) => {
      if (!file || !coverLetterId) return null;

      // Validate file type
      if (!file.type.startsWith("image/")) {
        throw new Error("Please select an image file");
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        throw new Error("File size must be less than 5MB");
      }

      const formData = new FormData();
      formData.append("file", file);

      try {
        const result = await uploadAvatar({
          id: coverLetterId,
          formData,
        }).unwrap();
        return result.profileImage;
      } catch (error) {
        throw error;
      }
    },
    [uploadAvatar, coverLetterId]
  );

  const triggerFileSelect = useCallback(() => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          await handleAvatarUpload(file);
        } catch (error) {
          console.error("Upload error:", error);
          // You can add toast notification here
        }
      }
    };
    input.click();
  }, [handleAvatarUpload]);

  return {
    handleAvatarUpload,
    triggerFileSelect,
    isLoading,
    error,
  };
};
