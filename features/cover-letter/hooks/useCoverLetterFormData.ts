import { useState, useEffect } from "react";
import { FormData } from "@/common/types/cover-letter";
import { CoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";
import { mapCoverLetterToFormData } from "@/lib/coverLetterMapper";

export const useCoverLetterFormData = (
  coverLetterData?: CoverLetter,
  language?: string
) => {
  const [formData, setFormData] = useState<FormData>(() =>
    mapCoverLetterToFormData(coverLetterData, language)
  );

  // Update form data when coverLetterData or language changes
  useEffect(() => {
    if (coverLetterData) {
      setFormData(mapCoverLetterToFormData(coverLetterData, language));
    }
  }, [coverLetterData, language]);

  return { formData, setFormData };
};
