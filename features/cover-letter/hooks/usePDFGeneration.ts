import { useState, useCallback } from "react";
import { generateCoverLetterPDF } from "@/lib/pdfGenerator";
import { CoverLetterPDFOptions } from "@/lib/pdfUtils";
import {
  uploadPDFToImageKit,
  generateCoverLetterFileName,
} from "@/lib/imagekit";

export interface PDFGenerationState {
  isGenerating: boolean;
  isUploading: boolean;
  error: string | null;
  pdfUrl: string | null;
}

export const usePDFGeneration = () => {
  const [state, setState] = useState<PDFGenerationState>({
    isGenerating: false,
    isUploading: false,
    error: null,
    pdfUrl: null,
  });

  const generateAndUploadPDF = useCallback(
    async (
      templateId: string,
      coverLetterId: string,
      userFullName?: string,
      pdfOptions?: Partial<CoverLetterPDFOptions>
    ): Promise<string> => {
      setState((prev) => ({
        ...prev,
        isGenerating: true,
        error: null,
        pdfUrl: null,
      }));

      try {
        const pdfBlob = await generateCoverLetterPDF(templateId, {
          templateId,
          ...pdfOptions,
        });

        setState((prev) => ({
          ...prev,
          isGenerating: false,
          isUploading: true,
        }));

        const fileName = generateCoverLetterFileName(
          coverLetterId,
          userFullName
        );

        const pdfUrl = await uploadPDFToImageKit(pdfBlob, fileName);

        setState((prev) => ({
          ...prev,
          isUploading: false,
          pdfUrl,
        }));

        return pdfUrl;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to generate or upload PDF";

        setState((prev) => ({
          ...prev,
          isGenerating: false,
          isUploading: false,
          error: errorMessage,
        }));

        throw error;
      }
    },
    []
  );

  const resetState = useCallback(() => {
    setState({
      isGenerating: false,
      isUploading: false,
      error: null,
      pdfUrl: null,
    });
  }, []);

  return {
    ...state,
    generateAndUploadPDF,
    resetState,
    isLoading: state.isGenerating || state.isUploading,
  };
};
