import { useState, useCallback, useEffect } from "react";
import { FormData } from "../../../common/types/cover-letter";
import { CoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";

// Define which sections can be moved/deleted
const MOVEABLE_SECTIONS = [
  "greeting",
  "openingParagraph",
  "experienceSection",
  "skillsSection",
  "closingParagraph",
];

const SECTION_ORDER = [
  "greeting",
  "openingParagraph",
  "experienceSection",
  "skillsSection",
  "closingParagraph",
];

interface UseSectionManagerProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  coverLetterData?: CoverLetter;
}

export const useSectionManager = ({
  formData,
  setFormData,
  coverLetterData,
}: UseSectionManagerProps) => {
  // State to track hidden sections - Initialize with backend data
  const [hiddenSections, setHiddenSections] = useState<Set<string>>(() => {
    if (coverLetterData?.sectionFormatting) {
      const hiddenSectionNames = Object.keys(
        coverLetterData.sectionFormatting
      ).filter(
        (key) => coverLetterData.sectionFormatting![key]?.isHidden === true
      );
      return new Set(hiddenSectionNames);
    }
    return new Set();
  });

  // Update hidden sections when coverLetterData changes
  useEffect(() => {
    if (coverLetterData?.sectionFormatting) {
      const hiddenSectionNames = Object.keys(
        coverLetterData.sectionFormatting
      ).filter(
        (key) => coverLetterData.sectionFormatting![key]?.isHidden === true
      );
      setHiddenSections(new Set(hiddenSectionNames));
    }
  }, [coverLetterData]);

  // Get the current order of sections based on their content
  const getCurrentSectionOrder = useCallback(() => {
    return SECTION_ORDER.filter((section) => {
      const key = section as keyof FormData;
      return formData[key] && String(formData[key]).trim() !== "";
    });
  }, [formData]);

  // Check if a section can be moved up
  const canMoveUp = useCallback(
    (field: string) => {
      if (!MOVEABLE_SECTIONS.includes(field)) return false;

      const currentOrder = getCurrentSectionOrder();
      const currentIndex = currentOrder.indexOf(field);
      return currentIndex > 1; // Can't move above greeting
    },
    [getCurrentSectionOrder]
  );

  // Check if a section can be moved down
  const canMoveDown = useCallback(
    (field: string) => {
      if (!MOVEABLE_SECTIONS.includes(field)) return false;

      const currentOrder = getCurrentSectionOrder();
      const currentIndex = currentOrder.indexOf(field);
      return currentIndex >= 0 && currentIndex < currentOrder.length - 1;
    },
    [getCurrentSectionOrder]
  );

  // Check if a section can be hidden/shown
  const canToggleVisibility = useCallback((field: string) => {
    return MOVEABLE_SECTIONS.includes(field);
  }, []);

  // Check if a section is hidden
  const isFieldHidden = useCallback(
    (field: string) => {
      return hiddenSections.has(field);
    },
    [hiddenSections]
  );

  // Move section up
  const moveUp = useCallback(
    (field: string) => {
      if (!canMoveUp(field)) return;

      const currentOrder = getCurrentSectionOrder();
      const currentIndex = currentOrder.indexOf(field);

      if (currentIndex > 1) {
        // Swap content with the section above
        const aboveField = currentOrder[currentIndex - 1];
        const currentContent = formData[field as keyof FormData];
        const aboveContent = formData[aboveField as keyof FormData];

        setFormData({
          ...formData,
          [field]: aboveContent,
          [aboveField]: currentContent,
        });
      }
    },
    [formData, setFormData, canMoveUp, getCurrentSectionOrder]
  );

  // Move section down
  const moveDown = useCallback(
    (field: string) => {
      if (!canMoveDown(field)) return;

      const currentOrder = getCurrentSectionOrder();
      const currentIndex = currentOrder.indexOf(field);

      if (currentIndex >= 0 && currentIndex < currentOrder.length - 1) {
        // Swap content with the section below
        const belowField = currentOrder[currentIndex + 1];
        const currentContent = formData[field as keyof FormData];
        const belowContent = formData[belowField as keyof FormData];

        setFormData({
          ...formData,
          [field]: belowContent,
          [belowField]: currentContent,
        });
      }
    },
    [formData, setFormData, canMoveDown, getCurrentSectionOrder]
  );

  // Toggle section visibility
  const toggleVisibility = useCallback(
    (field: string) => {
      if (!canToggleVisibility(field)) return;

      setHiddenSections((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(field)) {
          newSet.delete(field);
        } else {
          newSet.add(field);
        }
        return newSet;
      });
    },
    [canToggleVisibility]
  );

  // Check if BubbleMenu should be shown for a field
  const shouldShowBubbleMenu = useCallback((field: string) => {
    return MOVEABLE_SECTIONS.includes(field);
  }, []);

  return {
    canMoveUp,
    canMoveDown,
    canToggleVisibility,
    isFieldHidden,
    moveUp,
    moveDown,
    toggleVisibility,
    shouldShowBubbleMenu,
    hiddenSections,
  };
};
