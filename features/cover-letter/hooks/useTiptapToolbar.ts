"use client";
import { useCallback, useEffect, useState } from "react";
import { useTiptap } from "../../../providers/TiptapProvider";
import { extractDetailedFormatting } from "@/lib/detailedFormattingUtils";

interface UseTiptapToolbarProps {
  onClearDetailedFormatting?: (field: string) => void;
  onUpdateDetailedFormatting?: (field: string, ranges: any[]) => void;
  onClearSectionFormatting?: (field: string) => void;
  onResetToolbarStates?: () => void;
}

export const useTiptapToolbar = (props: UseTiptapToolbarProps = {}) => {
  const { activeEditor, activeField } = useTiptap();
  const {
    onClearDetailedFormatting,
    onUpdateDetailedFormatting,
    onClearSectionFormatting,
    onResetToolbarStates,
  } = props;

  // State to track current formatting
  const [currentStates, setCurrentStates] = useState({
    isBold: false,
    isItalic: false,
    isUnderline: false,
    textAlign: "left" as "left" | "center" | "right" | "justify",
    canUndo: false,
    canRedo: false,
  });

  // Update formatting states when editor or selection changes
  const updateFormattingStates = useCallback(() => {
    if (!activeEditor) {
      setCurrentStates({
        isBold: false,
        isItalic: false,
        isUnderline: false,
        textAlign: "left",
        canUndo: false,
        canRedo: false,
      });
      return;
    }

    // Get text alignment
    let textAlign: "left" | "center" | "right" | "justify" = "left";
    if (activeEditor.isActive({ textAlign: "center" })) {
      textAlign = "center";
    } else if (activeEditor.isActive({ textAlign: "right" })) {
      textAlign = "right";
    } else if (activeEditor.isActive({ textAlign: "justify" })) {
      textAlign = "justify";
    }

    setCurrentStates({
      isBold: activeEditor.isActive("bold"),
      isItalic: activeEditor.isActive("italic"),
      isUnderline: activeEditor.isActive("underline"),
      textAlign,
      canUndo: activeEditor.can().undo(),
      canRedo: activeEditor.can().redo(),
    });
  }, [activeEditor]);

  // Listen for editor changes and selection updates
  useEffect(() => {
    if (!activeEditor) {
      return;
    }

    // Update states immediately
    updateFormattingStates();

    // Listen for selection updates
    const handleUpdate = () => {
      updateFormattingStates();
    };

    // Listen for both selection and transaction updates
    activeEditor.on("selectionUpdate", handleUpdate);
    activeEditor.on("transaction", handleUpdate);

    return () => {
      activeEditor.off("selectionUpdate", handleUpdate);
      activeEditor.off("transaction", handleUpdate);
    };
  }, [activeEditor, updateFormattingStates]);

  // Font size mapping
  const fontSizeMap = {
    S: "14px",
    M: "16px",
    L: "18px",
  };

  // Line height mapping
  const lineHeightMap = {
    "1.2": "1.2",
    "1.5": "1.5",
    "1.8": "1.8",
  };

  const handleFontSizeChange = useCallback(
    (size: string) => {
      if (!activeEditor || !activeField) return;

      const fontSize = fontSizeMap[size as keyof typeof fontSizeMap] || "16px";

      // Try different approaches to apply font size
      try {
        // Method 1: Using setMark with style attribute
        activeEditor.chain().focus().setMark("textStyle", { fontSize }).run();

        // Method 2: If selection is empty, apply to whole content
        if (activeEditor.state.selection.empty) {
          const { from, to } = activeEditor.state.selection;
          activeEditor
            .chain()
            .focus()
            .setTextSelection({
              from: 0,
              to: activeEditor.state.doc.content.size,
            })
            .setMark("textStyle", { fontSize })
            .setTextSelection({ from, to })
            .run();
        }
      } catch (error) {
        // Font size change error - silently continue
      }
    },
    [activeEditor, activeField]
  );

  const handleLineHeightChange = useCallback(
    (height: string) => {
      if (!activeEditor || !activeField) return;

      const lineHeight =
        lineHeightMap[height as keyof typeof lineHeightMap] || "1.5";

      try {
        // Method 1: Apply to selected text
        activeEditor.chain().focus().setMark("textStyle", { lineHeight }).run();

        // Method 2: If no selection, apply to whole content
        if (activeEditor.state.selection.empty) {
          const { from, to } = activeEditor.state.selection;
          activeEditor
            .chain()
            .focus()
            .setTextSelection({
              from: 0,
              to: activeEditor.state.doc.content.size,
            })
            .setMark("textStyle", { lineHeight })
            .setTextSelection({ from, to })
            .run();
        }
      } catch (error) {
        // Line height change error - silently continue
      }
    },
    [activeEditor, activeField]
  );

  const handleBoldToggle = useCallback(() => {
    if (!activeEditor || !activeField) return;

    const { from, to } = activeEditor.state.selection;
    if (from === to) {
      return;
    }
    activeEditor.chain().focus().toggleBold().run();
  }, [activeEditor, activeField]);

  const handleItalicToggle = useCallback(() => {
    if (!activeEditor || !activeField) return;

    const { from, to } = activeEditor.state.selection;
    if (from === to) {
      return;
    }
    activeEditor.chain().focus().toggleItalic().run();
  }, [activeEditor, activeField]);

  const handleUnderlineToggle = useCallback(() => {
    if (!activeEditor || !activeField) return;

    const { from, to } = activeEditor.state.selection;
    if (from === to) {
      return;
    }
    activeEditor.chain().focus().toggleUnderline().run();
  }, [activeEditor, activeField]);

  const handleTextAlignChange = useCallback(
    (align: string) => {
      if (!activeEditor || !activeField) return;

      switch (align) {
        case "left":
          activeEditor.chain().focus().setTextAlign("left").run();
          break;
        case "center":
          activeEditor.chain().focus().setTextAlign("center").run();
          break;
        case "right":
          activeEditor.chain().focus().setTextAlign("right").run();
          break;
        case "justify":
          activeEditor.chain().focus().setTextAlign("justify").run();
          break;
      }
    },
    [activeEditor, activeField]
  );

  const handleUndo = useCallback(() => {
    if (!activeEditor || !activeField) return;

    // Perform undo operation
    activeEditor.chain().focus().undo().run();

    // Update detailed formatting after undo
    setTimeout(() => {
      if (onUpdateDetailedFormatting && activeField) {
        const html = activeEditor.getHTML();
        const plainText = activeEditor.getText();
        const newRanges = extractDetailedFormatting(
          activeField,
          html,
          plainText
        );
        onUpdateDetailedFormatting(activeField, newRanges);
      }
    }, 50);
  }, [activeEditor, activeField, onUpdateDetailedFormatting]);

  const handleRedo = useCallback(() => {
    if (!activeEditor || !activeField) return;

    // Perform redo operation
    activeEditor.chain().focus().redo().run();

    // Update detailed formatting after redo
    setTimeout(() => {
      if (onUpdateDetailedFormatting && activeField) {
        const html = activeEditor.getHTML();
        const plainText = activeEditor.getText();
        const newRanges = extractDetailedFormatting(
          activeField,
          html,
          plainText
        );
        onUpdateDetailedFormatting(activeField, newRanges);
      }
    }, 50);
  }, [activeEditor, activeField, onUpdateDetailedFormatting]);

  const handleClearFormat = useCallback(() => {
    if (!activeEditor || !activeField) return;

    // Clear formatting in editor
    activeEditor.chain().focus().clearNodes().unsetAllMarks().run();

    // Clear detailed formatting for this field
    if (onClearDetailedFormatting) {
      onClearDetailedFormatting(activeField);
    }

    // Clear section formatting for this field
    if (onClearSectionFormatting) {
      onClearSectionFormatting(activeField);
    }

    // Reset toolbar states to reflect cleared formatting
    if (onResetToolbarStates) {
      onResetToolbarStates();
    }
  }, [
    activeEditor,
    activeField,
    onClearDetailedFormatting,
    onClearSectionFormatting,
    onResetToolbarStates,
  ]);

  const handleAddSection = useCallback(() => {
    if (!activeEditor || !activeField) return;

    // Add a new paragraph at the end of the current editor content
    const { doc } = activeEditor.state;
    const endPos = doc.content.size;

    activeEditor
      .chain()
      .focus()
      .setTextSelection(endPos)
      .insertContent("<p><br></p><p>New section content...</p>")
      .run();
  }, [activeEditor, activeField]);

  // Get current formatting states
  const getCurrentStates = useCallback(() => {
    return currentStates;
  }, [currentStates]);

  return {
    activeEditor,
    activeField,
    handleFontSizeChange,
    handleLineHeightChange,
    handleBoldToggle,
    handleItalicToggle,
    handleUnderlineToggle,
    handleTextAlignChange,
    handleUndo,
    handleRedo,
    handleClearFormat,
    handleAddSection,
    getCurrentStates,
  };
};
