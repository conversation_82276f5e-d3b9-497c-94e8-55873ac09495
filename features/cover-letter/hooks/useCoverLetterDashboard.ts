import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  useGetMyCoverLettersQuery,
  useDeleteCoverLetterMutation,
} from "@/services/api/cover-letter";
import { CoverLetter as APICoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";

export const useCoverLetterDashboard = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [languageFilter, setLanguageFilter] = useState<string>("all");
  const [analystFilter, setAnalystFilter] = useState<string>("all");
  const [coverLetterModalOpen, setCoverLetterModalOpen] = useState(false);

  const {
    data: recentCoverLetters,
    isLoading: isLoadingRecent,
    error: recentError,
    refetch: refetchCoverLetters,
  } = useGetMyCoverLettersQuery(undefined);

  const [deleteCoverLetter, { isLoading: isDeleting }] =
    useDeleteCoverLetterMutation();
  const [deleteConfirm, setDeleteConfirm] = useState<{
    isOpen: boolean;
    coverLetterId: string;
    coverLetterTitle: string;
  }>({
    isOpen: false,
    coverLetterId: "",
    coverLetterTitle: "",
  });
  const filteredCoverLetters = recentCoverLetters
    ? recentCoverLetters.filter((coverLetter: APICoverLetter) => {
        const matchesSearch =
          coverLetter.content.header.targetPosition
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          coverLetter.content.recipientInfo.companyName
            .toLowerCase()
            .includes(searchTerm.toLowerCase());

        const language = coverLetter.language === "english" ? "EN" : "VN";
        const matchesLanguage =
          languageFilter === "all" || language === languageFilter;

        const analystType =
          coverLetter.agentType === "AI_COVER_LETTER_ROLE" ? "Role" : "JD";
        const matchesAnalyst =
          analystFilter === "all" || analystType === analystFilter;

        return matchesSearch && matchesLanguage && matchesAnalyst;
      })
    : [];
  const stats = {
    totalLetters: recentCoverLetters ? recentCoverLetters.length : 0,
    roleAnalyst: recentCoverLetters
      ? recentCoverLetters.filter(
          (coverLetter: APICoverLetter) =>
            coverLetter.agentType === "AI_COVER_LETTER_ROLE"
        ).length
      : 0,
    jdAnalyst: recentCoverLetters
      ? recentCoverLetters.filter(
          (coverLetter: APICoverLetter) =>
            coverLetter.agentType === "AI_COVER_LETTER_JD"
        ).length
      : 0,
    lastCreated:
      recentCoverLetters && recentCoverLetters.length > 0
        ? new Date(
            Math.max(
              ...recentCoverLetters.map((coverLetter: APICoverLetter) =>
                new Date(coverLetter.createdAt).getTime()
              )
            )
          ).toLocaleDateString()
        : "None",
  };
  const handleDeleteClick = (
    coverLetterId: string,
    coverLetterTitle: string
  ) => {
    setDeleteConfirm({
      isOpen: true,
      coverLetterId,
      coverLetterTitle,
    });
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteCoverLetter(deleteConfirm.coverLetterId).unwrap();
      setDeleteConfirm({
        isOpen: false,
        coverLetterId: "",
        coverLetterTitle: "",
      });
    } catch (error) {
      console.error("Failed to delete cover letter:", error);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirm({
      isOpen: false,
      coverLetterId: "",
      coverLetterTitle: "",
    });
  };

  const handleViewCoverLetter = (id: string) => {
    // Find the cover letter to get the PDF URL
    const coverLetter = recentCoverLetters?.find(
      (cl: APICoverLetter) => cl._id === id
    );
    if (coverLetter?.coverLetterFileUrl) {
      // Open PDF in new tab
      window.open(coverLetter.coverLetterFileUrl, "_blank");
    } else {
      console.error("PDF URL not found for cover letter:", id);
      alert("PDF file not found. Please try regenerating the cover letter.");
    }
  };

  const handleEditCoverLetter = (id: string) => {
    const coverLetter = recentCoverLetters?.find(
      (cl: APICoverLetter) => cl._id === id
    );
    if (coverLetter) {
      const template = coverLetter.templateId || "temp1";
      router.push(`/cover-letter/templates/${id}?template=${template}`);
    }
  };

  const handleDownloadCoverLetter = async (id: string) => {
    const coverLetter = recentCoverLetters?.find(
      (cl: APICoverLetter) => cl._id === id
    );

    if (!coverLetter?.coverLetterFileUrl) {
      console.error("PDF URL not found for cover letter:", id);
      alert("PDF file not found. Please try regenerating the cover letter.");
      return;
    }

    try {
      const cleanPosition = coverLetter.content.header.targetPosition
        .replace(/[^a-zA-Z0-9\s]/g, "")
        .replace(/\s+/g, "-");
      const cleanCompany = coverLetter.content.recipientInfo.companyName
        .replace(/[^a-zA-Z0-9\s]/g, "")
        .replace(/\s+/g, "-");
      const filename = `cover-letter-${cleanPosition}-${cleanCompany}.pdf`;
      const response = await fetch(coverLetter.coverLetterFileUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch PDF file");
      }

      const blob = await response.blob();
      const link = document.createElement("a");
      const url = window.URL.createObjectURL(blob);
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download failed:", error);
      try {
        const link = document.createElement("a");
        link.href = coverLetter.coverLetterFileUrl;
        link.download = `cover-letter-${coverLetter.content.header.targetPosition}-${coverLetter.content.recipientInfo.companyName}.pdf`;
        link.target = "_blank";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (fallbackError) {
        console.error("Fallback download also failed:", fallbackError);
        alert(
          "Failed to download PDF. Please try again or copy the URL to download manually."
        );
      }
    }
  };

  const handleViewFeedback = (id: string) => {
    router.push(`/cover-letter/evaluation/${id}`);
  };

  return {
    searchTerm,
    languageFilter,
    analystFilter,
    coverLetterModalOpen,
    deleteConfirm,
    recentCoverLetters,
    filteredCoverLetters,
    stats,
    isLoadingRecent,
    isDeleting,
    recentError,
    setSearchTerm,
    setLanguageFilter,
    setAnalystFilter,
    setCoverLetterModalOpen,
    handleDeleteClick,
    handleDeleteConfirm,
    handleDeleteCancel,
    handleViewCoverLetter,
    handleEditCoverLetter,
    handleDownloadCoverLetter,
    handleViewFeedback,
    refetchCoverLetters,
  };
};
