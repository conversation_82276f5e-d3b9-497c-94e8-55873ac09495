import { z } from "zod";

// Language enum
export const CoverLetterLanguageSchema = z.enum(["english", "vietnamese"], {
  errorMap: () => ({ message: "Please select a valid language" }),
});

// Analysis type enum
export const CoverLetterAnalysisTypeSchema = z.enum(["role", "jd"], {
  errorMap: () => ({ message: "Please select an analysis type" }),
});

// File validation schema
export const FileSchema = z
  .instanceof(File, { message: "Please upload a valid file" })
  .refine((file) => file.size <= 10 * 1024 * 1024, {
    message: "File size must be less than 10MB",
  })
  .refine((file) => file.type === "application/pdf", {
    message: "Only PDF files are allowed",
  });

// Base form schema
export const CoverLetterBaseFormSchema = z.object({
  language: CoverLetterLanguageSchema,
  cvFile: FileSchema,
});

// Role-based form schema
export const CoverLetterRoleFormSchema = CoverLetterBaseFormSchema.extend({
  analysisType: z.literal("role"),
  roleDescription: z
    .string()
    .min(10, "Role description must be at least 10 characters")
    .max(1000, "Role description must be less than 1000 characters"),
});

// JD-based form schema
export const CoverLetterJdFormSchema = CoverLetterBaseFormSchema.extend({
  analysisType: z.literal("jd"),
  jdFile: FileSchema,
});

// Combined form schema
export const CoverLetterFormSchema = z.discriminatedUnion("analysisType", [
  CoverLetterRoleFormSchema,
  CoverLetterJdFormSchema,
]);

// Type exports
export type CoverLetterLanguage = z.infer<typeof CoverLetterLanguageSchema>;
export type CoverLetterAnalysisType = z.infer<typeof CoverLetterAnalysisTypeSchema>;
export type CoverLetterBaseForm = z.infer<typeof CoverLetterBaseFormSchema>;
export type CoverLetterRoleForm = z.infer<typeof CoverLetterRoleFormSchema>;
export type CoverLetterJdForm = z.infer<typeof CoverLetterJdFormSchema>;
export type CoverLetterForm = z.infer<typeof CoverLetterFormSchema>;

// Form data for React Hook Form
export interface CoverLetterFormData {
  analysisType: CoverLetterAnalysisType | "";
  roleDescription: string;
  language: CoverLetterLanguage;
  cvFile: File | null;
  jdFile: File | null;
}

// Validation helper functions
export const validateCoverLetterForm = (data: CoverLetterFormData) => {
  try {
    if (data.analysisType === "") {
      throw new Error("Please select an analysis type");
    }

    if (data.analysisType === "role") {
      return CoverLetterRoleFormSchema.parse({
        analysisType: data.analysisType,
        roleDescription: data.roleDescription,
        language: data.language,
        cvFile: data.cvFile,
      });
    } else if (data.analysisType === "jd") {
      return CoverLetterJdFormSchema.parse({
        analysisType: data.analysisType,
        language: data.language,
        cvFile: data.cvFile,
        jdFile: data.jdFile,
      });
    }

    throw new Error("Invalid analysis type");
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors: Record<string, string> = {};
      error.errors.forEach((err) => {
        const path = err.path.join(".");
        fieldErrors[path] = err.message;
      });
      return { success: false, errors: fieldErrors };
    }
    return { success: false, errors: { general: (error as Error).message } };
  }
};
