export interface Template {
  id: string;
  name: string;
  description: string;
  uses: number;
  category: string;
}

export interface Tip {
  title: string;
  description: string;
}

export const mockTemplates: Template[] = [
  {
    id: "1",
    name: "Tech Professional",
    description: "Perfect for software engineers and developers",
    uses: 1250,
    category: "Technology",
  },
  {
    id: "2",
    name: "Creative Industry",
    description: "Ideal for designers and creative professionals",
    uses: 890,
    category: "Design",
  },
  {
    id: "3",
    name: "Business & Finance",
    description: "Tailored for business and finance roles",
    uses: 756,
    category: "Business",
  },
  {
    id: "4",
    name: "Healthcare",
    description: "Designed for medical and healthcare professionals",
    uses: 643,
    category: "Healthcare",
  },
  {
    id: "5",
    name: "Education",
    description: "Perfect for teaching and academic positions",
    uses: 521,
    category: "Education",
  },
];

export const tips: Tip[] = [
  {
    title: "Tailor to Job Description",
    description: "Customize your letter to match the specific job requirements",
  },
  {
    title: "Keep It Concise",
    description: "Aim for one page maximum to maintain recruiter attention",
  },
  {
    title: "Highlight Achievements",
    description: "Focus on quantifiable results and specific accomplishments",
  },
];
