"use client";
import React from "react";
import { EyeOff, ChevronUp, ChevronDown } from "lucide-react";

interface SectionBubbleMenuProps {
  field: string;
  onToggleVisibility: (field: string) => void;
  onMoveUp: (field: string) => void;
  onMoveDown: (field: string) => void;
  canMoveUp: boolean;
  canMoveDown: boolean;
  canToggleVisibility: boolean;
  isVisible: boolean;
  isFieldHidden: boolean;
}

const SectionBubbleMenu: React.FC<SectionBubbleMenuProps> = ({
  field,
  onToggleVisibility,
  onMoveUp,
  onMoveDown,
  canMoveUp,
  canMoveDown,
  canToggleVisibility,
  isVisible,
  isFieldHidden,
}) => {
  if (!isVisible) {
    return null;
  }

  return (
    <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 flex items-center gap-1 bg-white border border-gray-200 rounded-lg shadow-lg p-1 z-50">
      {/* Move Up Button */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onMoveUp(field);
        }}
        disabled={!canMoveUp}
        className={`p-2 rounded-md transition-all duration-200 ${
          canMoveUp
            ? "hover:bg-blue-50 text-blue-600 hover:text-blue-700"
            : "text-gray-300 cursor-not-allowed"
        }`}
        title="Move section up"
      >
        <ChevronUp className="w-4 h-4" />
      </button>

      {/* Move Down Button */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onMoveDown(field);
        }}
        disabled={!canMoveDown}
        className={`p-2 rounded-md transition-all duration-200 ${
          canMoveDown
            ? "hover:bg-blue-50 text-blue-600 hover:text-blue-700"
            : "text-gray-300 cursor-not-allowed"
        }`}
        title="Move section down"
      >
        <ChevronDown className="w-4 h-4" />
      </button>

      {/* Divider */}
      <div className="w-px h-6 bg-gray-200 mx-1"></div>

      {/* Hide Button - Only show EyeOff to hide sections */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onToggleVisibility(field);
        }}
        disabled={!canToggleVisibility || isFieldHidden}
        className={`p-2 rounded-md transition-all duration-200 ${
          canToggleVisibility && !isFieldHidden
            ? "hover:bg-orange-50 text-orange-600 hover:text-orange-700"
            : "text-gray-300 cursor-not-allowed"
        }`}
        title="Ẩn section"
      >
        <EyeOff className="w-4 h-4" />
      </button>
    </div>
  );
};

export default SectionBubbleMenu;
