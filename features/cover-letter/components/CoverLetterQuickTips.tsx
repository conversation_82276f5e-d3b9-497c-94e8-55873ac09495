"use client";

import { motion } from "framer-motion";
import { Lightbulb, FileText, Clock, TrendingUp } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tip } from "../constants/dashboardData";

interface CoverLetterQuickTipsProps {
  tips: Tip[];
}

export default function CoverLetterQuickTips({
  tips,
}: CoverLetterQuickTipsProps) {
  const getIcon = (index: number) => {
    const icons = [
      <FileText key="filetext" className="w-5 h-5" />,
      <Clock key="clock" className="w-5 h-5" />,
      <TrendingUp key="trending" className="w-5 h-5" />,
    ];
    return icons[index] || <FileText key="default" className="w-5 h-5" />;
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 0.8 }}
    >
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-yellow-600" />
            Quick Tips
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {tips.map((tip, index) => (
            <motion.div
              key={tip.title}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 1 + index * 0.1 }}
              className="flex gap-3 p-3 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50"
            >
              <div className="text-yellow-600 mt-0.5 flex-shrink-0">
                {getIcon(index)}
              </div>
              <div>
                <h4 className="font-semibold text-sm text-gray-800">
                  {tip.title}
                </h4>
                <p className="text-xs text-gray-600 mt-1">{tip.description}</p>
              </div>
            </motion.div>
          ))}
        </CardContent>
      </Card>
    </motion.div>
  );
}
