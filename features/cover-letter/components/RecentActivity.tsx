"use client";

import { motion } from "framer-motion";
import { Clock, Briefcase, <PERSON><PERSON>he<PERSON>, BookOpen } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export default function RecentActivity() {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 1.0 }}
    >
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-indigo-600" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-3 p-2 rounded-lg bg-emerald-50 border border-emerald-100">
            <Briefcase className="w-4 h-4 text-emerald-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-800">
                Role analysis completed
              </p>
              <p className="text-xs text-gray-600">
                Software Engineer at Google
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-2 rounded-lg bg-orange-50 border border-orange-100">
            <FileCheck className="w-4 h-4 text-orange-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-800">
                JD analysis created
              </p>
              <p className="text-xs text-gray-600">
                Frontend Developer at Netflix
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-2 rounded-lg bg-indigo-50 border border-indigo-100">
            <BookOpen className="w-4 h-4 text-indigo-600" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-800">Template used</p>
              <p className="text-xs text-gray-600">
                Tech Professional template
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
