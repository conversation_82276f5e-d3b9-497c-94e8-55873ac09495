"use client";

import { motion } from "framer-motion";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CoverLetterDashboardHeaderProps {
  onCreateClick: () => void;
}

export default function CoverLetterDashboardHeader({
  onCreateClick,
}: CoverLetterDashboardHeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 shadow-xl"
    >
      <div>
        <h1 className="text-4xl font-bold text-white">Cover Letter Studio</h1>
        <p className="text-indigo-100 mt-2">
          Create professional cover letters with AI assistance
        </p>
      </div>

      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
      >
        <Button
          size="lg"
          onClick={onCreateClick}
          className="bg-white text-indigo-600 hover:bg-gray-50 px-8 py-6 text-lg font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Plus className="w-5 h-5 mr-2" />
          Create Cover Letter AI
        </Button>
      </motion.div>
    </motion.div>
  );
}
