"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { Camera, ChevronRight } from "lucide-react";
import CoverLetterToolbar from "../../../components/CoverLetterToolbar";
import TiptapEditableField from "./TiptapEditableField";
import { TiptapProvider } from "../../../providers/TiptapProvider";
import { useTiptapToolbar } from "../hooks/useTiptapToolbar";
import { useCoverLetterHandlers } from "../../../components/CoverLetterHandlers";
import { CoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";
import { useCoverLetterData } from "../hooks/useCoverLetterData";
import { useAvatarUpload } from "../hooks/useAvatarUpload";
import { useSectionManager } from "../hooks/useSectionManager";
import { useCoverLetterSave } from "../hooks/useCoverLetterSave";
import SaveSuccessModal from "./SaveSuccessModal";
import AddSectionsModal from "./AddSectionsModal";
import { useCoverLetterFormData } from "../hooks/useCoverLetterFormData";
import { useCoverLetterState } from "../hooks/useCoverLetterState";
import { getFontSizeInPx } from "@/lib/fontSizeUtils";

interface CoverLetterTemp3Props {
  coverLetterData?: CoverLetter;
  coverLetterId?: string;
  templateId?: string;
}

const CoverLetterTemp3Content = ({
  coverLetterData: propCoverLetterData,
  coverLetterId: propCoverLetterId,
  templateId = "temp3",
}: CoverLetterTemp3Props) => {
  const router = useRouter();

  const { coverLetterData: hookCoverLetterData } =
    useCoverLetterData(propCoverLetterId);
  const coverLetterData = propCoverLetterData || hookCoverLetterData;

  const coverLetterState = useCoverLetterState(
    coverLetterData,
    propCoverLetterId
  );
  const { labels, isVietnamese } = coverLetterState;

  const { formData, setFormData } = useCoverLetterFormData(
    coverLetterData,
    coverLetterData?.language
  );
  const {
    handleSave,
    isLoading: isSaving,
    showSuccessModal,
    coverLetterFileUrl,
    closeSuccessModal,
    handleEdit,
  } = useCoverLetterSave(coverLetterState.coverLetterId, templateId);

  // Avatar upload functionality
  const { triggerFileSelect, isLoading: isUploadingAvatar } = useAvatarUpload(
    coverLetterState.coverLetterId,
    (imageUrl) => {
      setFormData((prev) => ({ ...prev, profileImage: imageUrl }));
    }
  );

  // UI State management
  const {
    editingField,
    setEditingField,
    contactColor,
    setContactColor,
    currentTheme,
    setCurrentTheme,
    showToolbar,
    setShowToolbar,
    fontSize,
    setFontSize,
    fontFamily,
    setFontFamily,
    lineHeight,
    setLineHeight,
    textAlign,
    setTextAlign,
    isBold,
    setIsBold,
    isItalic,
    setIsItalic,
    isUnderline,
    setIsUnderline,
    showAddSectionsModal,
    setShowAddSectionsModal,
  } = coverLetterState;

  // State and actions for handlers
  const state = {
    formData,
    editingField,
    headerColor: contactColor,
    contactColor,
    showToolbar,
    fontSize,
    fontFamily,
    lineHeight,
    textAlign,
    isBold,
    isItalic,
    isUnderline,
  };

  const actions = {
    setFormData,
    setEditingField,
    setHeaderColor: setContactColor,
    setContactColor,
    setShowToolbar,
    setFontSize,
    setFontFamily,
    setLineHeight,
    setTextAlign,
    setIsBold,
    setIsItalic,
    setIsUnderline,
  };

  const handlers = useCoverLetterHandlers(state, actions);

  // Tiptap toolbar handlers
  const tiptapToolbar = useTiptapToolbar({
    onClearDetailedFormatting: coverLetterState.clearDetailedFormatting,
    onUpdateDetailedFormatting: coverLetterState.updateDetailedFormatting,
    onClearSectionFormatting: coverLetterState.clearSectionFormatting,
    onResetToolbarStates: coverLetterState.resetToolbarStates,
  });

  // Custom theme change handler to track current theme
  const handleThemeChange = (theme: string) => {
    setCurrentTheme(theme);
    handlers.handleThemeChange(theme);
  };

  // Save handler
  const handleSaveClick = () => {
    // Always use the current section formatting from state
    // This ensures all previously saved formatting is preserved
    let updatedSectionFormatting = { ...coverLetterState.sectionFormatting };

    // If we're currently editing a field, update its text alignment formatting
    if (editingField) {
      const currentField = editingField;
      const currentFormatting =
        coverLetterState.getCurrentSectionFormatting(currentField);

      // Only save text alignment as bold/italic/underline are now applied to selected text only
      updatedSectionFormatting[currentField] = {
        ...currentFormatting,
        textAlign: textAlign,
      };

      // Update the section formatting in state as well
      coverLetterState.setSectionFormatting(updatedSectionFormatting);
    }

    // Prepare save state with current formatting
    const saveState = {
      fontSize,
      fontFamily,
      lineHeight,
      currentTheme,
      sectionFormatting: updatedSectionFormatting,
      hiddenSections: sectionManager.hiddenSections,
      detailedFormatting: coverLetterState.detailedFormatting,
    };

    handleSave(formData, saveState);
  };

  // Change template handler
  const handleChangeTemplate = () => {
    if (coverLetterState.coverLetterId) {
      router.push(
        `/cover-letter/templates?id=${coverLetterState.coverLetterId}`
      );
    }
  };

  // Get current Tiptap states for toolbar
  const tiptapStates = tiptapToolbar.getCurrentStates();

  // Section manager for BubbleMenu actions
  const sectionManager = useSectionManager({
    formData,
    setFormData,
    coverLetterData,
  });

  return (
    <div
      className="min-h-screen bg-gray-50 py-4"
      onClick={() => {
        // Click outside to close toolbar
        if (showToolbar) {
          setShowToolbar(null);
        }
      }}
    >
      <div className="max-w-4xl mx-auto px-4">
        {/* Toolbar */}
        <CoverLetterToolbar
          fontSize={fontSize}
          fontFamily={fontFamily}
          lineHeight={lineHeight}
          textAlign={textAlign}
          isBold={tiptapStates.isBold}
          isItalic={tiptapStates.isItalic}
          isUnderline={tiptapStates.isUnderline}
          currentTheme={currentTheme}
          onThemeChange={handleThemeChange}
          onFontSizeChange={handlers.handleFontSizeChange}
          onFontFamilyChange={handlers.handleFontFamilyChange}
          onLineHeightChange={handlers.handleLineHeightChange}
          onTextAlignChange={coverLetterState.handleTextAlignChange}
          onBoldToggle={tiptapToolbar.handleBoldToggle}
          onItalicToggle={tiptapToolbar.handleItalicToggle}
          onUnderlineToggle={tiptapToolbar.handleUnderlineToggle}
          onUndo={tiptapToolbar.handleUndo}
          onRedo={tiptapToolbar.handleRedo}
          onAddSection={() => setShowAddSectionsModal(true)}
          onClearFormat={tiptapToolbar.handleClearFormat}
          onSave={handleSaveClick}
          isSaving={isSaving}
          onChangeTemplate={handleChangeTemplate}
          isHidden={showSuccessModal || showAddSectionsModal}
          canUndo={tiptapStates.canUndo}
          canRedo={tiptapStates.canRedo}
        />

        <div
          id="temp3"
          className="bg-white rounded-lg shadow-lg p-4 relative mt-2"
          style={{
            fontFamily,
            fontSize: getFontSizeInPx(fontSize),
            lineHeight,
          }}
        >
          {/* Decorative Panel */}
          <div
            className="absolute top-0 left-64 w-10 h-32"
            style={{ backgroundColor: contactColor }}
          ></div>

          {/* Header Section */}
          <div className="mb-4 p-3 rounded-lg relative group  hover:ring-2 hover:ring-orange-300 transition-all">
            <div className="flex items-start gap-8">
              {/* Left: Avatar */}
              <div className="flex-shrink-0">
                <div
                  className="w-32 h-32 bg-gray-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors relative group"
                  onClick={triggerFileSelect}
                  title="Click to upload profile image"
                >
                  {formData.profileImage ? (
                    <img
                      src={formData.profileImage}
                      alt="Profile"
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Camera className="w-12 h-12 text-white" />
                  )}
                  {isUploadingAvatar && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                      <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <Camera className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>

              {/* Center: Name and Position */}
              <div className="flex-1 ml-32">
                <div className="flex items-center mb-2">
                  <div className="w-6 h-6 bg-blue-600 rounded-full mr-3 flex items-center justify-center">
                    <ChevronRight className="w-4 h-4 text-white" />
                  </div>
                  <TiptapEditableField
                    field="targetPosition"
                    value={formData.targetPosition}
                    className="text-base text-blue-600 pl-7 font-medium rounded-full"
                    style={{ backgroundColor: contactColor }}
                    placeholder="Target Position"
                    editingField={editingField}
                    onEdit={setEditingField}
                    onChange={handlers.handleInputChange}
                    borderColor="border-blue-500"
                    sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                      "targetPosition"
                    )}
                    detailedFormatting={coverLetterState.getDetailedFormatting(
                      "targetPosition"
                    )}
                    onDetailedFormattingChange={
                      coverLetterState.updateDetailedFormatting
                    }
                  />
                </div>
                <TiptapEditableField
                  field="fullName"
                  value={formData.fullName}
                  className="text-3xl font-bold text-blue-600 mb-3  "
                  placeholder="Full Name"
                  editingField={editingField}
                  onEdit={setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "fullName"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "fullName"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />

                {/* Contact Information - Moved to center section */}
                <div className="space-y-2">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                      </svg>
                    </div>
                    <div className="px-2 py-1 rounded">
                      <TiptapEditableField
                        field="emailAddress"
                        value={formData.emailAddress}
                        className="text-gray-700 text-sm"
                        placeholder="Địa chỉ email"
                        editingField={editingField}
                        onEdit={setEditingField}
                        onChange={handlers.handleInputChange}
                        borderColor="border-blue-500"
                        sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                          "emailAddress"
                        )}
                        detailedFormatting={coverLetterState.getDetailedFormatting(
                          "emailAddress"
                        )}
                        onDetailedFormattingChange={
                          coverLetterState.updateDetailedFormatting
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                      </svg>
                    </div>
                    <div className="px-2 py-1 rounded">
                      <TiptapEditableField
                        field="phoneNumber"
                        value={formData.phoneNumber}
                        className="text-gray-700 text-sm"
                        placeholder="Số điện thoại"
                        editingField={editingField}
                        onEdit={setEditingField}
                        onChange={handlers.handleInputChange}
                        borderColor="border-blue-500"
                        sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                          "phoneNumber"
                        )}
                        detailedFormatting={coverLetterState.getDetailedFormatting(
                          "phoneNumber"
                        )}
                        onDetailedFormattingChange={
                          coverLetterState.updateDetailedFormatting
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                    <TiptapEditableField
                      field="address"
                      value={formData.address}
                      className="text-gray-700 text-sm ml-3"
                      placeholder="Address"
                      editingField={editingField}
                      onEdit={setEditingField}
                      onChange={handlers.handleInputChange}
                      borderColor="border-blue-500"
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "address"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "address"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Recipient Information */}
          <div className="mb-2 space-y-1">
            <TiptapEditableField
              field="hiringManagerName"
              value={formData.hiringManagerName}
              className="text-blue-600"
              placeholder={`${labels.dear}:Ông/Bà [Tên]`}
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "hiringManagerName"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "hiringManagerName"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="hiringManagerTitle"
              value={formData.hiringManagerTitle}
              className="text-gray-600"
              placeholder={`[${labels.title} / Phòng ban]`}
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "hiringManagerTitle"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "hiringManagerTitle"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="companyName"
              value={formData.companyName || `[${labels.companyName}]`}
              className="text-gray-600"
              placeholder={`[${labels.companyName}]`}
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "companyName"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "companyName"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="companyAddress"
              value={formData.companyAddress}
              className="text-gray-600"
              placeholder={`[${labels.address}]`}
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "companyAddress"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "companyAddress"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="applicationDate"
              value={formData.applicationDate}
              className="text-gray-600 mt-2"
              placeholder={
                isVietnamese ? "Ngày 05 tháng 07 năm 2025" : "July 05, 2025"
              }
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "applicationDate"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "applicationDate"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="jobTitle"
              value={formData.jobTitle}
              className="text-gray-600"
              placeholder={
                isVietnamese
                  ? "Thư ứng tuyển [Vị trí cần tuyển]"
                  : "Application for [Position]"
              }
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "jobTitle"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "jobTitle"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
          </div>

          {/* Letter Content */}
          <div className="space-y-2">
            {/* Greeting */}
            {!sectionManager.isFieldHidden("greeting") && (
              <div
                data-section="greeting"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="greeting"
                  value={
                    formData.greeting ||
                    (isVietnamese
                      ? "Kính gửi Quý công ty,"
                      : "Dear Mr./Ms. [Name],")
                  }
                  className="text-justify"
                  placeholder={
                    isVietnamese
                      ? "Kính gửi Quý công ty,"
                      : "Dear Mr./Ms. [Name],"
                  }
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "greeting"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("greeting")}
                  canMoveDown={sectionManager.canMoveDown("greeting")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "greeting"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden("greeting")}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "greeting"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "greeting"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Opening Paragraph */}
            {!sectionManager.isFieldHidden("openingParagraph") && (
              <div
                data-section="openingParagraph"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="openingParagraph"
                  value={formData.openingParagraph}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Opening paragraph"
                  editingField={editingField}
                  onEdit={setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "openingParagraph"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("openingParagraph")}
                  canMoveDown={sectionManager.canMoveDown("openingParagraph")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "openingParagraph"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "openingParagraph"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "openingParagraph"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "openingParagraph"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Experience Section */}
            {!sectionManager.isFieldHidden("experienceSection") && (
              <div
                data-section="experienceSection"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="experienceSection"
                  value={formData.experienceSection}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Experience section"
                  editingField={editingField}
                  onEdit={setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "experienceSection"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("experienceSection")}
                  canMoveDown={sectionManager.canMoveDown("experienceSection")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "experienceSection"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "experienceSection"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "experienceSection"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "experienceSection"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Skills Section */}
            {!sectionManager.isFieldHidden("skillsSection") && (
              <div
                data-section="skillsSection"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="skillsSection"
                  value={formData.skillsSection}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Skills section"
                  editingField={editingField}
                  onEdit={setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "skillsSection"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("skillsSection")}
                  canMoveDown={sectionManager.canMoveDown("skillsSection")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "skillsSection"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden("skillsSection")}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "skillsSection"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "skillsSection"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Closing Paragraph */}
            {!sectionManager.isFieldHidden("closingParagraph") && (
              <div
                data-section="closingParagraph"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="closingParagraph"
                  value={formData.closingParagraph}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Closing paragraph"
                  editingField={editingField}
                  onEdit={setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "closingParagraph"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("closingParagraph")}
                  canMoveDown={sectionManager.canMoveDown("closingParagraph")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "closingParagraph"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "closingParagraph"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "closingParagraph"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "closingParagraph"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}
          </div>

          {/* Signature */}
          <div
            className="mt-4 text-left"
            data-section="signature"
            style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
          >
            <TiptapEditableField
              field="signatureClosing"
              value={
                formData.signatureClosing ||
                (isVietnamese ? "Trân trọng," : "Sincerely,")
              }
              className="block text-gray-600 mb-1"
              placeholder={isVietnamese ? "Trân trọng," : "Sincerely,"}
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "signatureClosing"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "signatureClosing"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="signatureName"
              value={formData.signatureName}
              className="block font-semibold text-gray-800"
              placeholder="Your Full Name"
              editingField={editingField}
              onEdit={setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "signatureName"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "signatureName"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
          </div>
        </div>
      </div>

      {/* Save Success Modal */}
      <SaveSuccessModal
        isOpen={showSuccessModal}
        onClose={closeSuccessModal}
        coverLetterFileUrl={coverLetterFileUrl}
        onEdit={handleEdit}
        coverLetterId={coverLetterState.coverLetterId}
      />

      {/* Add Sections Modal */}
      <AddSectionsModal
        isOpen={showAddSectionsModal}
        onClose={() => setShowAddSectionsModal(false)}
        hiddenSections={sectionManager.hiddenSections}
        onToggleSection={sectionManager.toggleVisibility}
      />
    </div>
  );
};

// Wrapper component with TiptapProvider
const CoverLetterTemp3 = (props: CoverLetterTemp3Props) => {
  return (
    <TiptapProvider>
      <CoverLetterTemp3Content {...props} />
    </TiptapProvider>
  );
};

export default CoverLetterTemp3;
