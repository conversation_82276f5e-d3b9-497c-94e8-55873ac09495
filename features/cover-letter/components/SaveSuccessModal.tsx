import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Download,
  Edit,
  CheckCircle,
  Copy,
  Home,
  Loader2,
  Star,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useEvaluateCoverLetterMutation } from "@/services/api/cover-letter";

interface SaveSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  coverLetterFileUrl: string;
  onEdit: () => void;
  coverLetterId?: string;
}

const SaveSuccessModal: React.FC<SaveSuccessModalProps> = ({
  isOpen,
  onClose,
  coverLetterFileUrl,
  onEdit,
  coverLetterId,
}) => {
  const router = useRouter();
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadStatus, setDownloadStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [showCopyTooltip, setShowCopyTooltip] = useState(false);
  const copyTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [evaluateCoverLetter, { isLoading: isEvaluating }] =
    useEvaluateCoverLetterMutation();

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (copyTimeoutRef.current) {
        clearTimeout(copyTimeoutRef.current);
      }
    };
  }, []);

  const handleDownload = async () => {
    if (!coverLetterFileUrl) return;

    setIsDownloading(true);
    setDownloadStatus("idle");

    try {
      // Generate filename with timestamp
      const now = new Date();
      const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, "");
      const filename = `cover-letter-${timestamp}.pdf`;

      // Fetch the file to ensure it exists and get blob
      const response = await fetch(coverLetterFileUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch PDF file");
      }

      const blob = await response.blob();

      // Create download link
      const link = document.createElement("a");
      const url = window.URL.createObjectURL(blob);
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setDownloadStatus("success");
    } catch (error) {
      console.error("Download failed:", error);
      setDownloadStatus("error");

      // Fallback: try direct link download
      try {
        const link = document.createElement("a");
        link.href = coverLetterFileUrl;
        link.download = "cover-letter.pdf";
        link.target = "_blank";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } catch (fallbackError) {
        console.error("Fallback download also failed:", fallbackError);
      }
    } finally {
      setIsDownloading(false);
    }
  };

  const handleCopyUrl = async () => {
    if (coverLetterFileUrl) {
      try {
        await navigator.clipboard.writeText(coverLetterFileUrl);
        setShowCopyTooltip(true);

        // Clear existing timeout if any
        if (copyTimeoutRef.current) {
          clearTimeout(copyTimeoutRef.current);
        }

        // Hide tooltip after 2 seconds
        copyTimeoutRef.current = setTimeout(() => {
          setShowCopyTooltip(false);
        }, 2000);
      } catch (error) {
        console.error("Failed to copy URL:", error);
      }
    }
  };

  const handleBackToHome = () => {
    router.push("/cover-letter");
    onClose();
  };

  const handleEvaluate = async () => {
    if (!coverLetterId) return;

    try {
      await evaluateCoverLetter(coverLetterId).unwrap();
      // Navigate to evaluation page
      router.push(`/cover-letter/evaluation/${coverLetterId}`);
      onClose();
    } catch (error) {
      console.error("Failed to evaluate cover letter:", error);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="relative bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 z-10"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>

            {/* Success Icon */}
            <div className="text-center mb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", duration: 0.6 }}
                className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4"
              >
                <CheckCircle className="w-8 h-8 text-green-600" />
              </motion.div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Cover Letter Saved Successfully!
              </h2>
              <p className="text-gray-600">
                Your cover letter has been saved and PDF file created
                successfully.
              </p>
            </div>

            {/* PDF URL Input */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                PDF File URL:
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={coverLetterFileUrl}
                  readOnly
                  className="flex-1 px-3 py-2.5 border border-gray-300 rounded-lg bg-gray-50 text-sm text-gray-600 focus:outline-none h-10"
                />
                <div className="relative">
                  <Button
                    variant="outline"
                    onClick={handleCopyUrl}
                    className="px-3 py-2.5 h-10 w-10 flex items-center justify-center"
                  >
                    <Copy className="w-4 h-4" />
                  </Button>

                  {/* Copy Tooltip */}
                  <AnimatePresence>
                    {showCopyTooltip && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.8 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                        className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap z-50"
                      >
                        Copied!
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-4">
              {/* Primary Action - Edit Button (Full Width) */}
              <Button
                onClick={onEdit}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-base font-medium"
              >
                <Edit className="w-5 h-5 mr-2" />
                Continue Editing
              </Button>

              {/* Secondary Actions - Icon Buttons in a Row */}
              <div className="flex justify-center gap-4">
                {/* Evaluate Button */}
                <div className="relative group">
                  <Button
                    onClick={handleEvaluate}
                    disabled={isEvaluating || !coverLetterId}
                    className="w-12 h-12 rounded-full bg-purple-100 hover:bg-purple-200 text-purple-600 disabled:opacity-50 disabled:cursor-not-allowed border-0 shadow-md hover:shadow-lg transition-all duration-200"
                  >
                    {isEvaluating ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <Star className="w-5 h-5" />
                    )}
                  </Button>
                  {/* Tooltip */}
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
                    {isEvaluating ? "Evaluating..." : "Evaluate"}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                  </div>
                </div>

                {/* Download Button */}
                <div className="relative group">
                  <Button
                    onClick={handleDownload}
                    disabled={isDownloading}
                    className={`w-12 h-12 rounded-full shadow-md hover:shadow-lg transition-all duration-200 border-0 ${
                      downloadStatus === "success"
                        ? "bg-green-100 hover:bg-green-200 text-green-600"
                        : downloadStatus === "error"
                        ? "bg-red-100 hover:bg-red-200 text-red-600"
                        : "bg-gray-100 hover:bg-gray-200 text-gray-600"
                    }`}
                  >
                    {isDownloading ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : downloadStatus === "success" ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <Download className="w-5 h-5" />
                    )}
                  </Button>
                  {/* Tooltip */}
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
                    {isDownloading
                      ? "Downloading..."
                      : downloadStatus === "success"
                      ? "Downloaded"
                      : downloadStatus === "error"
                      ? "Retry Download"
                      : "Download PDF"}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                  </div>
                </div>

                {/* Home Button */}
                <div className="relative group">
                  <Button
                    onClick={handleBackToHome}
                    className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 border-0 shadow-md hover:shadow-lg transition-all duration-200"
                  >
                    <Home className="w-5 h-5" />
                  </Button>
                  {/* Tooltip */}
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
                    Back to Home
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default SaveSuccessModal;
