"use client";

import React, { useState } from "react";
import { X, Edit } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { useDispatch } from "react-redux";
import {
  setSelectedTemplate,
  setCurrentCoverLetterId,
} from "@/redux/slices/coverLetterSlice";
import CoverLetterTemp1 from "./CoverLetterTemp1";
import CoverLetterTemp2 from "./CoverLetterTemp2";
import CoverLetterTemp3 from "./CoverLetterTemp3";
import { templates } from "@/services/data/cover-letter";

// Custom CSS to hide toolbar and apply dynamic styles
const getHideToolbarStyles = (fontFamily: string, selectedColor: string) => {
  // Theme colors matching CoverLetterHandlers.ts
  const themes: Record<
    string,
    { header: string; contact: string; panel: string; text: string }
  > = {
    blue: {
      header: "#E8F4FD",
      contact: "#E8F4FD",
      panel: "#74b9ff",
      text: "#74b9ff",
    },
    green: {
      header: "#F0F8E8",
      contact: "#F0F8E8",
      panel: "#00b894",
      text: "#00b894",
    },
    yellow: {
      header: "#FFF8E8",
      contact: "#FFF8E8",
      panel: "#fdcb6e",
      text: "#fdcb6e",
    },
  };

  const currentTheme = themes[selectedColor] || themes.blue;

  return `
    .template-preview {
      --theme-header-color: ${currentTheme.header};
      --theme-contact-color: ${currentTheme.contact};
      --theme-panel-color: ${currentTheme.panel};
      --theme-text-color: ${currentTheme.text};
    }

    .template-preview .sticky {
      display: none !important;
    }
    .template-preview .bg-blue-50 {
      display: none !important;
    }
    .template-preview .min-h-screen {
      min-height: auto !important;
      padding: 1rem !important;
    }
    .template-preview .bg-gray-50 {
      background: white !important;
    }
    .template-preview .max-w-4xl {
      max-width: 100% !important;
      padding: 0 !important;
    }
    .template-preview * {
      font-family: ${fontFamily}, sans-serif !important;
    }

    /* Force override any inline background-color styles */
    .template-preview [style*="background-color"],
    .template-preview [style*="backgroundColor"] {
      background-color: var(--theme-header-color) !important;
    }

    /* But keep specific elements that shouldn't change */
    .template-preview .bg-gray-600,
    .template-preview .bg-white,
    .template-preview .bg-gray-50,
    .template-preview [style*="background: white"],
    .template-preview [style*="background-color: white"],
    .template-preview [style*="backgroundColor: white"],
    .template-preview [style*="background-color: #ffffff"],
    .template-preview [style*="backgroundColor: #ffffff"],
    .template-preview [style*="background-color: rgb(255, 255, 255)"],
    .template-preview [style*="backgroundColor: rgb(255, 255, 255)"] {
      background-color: revert !important;
    }

    /* Target Tailwind background classes */
    .template-preview .bg-\\[\\#e6f1fd\\],
    .template-preview .bg-\\[\\#E8F4FD\\],
    .template-preview .bg-\\[\\#F0F8E8\\],
    .template-preview .bg-\\[\\#FFF8E8\\],
    .template-preview .bg-\\[\\#F4E8F8\\] {
      background-color: var(--theme-header-color) !important;
    }

    /* Light background colors - chỉ thay đổi background, không thay đổi text */
    .template-preview .bg-orange-100,
    .template-preview .bg-blue-100,
    .template-preview .bg-green-100 {
      background-color: var(--theme-header-color) !important;
    }

    /* KHÔNG thay đổi text colors - giữ nguyên màu text gốc */
    /* .template-preview .text-blue-600,
    .template-preview .text-orange-600,
    .template-preview .text-green-600 {
      color: var(--theme-text-color) !important;
    }

    .template-preview .text-blue-500,
    .template-preview .text-orange-500,
    .template-preview .text-green-500 {
      color: var(--theme-text-color) !important;
    } */

    /* KHÔNG thay đổi background colors của các elements nhỏ */
    /* .template-preview .bg-blue-600,
    .template-preview .bg-orange-600,
    .template-preview .bg-green-600 {
      background-color: var(--theme-panel-color) !important;
    } */

    /* KHÔNG thay đổi border colors */
    /* .template-preview .border-orange-400,
    .template-preview .border-orange-300,
    .template-preview .border-blue-400,
    .template-preview .border-blue-300,
    .template-preview .border-green-400,
    .template-preview .border-green-300 {
      border-color: var(--theme-panel-color) !important;
    } */
  `;
};

interface TemplatePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  templateId: string;
  templateName: string;
}

const TemplatePreviewModal: React.FC<TemplatePreviewModalProps> = ({
  isOpen,
  onClose,
  templateId,
  templateName,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch();

  // State for font and color controls
  const [fontFamily, setFontFamily] = useState("Calibri");
  const [selectedColor, setSelectedColor] = useState("blue");

  // Get cover letter ID from URL query parameter
  const coverLetterId = searchParams.get("id");

  if (!isOpen) return null;

  // Get template data
  const template = templates.find((t) => t.id.toString() === templateId);
  const displayName = template ? template.name : templateName;

  const renderTemplate = () => {
    const styles = getHideToolbarStyles(fontFamily, selectedColor);

    switch (templateId) {
      case "1":
        return (
          <div className="template-preview">
            <style dangerouslySetInnerHTML={{ __html: styles }} />
            <CoverLetterTemp1 />
          </div>
        );
      case "2":
        return (
          <div className="template-preview">
            <style dangerouslySetInnerHTML={{ __html: styles }} />
            <CoverLetterTemp2 />
          </div>
        );
      case "3":
        return (
          <div className="template-preview">
            <style dangerouslySetInnerHTML={{ __html: styles }} />
            <CoverLetterTemp3 />
          </div>
        );
      default:
        return (
          <div className="template-preview">
            <style dangerouslySetInnerHTML={{ __html: styles }} />
            <CoverLetterTemp1 />
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-7xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex h-full">
          {/* Left Side - Template Preview */}
          <div
            className="flex-1 overflow-y-auto bg-gray-50"
            style={{ maxHeight: "90vh" }}
          >
            <div className="p-4">
              {/* Template with scroll capability */}
              <div className="transform scale-75 origin-top-left w-[133%] overflow-visible">
                {renderTemplate()}
              </div>
            </div>
          </div>

          {/* Right Side - Controls */}
          <div className="w-80 bg-white border-l border-gray-200 flex flex-col">
            {/* Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-gray-900">
                  Sample Cover Letter {displayName}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 p-6 space-y-6">
              {/* Font */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Font
                </label>
                <select
                  value={fontFamily}
                  onChange={(e) => setFontFamily(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Calibri">Calibri</option>
                  <option value="Arial">Arial</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Cambria">Cambria</option>
                </select>
              </div>

              {/* Color Theme - matching CoverLetterToolbar */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Color scheme
                </label>
                <div className="flex gap-2">
                  <button
                    onClick={() => setSelectedColor("blue")}
                    className={`w-8 h-8 rounded-lg border-2 hover:scale-105 transition-all duration-200 shadow-sm ${
                      selectedColor === "blue"
                        ? "border-blue-400 ring-2 ring-blue-200"
                        : "border-gray-200 hover:border-blue-400"
                    }`}
                    style={{ backgroundColor: "#E8F4FD" }}
                  />
                  <button
                    onClick={() => setSelectedColor("green")}
                    className={`w-8 h-8 rounded-lg border-2 hover:scale-105 transition-all duration-200 shadow-sm ${
                      selectedColor === "green"
                        ? "border-green-400 ring-2 ring-green-200"
                        : "border-gray-200 hover:border-green-400"
                    }`}
                    style={{ backgroundColor: "#F0F8E8" }}
                  />
                  <button
                    onClick={() => setSelectedColor("yellow")}
                    className={`w-8 h-8 rounded-lg border-2 hover:scale-105 transition-all duration-200 shadow-sm ${
                      selectedColor === "yellow"
                        ? "border-yellow-400 ring-2 ring-yellow-200"
                        : "border-gray-200 hover:border-yellow-400"
                    }`}
                    style={{ backgroundColor: "#FFF8E8" }}
                  />
                </div>
              </div>
            </div>

            {/* Footer Actions */}
            <div className="p-6 border-t border-gray-200 space-y-3">
              <Button
                className="w-full bg-green-600 hover:bg-green-700 text-white py-3"
                onClick={() => {
                  if (coverLetterId) {
                    // Store selected template in Redux and navigate to specific template component
                    dispatch(setSelectedTemplate(`temp${templateId}`));
                    router.push(
                      `/cover-letter/${coverLetterId}?template=temp${templateId}`
                    );
                  } else {
                    // If no cover letter ID, prompt user to generate one first
                    alert("Vui lòng tạo cover letter trước khi chọn template");
                  }
                  onClose();
                }}
              >
                <Edit className="w-4 h-4 mr-2" />
                Use this template
              </Button>
              <Button
                variant="outline"
                className="w-full py-3"
                onClick={onClose}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplatePreviewModal;
