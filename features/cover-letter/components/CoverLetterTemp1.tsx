import React from "react";
import { Camera, Mail, Phone, MapPin, Calendar, Link } from "lucide-react";
import CoverLetterToolbar from "../../../components/CoverLetterToolbar";
import TiptapEditableField from "./TiptapEditableField";
import { TiptapProvider } from "../../../providers/TiptapProvider";
import { useTiptapToolbar } from "../hooks/useTiptapToolbar";
import { useCoverLetterHandlers } from "../../../components/CoverLetterHandlers";
import { CoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";
import { useCoverLetterData } from "../hooks/useCoverLetterData";
import { useAvatarUpload } from "../hooks/useAvatarUpload";
import { useSectionManager } from "../hooks/useSectionManager";
import { useCoverLetterSave } from "../hooks/useCoverLetterSave";
import SaveSuccessModal from "./SaveSuccessModal";
import AddSectionsModal from "./AddSectionsModal";
import { useCoverLetterFormData } from "../hooks/useCoverLetterFormData";
import { useCoverLetterState } from "../hooks/useCoverLetterState";
import { getFontSizeInPx } from "@/lib/fontSizeUtils";

interface CoverLetterTemp1Props {
  coverLetterData?: CoverLetter;
  coverLetterId?: string;
  templateId?: string;
}

const CoverLetterTemp1Content = ({
  coverLetterData: propCoverLetterData,
  coverLetterId: propCoverLetterId,
  templateId = "temp1",
}: CoverLetterTemp1Props) => {
  const { coverLetterData: hookCoverLetterData } =
    useCoverLetterData(propCoverLetterId);
  const coverLetterData = propCoverLetterData || hookCoverLetterData;

  const coverLetterState = useCoverLetterState(
    coverLetterData,
    propCoverLetterId
  );

  const { formData, setFormData } = useCoverLetterFormData(
    coverLetterData,
    coverLetterData?.language
  );

  const { triggerFileSelect, isLoading: isUploadingAvatar } = useAvatarUpload(
    coverLetterState.coverLetterId,
    (imageUrl) => {
      setFormData((prev) => ({ ...prev, profileImage: imageUrl }));
    }
  );
  const {
    handleSave,
    isLoading: isSaving,
    showSuccessModal,
    coverLetterFileUrl,
    closeSuccessModal,
    handleEdit,
  } = useCoverLetterSave(coverLetterState.coverLetterId, templateId);

  const state = {
    formData,
    editingField: coverLetterState.editingField,
    headerColor: coverLetterState.headerColor,
    contactColor: coverLetterState.contactColor,
    showToolbar: coverLetterState.showToolbar,
    fontSize: coverLetterState.fontSize,
    fontFamily: coverLetterState.fontFamily,
    lineHeight: coverLetterState.lineHeight,
    textAlign: coverLetterState.textAlign,
    isBold: coverLetterState.isBold,
    isItalic: coverLetterState.isItalic,
    isUnderline: coverLetterState.isUnderline,
  };

  const actions = {
    setFormData,
    setEditingField: coverLetterState.setEditingField,
    setHeaderColor: coverLetterState.setHeaderColor,
    setContactColor: coverLetterState.setContactColor,
    setShowToolbar: coverLetterState.setShowToolbar,
    setFontSize: coverLetterState.setFontSize,
    setFontFamily: coverLetterState.setFontFamily,
    setLineHeight: coverLetterState.setLineHeight,
    setTextAlign: coverLetterState.setTextAlign,
    setIsBold: coverLetterState.setIsBold,
    setIsItalic: coverLetterState.setIsItalic,
    setIsUnderline: coverLetterState.setIsUnderline,
  };

  const handlers = useCoverLetterHandlers(state, actions);

  const tiptapToolbar = useTiptapToolbar({
    onClearDetailedFormatting: coverLetterState.clearDetailedFormatting,
    onUpdateDetailedFormatting: coverLetterState.updateDetailedFormatting,
    onClearSectionFormatting: coverLetterState.clearSectionFormatting,
    onResetToolbarStates: coverLetterState.resetToolbarStates,
  });

  const handleThemeChange = (theme: string) => {
    coverLetterState.setCurrentTheme(theme);
    handlers.handleThemeChange(theme);
  };

  const handleSaveClick = () => {
    let updatedSectionFormatting = { ...coverLetterState.sectionFormatting };

    if (coverLetterState.editingField) {
      const currentField = coverLetterState.editingField;
      const currentFormatting =
        coverLetterState.getCurrentSectionFormatting(currentField);

      updatedSectionFormatting[currentField] = {
        ...currentFormatting,
        textAlign: coverLetterState.textAlign,
        isBold: coverLetterState.isBold,
        isItalic: coverLetterState.isItalic,
        isUnderline: coverLetterState.isUnderline,
      };

      coverLetterState.setSectionFormatting(updatedSectionFormatting);
    }

    const saveState = {
      fontSize: coverLetterState.fontSize,
      fontFamily: coverLetterState.fontFamily,
      lineHeight: coverLetterState.lineHeight,
      currentTheme: coverLetterState.currentTheme,
      sectionFormatting: updatedSectionFormatting,
      hiddenSections: sectionManager.hiddenSections,
      detailedFormatting: coverLetterState.detailedFormatting,
    };

    handleSave(formData, saveState);
  };

  const tiptapStates = tiptapToolbar.getCurrentStates();

  const sectionManager = useSectionManager({
    formData,
    setFormData,
    coverLetterData,
  });

  const handleBoldToggle = () => {
    if (!tiptapToolbar.activeEditor) {
      return;
    }

    const { from, to } = tiptapToolbar.activeEditor.state.selection;
    const hasSelection = from !== to;

    if (!hasSelection) {
      return;
    }

    tiptapToolbar.handleBoldToggle();
    coverLetterState.handleBoldToggle();
  };

  const handleItalicToggle = () => {
    if (!tiptapToolbar.activeEditor) {
      return;
    }

    const { from, to } = tiptapToolbar.activeEditor.state.selection;
    const hasSelection = from !== to;

    if (!hasSelection) {
      return;
    }

    tiptapToolbar.handleItalicToggle();
    coverLetterState.handleItalicToggle();
  };

  const handleUnderlineToggle = () => {
    if (!tiptapToolbar.activeEditor) {
      return;
    }

    const { from, to } = tiptapToolbar.activeEditor.state.selection;
    const hasSelection = from !== to;

    if (!hasSelection) {
      return;
    }

    tiptapToolbar.handleUnderlineToggle();
    coverLetterState.handleUnderlineToggle();
  };

  return (
    <div
      className="min-h-screen bg-gray-50 py-4"
      onClick={() => {
        if (coverLetterState.showToolbar) {
          coverLetterState.setShowToolbar(null);
        }
      }}
    >
      <div className="max-w-4xl mx-auto px-4">
        {/* Toolbar */}
        <CoverLetterToolbar
          fontSize={coverLetterState.fontSize}
          fontFamily={coverLetterState.fontFamily}
          lineHeight={coverLetterState.lineHeight}
          textAlign={coverLetterState.textAlign}
          isBold={tiptapStates.isBold}
          isItalic={tiptapStates.isItalic}
          isUnderline={tiptapStates.isUnderline}
          currentTheme={coverLetterState.currentTheme}
          onThemeChange={handleThemeChange}
          onFontSizeChange={handlers.handleFontSizeChange}
          onFontFamilyChange={handlers.handleFontFamilyChange}
          onLineHeightChange={handlers.handleLineHeightChange}
          onTextAlignChange={coverLetterState.handleTextAlignChange}
          onBoldToggle={handleBoldToggle}
          onItalicToggle={handleItalicToggle}
          onUnderlineToggle={handleUnderlineToggle}
          onUndo={tiptapToolbar.handleUndo}
          onRedo={tiptapToolbar.handleRedo}
          onAddSection={() => coverLetterState.setShowAddSectionsModal(true)}
          onClearFormat={tiptapToolbar.handleClearFormat}
          onSave={handleSaveClick}
          isSaving={isSaving}
          onChangeTemplate={coverLetterState.handleChangeTemplate}
          isHidden={showSuccessModal || coverLetterState.showAddSectionsModal}
          canUndo={tiptapStates.canUndo}
          canRedo={tiptapStates.canRedo}
        />

        <div
          id="temp1"
          className="bg-white rounded-lg shadow-lg p-4 mt-2"
          style={{
            fontFamily: coverLetterState.fontFamily,
            fontSize: getFontSizeInPx(coverLetterState.fontSize),
            lineHeight: coverLetterState.lineHeight,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header Section */}
          <div
            className="flex items-center mb-2 p-3 rounded-lg relative group"
            style={{
              backgroundColor: coverLetterState.headerColor,
              pageBreakInside: "avoid",
              breakInside: "avoid",
            }}
            data-section="header"
          >
            <div
              className="w-20 h-20 bg-gray-600 rounded-lg flex items-center justify-center mr-6 cursor-pointer hover:bg-gray-700 transition-colors relative group"
              onClick={triggerFileSelect}
              title="Click to upload profile image"
            >
              {formData.profileImage ? (
                <img
                  src={formData.profileImage}
                  alt="Profile"
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <Camera className="w-8 h-8 text-white" />
              )}
              {isUploadingAvatar && (
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <Camera className="w-6 h-6 text-white" />
              </div>
            </div>
            <div className="flex-1">
              <TiptapEditableField
                field="fullName"
                value={formData.fullName}
                className="text-2xl font-bold text-orange-500"
                placeholder="Full Name"
                editingField={coverLetterState.editingField}
                onEdit={coverLetterState.setEditingField}
                onChange={handlers.handleInputChange}
                sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                  "fullName"
                )}
                detailedFormatting={coverLetterState.getDetailedFormatting(
                  "fullName"
                )}
                onDetailedFormattingChange={
                  coverLetterState.updateDetailedFormatting
                }
              />
              <TiptapEditableField
                field="targetPosition"
                value={formData.targetPosition}
                className="text-blue-600 font-medium"
                placeholder="Target Position"
                editingField={coverLetterState.editingField}
                onEdit={coverLetterState.setEditingField}
                onChange={handlers.handleInputChange}
                sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                  "targetPosition"
                )}
                detailedFormatting={coverLetterState.getDetailedFormatting(
                  "targetPosition"
                )}
                onDetailedFormattingChange={
                  coverLetterState.updateDetailedFormatting
                }
              />
            </div>
          </div>

          {/* Contact Information & Recipient Information */}
          <div
            className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-2"
            style={{
              pageBreakInside: "avoid",
              breakInside: "avoid",
            }}
            data-section="contactInfo"
          >
            <div
              className="rounded-2xl relative group"
              style={{ backgroundColor: coverLetterState.contactColor }}
            >
              <div className="px-3 pt-3 pb-0">
                <h3 className="text-sm font-semibold text-orange-500 mb-2 bg-orange-100 border border-orange-300 rounded-2xl px-3 py-1 shadow-sm flex items-center justify-center text-center min-w-fit w-fit absolute -top-3 left-4 z-10">
                  {coverLetterState.labels.contactInfo}
                </h3>
              </div>
              <div className="px-3 pb-3">
                <div className="space-y-1">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 text-orange-500 mr-2 flex-shrink-0" />
                    <TiptapEditableField
                      field="dateOfBirth"
                      value={formData.dateOfBirth}
                      className="text-xs flex-1"
                      placeholder="Date of Birth"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "dateOfBirth"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "dateOfBirth"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                  <div className="flex items-center">
                    <Phone className="w-3 h-3 text-orange-500 mr-2 flex-shrink-0" />
                    <TiptapEditableField
                      field="phoneNumber"
                      value={formData.phoneNumber}
                      className="text-xs flex-1"
                      placeholder="Phone Number"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "phoneNumber"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "phoneNumber"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                  <div className="flex items-center">
                    <Mail className="w-3 h-3 text-orange-500 mr-2 flex-shrink-0" />
                    <TiptapEditableField
                      field="emailAddress"
                      value={formData.emailAddress}
                      className="text-xs flex-1"
                      placeholder="Email Address"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "emailAddress"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "emailAddress"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-3 h-3 text-orange-500 mr-2 flex-shrink-0" />
                    <TiptapEditableField
                      field="address"
                      value={formData.address}
                      className="text-xs flex-1"
                      placeholder="Address"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "address"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "address"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>

                  <div className="flex items-center">
                    <Link className="w-3 h-3 text-orange-500 mr-2 flex-shrink-0" />
                    <TiptapEditableField
                      field="linkedin"
                      value={formData.linkedin}
                      className="text-xs flex-1"
                      placeholder="LinkedIn (optional)"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "linkedin"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "linkedin"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="p-3 rounded-lg">
              <div className="space-y-1">
                <TiptapEditableField
                  field="hiringManagerName"
                  value={formData.hiringManagerName}
                  className="block w-full text-cyan-600"
                  placeholder={`${coverLetterState.labels.dear}: [Recipient Name]`}
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "hiringManagerName"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "hiringManagerName"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
                <TiptapEditableField
                  field="hiringManagerTitle"
                  value={formData.hiringManagerTitle}
                  className="block w-full text-cyan-600"
                  placeholder={`${coverLetterState.labels.title}: [Position / Department]`}
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "hiringManagerTitle"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "hiringManagerTitle"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
                <TiptapEditableField
                  field="companyName"
                  value={formData.companyName}
                  className="block w-full text-cyan-600"
                  placeholder={`${coverLetterState.labels.companyName}: [Company Name]`}
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "companyName"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "companyName"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
                <TiptapEditableField
                  field="companyAddress"
                  value={formData.companyAddress}
                  className="block w-full text-cyan-600"
                  placeholder={`${coverLetterState.labels.address}: [Company Address]`}
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "companyAddress"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "companyAddress"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
                <TiptapEditableField
                  field="applicationDate"
                  value={formData.applicationDate}
                  className="block w-full text-cyan-600"
                  placeholder={
                    coverLetterState.isVietnamese
                      ? "Ngày: [Application Date]"
                      : "Date: [Application Date]"
                  }
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "applicationDate"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "applicationDate"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
                <TiptapEditableField
                  field="jobTitle"
                  value={formData.jobTitle}
                  className="block w-full text-cyan-600"
                  placeholder={`${coverLetterState.labels.applicationFor}: [Position]`}
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "jobTitle"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "jobTitle"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            </div>
          </div>

          {/* Letter Content */}
          <div className="space-y-2 p-2 rounded-lg">
            {/* Greeting */}
            {!sectionManager.isFieldHidden("greeting") && (
              <div
                className="space-y-1"
                style={{
                  pageBreakInside: "avoid",
                  breakInside: "avoid",
                }}
                data-section="greeting"
              >
                <TiptapEditableField
                  field="greeting"
                  value={
                    formData.greeting ||
                    (coverLetterState.isVietnamese
                      ? "Kính gửi Quý công ty,"
                      : "Dear Mr./Ms. [Name],")
                  }
                  className="text-justify leading-relaxed"
                  style={{
                    pageBreakInside: "avoid",
                    breakInside: "avoid",
                  }}
                  placeholder={
                    coverLetterState.isVietnamese
                      ? "Kính gửi Quý công ty,"
                      : "Dear Mr./Ms. [Name],"
                  }
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "greeting"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("greeting")}
                  canMoveDown={sectionManager.canMoveDown("greeting")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "greeting"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden("greeting")}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "greeting"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "greeting"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Opening Paragraph */}
            {!sectionManager.isFieldHidden("openingParagraph") && (
              <div
                className="space-y-1"
                style={{
                  pageBreakInside: "avoid",
                  breakInside: "avoid",
                }}
                data-section="openingParagraph"
              >
                <TiptapEditableField
                  field="openingParagraph"
                  value={formData.openingParagraph}
                  className="text-justify leading-relaxed"
                  style={{
                    pageBreakInside: "avoid",
                    breakInside: "avoid",
                  }}
                  isTextarea={true}
                  placeholder="How you found the job, your excitement, and why you're a good fit"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "openingParagraph"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("openingParagraph")}
                  canMoveDown={sectionManager.canMoveDown("openingParagraph")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "openingParagraph"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "openingParagraph"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "openingParagraph"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "openingParagraph"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Experience Section */}
            {!sectionManager.isFieldHidden("experienceSection") && (
              <div
                className="space-y-1"
                style={{
                  pageBreakInside: "avoid",
                  breakInside: "avoid",
                }}
                data-section="experienceSection"
              >
                <TiptapEditableField
                  field="experienceSection"
                  value={formData.experienceSection}
                  className="text-justify leading-relaxed"
                  style={{
                    pageBreakInside: "avoid",
                    breakInside: "avoid",
                  }}
                  isTextarea={true}
                  placeholder="Education background, professional experience, and achievements"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "experienceSection"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("experienceSection")}
                  canMoveDown={sectionManager.canMoveDown("experienceSection")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "experienceSection"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "experienceSection"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "experienceSection"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "experienceSection"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Skills Section */}
            {!sectionManager.isFieldHidden("skillsSection") && (
              <div
                className="space-y-1"
                style={{
                  pageBreakInside: "avoid",
                  breakInside: "avoid",
                }}
                data-section="skillsSection"
              >
                <TiptapEditableField
                  field="skillsSection"
                  value={formData.skillsSection}
                  className="text-justify leading-relaxed"
                  style={{
                    pageBreakInside: "avoid",
                    breakInside: "avoid",
                  }}
                  isTextarea={true}
                  placeholder="Relevant skills and personal strengths"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "skillsSection"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("skillsSection")}
                  canMoveDown={sectionManager.canMoveDown("skillsSection")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "skillsSection"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden("skillsSection")}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "skillsSection"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "skillsSection"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Closing Paragraph */}
            {!sectionManager.isFieldHidden("closingParagraph") && (
              <div
                className="space-y-1"
                style={{
                  pageBreakInside: "avoid",
                  breakInside: "avoid",
                }}
                data-section="closingParagraph"
              >
                <TiptapEditableField
                  field="closingParagraph"
                  value={formData.closingParagraph}
                  className="text-justify leading-relaxed"
                  style={{
                    pageBreakInside: "avoid",
                    breakInside: "avoid",
                  }}
                  isTextarea={true}
                  placeholder="Interview request, gratitude, and enthusiasm"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "closingParagraph"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("closingParagraph")}
                  canMoveDown={sectionManager.canMoveDown("closingParagraph")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "closingParagraph"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "closingParagraph"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "closingParagraph"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "closingParagraph"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Signature */}
            <div
              className="flex flex-col"
              style={{
                pageBreakInside: "avoid",
                breakInside: "avoid",
              }}
              data-section="signature"
            >
              <TiptapEditableField
                field="signatureClosing"
                value={
                  formData.signatureClosing ||
                  (coverLetterState.isVietnamese ? "Trân trọng," : "Sincerely,")
                }
                className="mb-1"
                placeholder={
                  coverLetterState.isVietnamese ? "Trân trọng," : "Sincerely,"
                }
                editingField={coverLetterState.editingField}
                onEdit={coverLetterState.setEditingField}
                onChange={handlers.handleInputChange}
                sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                  "signatureClosing"
                )}
                detailedFormatting={coverLetterState.getDetailedFormatting(
                  "signatureClosing"
                )}
                onDetailedFormattingChange={
                  coverLetterState.updateDetailedFormatting
                }
              />
              <TiptapEditableField
                field="signatureName"
                value={formData.signatureName}
                className="font-semibold w-auto min-w-fit"
                placeholder="Your Full Name"
                editingField={coverLetterState.editingField}
                onEdit={coverLetterState.setEditingField}
                onChange={handlers.handleInputChange}
                sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                  "signatureName"
                )}
                detailedFormatting={coverLetterState.getDetailedFormatting(
                  "signatureName"
                )}
                onDetailedFormattingChange={
                  coverLetterState.updateDetailedFormatting
                }
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Success Modal */}
      <SaveSuccessModal
        isOpen={showSuccessModal}
        onClose={closeSuccessModal}
        coverLetterFileUrl={coverLetterFileUrl}
        onEdit={handleEdit}
        coverLetterId={coverLetterState.coverLetterId}
      />

      {/* Add Sections Modal */}
      <AddSectionsModal
        isOpen={coverLetterState.showAddSectionsModal}
        onClose={() => coverLetterState.setShowAddSectionsModal(false)}
        hiddenSections={sectionManager.hiddenSections}
        onToggleSection={sectionManager.toggleVisibility}
      />
    </div>
  );
};

// Wrapper component with TiptapProvider
const CoverLetterTemp1 = (props: CoverLetterTemp1Props) => {
  return (
    <TiptapProvider>
      <CoverLetterTemp1Content {...props} />
    </TiptapProvider>
  );
};

export default CoverLetterTemp1;
