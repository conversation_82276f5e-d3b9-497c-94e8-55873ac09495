"use client";
import { motion } from "framer-motion";
import { FileText, Briefcase, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  CoverLetterAnalysisTypeSelectProps, 
  CoverLetterModalHeaderProps, 
  CoverLetterModalActionsProps 
} from "../cover-letter";

export function CoverLetterModalHeader({}: CoverLetterModalHeaderProps) {
  return (
    <DialogHeader className="text-center pb-6">
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center mx-auto mb-4"
      >
        <FileText className="w-8 h-8 text-white" />
      </motion.div>
      <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
        Create Cover Letter with AI
      </DialogTitle>
      <p className="text-slate-600 font-medium">
        Generate a professional cover letter tailored to your job application
      </p>
    </DialogHeader>
  );
}

export function CoverLetterAnalysisTypeSelect({
  analysisType,
  onAnalysisTypeChange,
}: Readonly<CoverLetterAnalysisTypeSelectProps>) {
  const options = [
    {
      value: "role" as const,
      title: "By Role Description",
      description: "Create cover letter based on target role",
      icon: <Briefcase className="w-6 h-6" />,
      gradient: "from-emerald-500 to-teal-500",
      bgGradient: "from-emerald-50 to-teal-50",
      borderColor: "border-emerald-200",
      selectedBorder: "border-emerald-400",
    },
    {
      value: "jd" as const,
      title: "By Job Description",
      description: "Create cover letter based on job posting",
      icon: <FileText className="w-6 h-6" />,
      gradient: "from-orange-500 to-amber-500",
      bgGradient: "from-orange-50 to-amber-50",
      borderColor: "border-orange-200",
      selectedBorder: "border-orange-400",
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-4"
    >
      <div className="text-center">
        <h3 className="text-lg font-semibold text-slate-800 mb-2">
          Choose Analysis Method
        </h3>
        <p className="text-sm text-slate-600">
          Select how you'd like to create your cover letter
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {options.map((option) => (
          <motion.div
            key={option.value}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`relative p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
              analysisType === option.value
                ? `${option.selectedBorder} bg-gradient-to-br ${option.bgGradient} shadow-lg`
                : `${option.borderColor} bg-white hover:bg-gradient-to-br hover:${option.bgGradient} hover:shadow-md`
            }`}
            onClick={() => onAnalysisTypeChange(option.value)}
          >
            <div className="flex flex-col items-center text-center space-y-3">
              <div
                className={`w-12 h-12 rounded-xl bg-gradient-to-br ${option.gradient} flex items-center justify-center text-white shadow-lg`}
              >
                {option.icon}
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-1">
                  {option.title}
                </h4>
                <p className="text-sm text-slate-600">{option.description}</p>
              </div>
            </div>

            {analysisType === option.value && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute top-3 right-3 w-6 h-6 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center"
              >
                <div className="w-2 h-2 bg-white rounded-full" />
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}

export function CoverLetterModalActions({
  isGenerating,
  isFormValid,
  onGenerate,
  onClose,
}: Readonly<CoverLetterModalActionsProps>) {
  return (
    <div className="flex space-x-3 pt-6 mt-8 border-t border-slate-200">
      <Button
        variant="outline"
        onClick={onClose}
        className="flex-1 h-12 rounded-xl border-2 border-slate-200 hover:bg-slate-50 transition-all duration-200"
        disabled={isGenerating}
      >
        Cancel
      </Button>
      <Button
        onClick={onGenerate}
        disabled={!isFormValid || isGenerating}
        className="flex-1 h-12 rounded-xl bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 hover:from-indigo-600 hover:via-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
      >
        {isGenerating ? (
          <>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full"
            />
            Generating...
          </>
        ) : (
          <>
            <Sparkles className="w-4 h-4 mr-2" />
            Generate Cover Letter
          </>
        )}
      </Button>
    </div>
  );
}
