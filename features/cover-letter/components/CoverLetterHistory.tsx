"use client";

import { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FileText,
  Search,
  Eye,
  Briefcase,
  FileCheck,
  Edit,
  Download,
  MessageSquare,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { CoverLetter as APICoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";

interface CoverLetterHistoryProps {
  coverLetters: APICoverLetter[];
  filteredCoverLetters: APICoverLetter[];
  searchTerm: string;
  languageFilter: string;
  analystFilter: string;
  isLoadingRecent: boolean;
  recentError: any;
  onSearchChange: (value: string) => void;
  onLanguageFilterChange: (value: string) => void;
  onAnalystFilterChange: (value: string) => void;
  onViewCoverLetter: (id: string) => void;
  onEditCoverLetter?: (id: string) => void;
  onDownloadCoverLetter?: (id: string) => void;
  onViewFeedback?: (id: string) => void;
}

export default function CoverLetterHistory({
  coverLetters,
  filteredCoverLetters,
  searchTerm,
  languageFilter,
  analystFilter,
  isLoadingRecent,
  recentError,
  onSearchChange,
  onLanguageFilterChange,
  onAnalystFilterChange,
  onViewCoverLetter,
  onEditCoverLetter,
  onDownloadCoverLetter,
  onViewFeedback,
}: CoverLetterHistoryProps) {
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Calculate pagination
  const totalPages = Math.ceil(filteredCoverLetters.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredCoverLetters.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useMemo(() => {
    setCurrentPage(1);
  }, [searchTerm, languageFilter, analystFilter]);
  const getAnalystColor = (analyst: string) => {
    switch (analyst) {
      case "Role":
        return "bg-emerald-100 text-emerald-800 border-emerald-200 hover:bg-emerald-200 hover:text-emerald-900 hover:border-emerald-300 hover:shadow-md transition-all duration-300 cursor-pointer";
      case "JD":
        return "bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200 hover:text-orange-900 hover:border-orange-300 hover:shadow-md transition-all duration-300 cursor-pointer";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200 hover:text-gray-900 hover:border-gray-300 hover:shadow-md transition-all duration-300 cursor-pointer";
    }
  };

  const getAnalystIcon = (analyst: string) => {
    switch (analyst) {
      case "Role":
        return <Briefcase className="w-3 h-3 mr-1" />;
      case "JD":
        return <FileCheck className="w-3 h-3 mr-1" />;
      default:
        return null;
    }
  };

  const getLanguageFlag = (language: string) => {
    switch (language) {
      case "EN":
        return "🇺🇸";
      case "VN":
        return "🇻🇳";
      case "JP":
        return "🇯🇵";
      default:
        return "🌐";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.6 }}
    >
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-indigo-600" />
                Cover Letter History
              </CardTitle>
              <CardDescription>
                Manage and track your cover letter applications
              </CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search cover letters..."
                  value={searchTerm}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className="pl-10 w-full sm:w-64"
                />
              </div>

              <Select
                value={languageFilter}
                onValueChange={onLanguageFilterChange}
              >
                <SelectTrigger className="w-full sm:w-32">
                  <SelectValue placeholder="Language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Languages</SelectItem>
                  <SelectItem value="EN">English</SelectItem>
                  <SelectItem value="VN">Vietnamese</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={analystFilter}
                onValueChange={onAnalystFilterChange}
              >
                <SelectTrigger className="w-full sm:w-32">
                  <SelectValue placeholder="Analyst" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Analyst</SelectItem>
                  <SelectItem value="Role">Role</SelectItem>
                  <SelectItem value="JD">JD</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoadingRecent ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
              <span className="ml-3 text-gray-600">
                Loading cover letters...
              </span>
            </div>
          ) : recentError ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <FileText className="w-16 h-16 text-red-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-600 mb-2">
                Failed to load cover letters
              </h3>
              <p className="text-red-500">Please try refreshing the page</p>
            </motion.div>
          ) : !coverLetters || coverLetters.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                You haven't created any Cover Letters yet
              </h3>
              <p className="text-gray-500">
                Get started by creating your first cover letter with AI
                assistance
              </p>
            </motion.div>
          ) : filteredCoverLetters.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No matching cover letters found
              </h3>
              <p className="text-gray-500">
                Try adjusting your search or filters
              </p>
            </motion.div>
          ) : (
            <div className="space-y-4">
              <div className="rounded-lg border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-semibold">Title</TableHead>
                      <TableHead className="font-semibold">Company</TableHead>
                      <TableHead className="font-semibold">
                        Date Created
                      </TableHead>
                      <TableHead className="font-semibold">Language</TableHead>
                      <TableHead className="font-semibold">Analyst</TableHead>
                      <TableHead className="font-semibold">Feedback</TableHead>
                      <TableHead className="font-semibold text-right">
                        Actions
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <AnimatePresence>
                      {currentItems.map(
                        (coverLetter: APICoverLetter, index: number) => {
                          const isRoleAnalysis =
                            coverLetter.agentType === "AI_COVER_LETTER_ROLE";
                          const analystType = isRoleAnalysis ? "Role" : "JD";
                          const language =
                            coverLetter.language === "english" ? "EN" : "VN";

                          return (
                            <motion.tr
                              key={coverLetter._id}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -20 }}
                              transition={{
                                delay: 0.1 * index,
                                duration: 0.3,
                              }}
                              className="hover:bg-gray-50 transition-colors"
                            >
                              <TableCell className="font-medium">
                                {coverLetter.content.header.targetPosition}
                              </TableCell>
                              <TableCell className="text-gray-600">
                                {coverLetter.content.recipientInfo.companyName}
                              </TableCell>
                              <TableCell className="text-gray-600">
                                {new Date(
                                  coverLetter.createdAt
                                ).toLocaleDateString()}
                              </TableCell>
                              <TableCell>
                                <span className="flex items-center gap-1">
                                  {getLanguageFlag(language)}
                                  <span className="text-sm">{language}</span>
                                </span>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  className={`${getAnalystColor(
                                    analystType
                                  )} border flex items-center w-fit`}
                                >
                                  {getAnalystIcon(analystType)}
                                  {analystType}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="p-2 text-blue-600 hover:text-blue-700"
                                  onClick={() =>
                                    onViewFeedback?.(coverLetter._id)
                                  }
                                >
                                  <MessageSquare className="w-4 h-4 mr-1" />
                                  View Feedback
                                </Button>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex items-center justify-end gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="p-2"
                                    onClick={() =>
                                      onViewCoverLetter(coverLetter._id)
                                    }
                                  >
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="p-2"
                                    onClick={() =>
                                      onEditCoverLetter?.(coverLetter._id)
                                    }
                                  >
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="p-2"
                                    onClick={() =>
                                      onDownloadCoverLetter?.(coverLetter._id)
                                    }
                                  >
                                    <Download className="w-4 h-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </motion.tr>
                          );
                        }
                      )}
                    </AnimatePresence>
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Showing {Math.min(endIndex, filteredCoverLetters.length)} of{" "}
                    {filteredCoverLetters.length} results
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="flex items-center gap-1"
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>

                    <div className="flex items-center gap-1">
                      {(() => {
                        const maxVisiblePages = 5;
                        const pages = [];

                        if (totalPages <= maxVisiblePages) {
                          // Show all pages if total is small
                          for (let i = 1; i <= totalPages; i++) {
                            pages.push(i);
                          }
                        } else {
                          // Show smart pagination
                          if (currentPage <= 3) {
                            // Show first pages
                            for (let i = 1; i <= 4; i++) {
                              pages.push(i);
                            }
                            pages.push("...");
                            pages.push(totalPages);
                          } else if (currentPage >= totalPages - 2) {
                            // Show last pages
                            pages.push(1);
                            pages.push("...");
                            for (let i = totalPages - 3; i <= totalPages; i++) {
                              pages.push(i);
                            }
                          } else {
                            // Show middle pages
                            pages.push(1);
                            pages.push("...");
                            for (
                              let i = currentPage - 1;
                              i <= currentPage + 1;
                              i++
                            ) {
                              pages.push(i);
                            }
                            pages.push("...");
                            pages.push(totalPages);
                          }
                        }

                        return pages.map((page, index) => {
                          if (page === "...") {
                            return (
                              <span
                                key={`ellipsis-${index}`}
                                className="px-2 text-gray-400"
                              >
                                ...
                              </span>
                            );
                          }

                          return (
                            <Button
                              key={page}
                              variant={
                                currentPage === page ? "default" : "outline"
                              }
                              size="sm"
                              onClick={() => setCurrentPage(page as number)}
                              className="w-8 h-8 p-0"
                            >
                              {page}
                            </Button>
                          );
                        });
                      })()}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="flex items-center gap-1"
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
