"use client";
import { motion } from "framer-motion";
import { Globe, Briefcase } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUploadArea } from "@/features/analyst/components/FileUploadArea";
import { CoverLetterRoleFormProps, CoverLetterJDFormProps } from "../cover-letter";

export function CoverLetterRoleForm({
  roleDescription,
  onRoleDescriptionChange,
  language,
  onLanguageChange,
  cvFile,
  onFileUpload,
  onDragOver,
  onDrop,
}: Readonly<CoverLetterRoleFormProps>) {
  return (
    <motion.div
      key="role"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6 p-6 bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl border border-emerald-100">
        <h3 className="text-lg font-semibold text-emerald-800 flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
          <span>Role-Based Cover Letter</span>
        </h3>

        {/* Role Description */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700 flex items-center gap-2">
            <Briefcase className="w-4 h-4" />
            Role Description
          </Label>
          <Input
            placeholder="Enter the role you're targeting (e.g., Senior Frontend Developer, Product Manager...)"
            value={roleDescription}
            onChange={(e) => onRoleDescriptionChange(e.target.value)}
            className="h-12 border-2 border-slate-200 focus:border-emerald-400 rounded-xl"
          />
        </div>

        {/* Language Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700 flex items-center gap-2">
            <Globe className="w-4 h-4" />
            Language
          </Label>
          <Select
            value={language}
            onValueChange={(value) => onLanguageChange(value as any)}
          >
            <SelectTrigger className="h-12 border-2 border-slate-200 focus:border-emerald-400 rounded-xl">
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="English">🇺🇸 English</SelectItem>
              <SelectItem value="Vietnamese">🇻🇳 Vietnamese</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* CV Upload */}
        <FileUploadArea
          type="cv"
          label="Upload Your Resume"
          file={cvFile}
          onFileSelect={(file) => onFileUpload(file, "cv")}
          onDragOver={onDragOver}
          onDrop={onDrop}
        />
      </div>
    </motion.div>
  );
}

export function CoverLetterJDForm({
  language,
  onLanguageChange,
  cvFile,
  jdFile,
  onFileUpload,
  onDragOver,
  onDrop,
}: Readonly<CoverLetterJDFormProps>) {
  return (
    <motion.div
      key="jd"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="space-y-6 p-6 bg-gradient-to-br from-orange-50 to-amber-50 rounded-2xl border border-orange-100">
        <h3 className="text-lg font-semibold text-orange-800 flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-orange-500"></div>
          <span>Job Description-Based Cover Letter</span>
        </h3>

        {/* Language Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-slate-700 flex items-center gap-2">
            <Globe className="w-4 h-4" />
            Language
          </Label>
          <Select
            value={language}
            onValueChange={(value) => onLanguageChange(value as any)}
          >
            <SelectTrigger className="h-12 border-2 border-slate-200 focus:border-orange-400 rounded-xl">
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="English">🇺🇸 English</SelectItem>
              <SelectItem value="Vietnamese">🇻🇳 Vietnamese</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* File Uploads */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FileUploadArea
            type="cv"
            label="Upload Your Resume"
            file={cvFile}
            onFileSelect={(file) => onFileUpload(file, "cv")}
            onDragOver={onDragOver}
            onDrop={onDrop}
          />

          <FileUploadArea
            type="jd"
            label="Upload Job Description"
            file={jdFile}
            onFileSelect={(file) => onFileUpload(file, "jd")}
            onDragOver={onDragOver}
            onDrop={onDrop}
          />
        </div>
      </div>
    </motion.div>
  );
}
