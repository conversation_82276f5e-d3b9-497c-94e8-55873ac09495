"use client";
import React, { useState, useEffect } from "react";
import { X } from "lucide-react";

interface AddSectionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  hiddenSections: Set<string>;
  onToggleSection: (sectionName: string) => void;
}

const AddSectionsModal: React.FC<AddSectionsModalProps> = ({
  isOpen,
  onClose,
  hiddenSections,
  onToggleSection,
}) => {
  // Local state to track which sections user wants to add
  const [sectionsToAdd, setSectionsToAdd] = useState<Set<string>>(new Set());

  // Reset local state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSectionsToAdd(new Set());
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const sections = [
    {
      key: "greeting",
      title: "Greeting",
      description: "Address your letter to the hiring manager",
    },
    {
      key: "openingParagraph",
      title: "Opening Paragraph",
      description: "Introduce yourself and state your interest",
    },
    {
      key: "experienceSection",
      title: "Experience",
      description:
        "Choose information you want to share with potential employers",
    },
    {
      key: "skillsSection",
      title: "Skills",
      description: "Highlight your relevant skills and strengths",
    },
    {
      key: "closingParagraph",
      title: "Closing",
      description: "Enter your availability to relocate for this position",
    },
  ];

  const handleAdd = () => {
    // Apply all selected sections
    sectionsToAdd.forEach((sectionKey) => {
      onToggleSection(sectionKey);
    });
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  const handleCheckboxChange = (sectionKey: string) => {
    const newSectionsToAdd = new Set(sectionsToAdd);
    if (newSectionsToAdd.has(sectionKey)) {
      newSectionsToAdd.delete(sectionKey);
    } else {
      newSectionsToAdd.add(sectionKey);
    }
    setSectionsToAdd(newSectionsToAdd);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Add Sections To Your Letter
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {sections.map((section) => {
            const isHidden = hiddenSections.has(section.key);
            const isDisabled = !isHidden; // Disable if section is currently visible
            const isSelectedToAdd = sectionsToAdd.has(section.key);

            return (
              <div key={section.key} className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id={section.key}
                  checked={isSelectedToAdd} // Checked if user selected to add
                  disabled={isDisabled}
                  onChange={() => {
                    if (isHidden) {
                      handleCheckboxChange(section.key);
                    }
                  }}
                  className={`mt-1 w-4 h-4 rounded border-gray-300 ${
                    isDisabled
                      ? "text-gray-400 cursor-not-allowed opacity-50"
                      : "text-blue-600 focus:ring-blue-500"
                  }`}
                />
                <div className="flex-1">
                  <label
                    htmlFor={section.key}
                    className={`block text-sm font-medium ${
                      isDisabled ? "text-gray-400" : "text-gray-900"
                    }`}
                  >
                    {section.title}
                  </label>
                  <p
                    className={`text-sm ${
                      isDisabled ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    {section.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={handleCancel}
            className="px-6 py-2 border border-blue-600 text-blue-600 rounded-full hover:bg-blue-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleAdd}
            className="px-6 py-2 bg-orange-400 text-white rounded-full hover:bg-orange-500 transition-colors"
          >
            Add
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddSectionsModal;
