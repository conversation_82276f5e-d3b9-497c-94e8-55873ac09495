"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/redux/store";
import { setSelectedTemplate } from "@/redux/slices/coverLetterSlice";
import { Search, Eye, Star, Zap, Sparkles, Edit } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { filterOptions, templates } from "@/services/data/cover-letter";
import TemplatePreviewModal from "./TemplatePreviewModal";
import { useGetCoverLetterByIdQuery } from "@/services/api/cover-letter";

const TemplateCard = ({
  template,
  index,
  onPreview,
  onUseTemplate,
}: {
  template: any;
  index: number;
  onPreview: (templateId: string, templateName: string) => void;
  onUseTemplate: (templateId: string) => void;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Popular Badge */}
      {template.popular && (
        <div className="absolute top-4 left-4 z-10">
          <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-semibold">
            <Star className="w-3 h-3 mr-1" />
            Popular
          </Badge>
        </div>
      )}

      {/* Template Preview */}
      <div className="relative aspect-[3/4] overflow-hidden">
        <img
          src={template.image}
          alt={template.name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />

        {/* Overlay on hover */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/40 flex items-center justify-center gap-3"
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                <Button
                  size="sm"
                  variant="secondary"
                  className="bg-white/90 hover:bg-white"
                  onClick={() =>
                    onPreview(template.id.toString(), template.name)
                  }
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </Button>
              </motion.div>
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Button
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                  onClick={() => onUseTemplate(template.id.toString())}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Use Template
                </Button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Template Info */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-3">
          {template.name}
        </h3>
        <div className="flex flex-wrap gap-2">
          {template.tags.map((tag: string, tagIndex: number) => (
            <Badge key={tagIndex} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default function ListTemplates() {
  const router = useRouter();
  const dispatch = useDispatch();
  const searchParams = useSearchParams();

  const currentCoverLetterId = useSelector(
    (state: RootState) => state.coverLetter.currentCoverLetterId
  );

  // Get cover letter ID from URL or Redux
  const coverLetterIdFromUrl = searchParams.get("id");
  const coverLetterId = coverLetterIdFromUrl || currentCoverLetterId;

  // Fetch cover letter data if ID exists
  const { isLoading, error } = useGetCoverLetterByIdQuery(coverLetterId || "", {
    skip: !coverLetterId,
  });

  const [selectedFilters, setSelectedFilters] = useState<string[]>(["all"]);
  const [searchQuery, setSearchQuery] = useState("");
  const [previewModal, setPreviewModal] = useState<{
    isOpen: boolean;
    templateId: string;
    templateName: string;
  }>({
    isOpen: false,
    templateId: "",
    templateName: "",
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">An error occurred while loading data</p>
        </div>
      </div>
    );
  }

  const handlePreview = (templateId: string, templateName: string) => {
    setPreviewModal({
      isOpen: true,
      templateId,
      templateName,
    });
  };

  const closePreview = () => {
    setPreviewModal({
      isOpen: false,
      templateId: "",
      templateName: "",
    });
  };

  const handleUseTemplate = (templateId: string) => {
    if (coverLetterId) {
      const templateName = `temp${templateId}`;

      dispatch(setSelectedTemplate(templateName));

      router.push(
        `/cover-letter/templates/${coverLetterId}?template=${templateName}`
      );
    } else {
      alert("Please create a cover letter before selecting a template");
    }
  };

  const handleFilterToggle = (filterValue: string) => {
    if (filterValue === "all") {
      setSelectedFilters(["all"]);
    } else {
      setSelectedFilters((prev) => {
        const newFilters = prev.filter((f) => f !== "all");

        if (newFilters.includes(filterValue)) {
          const updated = newFilters.filter((f) => f !== filterValue);
          return updated.length === 0 ? ["all"] : updated;
        } else {
          return [...newFilters, filterValue];
        }
      });
    }
  };

  const filteredTemplates = templates.filter((template) => {
    // Search filter
    const matchesSearch =
      searchQuery === "" ||
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase())
      );

    if (!matchesSearch) return false;

    // Category filter
    if (selectedFilters.includes("all")) return true;

    return selectedFilters.some((filter) =>
      template.categories.includes(filter)
    );
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 pb-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="pt-8 pb-8 px-4 text-center"
        >
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="inline-flex items-center gap-2 bg-indigo-100 text-indigo-700 px-4 py-2 rounded-full text-sm font-medium mb-6"
            >
              <Sparkles className="w-4 h-4" />
              Updated 2025
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-indigo-900 to-gray-900 bg-clip-text text-transparent mb-6"
            >
              Modern Cover Letter
              <br />
              <span className="text-indigo-600">Templates 2025</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed"
            >
              Choose the perfect template that matches your industry and style.
              All templates are professionally designed and easily customizable.
            </motion.p>
          </div>
        </motion.div>

        {/* Search and Filter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-12"
        >
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto mb-8">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-3 rounded-full border-2 border-gray-200 focus:border-blue-500 transition-colors"
            />
          </div>

          {/* Filter Options */}
          <div className="flex flex-wrap justify-center gap-4">
            {filterOptions.map((option) => {
              const Icon = option.icon;
              const isSelected = selectedFilters.includes(option.value);

              return (
                <Button
                  key={option.value}
                  variant={isSelected ? "default" : "outline"}
                  onClick={() => handleFilterToggle(option.value)}
                  className={`rounded-full px-6 py-3 transition-all duration-200 ${
                    isSelected
                      ? "bg-blue-600 hover:bg-blue-700 text-white"
                      : "hover:bg-blue-50 hover:border-blue-300"
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {option.label}
                  {isSelected && option.value !== "all" && (
                    <span className="ml-2 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                      ✓
                    </span>
                  )}
                </Button>
              );
            })}
          </div>
        </motion.div>

        {/* Templates Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
        >
          <AnimatePresence mode="wait">
            {filteredTemplates.map((template, index) => (
              <TemplateCard
                key={template.id}
                template={template}
                index={index}
                onPreview={handlePreview}
                onUseTemplate={handleUseTemplate}
              />
            ))}
          </AnimatePresence>
        </motion.div>

        {/* No Results */}
        {filteredTemplates.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-16"
          >
            <div className="text-gray-400 text-6xl mb-4">📝</div>
            <h3 className="text-2xl font-bold text-gray-700 mb-2">
              No matching templates found
            </h3>
            <p className="text-gray-500 mb-4">
              Try searching with different keywords or select different filters
            </p>
            <Button
              onClick={() => {
                setSearchQuery("");
                setSelectedFilters(["all"]);
              }}
              variant="outline"
              className="rounded-full"
            >
              Clear Filters
            </Button>
          </motion.div>
        )}

        {/* Footer CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center mt-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white"
        >
          <h2 className="text-3xl font-bold mb-4">
            Can't find the perfect template?
          </h2>
          <p className="text-xl mb-6 opacity-90">
            Create a custom Cover Letter with our AI
          </p>
          <Button
            size="lg"
            variant="secondary"
            className="bg-white text-blue-600 hover:bg-gray-100"
          >
            <Zap className="w-5 h-5 mr-2" />
            Create with AI
          </Button>
        </motion.div>

        {/* Template Preview Modal */}
        <TemplatePreviewModal
          isOpen={previewModal.isOpen}
          onClose={closePreview}
          templateId={previewModal.templateId}
          templateName={previewModal.templateName}
        />
      </div>
    </div>
  );
}
