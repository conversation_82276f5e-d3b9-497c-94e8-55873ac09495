"use client";
import React from "react";
import { Camera, Mail, Phone, MapPin, Link } from "lucide-react";
import CoverLetterToolbar from "../../../components/CoverLetterToolbar";
import TiptapEditableField from "./TiptapEditableField";
import { TiptapProvider } from "../../../providers/TiptapProvider";
import { useTiptapToolbar } from "../hooks/useTiptapToolbar";
import { useCoverLetterHandlers } from "../../../components/CoverLetterHandlers";
import { CoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";
import { useCoverLetterData } from "../hooks/useCoverLetterData";
import { useAvatarUpload } from "../hooks/useAvatarUpload";
import { useSectionManager } from "../hooks/useSectionManager";
import { useCoverLetterSave } from "../hooks/useCoverLetterSave";
import SaveSuccessModal from "./SaveSuccessModal";
import AddSectionsModal from "./AddSectionsModal";
import { useCoverLetterFormData } from "../hooks/useCoverLetterFormData";
import { useCoverLetterState } from "../hooks/useCoverLetterState";
import { getFontSizeInPx } from "@/lib/fontSizeUtils";

interface CoverLetterTemp2Props {
  coverLetterData?: CoverLetter;
  coverLetterId?: string;
  templateId?: string;
}

const CoverLetterTemp2Content = ({
  coverLetterData: propCoverLetterData,
  coverLetterId: propCoverLetterId,
  templateId = "temp2",
}: CoverLetterTemp2Props) => {
  const { coverLetterData: hookCoverLetterData } =
    useCoverLetterData(propCoverLetterId);
  const coverLetterData = propCoverLetterData || hookCoverLetterData;

  const coverLetterState = useCoverLetterState(
    coverLetterData,
    propCoverLetterId
  );

  const { formData, setFormData } = useCoverLetterFormData(
    coverLetterData,
    coverLetterData?.language
  );

  const { triggerFileSelect, isLoading: isUploadingAvatar } = useAvatarUpload(
    coverLetterState.coverLetterId,
    (imageUrl) => {
      setFormData((prev) => ({ ...prev, profileImage: imageUrl }));
    }
  );
  const {
    handleSave,
    isLoading: isSaving,
    showSuccessModal,
    coverLetterFileUrl,
    closeSuccessModal,
    handleEdit,
  } = useCoverLetterSave(coverLetterState.coverLetterId, templateId);

  const state = {
    formData,
    editingField: coverLetterState.editingField,
    headerColor: coverLetterState.headerColor,
    contactColor: coverLetterState.contactColor,
    showToolbar: coverLetterState.showToolbar,
    fontSize: coverLetterState.fontSize,
    fontFamily: coverLetterState.fontFamily,
    lineHeight: coverLetterState.lineHeight,
    textAlign: coverLetterState.textAlign,
    isBold: coverLetterState.isBold,
    isItalic: coverLetterState.isItalic,
    isUnderline: coverLetterState.isUnderline,
  };

  const actions = {
    setFormData,
    setEditingField: coverLetterState.setEditingField,
    setHeaderColor: coverLetterState.setHeaderColor,
    setContactColor: coverLetterState.setContactColor,
    setShowToolbar: coverLetterState.setShowToolbar,
    setFontSize: coverLetterState.setFontSize,
    setFontFamily: coverLetterState.setFontFamily,
    setLineHeight: coverLetterState.setLineHeight,
    setTextAlign: coverLetterState.setTextAlign,
    setIsBold: coverLetterState.setIsBold,
    setIsItalic: coverLetterState.setIsItalic,
    setIsUnderline: coverLetterState.setIsUnderline,
  };

  const handlers = useCoverLetterHandlers(state, actions);

  const tiptapToolbar = useTiptapToolbar({
    onClearDetailedFormatting: coverLetterState.clearDetailedFormatting,
    onUpdateDetailedFormatting: coverLetterState.updateDetailedFormatting,
    onClearSectionFormatting: coverLetterState.clearSectionFormatting,
    onResetToolbarStates: coverLetterState.resetToolbarStates,
  });

  const handleThemeChange = (theme: string) => {
    coverLetterState.setCurrentTheme(theme);
    handlers.handleThemeChange(theme);
  };

  const handleSaveClick = () => {
    let updatedSectionFormatting = { ...coverLetterState.sectionFormatting };

    if (coverLetterState.editingField) {
      const currentField = coverLetterState.editingField;
      const currentFormatting =
        coverLetterState.getCurrentSectionFormatting(currentField);

      // Save all formatting including bold/italic/underline states for database persistence
      updatedSectionFormatting[currentField] = {
        ...currentFormatting,
        textAlign: coverLetterState.textAlign,
        isBold: coverLetterState.isBold,
        isItalic: coverLetterState.isItalic,
        isUnderline: coverLetterState.isUnderline,
      };

      // Update the section formatting in state as well
      coverLetterState.setSectionFormatting(updatedSectionFormatting);
    }

    // Prepare save state with current formatting
    const saveState = {
      fontSize: coverLetterState.fontSize,
      fontFamily: coverLetterState.fontFamily,
      lineHeight: coverLetterState.lineHeight,
      currentTheme: coverLetterState.currentTheme,
      sectionFormatting: updatedSectionFormatting,
      hiddenSections: sectionManager.hiddenSections,
      detailedFormatting: coverLetterState.detailedFormatting,
    };

    handleSave(formData, saveState);
  };

  // Composite handlers that apply Tiptap formatting AND track state for database saving
  const handleBoldToggle = () => {
    // First apply to the Tiptap editor (only works on selected text)
    tiptapToolbar.handleBoldToggle();

    // Only update state if there was actually a selection (for database saving)
    if (tiptapToolbar.activeEditor) {
      const { from, to } = tiptapToolbar.activeEditor.state.selection;
      if (from !== to) {
        // There was a selection, so update state for database saving
        coverLetterState.handleBoldToggle();
      }
    }
  };

  const handleItalicToggle = () => {
    // First apply to the Tiptap editor (only works on selected text)
    tiptapToolbar.handleItalicToggle();

    // Only update state if there was actually a selection
    if (tiptapToolbar.activeEditor) {
      const { from, to } = tiptapToolbar.activeEditor.state.selection;
      if (from !== to) {
        // There was a selection, so update state for database saving
        coverLetterState.handleItalicToggle();
      }
    }
  };

  const handleUnderlineToggle = () => {
    // First apply to the Tiptap editor (only works on selected text)
    tiptapToolbar.handleUnderlineToggle();

    // Only update state if there was actually a selection
    if (tiptapToolbar.activeEditor) {
      const { from, to } = tiptapToolbar.activeEditor.state.selection;
      if (from !== to) {
        // There was a selection, so update state for database saving
        coverLetterState.handleUnderlineToggle();
      }
    }
  };

  // Get current Tiptap states for toolbar
  const tiptapStates = tiptapToolbar.getCurrentStates();

  // Section manager for BubbleMenu actions
  const sectionManager = useSectionManager({
    formData,
    setFormData,
    coverLetterData,
  });

  return (
    <div
      className="min-h-screen bg-gray-50 py-4"
      onClick={() => {
        // Click outside to close toolbar
        if (coverLetterState.showToolbar) {
          coverLetterState.setShowToolbar(null);
        }
      }}
    >
      <div className="max-w-4xl mx-auto px-4">
        {/* Toolbar */}
        <CoverLetterToolbar
          fontSize={coverLetterState.fontSize}
          fontFamily={coverLetterState.fontFamily}
          lineHeight={coverLetterState.lineHeight}
          textAlign={coverLetterState.textAlign}
          isBold={tiptapStates.isBold}
          isItalic={tiptapStates.isItalic}
          isUnderline={tiptapStates.isUnderline}
          currentTheme={coverLetterState.currentTheme}
          onThemeChange={handleThemeChange}
          onFontSizeChange={handlers.handleFontSizeChange}
          onFontFamilyChange={handlers.handleFontFamilyChange}
          onLineHeightChange={handlers.handleLineHeightChange}
          onTextAlignChange={coverLetterState.handleTextAlignChange}
          onBoldToggle={handleBoldToggle}
          onItalicToggle={handleItalicToggle}
          onUnderlineToggle={handleUnderlineToggle}
          onUndo={tiptapToolbar.handleUndo}
          onRedo={tiptapToolbar.handleRedo}
          onAddSection={() => coverLetterState.setShowAddSectionsModal(true)}
          onClearFormat={tiptapToolbar.handleClearFormat}
          onSave={handleSaveClick}
          isSaving={isSaving}
          onChangeTemplate={coverLetterState.handleChangeTemplate}
          isHidden={showSuccessModal || coverLetterState.showAddSectionsModal}
          canUndo={tiptapStates.canUndo}
          canRedo={tiptapStates.canRedo}
        />

        <div
          id="temp2"
          className="bg-white rounded-lg shadow-lg p-4 mt-2"
          style={{
            fontFamily: coverLetterState.fontFamily,
            fontSize: getFontSizeInPx(coverLetterState.fontSize),
            lineHeight: coverLetterState.lineHeight,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header Section */}
          <div
            className="flex justify-between items-start mb-3 p-3 rounded-lg"
            style={{
              backgroundColor: coverLetterState.headerColor,
              pageBreakInside: "avoid",
              breakInside: "avoid",
            }}
          >
            <div className="flex items-center">
              <div
                className="w-24 h-24 bg-gray-600 rounded-lg flex items-center justify-center mr-6 cursor-pointer hover:bg-gray-700 transition-colors relative group"
                onClick={triggerFileSelect}
                title="Click to upload profile image"
              >
                {formData.profileImage ? (
                  <img
                    src={formData.profileImage}
                    alt="Profile"
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <Camera className="w-8 h-8 text-white" />
                )}
                {isUploadingAvatar && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                    <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <Camera className="w-6 h-6 text-white" />
                </div>
              </div>
              <div>
                <TiptapEditableField
                  field="fullName"
                  value={formData.fullName}
                  className="text-2xl font-bold text-blue-600 mb-1"
                  placeholder="Full Name"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "fullName"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "fullName"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
                <TiptapEditableField
                  field="targetPosition"
                  value={formData.targetPosition}
                  className="text-lg text-blue-600"
                  placeholder="Target Position"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "targetPosition"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "targetPosition"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-1">
              <div className="text-right">
                <div className="space-y-1">
                  <div className="flex items-center">
                    <div className="w-5 flex justify-center mr-2">
                      <Phone className="w-4 h-4 text-blue-600" />
                    </div>
                    <TiptapEditableField
                      field="phoneNumber"
                      value={formData.phoneNumber}
                      className="text-gray-600 flex-1"
                      placeholder="Phone Number"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      borderColor="border-blue-500"
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "phoneNumber"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "phoneNumber"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                  <div className="flex items-center">
                    <div className="w-5 flex justify-center mr-2">
                      <Mail className="w-4 h-4 text-blue-600" />
                    </div>
                    <TiptapEditableField
                      field="emailAddress"
                      value={formData.emailAddress}
                      className="text-gray-600 flex-1"
                      placeholder="Email Address"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      borderColor="border-blue-500"
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "emailAddress"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "emailAddress"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                  <div className="flex items-center">
                    <div className="w-5 flex justify-center mr-2">
                      <MapPin className="w-4 h-4 text-blue-600" />
                    </div>
                    <TiptapEditableField
                      field="address"
                      value={formData.address}
                      className="text-gray-600 flex-1"
                      placeholder="Address"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      borderColor="border-blue-500"
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "address"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "address"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                  <div className="flex items-center">
                    <div className="w-5 flex justify-center mr-2">
                      <Link className="w-4 h-4 text-blue-600" />
                    </div>
                    <TiptapEditableField
                      field="linkedin"
                      value={formData.linkedin}
                      className="text-gray-600 flex-1"
                      placeholder="LinkedIn (optional)"
                      editingField={coverLetterState.editingField}
                      onEdit={coverLetterState.setEditingField}
                      onChange={handlers.handleInputChange}
                      borderColor="border-blue-500"
                      sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                        "linkedin"
                      )}
                      detailedFormatting={coverLetterState.getDetailedFormatting(
                        "linkedin"
                      )}
                      onDetailedFormattingChange={
                        coverLetterState.updateDetailedFormatting
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recipient Information */}
          <div className="space-y-1 mb-3">
            <TiptapEditableField
              field="hiringManagerName"
              value={formData.hiringManagerName}
              className="text-blue-600"
              placeholder={`${coverLetterState.labels.dear}: [Recipient Name]`}
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "hiringManagerName"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "hiringManagerName"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="hiringManagerTitle"
              value={formData.hiringManagerTitle}
              className="text-gray-600"
              placeholder={`${coverLetterState.labels.title}: [Position / Department]`}
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "hiringManagerTitle"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "hiringManagerTitle"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="companyName"
              value={formData.companyName}
              className="text-gray-600"
              placeholder={`${coverLetterState.labels.companyName}: [Company Name]`}
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "companyName"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "companyName"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="companyAddress"
              value={formData.companyAddress}
              className="text-gray-600"
              placeholder={`${coverLetterState.labels.address}: [Company Address]`}
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "companyAddress"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "companyAddress"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="applicationDate"
              value={formData.applicationDate}
              className="text-gray-600"
              placeholder={
                coverLetterState.isVietnamese
                  ? "Ngày: [Application Date]"
                  : "Date: [Application Date]"
              }
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "applicationDate"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "applicationDate"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="jobTitle"
              value={formData.jobTitle}
              className="text-gray-600"
              placeholder={`${coverLetterState.labels.applicationFor}: [Position]`}
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "jobTitle"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "jobTitle"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
          </div>

          {/* Letter Content */}
          <div className="space-y-2">
            {/* Greeting */}
            {!sectionManager.isFieldHidden("greeting") && (
              <div
                data-section="greeting"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="greeting"
                  value={
                    formData.greeting ||
                    (coverLetterState.isVietnamese
                      ? "Kính gửi Quý công ty,"
                      : "Dear Mr./Ms. [Name],")
                  }
                  className="text-justify"
                  placeholder={
                    coverLetterState.isVietnamese
                      ? "Kính gửi Quý công ty,"
                      : "Dear Mr./Ms. [Name],"
                  }
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "greeting"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("greeting")}
                  canMoveDown={sectionManager.canMoveDown("greeting")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "greeting"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden("greeting")}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "greeting"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "greeting"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Opening Paragraph */}
            {!sectionManager.isFieldHidden("openingParagraph") && (
              <div
                data-section="openingParagraph"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="openingParagraph"
                  value={formData.openingParagraph}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Opening paragraph"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "openingParagraph"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("openingParagraph")}
                  canMoveDown={sectionManager.canMoveDown("openingParagraph")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "openingParagraph"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "openingParagraph"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "openingParagraph"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "openingParagraph"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Experience Section */}
            {!sectionManager.isFieldHidden("experienceSection") && (
              <div
                data-section="experienceSection"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="experienceSection"
                  value={formData.experienceSection}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Experience section"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "experienceSection"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("experienceSection")}
                  canMoveDown={sectionManager.canMoveDown("experienceSection")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "experienceSection"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "experienceSection"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "experienceSection"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "experienceSection"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Skills Section */}
            {!sectionManager.isFieldHidden("skillsSection") && (
              <div
                data-section="skillsSection"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="skillsSection"
                  value={formData.skillsSection}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Skills section"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "skillsSection"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("skillsSection")}
                  canMoveDown={sectionManager.canMoveDown("skillsSection")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "skillsSection"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden("skillsSection")}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "skillsSection"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "skillsSection"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}

            {/* Closing Paragraph */}
            {!sectionManager.isFieldHidden("closingParagraph") && (
              <div
                data-section="closingParagraph"
                style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
              >
                <TiptapEditableField
                  field="closingParagraph"
                  value={formData.closingParagraph}
                  className="text-justify"
                  isTextarea={true}
                  placeholder="Closing paragraph"
                  editingField={coverLetterState.editingField}
                  onEdit={coverLetterState.setEditingField}
                  onChange={handlers.handleInputChange}
                  borderColor="border-blue-500"
                  showBubbleMenu={sectionManager.shouldShowBubbleMenu(
                    "closingParagraph"
                  )}
                  onToggleVisibility={sectionManager.toggleVisibility}
                  onMoveUpSection={sectionManager.moveUp}
                  onMoveDownSection={sectionManager.moveDown}
                  canMoveUp={sectionManager.canMoveUp("closingParagraph")}
                  canMoveDown={sectionManager.canMoveDown("closingParagraph")}
                  canToggleVisibility={sectionManager.canToggleVisibility(
                    "closingParagraph"
                  )}
                  isFieldHidden={sectionManager.isFieldHidden(
                    "closingParagraph"
                  )}
                  sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                    "closingParagraph"
                  )}
                  detailedFormatting={coverLetterState.getDetailedFormatting(
                    "closingParagraph"
                  )}
                  onDetailedFormattingChange={
                    coverLetterState.updateDetailedFormatting
                  }
                />
              </div>
            )}
          </div>

          {/* Signature */}
          <div
            className="mt-4 text-left"
            data-section="signature"
            style={{ pageBreakInside: "avoid", breakInside: "avoid" }}
          >
            <TiptapEditableField
              field="signatureClosing"
              value={
                formData.signatureClosing ||
                (coverLetterState.isVietnamese ? "Trân trọng," : "Sincerely,")
              }
              className="block text-gray-600 mb-1"
              placeholder={
                coverLetterState.isVietnamese ? "Trân trọng," : "Sincerely,"
              }
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "signatureClosing"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "signatureClosing"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
            <TiptapEditableField
              field="signatureName"
              value={formData.signatureName}
              className="block font-semibold text-gray-800"
              placeholder="Your Full Name"
              editingField={coverLetterState.editingField}
              onEdit={coverLetterState.setEditingField}
              onChange={handlers.handleInputChange}
              borderColor="border-blue-500"
              sectionFormatting={coverLetterState.getCurrentSectionFormatting(
                "signatureName"
              )}
              detailedFormatting={coverLetterState.getDetailedFormatting(
                "signatureName"
              )}
              onDetailedFormattingChange={
                coverLetterState.updateDetailedFormatting
              }
            />
          </div>
        </div>
      </div>

      {/* Save Success Modal */}
      <SaveSuccessModal
        isOpen={showSuccessModal}
        onClose={closeSuccessModal}
        coverLetterFileUrl={coverLetterFileUrl}
        onEdit={handleEdit}
        coverLetterId={coverLetterState.coverLetterId}
      />

      {/* Add Sections Modal */}
      <AddSectionsModal
        isOpen={coverLetterState.showAddSectionsModal}
        onClose={() => coverLetterState.setShowAddSectionsModal(false)}
        hiddenSections={sectionManager.hiddenSections}
        onToggleSection={sectionManager.toggleVisibility}
      />
    </div>
  );
};

// Wrapper component with TiptapProvider
const CoverLetterTemp2 = (props: CoverLetterTemp2Props) => {
  return (
    <TiptapProvider>
      <CoverLetterTemp2Content {...props} />
    </TiptapProvider>
  );
};

export default CoverLetterTemp2;
