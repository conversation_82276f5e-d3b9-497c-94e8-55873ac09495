"use client";
import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import {
  CoverLetterModalProps,
  CoverLetterAnalysisType,
} from "../cover-letter";
import { useFileUpload } from "@/hooks/useFileUpload";
import {
  useCreateCoverLetterByRoleMutation,
  useCreateCoverLetterByJdMutation,
} from "@/services/api/cover-letter";
import { setCurrentCoverLetterId } from "@/redux/slices/coverLetterSlice";
import {
  CoverLetterModalHeader,
  CoverLetterAnalysisTypeSelect,
  CoverLetterModalActions,
} from "./CoverLetterModalForm";
import { CoverLetterRoleForm, CoverLetterJDForm } from "./CoverLetterForm";

export default function CoverLetterModal({
  open,
  onOpenChange,
}: Readonly<CoverLetterModalProps>) {
  const router = useRouter();
  const dispatch = useDispatch();
  const [isGenerating, setIsGenerating] = useState(false);
  const [analysisType, setAnalysisType] = useState<CoverLetterAnalysisType>("");
  const [roleDescription, setRoleDescription] = useState("");
  const [language, setLanguage] = useState<"Vietnamese" | "English">("English");
  const [cvFile, setCvFile] = useState<File | null>(null);
  const [jdFile, setJdFile] = useState<File | null>(null);

  const { handleFileUpload, handleDragOver, handleDrop } = useFileUpload();

  // RTK Query mutations
  const [createCoverLetterByRole] = useCreateCoverLetterByRoleMutation();
  const [createCoverLetterByJd] = useCreateCoverLetterByJdMutation();

  const onFileUpload = useCallback(
    (file: File | null, type: "cv" | "jd") => {
      if (type === "cv") {
        handleFileUpload(file, type, setCvFile);
      } else {
        handleFileUpload(file, type, setJdFile);
      }
    },
    [handleFileUpload]
  );

  const onDrop = useCallback(
    (e: React.DragEvent, type: "cv" | "jd") => {
      handleDrop(e, type, onFileUpload);
    },
    [handleDrop, onFileUpload]
  );

  const resetForm = () => {
    setAnalysisType("");
    setRoleDescription("");
    setLanguage("English");
    setCvFile(null);
    setJdFile(null);
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const handleGenerate = async () => {
    setIsGenerating(true);
    try {
      let result;

      if (analysisType === "role" && cvFile) {
        // Create FormData for role-based analysis
        const formData = new FormData();
        formData.append("agentType", "AI_COVER_LETTER_ROLE");
        formData.append("roleDescription", roleDescription);
        formData.append("language", language.toLowerCase());
        formData.append("cvFile", cvFile);

        result = await createCoverLetterByRole(formData).unwrap();
      } else if (analysisType === "jd" && cvFile && jdFile) {
        // Create FormData for JD-based analysis
        const formData = new FormData();
        formData.append("agentType", "AI_COVER_LETTER_JD");
        formData.append("language", language.toLowerCase());
        formData.append("cvFile", cvFile);
        formData.append("jdFile", jdFile);

        result = await createCoverLetterByJd(formData).unwrap();
      } else {
        throw new Error("Invalid form data");
      }

      if (result && result._id) {
        toast.success("Cover letter generated successfully!");

        // Store the cover letter ID in Redux
        dispatch(setCurrentCoverLetterId(result._id));

        // Close modal and redirect to templates page with the generated cover letter ID
        onOpenChange(false);
        resetForm();

        // Pass the cover letter ID via URL query parameter
        router.push(`/cover-letter/templates?id=${result._id}`);
      } else {
        throw new Error("Failed to generate cover letter");
      }
    } catch (error: any) {
      toast.error(
        error?.message || "Failed to generate cover letter. Please try again."
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const isFormValid = () => {
    if (analysisType === "role") {
      return !!(roleDescription.trim() && language && cvFile);
    } else if (analysisType === "jd") {
      return !!(language && cvFile && jdFile);
    }
    return false;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-white/95 backdrop-blur-xl border-0 shadow-2xl">
        <CoverLetterModalHeader />

        <div className="space-y-8">
          <CoverLetterAnalysisTypeSelect
            analysisType={analysisType}
            onAnalysisTypeChange={setAnalysisType}
          />

          <AnimatePresence mode="wait">
            {analysisType === "role" && (
              <CoverLetterRoleForm
                roleDescription={roleDescription}
                onRoleDescriptionChange={setRoleDescription}
                language={language}
                onLanguageChange={setLanguage}
                cvFile={cvFile}
                onFileUpload={onFileUpload}
                onDragOver={handleDragOver}
                onDrop={onDrop}
              />
            )}

            {analysisType === "jd" && (
              <CoverLetterJDForm
                language={language}
                onLanguageChange={setLanguage}
                cvFile={cvFile}
                jdFile={jdFile}
                onFileUpload={onFileUpload}
                onDragOver={handleDragOver}
                onDrop={onDrop}
              />
            )}
          </AnimatePresence>
        </div>

        <CoverLetterModalActions
          isGenerating={isGenerating}
          isFormValid={isFormValid()}
          onGenerate={handleGenerate}
          onClose={handleClose}
        />
      </DialogContent>
    </Dialog>
  );
}
