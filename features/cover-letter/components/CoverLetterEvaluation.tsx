import React from "react";
import { motion } from "framer-motion";
import {
  Star,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  AlertCircle,
  Edit,
  Home,
  User,
  Phone,
  Mail,
  MessageSquare,
  FileText,
  Award,
  PenTool,
  Target,
  Sparkles,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { CoverLetterEvaluation as EvaluationType } from "@/services/api/cover-letter/types/cover-letter.d";

interface CoverLetterEvaluationProps {
  evaluation: EvaluationType;
  coverLetterId: string;
  pdfUrl?: string;
}

const CoverLetterEvaluation: React.FC<CoverLetterEvaluationProps> = ({
  evaluation,
  coverLetterId,
  pdfUrl,
}) => {
  const router = useRouter();

  const handleEdit = () => {
    router.push(`/cover-letter/${coverLetterId}`);
  };

  const handleBackToHome = () => {
    router.push("/cover-letter");
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-emerald-600";
    if (score >= 60) return "text-amber-600";
    return "text-rose-600";
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return "bg-emerald-50 border-emerald-200";
    if (score >= 60) return "bg-amber-50 border-amber-200";
    return "bg-rose-50 border-rose-200";
  };

  const getScoreGradient = (score: number) => {
    if (score >= 80) return "from-emerald-500 to-teal-600";
    if (score >= 60) return "from-amber-500 to-orange-600";
    return "from-rose-500 to-red-600";
  };

  const sectionNames = {
    header: "Header",
    contactInfo: "Contact Information",
    recipientInfo: "Recipient Information",
    greeting: "Greeting",
    opening: "Opening Paragraph",
    body: "Main Content",
    closing: "Closing Paragraph",
    signature: "Signature",
  };

  const sectionIcons = {
    header: User,
    contactInfo: Phone,
    recipientInfo: Mail,
    greeting: MessageSquare,
    opening: FileText,
    body: Award,
    closing: Target,
    signature: PenTool,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                Cover Letter Evaluation Results
              </h1>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={handleEdit}
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button
                variant="outline"
                onClick={handleBackToHome}
                className="border-slate-300 text-slate-700 hover:bg-white hover:shadow-md transition-all duration-200"
              >
                <Home className="w-4 h-4 mr-2" />
                Home
              </Button>
            </div>
          </div>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 items-start">
          {/* Left: PDF Preview */}
          <div className="w-full lg:w-1/2 bg-white rounded-2xl shadow-xl p-6 lg:sticky lg:top-8 border border-slate-200">
            <h2 className="text-xl font-semibold text-slate-900 mb-4 flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600" />
              Cover Letter PDF
            </h2>
            {pdfUrl ? (
              <div className="w-full h-[600px] border rounded-xl overflow-hidden bg-slate-100 shadow-inner">
                <iframe
                  src={`${pdfUrl}#toolbar=0&navpanes=0&scrollbar=0`}
                  className="w-full h-full"
                  title="Cover Letter PDF"
                  style={{ border: "none" }}
                />
              </div>
            ) : (
              <div className="w-full h-[600px] border rounded-xl flex items-center justify-center bg-slate-50">
                <div className="text-center text-slate-500">
                  <AlertCircle className="w-12 h-12 mx-auto mb-4 text-slate-400" />
                  <p>PDF not available</p>
                </div>
              </div>
            )}
          </div>

          {/* Right: Evaluation Results */}
          <div className="w-full lg:w-1/2 space-y-8">
            {/* Overall Score */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="relative bg-white rounded-2xl shadow-2xl p-8 border border-slate-200 overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-500/10 to-blue-500/10 rounded-full blur-2xl"></div>

              <div className="relative text-left">
                <div className="mb-6 text-center">
                  <div className="relative inline-block">
                    <div
                      className={`inline-flex items-center justify-center w-32 h-32 rounded-full ${getScoreBgColor(
                        evaluation.overallScore
                      )} border-4 border-white shadow-2xl relative overflow-hidden`}
                    >
                      <div
                        className={`absolute inset-0 bg-gradient-to-br ${getScoreGradient(
                          evaluation.overallScore
                        )} opacity-10`}
                      ></div>
                      <span
                        className={`text-4xl font-bold ${getScoreColor(
                          evaluation.overallScore
                        )} relative z-10`}
                      >
                        {evaluation.overallScore}
                      </span>
                    </div>
                    <div className="absolute -top-3 -right-3 animate-pulse">
                      <div className="relative">
                        <Star className="w-10 h-10 text-amber-400 fill-amber-400 drop-shadow-lg" />
                        <Sparkles className="w-4 h-4 text-amber-300 absolute -top-1 -right-1 animate-spin" />
                      </div>
                    </div>
                  </div>
                </div>

                <h3 className="text-2xl font-bold text-slate-900 mb-4 flex items-center justify-center gap-2">
                  <Target className="w-6 h-6 text-blue-600" />
                  Overall Score
                </h3>

                <div className="bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl p-6 shadow-inner border border-slate-100">
                  <p className="text-slate-700 leading-relaxed text-lg">
                    {evaluation.overallFeedback}
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Section Scores */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-2xl shadow-xl p-8 border border-slate-200"
            >
              <div className="flex items-center gap-3 mb-8">
                <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-lg">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-semibold text-slate-900">
                  Section Scores
                </h3>
              </div>

              <div className="grid gap-6">
                {Object.entries(evaluation.sections).map(
                  ([key, section], index) => {
                    const IconComponent =
                      sectionIcons[key as keyof typeof sectionIcons];
                    return (
                      <motion.div
                        key={key}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 + index * 0.05 }}
                        className="group relative bg-gradient-to-r from-slate-50 to-blue-50 rounded-xl p-6 border border-slate-200 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-4">
                            <div className="p-3 bg-white rounded-xl shadow-md group-hover:shadow-lg transition-shadow duration-300">
                              <IconComponent className="w-6 h-6 text-blue-600" />
                            </div>
                            <div>
                              <span className="font-semibold text-slate-900 text-lg">
                                {sectionNames[key as keyof typeof sectionNames]}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="relative">
                              <div
                                className={`w-16 h-16 rounded-full ${getScoreBgColor(
                                  section.score
                                )} border-3 border-white shadow-lg flex items-center justify-center`}
                              >
                                <span
                                  className={`text-lg font-bold ${getScoreColor(
                                    section.score
                                  )}`}
                                >
                                  {section.score}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white rounded-xl p-4 mb-4 shadow-sm border border-slate-100">
                          <p className="text-slate-700 leading-relaxed">
                            {section.feedback}
                          </p>
                        </div>

                        {section.suggestions.length > 0 && (
                          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200">
                            <div className="flex items-center gap-3 mb-3">
                              <div className="p-2 bg-blue-100 rounded-lg">
                                <CheckCircle className="w-5 h-5 text-blue-600" />
                              </div>
                              <strong className="text-blue-800 font-semibold">
                                Improvement Suggestions:
                              </strong>
                            </div>
                            <div className="space-y-2">
                              {section.suggestions.map(
                                (suggestion, suggestionIndex) => (
                                  <div
                                    key={suggestionIndex}
                                    className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm border border-blue-100 hover:shadow-md transition-shadow"
                                  >
                                    <div className="p-1 bg-blue-100 rounded-full mt-1">
                                      <ChevronRight className="w-3 h-3 text-blue-600" />
                                    </div>
                                    <span className="text-blue-700 text-sm leading-relaxed">
                                      {suggestion}
                                    </span>
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )}
                      </motion.div>
                    );
                  }
                )}
              </div>
            </motion.div>

            {/* Strengths */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="relative bg-white rounded-2xl shadow-xl p-8 border border-slate-200 overflow-hidden"
            >
              <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-full blur-3xl"></div>

              <div className="relative">
                <div className="flex items-center gap-4 mb-8">
                  <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl shadow-lg">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-semibold text-slate-900">
                    Strengths
                  </h3>
                </div>

                <div className="grid gap-4">
                  {evaluation.strengths.map((strength, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 + index * 0.1 }}
                      className="flex items-start gap-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-5 shadow-sm border border-emerald-200 hover:shadow-md transition-all duration-300"
                    >
                      <div className="p-2 bg-emerald-100 rounded-lg shadow-sm">
                        <TrendingUp className="w-5 h-5 text-emerald-600" />
                      </div>
                      <span className="text-slate-700 leading-relaxed flex-1">
                        {strength}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Improvements */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="relative bg-white rounded-2xl shadow-xl p-8 border border-slate-200 overflow-hidden"
            >
              <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-orange-500/10 to-red-500/10 rounded-full blur-3xl"></div>

              <div className="relative">
                <div className="flex items-center gap-4 mb-8">
                  <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg">
                    <AlertCircle className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-semibold text-slate-900">
                    Improvements
                  </h3>
                </div>

                <div className="grid gap-4">
                  {evaluation.improvements.map((improvement, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      className="flex items-start gap-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-5 shadow-sm border border-orange-200 hover:shadow-md transition-all duration-300"
                    >
                      <div className="p-2 bg-orange-100 rounded-lg shadow-sm">
                        <TrendingDown className="w-5 h-5 text-orange-600" />
                      </div>
                      <span className="text-slate-700 leading-relaxed flex-1">
                        {improvement}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoverLetterEvaluation;
