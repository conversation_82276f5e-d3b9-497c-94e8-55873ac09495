"use client";
import React, { useEffect, useRef, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import FontFamily from "@tiptap/extension-font-family";
import { Edit3 } from "lucide-react";
import { useTiptap } from "../../../providers/TiptapProvider";
import SectionBubbleMenu from "./SectionBubbleMenu";
import "@/styles/customeStyle/tiptap-styles.css";
import {
  extractDetailedFormatting,
  applyDetailedFormatting,
  DetailedFormatRange,
} from "@/lib/detailedFormattingUtils";

interface TiptapEditableFieldProps {
  field: string;
  value: string;
  className?: string;
  style?: React.CSSProperties;
  isTextarea?: boolean;
  placeholder?: string;
  editingField: string | null;
  onEdit: (field: string | null) => void;
  onChange: (field: string, value: string) => void;
  borderColor?: string;
  // BubbleMenu props
  showBubbleMenu?: boolean;
  onToggleVisibility?: (field: string) => void;
  onMoveUpSection?: (field: string) => void;
  onMoveDownSection?: (field: string) => void;
  canMoveUp?: boolean;
  canMoveDown?: boolean;
  canToggleVisibility?: boolean;
  isFieldHidden?: boolean;
  // Per-section formatting
  sectionFormatting?: {
    textAlign?: "left" | "center" | "right" | "justify";
    isBold?: boolean;
    isItalic?: boolean;
    isUnderline?: boolean;
    isHidden?: boolean;
  };
  // Detailed character-level formatting
  detailedFormatting?: DetailedFormatRange[];
  onDetailedFormattingChange?: (
    field: string,
    ranges: DetailedFormatRange[]
  ) => void;
}

const TiptapEditableField: React.FC<TiptapEditableFieldProps> = ({
  field,
  value,
  className = "",
  style,
  isTextarea = false,
  placeholder = "",
  editingField,
  onEdit,
  onChange,
  borderColor = "border-orange-500",
  showBubbleMenu = false,
  onToggleVisibility,
  onMoveUpSection,
  onMoveDownSection,
  canMoveUp = false,
  canMoveDown = false,
  canToggleVisibility = false,
  isFieldHidden = false,
  sectionFormatting = {},
  detailedFormatting = [],
  onDetailedFormattingChange,
}) => {
  const { setActiveEditor, setActiveField, editorRefs } = useTiptap();
  const isEditing = editingField === field;
  const containerRef = useRef<HTMLDivElement>(null);
  const [showMenu, setShowMenu] = useState(false);
  const isApplyingFormatting = useRef(false);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextStyle.configure({
        HTMLAttributes: {
          style: "font-size: inherit; line-height: inherit;",
        },
      }),
      FontFamily.configure({
        types: ["textStyle"],
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
      }),
    ],
    content: (() => {
      // If we have real content (not empty and not just placeholder), use it
      if (value && value.trim() !== "" && value !== `<p>${placeholder}</p>`) {
        // Check if the value is HTML (from old data) or plain text (new format)
        if (
          value.includes("<p>") ||
          value.includes("<strong>") ||
          value.includes("<em>") ||
          value.includes("<u>")
        ) {
          // It's HTML content (legacy data) - use as is
          return value;
        } else {
          // It's plain text (new format) - wrap in paragraph for display
          return `<p>${value}</p>`;
        }
      }
      // Otherwise show placeholder only when editing
      return isEditing ? `<p>${placeholder}</p>` : "<p></p>";
    })(),
    editable: isEditing,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const text = editor.getText();

      // Don't save placeholder content or empty content
      // Only clear if it's truly empty or exactly matches unformatted placeholder
      if (html === "<p></p>" || html === `<p>${placeholder}</p>`) {
        onChange(field, "");
        // Clear detailed formatting for empty content
        if (onDetailedFormattingChange && !isApplyingFormatting.current) {
          onDetailedFormattingChange(field, []);
        }
      } else {
        // Always save plain text to database (strip HTML tags)
        // The formatting is preserved in the UI through Tiptap, but database stores clean text
        onChange(field, text);

        // Extract and save detailed formatting only when user is editing
        // Skip if we're applying formatting to prevent infinite loops
        if (
          onDetailedFormattingChange &&
          isEditing &&
          !isApplyingFormatting.current
        ) {
          const ranges = extractDetailedFormatting(field, html, text);
          onDetailedFormattingChange(field, ranges);
        }
      }
    },
    onFocus: () => {
      setActiveEditor(editor);
      setActiveField(field);
    },
    onBlur: () => {
      // Delay to allow toolbar interactions
      setTimeout(() => {
        if (editor && !editor.isFocused) {
          // Check if user is interacting with toolbar by checking if any toolbar button is being clicked
          const activeElement = document.activeElement;
          const isToolbarInteraction =
            activeElement &&
            (activeElement.closest(".cover-letter-toolbar") ||
              activeElement.closest("[data-toolbar-button]") ||
              activeElement.tagName === "BUTTON");

          // Only unfocus if it's not a toolbar interaction
          if (!isToolbarInteraction) {
            onEdit(null);
            setActiveEditor(null);
            setActiveField(null);
          }
        }
      }, 150); // Increased delay to 150ms for better toolbar interaction handling
    },
  });

  // Apply section formatting when editor is created or formatting changes
  useEffect(() => {
    if (editor && sectionFormatting && editor.isEditable) {
      try {
        if (
          sectionFormatting.textAlign &&
          editor.can().setTextAlign(sectionFormatting.textAlign)
        ) {
          editor.chain().setTextAlign(sectionFormatting.textAlign).run();
        }
      } catch (error) {
        // Error applying section formatting - silently continue
      }
    }
  }, [editor, sectionFormatting?.textAlign]);

  const detailedFormattingApplied = useRef(false);
  const lastAppliedDetailedFormatting = useRef<string>("");
  const wasJustEditing = useRef(false);
  useEffect(() => {
    if (!isEditing && wasJustEditing.current) {
      setTimeout(() => {
        wasJustEditing.current = false;
      }, 500);
    } else if (isEditing) {
      wasJustEditing.current = true;
      lastAppliedDetailedFormatting.current = "";
    }
  }, [isEditing]);

  useEffect(() => {
    if (
      editor &&
      detailedFormatting &&
      value &&
      !isEditing &&
      !wasJustEditing.current && // Don't apply if user just finished editing
      detailedFormatting.length > 0 &&
      !isApplyingFormatting.current
    ) {
      // Create a unique key for this formatting state
      const formattingKey = `${field}-${JSON.stringify(
        detailedFormatting
      )}-${value}`;

      // Only apply if this exact formatting hasn't been applied before
      if (lastAppliedDetailedFormatting.current !== formattingKey) {
        try {
          isApplyingFormatting.current = true;
          lastAppliedDetailedFormatting.current = formattingKey;

          // Apply detailed character-level formatting
          applyDetailedFormatting(editor, detailedFormatting);

          // Reset flag after formatting is applied
          setTimeout(() => {
            isApplyingFormatting.current = false;
          }, 100);
        } catch (error) {
          // Error applying detailed formatting - silently continue
          isApplyingFormatting.current = false;
          lastAppliedDetailedFormatting.current = "";
        }
      }
    }
  }, [editor, value, detailedFormatting, isEditing, field]); // Include all relevant dependencies

  // Fallback: Apply section-level formatting if no detailed formatting exists
  // Track if section formatting has been applied
  const sectionFormattingApplied = useRef(false);

  // Reset section formatting applied flag when section formatting is cleared
  useEffect(() => {
    if (
      sectionFormatting &&
      !sectionFormatting.isBold &&
      !sectionFormatting.isItalic &&
      !sectionFormatting.isUnderline
    ) {
      sectionFormattingApplied.current = false;
    }
  }, [sectionFormatting]);

  useEffect(() => {
    if (
      editor &&
      sectionFormatting &&
      value &&
      !isEditing &&
      (!detailedFormatting || detailedFormatting.length === 0) &&
      !isApplyingFormatting.current &&
      !sectionFormattingApplied.current &&
      !detailedFormattingApplied.current
    ) {
      try {
        // Only apply if we have formatting to apply and content exists
        if (
          (sectionFormatting.isBold ||
            sectionFormatting.isItalic ||
            sectionFormatting.isUnderline) &&
          value.trim() !== ""
        ) {
          isApplyingFormatting.current = true;
          sectionFormattingApplied.current = true;

          // Select all content first
          const { from, to } = { from: 0, to: editor.state.doc.content.size };
          let chain = editor.chain().setTextSelection({ from, to });

          // Apply formatting based on saved state
          if (sectionFormatting.isBold) {
            chain = chain.toggleBold();
          }
          if (sectionFormatting.isItalic) {
            chain = chain.toggleItalic();
          }
          if (sectionFormatting.isUnderline) {
            chain = chain.toggleUnderline();
          }

          // Execute the chain and then clear selection
          chain.run();
          // Clear selection to avoid having all text selected
          editor.commands.setTextSelection(editor.state.doc.content.size);

          // Reset flag after formatting is applied
          setTimeout(() => {
            isApplyingFormatting.current = false;
          }, 100);
        }
      } catch (error) {
        // Error applying section-level formatting - silently continue
        isApplyingFormatting.current = false;
        sectionFormattingApplied.current = false;
      }
    }

    // Reset when editing starts
    if (isEditing) {
      sectionFormattingApplied.current = false;
    }
  }, [editor, value, isEditing]); // Include isEditing to reset flag

  // Get computed styles based on section formatting
  const getComputedStyles = () => {
    let styles: React.CSSProperties = {
      fontSize: "inherit",
      lineHeight: "inherit",
      fontFamily: "inherit",
      ...style,
    };

    if (sectionFormatting && typeof sectionFormatting === "object") {
      // Only apply text alignment as CSS - this affects the whole field
      if (sectionFormatting.textAlign) {
        styles.textAlign = sectionFormatting.textAlign;
      }
      // Note: Bold, italic, underline are now handled by Tiptap for selected text only
      // We don't apply them as CSS styles to avoid field-level formatting
    }

    return styles;
  };

  // Store editor reference
  useEffect(() => {
    if (editor) {
      editorRefs.current[field] = editor;
    }
    return () => {
      if (editorRefs.current[field]) {
        delete editorRefs.current[field];
      }
    };
  }, [editor, field, editorRefs]);

  // Update editor content when value changes - but only when not editing to avoid disrupting user input
  useEffect(() => {
    if (editor && !isEditing) {
      const currentText = editor.getText();

      let newContent;
      let expectedText;

      // If we have real content (not empty and not just placeholder), use it
      if (value && value.trim() !== "" && value !== `<p>${placeholder}</p>`) {
        // Check if the value is HTML (from old data) or plain text (new format)
        if (
          value.includes("<p>") ||
          value.includes("<strong>") ||
          value.includes("<em>") ||
          value.includes("<u>")
        ) {
          // It's HTML content (legacy data) - use as is
          newContent = value;
          // Extract expected text from the HTML value
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = value;
          expectedText = tempDiv.textContent || tempDiv.innerText || "";
        } else {
          // It's plain text (new format) - wrap in paragraph for display
          newContent = `<p>${value}</p>`;
          expectedText = value;
        }
      } else {
        newContent = "<p></p>";
        expectedText = "";
      }

      // Only update if the text content is actually different (ignore HTML structure differences)
      if (currentText.trim() !== expectedText.trim()) {
        editor.commands.setContent(newContent);
      }
    }
  }, [value, editor, placeholder, isEditing]);

  // Update editor editable state
  useEffect(() => {
    if (editor) {
      editor.setEditable(isEditing);
    }
  }, [isEditing, editor]);

  const handleClick = () => {
    // Show menu immediately when clicked
    if (showBubbleMenu) {
      setShowMenu(true);
    }

    if (!isEditing) {
      onEdit(field);
      setTimeout(() => {
        if (editor) {
          editor.commands.focus();
        }
      }, 0);
    }
  };

  // Hide menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setShowMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Hide menu when editing stops
  useEffect(() => {
    if (!isEditing) {
      setShowMenu(false);
    }
  }, [isEditing]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isTextarea && e.key === "Enter" && e.ctrlKey) {
      onEdit(null);
    } else if (!isTextarea && e.key === "Enter") {
      e.preventDefault();
      onEdit(null);
    }
  };

  return (
    <div className="group relative" ref={containerRef}>
      {/* Custom Section Menu - shows when clicked */}
      {showBubbleMenu &&
        showMenu &&
        onToggleVisibility &&
        onMoveUpSection &&
        onMoveDownSection && (
          <SectionBubbleMenu
            field={field}
            onToggleVisibility={(field) => {
              onToggleVisibility(field);
              setShowMenu(false);
            }}
            onMoveUp={(field) => {
              onMoveUpSection(field);
              setShowMenu(false);
            }}
            onMoveDown={(field) => {
              onMoveDownSection(field);
              setShowMenu(false);
            }}
            canMoveUp={canMoveUp}
            canMoveDown={canMoveDown}
            canToggleVisibility={canToggleVisibility}
            isVisible={showMenu}
            isFieldHidden={isFieldHidden}
          />
        )}

      {isEditing ? (
        <div
          className={`${className} border-2 ${borderColor} rounded p-2 focus-within:ring-2 focus-within:ring-blue-200`}
          style={getComputedStyles()}
          onKeyDown={handleKeyDown}
          data-field={field}
        >
          <EditorContent
            editor={editor}
            className="prose prose-sm max-w-none focus:outline-none tiptap-editor"
            style={getComputedStyles()}
          />
        </div>
      ) : (
        <div
          className={`${className} cursor-pointer hover:bg-gray-100 rounded p-2 pr-8 transition-colors relative`}
          style={getComputedStyles()}
          onClick={handleClick}
          data-field={field}
        >
          <EditorContent
            editor={editor}
            className="prose prose-sm max-w-none pointer-events-none tiptap-editor"
            style={getComputedStyles()}
          />
          <Edit3 className="w-4 h-4 text-gray-400 absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      )}
    </div>
  );
};

export default TiptapEditableField;
