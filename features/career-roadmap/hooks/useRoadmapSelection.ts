import { useState, useCallback } from 'react';
import { Node } from 'reactflow';

export const useRoadmapSelection = () => {
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);

  const handleNodeClick = useCallback((_event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedNode(null);
  }, []);

  const handlePaneClick = useCallback(() => {
    clearSelection();
  }, [clearSelection]);

  return {
    selectedNode,
    handleNodeClick,
    clearSelection,
    handlePaneClick,
  };
};
