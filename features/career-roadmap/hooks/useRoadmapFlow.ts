import { useCallback, useMemo } from "react";
import {
    useNodesState,
    useEdgesState,
    addEdge,
    Connection,
    Edge,
    Node,
    NodeChange,
    MarkerType,
} from "reactflow";
import { RoadmapNode, RoadmapEdge } from "@/common/types/career-roadmap";

interface UseRoadmapFlowProps {
    initialNodes: RoadmapNode[];
    initialEdges?: RoadmapEdge[];
    onNodePositionChange?: (
        nodeId: string,
        position: { x: number; y: number }
    ) => void;
}

export const useRoadmapFlow = ({
    initialNodes,
    initialEdges = [],
    onNodePositionChange,
}: UseRoadmapFlowProps) => {
    const convertToFlowNodes = useCallback(
        (roadmapNodes: RoadmapNode[]): Node[] => {
            return roadmapNodes.map((node) => ({
                id: node.id,
                type: "roadmapNode",
                position: node.position,
                data: {
                    ...node.data,
                    type: node.type,
                },
                draggable: true,
            }));
        },
        []
    );
    const convertToFlowEdges = useCallback(
        (roadmapEdges: RoadmapEdge[]): Edge[] => {
            return roadmapEdges.map((edge) => ({
                id: edge.id,
                source: edge.source,
                target: edge.target,
                type: "curved",
                animated: false,
                style: {
                    stroke: "#10b981",
                    strokeWidth: 2,
                    strokeLinecap: "round",
                },
                markerEnd: {
                    type: MarkerType.ArrowClosed,
                    color: "#10b981",
                    width: 16,
                    height: 16,
                },
            }));
        },
        []
    );

    const flowNodes = useMemo(
        () => convertToFlowNodes(initialNodes),
        [initialNodes, convertToFlowNodes]
    );
    const flowEdges = useMemo(
        () => convertToFlowEdges(initialEdges),
        [initialEdges, convertToFlowEdges]
    );

    const [nodes, setNodes, onNodesChange] = useNodesState(flowNodes);
    const [edges, setEdges, onEdgesChange] = useEdgesState(flowEdges);

    const onConnect = useCallback(
        (params: Edge | Connection) => setEdges((eds) => addEdge(params, eds)),
        [setEdges]
    );

    const handleNodesChange = useCallback(
        (changes: NodeChange[]) => {
            onNodesChange(changes);

            changes.forEach((change) => {
                if (
                    change.type === "position" &&
                    change.position &&
                    onNodePositionChange
                ) {
                    onNodePositionChange(change.id, change.position);
                }
            });
        },
        [onNodesChange, onNodePositionChange]
    );

    useMemo(() => {
        const newFlowNodes = convertToFlowNodes(initialNodes);
        setNodes(newFlowNodes);
    }, [initialNodes, convertToFlowNodes, setNodes]);

    useMemo(() => {
        const newFlowEdges = convertToFlowEdges(initialEdges);
        setEdges(newFlowEdges);
    }, [initialEdges, convertToFlowEdges, setEdges]);

    return {
        nodes,
        edges,
        onNodesChange: handleNodesChange,
        onEdgesChange,
        onConnect,
        setNodes,
        setEdges,
    };
};
