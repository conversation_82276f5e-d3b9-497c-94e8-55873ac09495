import { useState, useEffect } from "react";

interface UseImagePreviewProps {
    cvFileUrl?: string;
    jdFileUrl?: string;
}

export const useImagePreview = ({ cvFileUrl, jdFileUrl }: UseImagePreviewProps) => {
    const [selectedType, setSelectedType] = useState<"cv" | "jd">("cv");
    const [imageError, setImageError] = useState<{ cv: boolean; jd: boolean }>({
        cv: false,
        jd: false,
    });
    const [isLoading, setIsLoading] = useState<{ cv: boolean; jd: boolean }>({
        cv: true,
        jd: true,
    });
    const [pdfError, setPdfError] = useState<{ cv: boolean; jd: boolean }>({
        cv: false,
        jd: false,
    });
    const [pdfLoaded, setPdfLoaded] = useState<{ cv: boolean; jd: boolean }>({
        cv: false,
        jd: false,
    });
    const [showPdfLoader, setShowPdfLoader] = useState<{ cv: boolean; jd: boolean }>({
        cv: false,
        jd: false,
    });
    const [downloadLoading, setDownloadLoading] = useState(false);

    useEffect(() => {
        setSelectedType("cv");
        setImageError({ cv: false, jd: false });
        setIsLoading({ cv: true, jd: true });
        setPdfError({ cv: false, jd: false });
        setPdfLoaded({ cv: false, jd: false });
        setShowPdfLoader({ cv: false, jd: false });
        setDownloadLoading(false);
    }, [cvFileUrl, jdFileUrl]);

    useEffect(() => {
        const cvTimer = setTimeout(() => {
            if (!pdfLoaded.cv && cvFileUrl && !pdfError.cv) {
                setShowPdfLoader(prev => ({ ...prev, cv: true }));
            }
        }, 1000);

        const jdTimer = setTimeout(() => {
            if (!pdfLoaded.jd && jdFileUrl && !pdfError.jd) {
                setShowPdfLoader(prev => ({ ...prev, jd: true }));
            }
        }, 1000);

        return () => {
            clearTimeout(cvTimer);
            clearTimeout(jdTimer);
        };
    }, [cvFileUrl, jdFileUrl, pdfLoaded.cv, pdfLoaded.jd, pdfError.cv, pdfError.jd]);

    useEffect(() => {
        const timer = setTimeout(() => {
            setShowPdfLoader({ cv: false, jd: false });
        }, 10000);

        return () => clearTimeout(timer);
    }, [cvFileUrl, jdFileUrl]);

    const handleImageLoad = (type: "cv" | "jd") => {
        setIsLoading(prev => ({ ...prev, [type]: false }));
        setImageError(prev => ({ ...prev, [type]: false }));
    };

    const handleImageError = (type: "cv" | "jd") => {
        setIsLoading(prev => ({ ...prev, [type]: false }));
        setImageError(prev => ({ ...prev, [type]: true }));
    };

    const handlePdfLoad = (type: "cv" | "jd") => {
        setPdfLoaded(prev => ({ ...prev, [type]: true }));
        setShowPdfLoader(prev => ({ ...prev, [type]: false }));
        setPdfError(prev => ({ ...prev, [type]: false }));
    };

    const handlePdfError = (type: "cv" | "jd") => {
        setPdfError(prev => ({ ...prev, [type]: true }));
        setShowPdfLoader(prev => ({ ...prev, [type]: false }));
    };

    const handleDownload = async (url: string, filename: string) => {
        setDownloadLoading(true);
        try {
            const response = await fetch(url);
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = downloadUrl;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(downloadUrl);
        } catch (error) {
            console.error("Download failed:", error);
        } finally {
            setDownloadLoading(false);
        }
    };

    const getCurrentUrl = () => {
        return selectedType === "cv" ? cvFileUrl : jdFileUrl;
    };

    const isCurrentPdf = () => {
        const url = getCurrentUrl();
        return url?.toLowerCase().includes(".pdf");
    };

    const hasFiles = () => {
        return Boolean(cvFileUrl || jdFileUrl);
    };

    return {
        selectedType,
        setSelectedType,
        imageError,
        isLoading,
        pdfError,
        pdfLoaded,
        showPdfLoader,
        downloadLoading,
        
        handleImageLoad,
        handleImageError,
        handlePdfLoad,
        handlePdfError,
        handleDownload,
        
        getCurrentUrl,
        isCurrentPdf,
        hasFiles,
    };
};
