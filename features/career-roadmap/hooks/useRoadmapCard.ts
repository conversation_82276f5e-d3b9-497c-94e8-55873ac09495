import { useRouter } from "next/navigation";

export const useRoadmapCard = () => {
    const router = useRouter();    const formatDate = (date: Date) => {
        return new Date(date).toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        });
    };

    const handleViewDetails = (roadmapId: string) => {
        router.push(`/career-roadmap/${roadmapId}`);
    };    const getAgentTypeBadge = (agentType: string) => {
        console.log('Agent Type received in hook:', agentType, 'Type of:', typeof agentType);
        
        switch (agentType) {
            case "AI_ROADMAP_AI_ROLE":
                return {
                    label: "By Role",
                    variant: "default" as const,
                    icon: "briefcase",
                };
            case "AI_ROADMAP_AI_JD":
                return {
                    label: "By JD",
                    variant: "secondary" as const,
                    icon: "fileText",
                };
            case "ROADMAP_AI_TYPE_BY_ROLE_DESCRIPTION":
                return {
                    label: "By Role",
                    variant: "default" as const,
                    icon: "briefcase",
                };
            case "ROADMAP_AI_TYPE_BY_JOB_DESCRIPTION":
                return {
                    label: "By JD",
                    variant: "secondary" as const,
                    icon: "fileText",
                };
            default:
                console.log('Unknown agent type in hook:', agentType);
                return {
                    label: "Unknown",
                    variant: "outline" as const,
                    icon: "fileText",
                };
        }
    };

    return {
        formatDate,
        handleViewDetails,
        getAgentTypeBadge,
    };
};
