import { useState } from "react";
import { toast } from "sonner";
import {
    useCreateRoadmapByRoleMutation,
    useCreateRoadmapByJdMutation,
} from "@/services/api/career-roadmap/hooks";
import {
    createRoadmapByRoleFormData,
    createRoadmapByJdFormData,
} from "@/services/api/career-roadmap/helpers";

export const useCreateRoadmapModal = (onSuccess: () => void, onClose: () => void) => {
    const [activeTab, setActiveTab] = useState("role");
    
    const [roleDescription, setRoleDescription] = useState("");
    const [roleCvFile, setRoleCvFile] = useState<File | null>(null);
    
    const [jdCvFile, setJdCvFile] = useState<File | null>(null);
    const [jdFile, setJdFile] = useState<File | null>(null);

    const [createRoadmapByRole, { isLoading: isRoleLoading }] =
        useCreateRoadmapByRoleMutation();
    const [createRoadmapByJd, { isLoading: isJdLoading }] =
        useCreateRoadmapByJdMutation();

    const resetForm = () => {
        setActiveTab("role");
        setRoleDescription("");
        setRoleCvFile(null);
        setJdCvFile(null);
        setJdFile(null);
    };    const handleRoleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!roleDescription.trim() || !roleCvFile) return;

        try {
            const formData = createRoadmapByRoleFormData({
                roleDescription,
                cvFile: roleCvFile,
            });

            await createRoadmapByRole(formData).unwrap();
            toast.success("Career roadmap created successfully!", {
                description: "Your roadmap is ready to view and follow.",
            });
            resetForm();
            onSuccess();
            onClose();
        } catch (error) {
            console.error("Error creating roadmap by role:", error);
            toast.error("Failed to create roadmap", {
                description: "Please try again or contact support if the problem persists.",
            });
        }
    };    const handleJdSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!jdCvFile || !jdFile) return;

        try {
            const formData = createRoadmapByJdFormData({
                cvFile: jdCvFile,
                jdFile,
            });

            await createRoadmapByJd(formData).unwrap();
            toast.success("Career roadmap created successfully!", {
                description: "Your roadmap is ready to view and follow.",
            });
            resetForm();
            onSuccess();
            onClose();
        } catch (error) {
            console.error("Error creating roadmap by JD:", error);
            toast.error("Failed to create roadmap", {
                description: "Please try again or contact support if the problem persists.",
            });
        }
    };

    return {
        activeTab,
        setActiveTab,
        roleDescription,
        setRoleDescription,
        roleCvFile,
        setRoleCvFile,
        jdCvFile,
        setJdCvFile,
        jdFile,
        setJdFile,
        
        isRoleLoading,
        isJdLoading,
        
        handleRoleSubmit,
        handleJdSubmit,
        resetForm,
    };
};
