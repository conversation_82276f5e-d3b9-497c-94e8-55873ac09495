import React from 'react';
import {
  BaseEdge,
  EdgeProps,
  getBezierPath,
} from 'reactflow';

export default function CurvedEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  selected,
  animated,
  ...restProps
}: EdgeProps) {
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    curvature: 0.25,
  });

  const edgeStyle = {
    stroke: selected ? '#4F46E5' : '#6366F1',
    strokeWidth: selected ? 3 : 2.5,
    strokeLinecap: 'round' as const,
    strokeLinejoin: 'round' as const,
    filter: 'drop-shadow(0 1px 3px rgba(99, 102, 241, 0.1))',
    transition: 'all 0.2s ease-in-out',
    strokeDasharray: animated ? '5 5' : 'none',
    ...style,
  };

  return (
    <BaseEdge 
      path={edgePath} 
      markerEnd={markerEnd}
      style={edgeStyle}
      {...restProps}
    />
  );
}
