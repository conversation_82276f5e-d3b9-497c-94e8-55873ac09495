"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { RoadmapNode } from "@/common/types/career-roadmap";
import { Target, Clock } from "lucide-react";

interface RoadmapPreviewProps {
    nodes: RoadmapNode[];
    className?: string;
}

export default function RoadmapPreview({
    nodes,
    className = "",
}: RoadmapPreviewProps) {
    const getNodeColor = (type: string) => {
        switch (type.toLowerCase()) {
            case "milestone":
                return "#3B82F6";
            case "skill":
                return "#10B981";
            case "course":
                return "#F59E0B";
            case "project":
                return "#8B5CF6";
            default:
                return "#6B7280";
        }
    };

    const validNodes =
        nodes?.filter(
            (node) =>
                node.position &&
                typeof node.position.x === "number" &&
                typeof node.position.y === "number" &&
                !isNaN(node.position.x) &&
                !isNaN(node.position.y)
        ) || [];

    if (validNodes.length === 0) {
        return (
            <Card className={className}>
                <CardContent className="h-40 flex items-center justify-center">
                    <div className="text-center text-gray-400">
                        <Target className="w-8 h-8 mx-auto mb-2" />
                        <p className="text-sm">No roadmap yet</p>
                    </div>
                </CardContent>
            </Card>
        );
    }

    const positions = validNodes.map((node) => node.position);
    const minX = Math.min(...positions.map((p) => p.x));
    const maxX = Math.max(...positions.map((p) => p.x));
    const minY = Math.min(...positions.map((p) => p.y));
    const maxY = Math.max(...positions.map((p) => p.y));

    const width = Math.max(maxX - minX, 200);
    const height = Math.max(maxY - minY, 100);
    const padding = 30;

    const availableWidth = 300 - padding * 2;
    const availableHeight = 140 - padding * 2;
    const scaleX = availableWidth / width;
    const scaleY = availableHeight / height;
    const scale = Math.min(scaleX, scaleY, 0.8);

    return (
        <Card className={className}>
            <CardContent className="p-4">
                <div className="relative">
                    <svg
                        width="100%"
                        height="140"
                        viewBox="0 0 300 140"
                        className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded border"
                    >
                        {validNodes.slice(0, 8).map((node) => {
                            const x =
                                padding + (node.position.x - minX) * scale;
                            const y =
                                padding + (node.position.y - minY) * scale;
                            const color = getNodeColor(node.type);
                            const size = 20;

                            return (
                                <g key={node.id}>
                                    <rect
                                        x={x - size / 2 + 2}
                                        y={y - size / 2 + 2}
                                        width={size}
                                        height={size}
                                        rx="4"
                                        fill="rgba(0,0,0,0.15)"
                                    />

                                    <rect
                                        x={x - size / 2}
                                        y={y - size / 2}
                                        width={size}
                                        height={size}
                                        rx="4"
                                        fill={color}
                                        stroke="#FFFFFF"
                                        strokeWidth="2"
                                    />

                                    <circle
                                        cx={x - size / 2 + 5}
                                        cy={y - size / 2 + 5}
                                        r="2.5"
                                        fill="rgba(255,255,255,0.9)"
                                    />
                                </g>
                            );
                        })}
                        {validNodes.length > 8 && (
                            <text
                                x="250"
                                y="20"
                                fontSize="11"
                                fill="#6B7280"
                                fontWeight="600"
                            >
                                +{validNodes.length - 8} more
                            </text>
                        )}
                    </svg>
                    <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                                <Target className="w-3 h-3" />
                                <span>{validNodes.length} steps</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                <span>Visual roadmap</span>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
