"use client";

import { motion } from "framer-motion";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Upload, FileText, Briefcase, Loader2, Sparkles } from "lucide-react";
import { useCreateRoadmapModal } from "../hooks";

interface CreateRoadmapModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
}

export const CreateRoadmapModal = ({
    isOpen,
    onClose,
    onSuccess,
}: CreateRoadmapModalProps) => {
    const {
        activeTab,
        setActiveTab,
        roleDescription,
        setRoleDescription,
        roleCvFile,
        setRoleCvFile,
        jdCvFile,
        setJdCvFile,
        jdFile,
        setJdFile,
        isRoleLoading,
        isJdLoading,
        handleRoleSubmit,
        handleJdSubmit,
        resetForm,
    } = useCreateRoadmapModal(onSuccess, onClose);

    const handleClose = () => {
        resetForm();
        onClose();
    };    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-gradient-to-br from-blue-50 via-white to-indigo-50 border-none shadow-2xl">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                >
                    <DialogHeader className="text-center pb-6">
                        <div className="mx-auto w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mb-4">
                            <Sparkles className="h-6 w-6 text-white" />
                        </div>
                        <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Create New Career Roadmap
                        </DialogTitle>
                        <p className="text-gray-600 mt-2">
                            Let AI create a personalized roadmap for your career journey
                        </p>
                    </DialogHeader>
                    
                    <Tabs
                        value={activeTab}
                        onValueChange={setActiveTab}
                        className="w-full"
                    >
                        <TabsList className="grid w-full grid-cols-2 bg-white/50 backdrop-blur-sm">
                            <TabsTrigger
                                value="role"
                                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-500 data-[state=active]:text-white"
                            >
                                <Briefcase className="w-4 h-4" />
                                By Role Description
                            </TabsTrigger>
                            <TabsTrigger
                                value="jd"
                                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-500 data-[state=active]:text-white"
                            >
                                <FileText className="w-4 h-4" />
                                By Job Description
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="role" className="space-y-6 mt-6">
                            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                                <CardHeader className="text-center">
                                    <div className="mx-auto w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                                        <Briefcase className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <CardTitle className="text-xl text-gray-900">
                                        Create Roadmap by Role Description
                                    </CardTitle>
                                    <CardDescription className="text-gray-600">
                                        Upload your CV and describe your desired role for AI to create a suitable roadmap
                                    </CardDescription>
                                </CardHeader>                                <CardContent className="p-6">
                                    <form
                                        onSubmit={handleRoleSubmit}
                                        className="space-y-6"
                                    >
                                        <div className="space-y-3">
                                            <Label htmlFor="roleDescription" className="text-sm font-medium text-gray-700">
                                                Desired role description
                                            </Label>
                                            <Textarea
                                                id="roleDescription"
                                                placeholder="E.g.: I want to become a Senior Frontend Developer with experience in React, TypeScript and Next.js..."
                                                value={roleDescription}
                                                onChange={(e) =>
                                                    setRoleDescription(
                                                        e.target.value
                                                    )
                                                }
                                                rows={4}
                                                required
                                                className="border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                                            />
                                        </div>
                                        
                                        <div className="space-y-3">
                                            <Label htmlFor="roleCvFile" className="text-sm font-medium text-gray-700">
                                                Upload CV
                                            </Label>
                                            <div className="border-2 border-dashed border-blue-200 bg-blue-50/50 rounded-xl p-8 text-center hover:border-blue-300 transition-colors">
                                                <Upload className="w-10 h-10 text-blue-400 mx-auto mb-3" />
                                                <Input
                                                    id="roleCvFile"
                                                    type="file"
                                                    accept=".pdf,.doc,.docx"
                                                    onChange={(e) =>
                                                        setRoleCvFile(
                                                            e.target.files?.[0] ||
                                                                null
                                                        )
                                                    }
                                                    className="hidden"
                                                    required
                                                />
                                                <Label
                                                    htmlFor="roleCvFile"
                                                    className="cursor-pointer"
                                                >
                                                    {roleCvFile ? (
                                                        <div className="space-y-1">
                                                            <div className="text-green-600 font-medium">
                                                                ✓ File selected
                                                            </div>
                                                            <div className="text-sm text-gray-600 truncate max-w-full">
                                                                {roleCvFile.name}
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div className="space-y-1">
                                                            <div className="text-blue-600 font-medium">
                                                                Click to upload CV
                                                            </div>
                                                            <div className="text-sm text-gray-500">
                                                                PDF, DOC, DOCX files supported
                                                            </div>
                                                        </div>
                                                    )}
                                                </Label>
                                            </div>
                                        </div>
                                        
                                        <Button
                                            type="submit"
                                            className="w-full h-12 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                                            disabled={
                                                isRoleLoading ||
                                                !roleDescription.trim() ||
                                                !roleCvFile
                                            }
                                        >
                                            {isRoleLoading ? (
                                                <>
                                                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                                                    Creating roadmap...
                                                </>
                                            ) : (
                                                <>
                                                    <Sparkles className="w-5 h-5 mr-2" />
                                                    Create Roadmap
                                                </>
                                            )}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>

                        <TabsContent value="jd" className="space-y-6 mt-6">
                            <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                                <CardHeader className="text-center">
                                    <div className="mx-auto w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-3">
                                        <FileText className="h-5 w-5 text-green-600" />
                                    </div>
                                    <CardTitle className="text-xl text-gray-900">
                                        Create Roadmap by Job Description
                                    </CardTitle>
                                    <CardDescription className="text-gray-600">
                                        Upload CV and Job Description for AI to analyze and create a suitable roadmap
                                    </CardDescription>
                                </CardHeader>                                <CardContent className="p-6">
                                    <form
                                        onSubmit={handleJdSubmit}
                                        className="space-y-6"
                                    >
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="space-y-3">
                                                <Label htmlFor="jdCvFile" className="text-sm font-medium text-gray-700">
                                                    Upload CV
                                                </Label>
                                                <div className="border-2 border-dashed border-blue-200 bg-blue-50/50 rounded-xl p-6 text-center hover:border-blue-300 transition-colors">
                                                    <FileText className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                                                    <Input
                                                        id="jdCvFile"
                                                        type="file"
                                                        accept=".pdf,.doc,.docx"
                                                        onChange={(e) =>
                                                            setJdCvFile(
                                                                e.target.files?.[0] || null
                                                            )
                                                        }
                                                        className="hidden"
                                                        required
                                                    />
                                                    <Label
                                                        htmlFor="jdCvFile"
                                                        className="cursor-pointer"
                                                    >
                                                        {jdCvFile ? (
                                                            <div className="space-y-1">
                                                                <div className="text-green-600 font-medium text-sm">
                                                                    ✓ CV Selected
                                                                </div>
                                                                <div className="text-xs text-gray-600 truncate">
                                                                    {jdCvFile.name}
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <div className="space-y-1">
                                                                <div className="text-blue-600 font-medium text-sm">
                                                                    Upload CV
                                                                </div>
                                                                <div className="text-xs text-gray-500">
                                                                    PDF, DOC, DOCX
                                                                </div>
                                                            </div>
                                                        )}
                                                    </Label>
                                                </div>
                                            </div>

                                            <div className="space-y-3">
                                                <Label htmlFor="jdFile" className="text-sm font-medium text-gray-700">
                                                    Upload Job Description
                                                </Label>
                                                <div className="border-2 border-dashed border-green-200 bg-green-50/50 rounded-xl p-6 text-center hover:border-green-300 transition-colors">
                                                    <Briefcase className="w-8 h-8 text-green-400 mx-auto mb-2" />
                                                    <Input
                                                        id="jdFile"
                                                        type="file"
                                                        accept=".pdf,.doc,.docx,.txt"
                                                        onChange={(e) =>
                                                            setJdFile(
                                                                e.target.files?.[0] || null
                                                            )
                                                        }
                                                        className="hidden"
                                                        required
                                                    />
                                                    <Label
                                                        htmlFor="jdFile"
                                                        className="cursor-pointer"
                                                    >
                                                        {jdFile ? (
                                                            <div className="space-y-1">
                                                                <div className="text-green-600 font-medium text-sm">
                                                                    ✓ JD Selected
                                                                </div>
                                                                <div className="text-xs text-gray-600 truncate">
                                                                    {jdFile.name}
                                                                </div>
                                                            </div>
                                                        ) : (
                                                            <div className="space-y-1">
                                                                <div className="text-green-600 font-medium text-sm">
                                                                    Upload JD
                                                                </div>
                                                                <div className="text-xs text-gray-500">
                                                                    PDF, DOC, DOCX, TXT
                                                                </div>
                                                            </div>
                                                        )}
                                                    </Label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <Button
                                            type="submit"
                                            className="w-full h-12 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                                            disabled={
                                                isJdLoading || !jdCvFile || !jdFile
                                            }
                                        >
                                            {isJdLoading ? (
                                                <>
                                                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                                                    Creating roadmap...
                                                </>
                                            ) : (
                                                <>
                                                    <Sparkles className="w-5 h-5 mr-2" />
                                                    Create Roadmap
                                                </>
                                            )}
                                        </Button>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </motion.div>
            </DialogContent>
        </Dialog>
    );
};
