'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export const RoadmapSkeleton = () => {
  return (
    <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 border-none rounded-2xl shadow-lg h-full">
      <CardContent className="p-6">
        {/* Header Section */}
        <div className="text-center mb-4">
          <Skeleton className="w-10 h-10 rounded-full mx-auto mb-2" />
          <Skeleton className="h-6 w-3/4 mx-auto mb-2" />
          <Skeleton className="h-4 w-1/2 mx-auto" />
        </div>

        {/* Preview Section */}
        <div className="mb-4 bg-white/50 rounded-lg p-3">
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/6" />
          </div>
        </div>

        {/* Info Section */}
        <div className="space-y-2 mb-4">
          <div className="p-2 bg-white/50 rounded-lg">
            <Skeleton className="h-3 w-2/3" />
          </div>
          <div className="p-2 bg-white/50 rounded-lg">
            <Skeleton className="h-3 w-1/2" />
          </div>
          <div className="p-2 bg-white/50 rounded-lg">
            <Skeleton className="h-3 w-3/4" />
          </div>
        </div>

        {/* Button */}
        <Skeleton className="h-10 w-full rounded-xl" />
      </CardContent>
    </Card>
  );
};
