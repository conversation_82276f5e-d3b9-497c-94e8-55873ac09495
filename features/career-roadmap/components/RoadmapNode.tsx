"use client";

import React from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { Badge } from "@/components/ui/badge";
import { Target, BookOpen, Clock, ChevronRight } from "lucide-react";

interface RoadmapNodeData {
    title: string;
    description: string;
    type: string;
    link?: string;
    estimatedTime?: string;
}

const RoadmapNode: React.FC<NodeProps<RoadmapNodeData>> = ({
    data,
    selected,
}) => {
    const getNodeStyle = (type: string) => {
        switch (type.toLowerCase()) {
            case "milestone":
                return {
                    bg: "bg-white",
                    border: "border-gray-300",
                    text: "text-gray-900",
                    badge: "bg-blue-50 text-blue-700 border-blue-200",
                };
            case "skill":
                return {
                    bg: "bg-white",
                    border: "border-gray-300",
                    text: "text-gray-900",
                    badge: "bg-green-50 text-green-700 border-green-200",
                };
            case "course":
                return {
                    bg: "bg-white",
                    border: "border-gray-300",
                    text: "text-gray-900",
                    badge: "bg-orange-50 text-orange-700 border-orange-200",
                };
            case "project":
                return {
                    bg: "bg-white",
                    border: "border-gray-300",
                    text: "text-gray-900",
                    badge: "bg-purple-50 text-purple-700 border-purple-200",
                };
            default:
                return {
                    bg: "bg-white",
                    border: "border-gray-300",
                    text: "text-gray-900",
                    badge: "bg-gray-50 text-gray-700 border-gray-200",
                };
        }
    };
    const nodeStyle = getNodeStyle(data.type);
    return (
        <div
            className={`
        roadmap-node relative min-w-[200px] max-w-[240px] p-4 rounded-lg border 
        ${nodeStyle.bg} ${nodeStyle.border} ${nodeStyle.text}
        shadow-sm transition-all duration-300 hover:shadow-md hover:border-gray-400
        ${
            selected
                ? "roadmap-node-selected ring-2 ring-blue-400 ring-opacity-60 shadow-lg border-blue-300 scale-105"
                : ""
        }
      `}
        >
            {" "}
            <Handle
                type="target"
                position={Position.Top}
                className={`w-2 h-2 border-0 rounded-full transition-all duration-300 ${
                    selected
                        ? "bg-blue-500 opacity-100 w-3 h-3"
                        : "bg-gray-400 opacity-60"
                }`}
                style={{ top: selected ? -6 : -4 }}
            />
            <div className="space-y-3">
                <h3 className="font-semibold text-sm leading-tight">
                    {data.title}
                </h3>

                {data.description && (
                    <p className="text-xs text-gray-600 leading-relaxed line-clamp-2">
                        {data.description}
                    </p>
                )}

                {data.estimatedTime && (
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Clock className="w-3 h-3" />
                        <span>{data.estimatedTime}</span>
                    </div>
                )}
            </div>{" "}
            <Handle
                type="source"
                position={Position.Bottom}
                className={`w-2 h-2 border-0 rounded-full transition-all duration-300 ${
                    selected
                        ? "bg-blue-500 opacity-100 w-3 h-3"
                        : "bg-gray-400 opacity-60"
                }`}
                style={{ bottom: selected ? -6 : -4 }}
            />
        </div>
    );
};

export default RoadmapNode;
