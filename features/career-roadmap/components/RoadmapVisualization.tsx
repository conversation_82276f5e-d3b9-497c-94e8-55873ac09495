"use client";

import React from "react";
import ReactFlow, {
    Background,
    Controls,
    MiniMap,
    BackgroundVariant,
    NodeTypes,
    EdgeTypes,
} from "reactflow";
import "reactflow/dist/style.css";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Target, BookOpen, Clock, Move, Eye, EyeOff, ExternalLink } from "lucide-react";

import {
    RoadmapNode as RoadmapNodeType,
    RoadmapEdge,
} from "@/common/types/career-roadmap";
import { useRoadmapFlow, useRoadmapSelection } from "../hooks";
import RoadmapNode from "./RoadmapNode";
import CurvedEdge from "./CurvedEdge";

interface RoadmapVisualizationProps {
    nodes: RoadmapNodeType[];
    edges?: RoadmapEdge[];
    title?: string;
    className?: string;
    onNodePositionChange?: (
        nodeId: string,
        position: { x: number; y: number }
    ) => void;
}

const nodeTypes: NodeTypes = {
    roadmapNode: RoadmapNode,
};

const edgeTypes: EdgeTypes = {
    curved: CurvedEdge,
};

export default function RoadmapVisualization({
    nodes,
    edges = [],
    title = "Career Roadmap",
    className = "",
    onNodePositionChange,
}: RoadmapVisualizationProps) {
    const [showMinimap, setShowMinimap] = React.useState(true);

    const {
        nodes: flowNodes,
        edges: flowEdges,
        onNodesChange,
        onEdgesChange,
        onConnect,
    } = useRoadmapFlow({
        initialNodes: nodes,
        initialEdges: edges,
        onNodePositionChange,
    });

    const { selectedNode, handleNodeClick, handlePaneClick } =
        useRoadmapSelection();

    if (!nodes || nodes.length === 0) {
        return (
            <Card className={className}>
                <CardContent className="text-center py-12">
                    <Target className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No roadmap data
                    </h3>
                    <p className="text-gray-500">
                        No nodes have been created for this roadmap yet.
                    </p>
                </CardContent>
            </Card>
        );
    }
    return (
        <Card className={className}>
            <CardHeader>
                {" "}
                <CardTitle className="flex items-center justify-between">
                    <span>{title}</span>
                    <div className="flex items-center gap-2">                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowMinimap(!showMinimap)}
                            className="text-xs"
                        >
                            {showMinimap ? (
                                <Eye className="w-3 h-3 mr-1" />
                            ) : (
                                <EyeOff className="w-3 h-3 mr-1" />
                            )}
                            {showMinimap ? "Hide" : "Show"} Overview
                        </Button>
                        <div className="flex items-center gap-1 text-xs text-gray-500 bg-white px-2 py-1 rounded border">
                            <Move className="w-3 h-3" />
                            <span>Drag nodes • Pan view • Scroll to zoom</span>
                        </div>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                {" "}
                <div className="flex gap-6 h-[600px]">
                    <div className="w-[70%]">
                        <div className="border rounded-lg overflow-hidden bg-white relative h-full shadow-sm">
                            <ReactFlow
                                nodes={flowNodes}
                                edges={flowEdges}
                                onNodesChange={onNodesChange}
                                onEdgesChange={onEdgesChange}
                                onConnect={onConnect}
                                onNodeClick={handleNodeClick}
                                onPaneClick={handlePaneClick}
                                nodeTypes={nodeTypes}
                                edgeTypes={edgeTypes}
                                fitView
                                fitViewOptions={{
                                    padding: 0.2,
                                    includeHiddenNodes: false,
                                }}
                                className="bg-white"
                                panOnScroll={false}
                                zoomOnScroll={true}
                                zoomOnPinch={true}
                                panOnDrag={true}
                                selectionOnDrag={false}
                                selectNodesOnDrag={false}
                                minZoom={0.1}
                                maxZoom={4}
                                nodesDraggable={true}
                                nodesConnectable={false}
                                elementsSelectable={true}
                                defaultEdgeOptions={{
                                    type: "curved",
                                    animated: false,
                                    style: {
                                        strokeWidth: 2,
                                        stroke: "#10b981",
                                    },
                                }}
                            >
                                {" "}
                                <Background
                                    variant={BackgroundVariant.Dots}
                                    gap={24}
                                    size={0.8}
                                    color="#E5E7EB"
                                />{" "}
                                <Controls
                                    position="top-right"
                                    showInteractive={false}
                                    className="bg-white shadow-md border border-gray-200 rounded-lg"
                                />{" "}
                                {showMinimap && (
                                    <MiniMap
                                        nodeColor={(node) => {
                                            switch (
                                                node.data?.type?.toLowerCase()
                                            ) {
                                                case "milestone":
                                                    return "#3B82F6";
                                                case "skill":
                                                    return "#10B981";
                                                case "course":
                                                    return "#F59E0B";
                                                case "project":
                                                    return "#8B5CF6";
                                                default:
                                                    return "#6B7280";
                                            }
                                        }}
                                        nodeStrokeWidth={2}
                                        nodeStrokeColor="#FFFFFF"
                                        nodeBorderRadius={6}
                                        maskColor="rgba(59, 130, 246, 0.1)"
                                        position="bottom-right"
                                        pannable={true}
                                        zoomable={true}
                                        ariaLabel="Roadmap Overview"
                                        style={{
                                            width: 220,
                                            height: 160,
                                            border: "1px solid #e5e7eb",
                                            borderRadius: "8px",
                                            backgroundColor: "#ffffff",
                                            boxShadow:
                                                "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                                        }}
                                    />
                                )}
                            </ReactFlow>
                        </div>
                    </div>

                    <div className="w-[30%] h-full">
                        {selectedNode ? (
                            <Card className="h-full">
                                <CardHeader className="pb-4">
                                    <div className="flex items-start justify-between">
                                        <div>
                                            <CardTitle className="text-lg">
                                                {selectedNode.data.title}
                                            </CardTitle>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {selectedNode.data.description && (
                                        <div>
                                            <h4 className="font-medium mb-2">
                                                Description:
                                            </h4>
                                            <p className="text-sm text-gray-600">
                                                {selectedNode.data.description}
                                            </p>
                                        </div>
                                    )}

                                    {selectedNode.data.estimatedTime && (
                                        <div>
                                            <h4 className="font-medium mb-2 flex items-center gap-1">
                                                <Clock className="w-4 h-4" />
                                                Estimated time:
                                            </h4>
                                            <p className="text-sm text-gray-600">
                                                {
                                                    selectedNode.data
                                                        .estimatedTime
                                                }
                                            </p>
                                        </div>
                                    )}

                                    {selectedNode.data.link && (
                                        <div>
                                            <h4 className="font-medium mb-2">
                                                Documentation:
                                            </h4>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() =>
                                                    window.open(
                                                        selectedNode.data.link,
                                                        "_blank"
                                                    )
                                                }
                                                className="text-xs w-full"
                                            >
                                                <BookOpen className="w-3 h-3 mr-1" />
                                                View documentation
                                            </Button>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ) : (
                            <Card className="border-dashed border-2 border-gray-200 h-full">
                                <CardContent className="text-center py-8 h-full flex flex-col justify-center">
                                    <div className="space-y-3">
                                        <Target className="w-12 h-12 text-gray-300 mx-auto" />
                                        <div>
                                            <h3 className="text-lg font-medium text-gray-700 mb-1">
                                                Node Details
                                            </h3>
                                            <p className="text-gray-500 text-sm">
                                                Click on a node in the roadmap
                                                to view detailed information
                                            </p>
                                            <p className="text-gray-400 text-xs mt-2">
                                                Drag and drop to move nodes
                                            </p>
                                        </div>
                                        <div className="flex items-center justify-center gap-2 pt-2">
                                            <div className="w-3 h-3 bg-blue-100 rounded-full border-2 border-blue-400"></div>
                                            <span className="text-xs text-gray-500">
                                                Milestone
                                            </span>
                                            <div className="w-3 h-3 bg-green-100 rounded-full border-2 border-green-400"></div>
                                            <span className="text-xs text-gray-500">
                                                Skill
                                            </span>
                                            <div className="w-3 h-3 bg-yellow-100 rounded-full border-2 border-yellow-400"></div>
                                            <span className="text-xs text-gray-500">
                                                Course
                                            </span>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
