"use client";

import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
    FileText,
    Download,
    Eye,
    AlertCircle,
    Image as ImageIcon,
} from "lucide-react";
import { useImagePreview } from "../hooks";

interface ImagePreviewProps {
    cvFileUrl?: string;
    jdFileUrl?: string;
    className?: string;
    compact?: boolean;
}

export default function ImagePreview({
    cvFileUrl,
    jdFileUrl,
    className = "",
    compact = false,
}: ImagePreviewProps) {
    const {
        selectedType,
        setSelectedType,
        imageError,
        isLoading,
        pdfError,
        pdfLoaded,
        showPdfLoader,
        downloadLoading,
        handleImageLoad,
        handleImageError,
        handlePdfLoad,
        handlePdfError,
        handleDownload,
        getCurrentUrl,
        isCurrentPdf,
        hasFiles,
    } = useImagePreview({ cvFileUrl, jdFileUrl });
    const handleViewFullscreen = (url: string) => {
        window.open(url, "_blank");
    };

    // Helper functions
    const isValidUrl = (url?: string): boolean => {
        if (!url || typeof url !== "string" || url.trim() === "") return false;
        try {
            new URL(url);
            return true;
        } catch {
            return (
                url.startsWith("/") ||
                url.startsWith("data:") ||
                url.startsWith("blob:")
            );
        }
    };

    const getFileType = (url?: string): "image" | "pdf" | "unknown" => {
        if (!url) return "unknown";
        const extension = url.split(".").pop()?.toLowerCase();
        if (
            ["jpg", "jpeg", "png", "gif", "webp", "svg"].includes(
                extension || ""
            )
        ) {
            return "image";
        }
        if (extension === "pdf") {
            return "pdf";
        }
        return "unknown";
    };

    const getFileName = (url: string, type: "cv" | "jd"): string => {
        const urlParts = url.split("/");
        const lastPart = urlParts[urlParts.length - 1];

        if (lastPart && lastPart.includes(".")) {
            const cleanName = lastPart.split("?")[0];
            const extension = cleanName.split(".").pop()?.toLowerCase();
            const nameWithoutExt = cleanName.replace(/\.[^/.]+$/, "");

            return `${type.toUpperCase()}_${nameWithoutExt}.${extension}`;
        }

        const timestamp = new Date()
            .toISOString()
            .slice(0, 19)
            .replace(/[:.]/g, "-");
        const extension = url.split(".").pop() || "pdf";
        return `${type.toUpperCase()}_${timestamp}.${extension}`;
    };

    const hasValidCv = isValidUrl(cvFileUrl);
    const hasValidJd = isValidUrl(jdFileUrl);
    const cvFileType = getFileType(cvFileUrl);
    const jdFileType = getFileType(jdFileUrl);

    if (!hasFiles()) {
        return (
            <Card className={className}>
                <CardContent className="text-center py-8">
                    <FileText className="w-8 h-8 text-gray-300 mx-auto mb-2" />{" "}
                    <p className="text-gray-500 text-sm">No files to display</p>
                </CardContent>
            </Card>
        );
    }

    const currentUrl =
        selectedType === "cv"
            ? hasValidCv
                ? cvFileUrl
                : ""
            : hasValidJd
            ? jdFileUrl
            : "";
    const currentFileType = selectedType === "cv" ? cvFileType : jdFileType;
    const currentError = imageError[selectedType];
    const currentPdfError = pdfError[selectedType];
    const currentShowPdfLoader = showPdfLoader[selectedType];
    const currentLoading = isLoading[selectedType];

    return (
        <Card className={className}>
            <CardHeader className={compact ? "pb-2 px-3" : ""}>
                <CardTitle
                    className={`flex items-center justify-between ${
                        compact ? "text-sm" : "text-base"
                    }`}
                >
                    <span></span>
                    <div className="flex gap-1">
                        {hasValidCv && (
                            <Button
                                variant={
                                    selectedType === "cv"
                                        ? "default"
                                        : "outline"
                                }
                                size="sm"
                                onClick={() => setSelectedType("cv")}
                                className={
                                    compact ? "text-xs px-2 py-1 h-6" : ""
                                }
                            >
                                <FileText
                                    className={`${
                                        compact ? "w-2 h-2" : "w-3 h-3"
                                    } mr-1`}
                                />
                                CV
                            </Button>
                        )}
                        {hasValidJd && (
                            <Button
                                variant={
                                    selectedType === "jd"
                                        ? "default"
                                        : "outline"
                                }
                                size="sm"
                                onClick={() => setSelectedType("jd")}
                                className={
                                    compact ? "text-xs px-2 py-1 h-6" : ""
                                }
                            >
                                <FileText
                                    className={`${
                                        compact ? "w-2 h-2" : "w-3 h-3"
                                    } mr-1`}
                                />
                                JD
                            </Button>
                        )}
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent className={`space-y-4 ${compact ? "px-3 py-2" : ""}`}>
                {currentUrl ? (
                    <>
                        <div className="relative bg-gray-50 rounded-lg overflow-hidden border">
                            {currentLoading && currentFileType === "image" && (
                                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                                    <div
                                        className={`animate-spin rounded-full border-b-2 border-blue-500 ${
                                            compact ? "h-4 w-4" : "h-6 w-6"
                                        }`}
                                    ></div>
                                </div>
                            )}

                            {currentError ? (
                                <div
                                    className={`flex flex-col items-center justify-center text-gray-400 ${
                                        compact ? "h-32" : "h-40"
                                    }`}
                                >
                                    <AlertCircle
                                        className={`${
                                            compact ? "w-6 h-6" : "w-8 h-8"
                                        } mb-2`}
                                    />{" "}
                                    <p
                                        className={`${
                                            compact ? "text-xs" : "text-sm"
                                        } text-center`}
                                    >
                                        Cannot load file
                                    </p>
                                </div>
                            ) : currentFileType === "pdf" ? (
                                currentPdfError ? (
                                    <div
                                        className={`flex flex-col items-center justify-center bg-red-50 border-2 border-red-200 ${
                                            compact ? "h-32" : "h-40"
                                        }`}
                                    >
                                        <FileText
                                            className={`${
                                                compact
                                                    ? "w-8 h-8"
                                                    : "w-12 h-12"
                                            } text-red-500 mb-2`}
                                        />{" "}
                                        <p
                                            className={`${
                                                compact ? "text-xs" : "text-sm"
                                            } text-gray-700 text-center font-medium`}
                                        >
                                            PDF cannot be displayed
                                        </p>
                                        <p
                                            className={`${
                                                compact ? "text-xs" : "text-sm"
                                            } text-gray-500 text-center mt-1`}
                                        >
                                            Click "View" to open PDF
                                        </p>
                                    </div>
                                ) : (
                                    <div className="relative">
                                        {" "}
                                        <iframe
                                            src={`${currentUrl}#toolbar=0&navpanes=0&scrollbar=0`}
                                            className={`w-full border-0 bg-white transition-opacity duration-300 ${
                                                compact ? "h-80" : "h-[40rem]"
                                            }`}
                                            title={`${selectedType.toUpperCase()} PDF Preview`}
                                            style={{
                                                minHeight: compact
                                                    ? "20rem"
                                                    : "40rem",
                                                opacity: currentShowPdfLoader
                                                    ? 0.3
                                                    : 1,
                                            }}
                                            onError={() =>
                                                handlePdfError(selectedType)
                                            }
                                            onLoad={() =>
                                                handlePdfLoad(selectedType)
                                            }
                                        />
                                        <div className="absolute top-2 right-2 bg-white bg-opacity-90 px-2 py-1 rounded text-xs font-medium text-gray-600 shadow-sm">
                                            PDF Preview
                                        </div>
                                        {currentShowPdfLoader && (
                                            <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-95 backdrop-blur-sm">
                                                <div className="text-center p-4">
                                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-3"></div>{" "}
                                                    <p className="text-sm text-gray-600 mb-2">
                                                        PDF loading...
                                                    </p>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() =>
                                                            handleViewFullscreen(
                                                                currentUrl
                                                            )
                                                        }
                                                        className="text-xs"
                                                    >
                                                        {" "}
                                                        <Eye className="w-3 h-3 mr-1" />
                                                        Open PDF
                                                    </Button>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )
                            ) : currentFileType === "image" ? (
                                <img
                                    src={currentUrl}
                                    alt={`${selectedType.toUpperCase()} Preview`}
                                    className={`w-full h-auto object-contain bg-white ${
                                        compact ? "max-h-48" : "max-h-80"
                                    }`}
                                    onLoad={() => handleImageLoad(selectedType)}
                                    onError={() =>
                                        handleImageError(selectedType)
                                    }
                                />
                            ) : (
                                <div
                                    className={`flex flex-col items-center justify-center bg-yellow-50 border-2 border-yellow-200 ${
                                        compact ? "h-32" : "h-40"
                                    }`}
                                >
                                    <FileText
                                        className={`${
                                            compact ? "w-8 h-8" : "w-12 h-12"
                                        } text-yellow-500 mb-2`}
                                    />{" "}
                                    <p
                                        className={`${
                                            compact ? "text-xs" : "text-sm"
                                        } text-gray-600 text-center`}
                                    >
                                        File preview not supported
                                    </p>
                                    <p
                                        className={`${
                                            compact ? "text-xs" : "text-sm"
                                        } text-gray-500 text-center`}
                                    >
                                        Type: {currentFileType}
                                    </p>
                                </div>
                            )}
                        </div>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                    {selectedType === "cv"
                                        ? "Curriculum Vitae"
                                        : "Job Description"}
                                </Badge>
                                <Badge
                                    variant={
                                        currentFileType === "pdf"
                                            ? "default"
                                            : "secondary"
                                    }
                                    className="text-xs"
                                >
                                    {currentFileType.toUpperCase()}
                                </Badge>
                            </div>

                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                        handleViewFullscreen(currentUrl)
                                    }
                                    disabled={currentError}
                                    className={
                                        compact
                                            ? "text-xs px-2 py-1 h-6"
                                            : "text-xs"
                                    }
                                >
                                    <Eye
                                        className={`${
                                            compact ? "w-2 h-2" : "w-3 h-3"
                                        } mr-1`}
                                    />
                                    View
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                        handleDownload(
                                            currentUrl,
                                            getFileName(
                                                currentUrl,
                                                selectedType
                                            )
                                        )
                                    }
                                    disabled={currentError || downloadLoading}
                                    className={
                                        compact
                                            ? "text-xs px-2 py-1 h-6"
                                            : "text-xs"
                                    }
                                >
                                    {downloadLoading ? (
                                        <div
                                            className={`animate-spin rounded-full border-b-2 border-current ${
                                                compact ? "w-2 h-2" : "w-3 h-3"
                                            } mr-1`}
                                        ></div>
                                    ) : (
                                        <Download
                                            className={`${
                                                compact ? "w-2 h-2" : "w-3 h-3"
                                            } mr-1`}
                                        />
                                    )}
                                    Download
                                </Button>
                            </div>
                        </div>
                        {!compact && (
                            <div className="text-xs text-gray-500 space-y-1 border-t pt-3">
                                <p>• Click "View" to open in fullscreen</p>
                                <p>
                                    • Click "Download" to download file to
                                    computer
                                </p>
                                {hasValidCv && hasValidJd && (
                                    <p>
                                        • Switch between CV and JD using the
                                        buttons above
                                    </p>
                                )}
                            </div>
                        )}
                    </>
                ) : (
                    <div
                        className={`text-center text-gray-500 ${
                            compact ? "py-4" : "py-8"
                        }`}
                    >
                        <ImageIcon
                            className={`${
                                compact ? "w-6 h-6" : "w-8 h-8"
                            } mx-auto mb-2 text-gray-300`}
                        />{" "}
                        <p className={compact ? "text-xs" : "text-sm"}>
                            {selectedType.toUpperCase()} file not available
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
