"use client";
import { motion } from "framer-motion";
import {
    <PERSON>,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, Eye, Trash2, FileText, Briefcase, ArrowRight, Target } from "lucide-react";
import { ResponseRoadmapAiDto } from "@/common/types/career-roadmap";
import RoadmapPreview from "./RoadmapPreview";
import { useRoadmapCard } from "../hooks";

interface RoadmapCardProps {
    roadmap: ResponseRoadmapAiDto;
    onDelete: (id: string) => void;
}

export const RoadmapCard = ({ roadmap, onDelete }: RoadmapCardProps) => {
    const { formatDate, handleViewDetails, getAgentTypeBadge } =
        useRoadmapCard();

    const agentTypeBadge = getAgentTypeBadge(roadmap.agentType);

    return (
        <motion.div
            whileHover={{ y: -4, scale: 1.02 }}
            transition={{ duration: 0.2 }}
        >
            <Card className="relative bg-gradient-to-br from-blue-50 to-cyan-100 border-none rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col">
                {/* Delete button */}
                <button
                    onClick={() => onDelete(roadmap._id)}
                    className="absolute top-3 right-3 w-8 h-8 bg-red-400 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 z-10"
                    aria-label="Delete roadmap"
                >
                    <Trash2 className="h-4 w-4" />
                </button>

                <CardContent className="p-6 flex flex-col h-full">
                    <div className="text-center mb-4">
                        <div className="w-10 h-10 bg-cyan-200 rounded-full flex items-center justify-center mx-auto mb-2">
                            <Target className="h-5 w-5 text-cyan-600" />
                        </div>
                        <h3 className="font-semibold text-slate-700 text-lg truncate">
                            {roadmap.content.roadmapTitle || "AI Career Roadmap"}
                        </h3>
                        <div className="mt-2">
                            <Badge 
                                variant={agentTypeBadge.variant}
                                className="bg-white/70 text-cyan-700 border-cyan-200"
                            >
                                {agentTypeBadge.label}
                            </Badge>
                        </div>
                    </div>

                    {/* Roadmap Preview */}
                    {roadmap.content?.initialNodes &&
                        roadmap.content.initialNodes.length > 0 && (
                            <div className="mb-4 bg-white/50 rounded-lg p-3">
                                <RoadmapPreview nodes={roadmap.content.initialNodes} />
                            </div>
                        )}

                    {/* Info Section */}
                    <div className="space-y-2 mb-4 flex-grow">
                        <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
                            <CalendarDays className="h-3 w-3 text-cyan-600" />
                            <span className="text-sm text-slate-700">
                                Created {formatDate(roadmap.createdAt)}
                            </span>
                        </div>
                        
                        {roadmap.cvFileUrl && roadmap.cvFileUrl.trim() !== "" && (
                            <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
                                <FileText className="h-3 w-3 text-cyan-600" />
                                <span className="text-sm text-slate-700">CV uploaded</span>
                            </div>
                        )}
                        
                        {roadmap.jdFileUrl && roadmap.jdFileUrl.trim() !== "" && (
                            <div className="flex items-center space-x-2 p-2 bg-white/50 rounded-lg">
                                <Briefcase className="h-3 w-3 text-cyan-600" />
                                <span className="text-sm text-slate-700">JD uploaded</span>
                            </div>
                        )}
                    </div>

                    {/* Action Button */}
                    <Button
                        className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white rounded-xl py-2 text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-300"
                        onClick={() => handleViewDetails(roadmap._id)}
                    >
                        <ArrowRight className="mr-2 h-3 w-3" />
                        View Roadmap
                    </Button>
                </CardContent>
            </Card>
        </motion.div>
    );
};
