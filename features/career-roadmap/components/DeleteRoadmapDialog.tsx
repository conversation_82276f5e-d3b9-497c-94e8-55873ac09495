"use client";

import React from "react";
import { motion } from "framer-motion";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Trash2, Loader2 } from "lucide-react";
import { ResponseRoadmapAiDto } from "@/common/types/career-roadmap";

interface DeleteRoadmapDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    roadmap: ResponseRoadmapAiDto | null;
    onConfirm: () => void;
    isDeleting?: boolean;
}

export default function DeleteRoadmapDialog({
    open,
    onOpenChange,
    roadmap,
    onConfirm,
    isDeleting = false,
}: DeleteRoadmapDialogProps) {
    const handleConfirm = () => {
        onConfirm();
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md bg-gradient-to-br from-red-50 via-white to-pink-50 border-none shadow-2xl">
                <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.2 }}
                >
                    <DialogHeader className="text-center pb-4">
                        <div className="mx-auto w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mb-4">
                            <AlertTriangle className="h-8 w-8 text-white" />
                        </div>
                        <DialogTitle className="text-xl font-bold text-gray-900">
                            Delete Career Roadmap
                        </DialogTitle>
                        <DialogDescription className="text-gray-600 mt-2">
                            This action cannot be undone. This will permanently delete your roadmap.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="py-4">
                        <div className="rounded-xl border border-red-200 bg-red-50/50 p-4">
                            <div className="flex items-start gap-3">
                                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
                                    <Trash2 className="h-5 w-5 text-red-600" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <h4 className="font-semibold text-gray-900">
                                        {roadmap?.content.roadmapTitle ||
                                            roadmap?.jobPosition ||
                                            "Career Roadmap"}
                                    </h4>
                                    <p className="text-sm text-gray-600 mt-1">
                                        Created on{" "}
                                        {roadmap?.createdAt
                                            ? new Date(
                                                  roadmap.createdAt
                                              ).toLocaleDateString("en-US", {
                                                  year: "numeric",
                                                  month: "short",
                                                  day: "numeric",
                                              })
                                            : "Unknown date"}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <DialogFooter className="gap-3 pt-2">
                        <Button
                            variant="outline"
                            onClick={() => onOpenChange(false)}
                            disabled={isDeleting}
                            className="flex-1 h-11 rounded-xl"
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleConfirm}
                            disabled={isDeleting}
                            className="flex-1 h-11 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                            {isDeleting ? (
                                <>
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Deleting...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Delete Roadmap
                                </>
                            )}
                        </Button>
                    </DialogFooter>                </motion.div>
            </DialogContent>
        </Dialog>
    );
}
