"use client";
import { useState, useCallback } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { AnimatePresence, motion } from "framer-motion";
import { AnalysisModalActions } from "@/components/AnalysisModalActions";
import { AnalysisType, CVAnalyzerModalProps } from "@/features/analyst/analyst";
import { useFileUpload } from "@/hooks/useFileUpload";
import { useAnalysisLogic } from "@/features/analyst/hooks/useAnalysisLogic";
import { AnalysisModalHeader } from "@/components/AnalysisModalHeader";
import { AnalysisTypeSelect } from "@/features/analyst/components/AnalysisTypeSelect";
import { RoleAnalysisForm } from "@/features/analyst/components/RoleAnalysisForm";
import { JDAnalysisForm } from "@/features/analyst/components/JDAnalysisForm";

export default function CVAnalyzerModal({
  open,
  onOpenChange,
}: Readonly<CVAnalyzerModalProps>) {
  const [analysisType, setAnalysisType] = useState<AnalysisType>("");
  const [roleDescription, setRoleDescription] = useState("");
  const [cvFile, setCvFile] = useState<File | null>(null);
  const [jdFile, setJdFile] = useState<File | null>(null);

  const { handleFileUpload, handleDragOver, handleDrop } = useFileUpload();
  const { isAnalyzing, isFormValid, handleAnalyze } = useAnalysisLogic();

  const onFileUpload = useCallback(
    (file: File | null, type: "cv" | "jd") => {
      if (type === "cv") {
        handleFileUpload(file, type, setCvFile);
      } else {
        handleFileUpload(file, type, setJdFile);
      }
    },
    [handleFileUpload]
  );

  const onDrop = useCallback(
    (e: React.DragEvent, type: "cv" | "jd") => {
      handleDrop(e, type, onFileUpload);
    },
    [handleDrop, onFileUpload]
  );

  const resetForm = () => {
    setAnalysisType("");
    setRoleDescription("");
    setCvFile(null);
    setJdFile(null);
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const onAnalyze = async () => {
    await handleAnalyze(analysisType, roleDescription, cvFile, jdFile, () => {
      onOpenChange(false);
      resetForm();
    });
  };

  const formValid = !!isFormValid(
    analysisType,
    roleDescription,
    cvFile,
    jdFile
  );
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-white/95 backdrop-blur-xl border-0 shadow-2xl rounded-2xl">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className="space-y-8"
        >
          <AnalysisModalHeader />

          <div className="space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <AnalysisTypeSelect
                analysisType={analysisType}
                onAnalysisTypeChange={setAnalysisType}
              />
            </motion.div>

            <AnimatePresence mode="wait">
              {analysisType === "role" && (
                <motion.div
                  key="role"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <RoleAnalysisForm
                    roleDescription={roleDescription}
                    onRoleDescriptionChange={setRoleDescription}
                    cvFile={cvFile}
                    onFileUpload={onFileUpload}
                    onDragOver={handleDragOver}
                    onDrop={onDrop}
                  />
                </motion.div>
              )}

              {analysisType === "jd" && (
                <motion.div
                  key="jd"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <JDAnalysisForm
                    cvFile={cvFile}
                    jdFile={jdFile}
                    onFileUpload={onFileUpload}
                    onDragOver={handleDragOver}
                    onDrop={onDrop}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <AnalysisModalActions
              isAnalyzing={isAnalyzing}
              isFormValid={formValid}
              onAnalyze={onAnalyze}
              onClose={handleClose}
            />
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
