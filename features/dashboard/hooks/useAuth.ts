import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import { logout } from "@/redux/slices/authSlice";
import { RootState } from "@/redux/store";
import { toast } from "sonner";

export const useAuth = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const router = useRouter();

  const handleLogout = () => {
    dispatch(logout());
    toast.success("Đăng xuất thành công!");
    router.push("/login");
  };

  return { user, handleLogout };
};