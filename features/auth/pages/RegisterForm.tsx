"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { CardContent } from "@/components/ui/card";
import {
  registerSchema,
  RegisterFormData,
} from "@/features/auth/schemas/registerSchema";
import { RegisterFormProps } from "../auth";
import GoogleLoginButton from "../components/GoogleLoginButton";
import FormDivider from "../components/FormDivider";
import FormField from "../components/FormField";
import PasswordInput from "../components/PasswordInput";
import TermsCheckbox from "../components/TermsCheckbox";
import AuthLink from "../components/AuthLink";

export default function RegisterForm({
  onSubmit,
  isLoading,
}: Readonly<RegisterFormProps>) {
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [isCheckboxBlurred, setIsCheckboxBlurred] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const handleFormSubmit = (data: RegisterFormData) => {
    if (!acceptTerms) {
      return;
    }
    onSubmit(data);
  };

  return (
    <CardContent className="space-y-6">
      <GoogleLoginButton text="Đăng ký với Google" />

      {/* Divider */}
      <FormDivider text="Hoặc đăng ký với email" />

      {/* Register Form */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
        {/* Name  */}
        <div className="grid grid-cols-2 gap-3">
          <FormField
            id="firstName"
            label="Họ"
            placeholder="Nguyễn"
            required
            error={errors.firstName?.message}
            {...register("firstName")}
          />
          <FormField
            id="lastName"
            label="Tên"
            placeholder="Văn A"
            required
            error={errors.lastName?.message}
            {...register("lastName")}
          />
        </div>

        {/* Email */}
        <FormField
          id="email"
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          required
          error={errors.email?.message}
          {...register("email")}
        />

        {/* Password */}
        <PasswordInput
          id="password"
          label="Mật khẩu"
          required
          hint="Mật khẩu phải có ít nhất 6 ký tự"
          error={errors.password?.message}
          {...register("password")}
        />

        {/* Confirm Password */}
        <PasswordInput
          id="confirmPassword"
          label="Xác nhận mật khẩu"
          required
          error={errors.confirmPassword?.message}
          {...register("confirmPassword")}
        />

        {/* Terms and Conditions */}
        <TermsCheckbox
          checked={acceptTerms}
          onCheckedChange={setAcceptTerms}
          error={!acceptTerms}
          isCheckboxBlurred={isCheckboxBlurred}
          setIsCheckboxBlurred={setIsCheckboxBlurred}
        />

        {/* Submit */}
        <Button
          type="submit"
          className="w-full"
          disabled={isLoading || !acceptTerms}
          size="lg"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Đang đăng ký...
            </>
          ) : (
            "Tạo tài khoản"
          )}
        </Button>
      </form>

      {/* Login Link */}
      <AuthLink
        text="Đã có tài khoản?"
        linkText="Đăng nhập ngay"
        href="/login"
      />
    </CardContent>
  );
}
