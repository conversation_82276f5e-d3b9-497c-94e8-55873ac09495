"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { CardContent } from "@/components/ui/card";
import {
  loginSchema,
  LoginFormData,
} from "@/features/auth/schemas/loginSchema";
import { LoginFormProps } from "../auth";
import GoogleLoginButton from "../components/GoogleLoginButton";
import FormDivider from "../components/FormDivider";
import FormField from "../components/FormField";
import PasswordInput from "../components/PasswordInput";
import AuthLink from "../components/AuthLink";
import ForgotPasswordModal from "../components/ForgotPasswordModal";

export default function LoginForm({
  onSubmit,
  isLoading,
}: Readonly<LoginFormProps>) {
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  return (
    <>
      <CardContent className="space-y-6">
        {/* Google */}
        <GoogleLoginButton />

        {/* Divider */}
        <FormDivider text="Hoặc đăng nhập với email" />

        {/* Login Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Email */}
          <FormField
            id="email"
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            required
            error={errors.email?.message}
            {...register("email")}
          />

          {/* Password & Forgot Password */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                Mật khẩu <span className="text-red-500">*</span>
              </span>
              <button
                type="button"
                onClick={() => setShowForgotPassword(true)}
                className="text-sm text-primary hover:underline"
              >
                Quên mật khẩu?
              </button>
            </div>
            <PasswordInput
              id="password"
              label=""
              error={errors.password?.message}
              {...register("password")}
            />
          </div>

          {/* Submit */}
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
            size="lg"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Đang đăng nhập...
              </>
            ) : (
              "Đăng nhập"
            )}
          </Button>
        </form>

        {/* Register */}
        <AuthLink
          text="Chưa có tài khoản?"
          linkText="Đăng ký ngay"
          href="/register"
        />
      </CardContent>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        open={showForgotPassword}
        onOpenChange={setShowForgotPassword}
      />
    </>
  );
}
