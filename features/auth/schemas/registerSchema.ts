import * as z from "zod";

export const registerSchema = z
  .object({
    firstName: z.string().min(1, "<PERSON><PERSON> không được để trống"),
    lastName: z.string().min(1, "Tên không được để trống"),
    email: z.string().email("<PERSON>ail không hợp lệ"),
    password: z.string().min(6, "Mật khẩu phải có ít nhất 6 ký tự"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "<PERSON>ật khẩu xác nhận không khớp",
    path: ["confirmPassword"],
  });

export type RegisterFormData = z.infer<typeof registerSchema>;
