import { z } from "zod";

export const emailSchema = z.object({
  email: z.string().min(1, "<PERSON>ail là bắt buộc").email("Email không hợp lệ"),
});

export const otpSchema = z.object({
  otp: z
    .string()
    .min(6, "Mã OTP phải có 6 ký tự")
    .max(6, "Mã OTP phải có 6 ký tự")
    .regex(/^\d{6}$/, "Mã OTP chỉ chứa số"),
});

export const resetPasswordSchema = z
  .object({
    password: z.string().min(6, "<PERSON>ật khẩu phải có ít nhất 6 ký tự"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Mật khẩu xác nhận không khớp",
    path: ["confirmPassword"],
  });

export const changePasswordSchema = z
  .object({
    oldPassword: z.string().min(1, "Mật khẩu cũ là bắt buộc"),
    newPassword: z.string().min(6, "<PERSON>ật khẩu mới phải có ít nhất 6 ký tự"),
    confirmPassword: z.string().min(1, "Vui lòng xác nhận mật khẩu mới"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Mật khẩu xác nhận không khớp",
    path: ["confirmPassword"],
  });

// Type definitions
export type EmailFormData = z.infer<typeof emailSchema>;
export type OtpFormData = z.infer<typeof otpSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type ForgotPasswordStep = "email" | "otp" | "reset-password";

export interface ForgotPasswordModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface ChangePasswordModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
