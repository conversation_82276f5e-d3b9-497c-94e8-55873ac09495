import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { OtpFormData, otpSchema } from "../schemas/forgotSchema";
import { OtpStepProps } from "../auth";

export default function OtpStep({
  email,
  onSubmit,
  onResend,
  isLoading,
}: Readonly<OtpStepProps>) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<OtpFormData>({
    resolver: zodResolver(otpSchema),
  });

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="otp">Mã OTP</Label>
        <Input
          id="otp"
          type="text"
          placeholder="123456"
          maxLength={6}
          {...register("otp")}
          className={`text-center text-lg tracking-widest ${
            errors.otp ? "border-red-500" : ""
          }`}
        />
        {errors.otp && (
          <p className="text-sm text-red-500">{errors.otp.message}</p>
        )}
      </div>
      <div className="text-center">
        <button
          type="button"
          onClick={onResend}
          disabled={isLoading}
          className="text-sm text-primary hover:underline disabled:opacity-50"
        >
          Gửi lại mã OTP
        </button>
      </div>
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Đang xác thực...
          </>
        ) : (
          "Xác thực OTP"
        )}
      </Button>
    </form>
  );
}
