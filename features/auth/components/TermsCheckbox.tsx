"use client";

import Link from "next/link";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { TermsCheckboxProps } from "../auth";

export default function TermsCheckbox({
  checked,
  onCheckedChange,
  error = false,
  isCheckboxBlurred,
  setIsCheckboxBlurred,
}: Readonly<TermsCheckboxProps>) {
  return (
    <div className="space-y-2">
      <div className="flex items-start space-x-2 pt-2">
        <Checkbox
          id="terms"
          checked={checked}
          onCheckedChange={(checked) => {
            setIsCheckboxBlurred(false);
            onCheckedChange(checked as boolean);
          }}
          onBlur={() => setIsCheckboxBlurred(true)}
          className="mt-1"
        />
        <div className="grid gap-1.5 leading-none">
          <Label
            htmlFor="terms"
            className="text-sm font-normal leading-relaxed peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            T<PERSON><PERSON> đồng <PERSON> với{" "}
            <Link href="/terms" className="text-primary hover:underline">
              Đi<PERSON><PERSON> k<PERSON> dịch vụ
            </Link>{" "}
            và{" "}
            <Link href="/register" className="text-primary hover:underline">
              Chính sách bảo mật
            </Link>
          </Label>
        </div>
      </div>
      {error && isCheckboxBlurred && !checked && (
        <p className="text-sm text-red-500 -mt-2">
          Vui lòng đồng ý với điều khoản để tiếp tục
        </p>
      )}
    </div>
  );
}
