"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import PasswordInput from "./PasswordInput";
import { Key } from "lucide-react";
import { toast } from "sonner";
import { useChangePasswordMutation } from "@/services/api/auth";
import {
  ChangePasswordFormData,
  changePasswordSchema,
} from "../schemas/forgotSchema";

interface ChangePasswordModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export default function ChangePasswordModal({
  open,
  onOpenChange,
  onSuccess,
}: Readonly<ChangePasswordModalProps>) {
  const [isLoading, setIsLoading] = useState(false);
  const [changePassword] = useChangePasswordMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
  });

  const handleClose = () => {
    reset();
    onOpenChange(false);
  };

  const onSubmit = async (data: ChangePasswordFormData) => {
    setIsLoading(true);
    try {
      const result = await changePassword({
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      }).unwrap();

      toast.success(result.message ?? "Thay đổi mật khẩu thành công!");
      handleClose();

      // Call callback to logout and redirect
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
          toast.info("Vui lòng đăng nhập lại với mật khẩu mới");
        }, 500);
      }
    } catch (error: any) {
      console.error("Change password error:", error);
      toast.error(error?.data?.message ?? "Có lỗi xảy ra, vui lòng thử lại");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="text-primary">
              <Key className="h-6 w-6" />
            </div>
          </div>
          <div>
            <DialogTitle>Thay đổi mật khẩu</DialogTitle>
            <DialogDescription className="mt-1">
              Nhập mật khẩu cũ và mật khẩu mới để thay đổi. Bạn sẽ cần đăng nhập
              lại sau khi thay đổi mật khẩu.
            </DialogDescription>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <PasswordInput
            id="old-password"
            label="Mật khẩu cũ"
            placeholder="••••••••"
            required
            error={errors.oldPassword?.message}
            {...register("oldPassword")}
          />

          <PasswordInput
            id="new-password"
            label="Mật khẩu mới"
            placeholder="••••••••"
            required
            error={errors.newPassword?.message}
            hint="Mật khẩu phải có ít nhất 6 ký tự"
            {...register("newPassword")}
          />

          <PasswordInput
            id="confirm-new-password"
            label="Xác nhận mật khẩu mới"
            placeholder="••••••••"
            required
            error={errors.confirmPassword?.message}
            {...register("confirmPassword")}
          />

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={isLoading}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={isLoading} className="flex-1">
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Đang thay đổi...
                </>
              ) : (
                "Thay đổi mật khẩu"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
