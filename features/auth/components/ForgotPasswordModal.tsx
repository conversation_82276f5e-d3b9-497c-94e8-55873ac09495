"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { ForgotPasswordModalProps } from "../schemas/forgotSchema";
import { useForgotPassword } from "../hooks/useForgotPassword";
import EmailStep from "../components/EmailStep";
import OtpStep from "../components/OtpStep";
import ResetPasswordStep from "../components/ResetPasswordStep";
import { getStepInfo } from "@/lib/getStepInfo";

export default function ForgotPasswordModal({
  open,
  onOpenChange,
}: Readonly<ForgotPasswordModalProps>) {
  const {
    step,
    email,
    isLoading,
    handleEmailSubmit,
    handleOtpSubmit,
    handleResetPasswordSubmit,
    handleResendOtp,
    resetFlow,
    goBackStep,
  } = useForgotPassword();

  const handleClose = () => {
    resetFlow();
    onOpenChange(false);
  };

  const handleResetPassword = async (data: any) => {
    const success = await handleResetPasswordSubmit(data);
    if (success) {
      handleClose();
    }
    return success;
  };

  const stepInfo = getStepInfo(step, email);
  const IconComponent = stepInfo.icon;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="space-y-4">
          <div className="flex items-center space-x-2">
            {step !== "email" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={goBackStep}
                disabled={isLoading}
                className="p-1 h-8 w-8"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            {IconComponent && (
              <div className="text-primary">
                <IconComponent className="h-6 w-6" />
              </div>
            )}
          </div>
          <div>
            <DialogTitle>{stepInfo.title}</DialogTitle>
            <DialogDescription className="mt-1">
              {stepInfo.description}
            </DialogDescription>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {step === "email" && (
            <EmailStep
              onSubmit={handleEmailSubmit}
              onCancel={handleClose}
              isLoading={isLoading}
            />
          )}

          {step === "otp" && (
            <OtpStep
              email={email}
              onSubmit={handleOtpSubmit}
              onResend={handleResendOtp}
              isLoading={isLoading}
            />
          )}

          {step === "reset-password" && (
            <ResetPasswordStep
              onSubmit={handleResetPassword}
              isLoading={isLoading}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
