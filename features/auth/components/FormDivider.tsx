"use client";

import { Separator } from "@/components/ui/separator";
import { FormDividerProps } from "../auth";

export default function FormDivider({ text }: Readonly<FormDividerProps>) {
  return (
    <div className="relative">
      <div className="absolute inset-0 flex items-center">
        <Separator className="w-full" />
      </div>
      <div className="relative flex justify-center text-xs uppercase">
        <span className="bg-white px-2 text-muted-foreground">{text}</span>
      </div>
    </div>
  );
}
