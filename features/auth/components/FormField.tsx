"use client";

import { forwardRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FormFieldProps } from "../auth";

const FormField = forwardRef<HTMLInputElement, FormFieldProps>(
  (
    {
      id,
      label,
      type = "text",
      placeholder,
      error,
      required = false,
      hint,
      className = "",
      ...props
    },
    ref
  ) => {
    return (
      <div className="space-y-2">
        <Label htmlFor={id} className="text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
        <Input
          id={id}
          ref={ref}
          type={type}
          placeholder={placeholder}
          className={`${
            error
              ? "border-red-500 focus-visible:ring-red-500"
              : "focus-visible:ring-2 focus-visible:ring-primary"
          } ${className}`}
          {...props}
        />
        {error && <p className="text-sm text-red-500">{error}</p>}
        {hint && !error && (
          <p className="text-xs text-muted-foreground">{hint}</p>
        )}
      </div>
    );
  }
);

FormField.displayName = "FormField";

export default FormField;
