import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { toast } from "sonner";
import { setCredentials } from "@/redux/slices/authSlice";
import { useGetMeQuery } from "@/services/api/auth";

export const useGoogleAuthCallback = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [googleTokens, setGoogleTokens] = useState<{
    accessToken: string;
    refreshToken: string;
  } | null>(null);

  // Use ref to check if URL params were processed
  const hasProcessedUrlParams = useRef(false);

  const {
    data: userData,
    error: userError,
    isLoading: isLoadingUser,
  } = useGetMeQuery(undefined, {
    skip: !googleTokens,
  });

  // Handling URL params
  useEffect(() => {
    if (hasProcessedUrlParams.current) return;

    const urlParams = new URLSearchParams(window.location.search);
    const accessToken = urlParams.get("accessToken");
    const refreshToken = urlParams.get("refreshToken");
    const error = urlParams.get("error");

    if (error) {
      toast.error("Đăng nhập Google thất bại: " + error);
      router.replace("/login");
      hasProcessedUrlParams.current = true;
      return;
    }

    // Successful Google OAuth
    if (accessToken && refreshToken) {
      localStorage.setItem("accessToken", accessToken);
      localStorage.setItem("refreshToken", refreshToken);

      dispatch(
        setCredentials({
          user: null,
          accessToken,
          refreshToken,
        })
      );

      setGoogleTokens({ accessToken, refreshToken });
      hasProcessedUrlParams.current = true;
    }
  }, []);

  // Handling user data response
  useEffect(() => {
    if (!googleTokens) return;

    if (userData) {
      dispatch(
        setCredentials({
          user: userData,
          accessToken: googleTokens.accessToken,
          refreshToken: googleTokens.refreshToken,
        })
      );

      router.replace("/dashboard");
      toast.success("Đăng nhập Google thành công!");
      setGoogleTokens(null);
    } else if (userError) {
      console.error("Google auth error:", userError);
      toast.error("Đăng nhập Google thất bại");

      localStorage.removeItem("accessToken");
      localStorage.removeItem("refreshToken");
      dispatch(
        setCredentials({ user: null, accessToken: "", refreshToken: "" })
      );

      router.replace("/login");
      setGoogleTokens(null);
    }
  }, [userData, userError, googleTokens, dispatch, router]);

  return {
    isProcessingGoogleAuth: !!googleTokens,
    isLoadingUser,
  };
};
