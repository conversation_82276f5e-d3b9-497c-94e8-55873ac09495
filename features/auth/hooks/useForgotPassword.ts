import { useState } from "react";
import { toast } from "sonner";
import {
  useCreateOtpMutation,
  useVerifyOtpMutation,
  useForgotPasswordMutation,
} from "@/services/api/auth";
import {
  EmailFormData,
  OtpFormData,
  ResetPasswordFormData,
  ForgotPasswordStep,
} from "../schemas/forgotSchema";

export const useForgotPassword = () => {
  const [step, setStep] = useState<ForgotPasswordStep>("email");
  const [email, setEmail] = useState("");
  const [verifiedOtp, setVerifiedOtp] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [createOtp] = useCreateOtpMutation();
  const [verifyOtp] = useVerifyOtpMutation();
  const [forgotPassword] = useForgotPasswordMutation();

  const handleEmailSubmit = async (data: EmailFormData) => {
    setIsLoading(true);
    try {
      const result = await createOtp({ email: data.email }).unwrap();
      setEmail(data.email);
      setStep("otp");
      toast.success(result.message ?? "Mã OTP đã được gửi về email của bạn");
    } catch (error: any) {
      console.error("Create OTP error:", error);
      toast.error(error?.data?.message ?? "Có lỗi xảy ra, vui lòng thử lại");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpSubmit = async (data: OtpFormData) => {
    setIsLoading(true);
    try {
      const result = await verifyOtp({
        email,
        otp: data.otp,
      }).unwrap();
      setVerifiedOtp(data.otp);
      setStep("reset-password");
      toast.success(result.message ?? "Mã OTP hợp lệ");
    } catch (error: any) {
      console.error("Verify OTP error:", error);
      toast.error(error?.data?.message ?? "Mã OTP không đúng");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPasswordSubmit = async (data: ResetPasswordFormData) => {
    setIsLoading(true);
    console.log("Reset password data:", email, verifiedOtp, data);

    try {
      const result = await forgotPassword({
        email,
        otp: verifiedOtp,
        password: data.password,
        confirmPassword: data.confirmPassword,
      }).unwrap();
      toast.success(result.message ?? "Đặt lại mật khẩu thành công!");
      return true;
    } catch (error: any) {
      console.error("Reset password error:", error);
      toast.error(error?.data?.message ?? "Có lỗi xảy ra, vui lòng thử lại");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (!email) return;

    setIsLoading(true);
    try {
      const result = await createOtp({ email }).unwrap();
      setVerifiedOtp("");
      toast.success(result.message ?? "Mã OTP mới đã được gửi");
    } catch (error: any) {
      console.error("Resend OTP error:", error);
      toast.error(error?.data?.message ?? "Có lỗi xảy ra khi gửi lại OTP");
    } finally {
      setIsLoading(false);
    }
  };

  const resetFlow = () => {
    setStep("email");
    setEmail("");
    setVerifiedOtp("");
    setIsLoading(false);
  };

  const goBackStep = () => {
    if (step === "otp") setStep("email");
    if (step === "reset-password") setStep("otp");
  };

  return {
    step,
    email,
    isLoading,
    handleEmailSubmit,
    handleOtpSubmit,
    handleResetPasswordSubmit,
    handleResendOtp,
    resetFlow,
    goBackStep,
  };
};
