export interface LoginFormProps {
  onSubmit: (data: LoginFormData) => void;
  isLoading: boolean;
}

export interface RegisterFormProps {
  onSubmit: (data: RegisterFormData) => void;
  isLoading: boolean;
}

export interface PasswordInputProps {
  id: string;
  label: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  hint?: string;
  className?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  name?: string;
}

export interface FormFieldProps {
  id: string;
  label: string;
  type?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  hint?: string;
  className?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  name?: string;
}

export interface FormDividerProps {
  text: string;
}

export interface TermsCheckboxProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  error?: boolean;
  isCheckboxBlurred?: boolean;
  setIsCheckboxBlurred: (blurred: boolean) => void;
}

export interface AuthLinkProps {
  text: string;
  linkText: string;
  href: string;
}

export interface GoogleLoginButtonProps {
  text?: string;
}

export interface LoadingSpinnerProps {
  message?: string;
}

export interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

export interface EmailStepProps {
  onSubmit: (data: EmailFormData) => Promise<void>;
  onCancel: () => void;
  isLoading: boolean;
}

export interface OtpStepProps {
  email: string;
  onSubmit: (data: OtpFormData) => Promise<void>;
  onResend: () => Promise<void>;
  isLoading: boolean;
}

export interface ResetPasswordStepProps {
  onSubmit: (data: ResetPasswordFormData) => Promise<boolean>;
  isLoading: boolean;
}

export interface ChangePasswordModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}
