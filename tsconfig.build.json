{
  "extends": "./tsconfig.json",
  "exclude": ["node_modules", "test", "dist", "**/*spec.ts"],
  "watchOptions": {
    // Use native file system events for files and directories
    "watchFile": "priorityPollingInterval",
    "watchDirectory": "dynamicprioritypolling",
    // Poll files for updates more frequently
    // when they're updated a lot.
    "fallbackPolling": "dynamicPriority",
    // Don't coalesce watch notification
    "synchronousWatchDirectory": true,
    // Finally, two additional settings for reducing the amount of possible
    // files to track  work from these directories
    "excludeDirectories": ["**/node_modules", "dist"]
  }
}
