import { configureStore } from "@reduxjs/toolkit";
import authReducer from "@/redux/slices/authSlice";
import { authApi } from "@/services/api/auth";
import { analyst<PERSON>pi } from "@/services/api/analyst";
import chatAiReducer from "@/redux/slices/chatAiSlice";
import { chatAiApi } from "@/services/api/chat-ai";
import { careerRoadmapApi } from "@/services/api/career-roadmap";
import { profileApi } from "@/services/api/profile";
import { persistReducer, persistStore } from "redux-persist";
import { analystPersistConfig } from "./persist/analystPersistConfig";
import { setupListeners } from "@reduxjs/toolkit/query";
import { interviewQuestionApi } from "@/services/api/interview-question";
import { interviewQuestionPersistConfig } from "./persist/interviewQuestionPersistConfig";
import { conversationPersistConfig } from "./persist/conversationPersisConfig";
import { conversationApi } from "@/services/api/conversation";
import { coverLetterAI } from "@/services/api/cover-letter";
import { persistedCoverLetterReducer } from "./persist/coverLetterPersistConfig";
const persistedAnalystReducer = persistReducer(
  analystPersistConfig,
  analystApi.reducer
);

const persistedInterviewQuestionReducer = persistReducer(
  interviewQuestionPersistConfig,
  interviewQuestionApi.reducer
);

const persistedConversationReducer = persistReducer(
  conversationPersistConfig,
  conversationApi.reducer
);

export const store = configureStore({
  reducer: {
    auth: authReducer,
    chatAi: chatAiReducer,
    coverLetter: persistedCoverLetterReducer,
    [chatAiApi.reducerPath]: chatAiApi.reducer,
    [authApi.reducerPath]: authApi.reducer,
    [analystApi.reducerPath]: persistedAnalystReducer,
    [careerRoadmapApi.reducerPath]: careerRoadmapApi.reducer,
    [profileApi.reducerPath]: profileApi.reducer,
    [interviewQuestionApi.reducerPath]: persistedInterviewQuestionReducer,
    [conversationApi.reducerPath]: persistedConversationReducer,
    [coverLetterAI.reducerPath]: coverLetterAI.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          "persist/PERSIST",
          "persist/REHYDRATE",
          "persist/PAUSE",
          "persist/PURGE",
          "persist/FLUSH",
          "persist/REGISTER",
        ],
      },
    }).concat(
      authApi.middleware,
      chatAiApi.middleware,
      analystApi.middleware,
      careerRoadmapApi.middleware,
      profileApi.middleware,
      interviewQuestionApi.middleware,
      conversationApi.middleware,
      coverLetterAI.middleware
    ),
});

setupListeners(store.dispatch);

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
