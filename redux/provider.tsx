"use client";

import { useRef, useEffect } from "react";
import { Provider } from "react-redux";
import { persistor, store } from "@/redux/store";
import { initializeAuth } from "./slices/authSlice";
import { PersistGate } from "redux-persist/integration/react";

export default function ReduxProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current) {
      if (typeof window !== "undefined") {
        store.dispatch(initializeAuth());
        initialized.current = true;
      }
    }
  }, []);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        {children}
      </PersistGate>
    </Provider>
  );
}
