import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import coverLetterReducer from '../slices/coverLetterSlice';

const coverLetterPersistConfig = {
  key: 'coverLetter',
  storage,
  whitelist: ['selectedTemplate', 'currentCoverLetterId'], // Only persist these fields
};

export const persistedCoverLetterReducer = persistReducer(
  coverLetterPersistConfig,
  coverLetterReducer
);
