import { conversationApi } from "@/services/api/conversation";
import { PersistConfig } from "redux-persist";
import storage from "redux-persist/lib/storage";

const noopStorage = {
  getItem: () => Promise.resolve(null),
  setItem: () => Promise.resolve(),
  removeItem: () => Promise.resolve(),
};

const storageEngine = typeof window !== "undefined" ? storage : noopStorage;

export const conversationPersistConfig: PersistConfig<any> = {
  key: conversationApi.reducerPath,
  storage: storageEngine,
  whitelist: [],
};
