import { interviewQuestionApi } from "@/services/api/interview-question";
import { PersistConfig } from "redux-persist";
import storage from "redux-persist/lib/storage";

const noopStorage = {
  getItem: () => Promise.resolve(null),
  setItem: () => Promise.resolve(),
  removeItem: () => Promise.resolve(),
};

const storageEngine = typeof window !== "undefined" ? storage : noopStorage;

export const interviewQuestionPersistConfig: PersistConfig<any> = {
  key: interviewQuestionApi.reducerPath,
  storage: storageEngine,
  whitelist: [],
};
