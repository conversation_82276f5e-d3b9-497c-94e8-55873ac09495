import storage from "redux-persist/lib/storage";
import { PersistConfig } from "redux-persist";
import { analystApi } from "@/services/api/analyst";

const noopStorage = {
  getItem: () => Promise.resolve(null),
  setItem: () => Promise.resolve(),
  removeItem: () => Promise.resolve(),
};

const storageEngine = typeof window !== "undefined" ? storage : noopStorage;

export const analystPersistConfig: PersistConfig<any> = {
  key: analystApi.reducerPath,
  storage: storageEngine,
  whitelist: [],
};
