import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface CoverLetterState {
  selectedTemplate: string | null; // 'temp1', 'temp2', 'temp3'
  currentCoverLetterId: string | null;
}

const initialState: CoverLetterState = {
  selectedTemplate: null,
  currentCoverLetterId: null,
};

const coverLetterSlice = createSlice({
  name: 'coverLetter',
  initialState,
  reducers: {
    setSelectedTemplate: (state, action: PayloadAction<string>) => {
      state.selectedTemplate = action.payload;
    },
    setCurrentCoverLetterId: (state, action: PayloadAction<string>) => {
      state.currentCoverLetterId = action.payload;
    },
    clearCoverLetterSelection: (state) => {
      state.selectedTemplate = null;
      state.currentCoverLetterId = null;
    },
  },
});

export const {
  setSelectedTemplate,
  setCurrentCoverLetterId,
  clearCoverLetterSelection,
} = coverLetterSlice.actions;

export default coverLetterSlice.reducer;
