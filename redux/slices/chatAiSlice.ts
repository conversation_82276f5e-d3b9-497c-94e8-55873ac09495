import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ChatAiState } from "../types/chatAi";
import { ChatMessageContent, ChatSession } from "@/common/types/chat-ai";
const initialState: ChatAiState = {
  mySessions: [],
  currentSession: {
    _id: "",
    sessionId: "",
    content: [],
    loading: false,
  },
};

const chatAiSlice = createSlice({
  name: "chatAi",
  initialState,
  reducers: {
    createNewSession: (state, action: PayloadAction<ChatSession>) => {
      state.mySessions.unshift(action.payload);
      state.currentSession = action.payload;
    },
    setMySessions: (state, action: PayloadAction<ChatSession[]>) => {
      state.mySessions = action.payload;
    },
    setCurrentSession: (state, action: PayloadAction<ChatSession>) => {
      if (action.payload) {
        state.currentSession = action.payload;
      } else {
        state.currentSession = {
          _id: "",
          sessionId: "",
          content: [],
          loading: false,
        };
      }
    },
    setLoadingChatSession: (state, action: PayloadAction<boolean>) => {
      if (state.currentSession) {
        state.currentSession.loading = action.payload;
      }
    },
    setMessageChatSession: (
      state,
      action: PayloadAction<{ sessionId: string; message: ChatMessageContent }>
    ) => {
      const { sessionId, message } = action.payload;
      // cập nhật currentSession
      if (state.currentSession?.sessionId === sessionId) {
        state.currentSession.content.push(message);
      } else {
        state.currentSession = {
          _id: sessionId,
          sessionId,
          content: [message],
        };
      }

      // cập nhật mySessions
      const index = state.mySessions.findIndex(
        (session) => session.sessionId === sessionId
      );
      if (index !== -1) {
        state.mySessions[index].content.unshift(message);
      } else {
        state.mySessions.push({
          _id: sessionId,
          sessionId,
          content: [message],
        });
      }
    },
  },
});

export const {
  setMySessions,
  setCurrentSession,
  setLoadingChatSession,
  setMessageChatSession,
  createNewSession,
} = chatAiSlice.actions;
export default chatAiSlice.reducer;
