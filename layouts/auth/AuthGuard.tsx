"use client";

import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { RootState } from "@/redux/store";
import LoadingSpinner from "@/features/auth/components/LoadingSpinner";
import { AuthGuardProps } from "@/features/auth/auth";

export default function AuthGuard({
  children,
  requireAuth = true,
}: Readonly<AuthGuardProps>) {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const { isAuthenticated, accessToken, authLoaded, user } = useSelector(
    (state: RootState) => state.auth
  );

  useEffect(() => {
    if (!authLoaded || isRedirecting) return;

    if (requireAuth && !isAuthenticated && !accessToken) {
      setIsRedirecting(true);
      router.push("/login");
      return;
    }

    if (requireAuth && accessToken && !user) {
      return;
    }
  }, [
    isAuthenticated,
    accessToken,
    user,
    requireAuth,
    router,
    authLoaded,
    isRedirecting,
  ]);

  if (!authLoaded) {
    return <LoadingSpinner message="Đang kiểm tra xác thực..." />;
  }

  if (isRedirecting) {
    return <LoadingSpinner message="Đang chuyển hướng..." />;
  }

  if (requireAuth && accessToken && !user) {
    return <LoadingSpinner message="Đang tải thông tin người dùng..." />;
  }

  if (requireAuth && !isAuthenticated && !accessToken) {
    return <LoadingSpinner message="Đang xác thực..." />;
  }

  return <>{children}</>;
}
