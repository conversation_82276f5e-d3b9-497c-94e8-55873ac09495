diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/.transforms/f3d6a5ad04adfb70b17b4e5ddf93ddcd/results.bin b/node_modules/@daily-co/react-native-daily-js/android/build/.transforms/f3d6a5ad04adfb70b17b4e5ddf93ddcd/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/.transforms/f3d6a5ad04adfb70b17b4e5ddf93ddcd/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/.transforms/f3d6a5ad04adfb70b17b4e5ddf93ddcd/transformed/classes/classes_dex/classes.dex b/node_modules/@daily-co/react-native-daily-js/android/build/.transforms/f3d6a5ad04adfb70b17b4e5ddf93ddcd/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..5d9a094
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/.transforms/f3d6a5ad04adfb70b17b4e5ddf93ddcd/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/generated/source/buildConfig/debug/com/daily/reactlibrary/BuildConfig.java b/node_modules/@daily-co/react-native-daily-js/android/build/generated/source/buildConfig/debug/com/daily/reactlibrary/BuildConfig.java
new file mode 100644
index 0000000..06207c8
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/generated/source/buildConfig/debug/com/daily/reactlibrary/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.daily.reactlibrary;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.daily.reactlibrary";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..f87c0e6
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.daily.reactlibrary" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..98a93ff
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.daily.reactlibrary",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar
new file mode 100644
index 0000000..5364bf3
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..d89ce09
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..b931681
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
@@ -0,0 +1 @@
+int drawable ic_daily_videocam_24dp 0x0
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_ic_daily_videocam_24dp.xml.flat b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_ic_daily_videocam_24dp.xml.flat
new file mode 100644
index 0000000..bb7e713
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/compiled_local_resources/debug/compileDebugLibraryResources/out/drawable_ic_daily_videocam_24dp.xml.flat differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..b49fb4c
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1,2 @@
+#Tue Jul 01 01:05:00 ICT 2025
+com.daily.reactlibrary.daily-co_react-native-daily-js-main-6\:/drawable/ic_daily_videocam_24dp.xml=E\:\\Summer2025\\Project_Group\\learn_vox_mobile\\node_modules\\@daily-co\\react-native-daily-js\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_daily_videocam_24dp.xml
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..42b92a6
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\res"><file name="ic_daily_videocam_24dp" path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\res\drawable\ic_daily_videocam_24dp.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..5e7e980
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\debug\jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..6db0a18
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\debug\shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..e672e4b
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/BuildConfig.class b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/BuildConfig.class
new file mode 100644
index 0000000..4568211
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/BuildConfig.class differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtils$1.class b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtils$1.class
new file mode 100644
index 0000000..a39b926
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtils$1.class differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtils.class b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtils.class
new file mode 100644
index 0000000..3184014
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtils.class differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtilsPackage.class b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtilsPackage.class
new file mode 100644
index 0000000..8cbbbcc
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyNativeUtilsPackage.class differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyOngoingMeetingForegroundService.class b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyOngoingMeetingForegroundService.class
new file mode 100644
index 0000000..9f6838d
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/daily/reactlibrary/DailyOngoingMeetingForegroundService.class differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..6a89439
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,3 @@
+R_DEF: Internal format may change without notice
+local
+drawable ic_daily_videocam_24dp
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..5e31c6f
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,7 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.daily.reactlibrary" >
+4
+5    <uses-sdk android:minSdkVersion="24" />
+6
+7</manifest>
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..f87c0e6
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.daily.reactlibrary" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/ic_daily_videocam_24dp.xml b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/ic_daily_videocam_24dp.xml
new file mode 100644
index 0000000..e23eac8
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/packaged_res/debug/packageDebugResources/drawable/ic_daily_videocam_24dp.xml
@@ -0,0 +1,9 @@
+<vector xmlns:android="http://schemas.android.com/apk/res/android"
+        android:width="24dp"
+        android:height="24dp"
+        android:viewportWidth="24.0"
+        android:viewportHeight="24.0">
+    <path
+        android:fillColor="#FF000000"
+        android:pathData="M17,10.5V7c0,-0.55 -0.45,-1 -1,-1H4c-0.55,0 -1,0.45 -1,1v10c0,0.55 0.45,1 1,1h12c0.55,0 1,-0.45 1,-1v-3.5l4,4v-11l-4,4z"/>
+</vector>
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar
new file mode 100644
index 0000000..120a7d6
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar differ
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..9dee578
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1,2 @@
+com.daily.reactlibrary
+drawable ic_daily_videocam_24dp
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@daily-co/react-native-daily-js/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..600d21f
--- /dev/null
+++ b/node_modules/@daily-co/react-native-daily-js/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,17 @@
+-- Merging decision tree log ---
+manifest
+ADDED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml:1:1-4:12
+INJECTED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml:1:1-4:12
+	package
+		ADDED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml:2:11-43
+		INJECTED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml
+	xmlns:android
+		ADDED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml:1:11-69
+uses-sdk
+INJECTED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml
+INJECTED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from E:\Summer2025\Project_Group\learn_vox_mobile\node_modules\@daily-co\react-native-daily-js\android\src\main\AndroidManifest.xml
diff --git a/node_modules/@daily-co/react-native-daily-js/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/@daily-co/react-native-daily-js/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..09e8602
Binary files /dev/null and b/node_modules/@daily-co/react-native-daily-js/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@daily-co/react-native-daily-js/dist/DailyMediaView.js b/node_modules/@daily-co/react-native-daily-js/dist/DailyMediaView.js
index 41f7f84..2c6b585 100644
--- a/node_modules/@daily-co/react-native-daily-js/dist/DailyMediaView.js
+++ b/node_modules/@daily-co/react-native-daily-js/dist/DailyMediaView.js
@@ -26,7 +26,7 @@ Object.defineProperty(exports, "__esModule", { value: true });
 var React = __importStar(require("react"));
 var react_1 = require("react");
 var react_native_1 = require("react-native");
-var react_native_webrtc_1 = require("@daily-co/react-native-webrtc");
+var react_native_webrtc_1 = require("react-native-webrtc");
 function DailyMediaView(props) {
     var _a = (0, react_1.useState)(null), stream = _a[0], setStream = _a[1];
     (0, react_1.useEffect)(function () {
diff --git a/node_modules/@daily-co/react-native-daily-js/dist/index.js b/node_modules/@daily-co/react-native-daily-js/dist/index.js
index ab000ab..580b7fd 100644
--- a/node_modules/@daily-co/react-native-daily-js/dist/index.js
+++ b/node_modules/@daily-co/react-native-daily-js/dist/index.js
@@ -30,7 +30,7 @@ var __importDefault = (this && this.__importDefault) || function (mod) {
 Object.defineProperty(exports, "__esModule", { value: true });
 exports.DailyMediaView = void 0;
 var daily_js_1 = __importDefault(require("@daily-co/daily-js"));
-var react_native_webrtc_1 = require("@daily-co/react-native-webrtc");
+var react_native_webrtc_1 = require("react-native-webrtc");
 var DailyMediaView_1 = __importDefault(require("./DailyMediaView"));
 exports.DailyMediaView = DailyMediaView_1.default;
 var iOSCallObjectBundleCache_1 = __importDefault(require("./iOSCallObjectBundleCache"));
@@ -147,4 +147,4 @@ setupEventListeners();
 setupGlobals();
 exports.default = daily_js_1.default;
 __exportStar(require("@daily-co/daily-js"), exports);
-__exportStar(require("@daily-co/react-native-webrtc"), exports);
+__exportStar(require("react-native-webrtc"), exports);
