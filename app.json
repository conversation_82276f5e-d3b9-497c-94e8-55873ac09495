{"expo": {"name": "Learn Vox", "slug": "ai-interviewer", "version": "1.0.0", "orientation": "portrait", "owner": "wyn2004", "icon": "./assets/images/icon.png", "scheme": "aiinterviewer", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.nhanbernie.boltexponativewind", "infoPlist": {"UIBackgroundModes": ["voip", "audio"], "NSMicrophoneUsageDescription": "This app needs access to microphone for voice calls.", "NSCameraUsageDescription": "This app needs access to camera for video calls.", "AVAudioSessionCategoryAmbient": false, "AVAudioSessionCategoryPlayback": true}, "bitcode": false}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-font", "expo-web-browser", "expo-secure-store", "@daily-co/config-plugin-rn-daily-js", ["expo-build-properties", {"android": {"minSdkVersion": 24}, "ios": {"deploymentTarget": "15.1"}}], "expo-audio"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "8740da54-8ba0-40ff-ad64-018f250cbe6c"}}, "android": {"package": "com.nhanbernie.boltexponativewind", "permissions": ["RECORD_AUDIO", "CAMERA", "MODIFY_AUDIO_SETTINGS", "WAKE_LOCK", "READ_EXTERNAL_STORAGE", "android.permission.ACCESS_NETWORK_STATE", "android.permission.CAMERA", "android.permission.INTERNET", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.RECORD_AUDIO", "android.permission.SYSTEM_ALERT_WINDOW", "android.permission.WAKE_LOCK", "android.permission.BLUETOOTH", "android.permission.POST_NOTIFICATIONS", "android.permission.BLUETOOTH_ADMIN", "android.permission.BROADCAST_STICKY"]}, "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/8740da54-8ba0-40ff-ad64-018f250cbe6c"}}}