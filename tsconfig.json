{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/common/*": ["./common/*"], "@/types/*": ["./common/types/*"], "@/enums/*": ["./common/enums/*"], "@/constants/*": ["./common/constants/*"], "@/config/*": ["./common/config/*"], "@/components/*": ["./components/*"], "@/layouts/*": ["./components/layouts/*"], "@/contexts/*": ["./contexts/*"], "@/hooks/*": ["./hooks/*"], "@/locales/*": ["./locales/*"], "@/services/*": ["./services/*"], "@/styles/*": ["./styles/*"], "@/utils/*": ["./utils/*"], "@/libs/*": ["./libs/*"], "@/selectors/*": ["./redux/selectors/*"], "@/thunks/*": ["./redux/thunks/*"], "@/slices/*": ["./redux/slices/*"], "@/redux/*": ["./redux/*"], "@/screens/*": ["./screens/*"], "@/assets/*": ["./assets/*"], "@/ui/*": ["./components/ui/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts", "babel.config.js", "src/screens/index.ts"]}