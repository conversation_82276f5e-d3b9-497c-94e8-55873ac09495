{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@modules/*": ["src/modules/*"], "@configs/*": ["src/configs/*"], "@common/*": ["src/common/*"], "@exceptions/*": ["src/exceptions/*"], "@utils/*": ["src/utils/*"], "@middlewares/*": ["src/middlewares/*"], "@guards/*": ["src/guards/*"], "@decorators/*": ["src/decorators/*"], "@database/*": ["src/database/*"], "@constants/*": ["src/constants/*"], "@types/*": ["src/types/*"]}}}