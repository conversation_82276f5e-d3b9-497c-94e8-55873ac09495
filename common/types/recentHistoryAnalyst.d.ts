export interface Analyst {
  _id: string;
  createdAt?: string;
  cvFileUrl: string;
  cvText?: string;
  content: {
    overall_score: number;
  };
  analysisType?: "role" | "jd";
  agentType?: string;
}

export interface RecentAnalysesProps {
  analysts: Analyst[];
  displayedAnalysts: Analyst[];
  isLoading: boolean;
  error: any;
  showAllHistory: boolean;
  onShowAllHistory: (show: boolean) => void;
  onViewReport: (analystId: string, analysisType?: "role" | "jd") => void;
  onRefetch: () => void;
}