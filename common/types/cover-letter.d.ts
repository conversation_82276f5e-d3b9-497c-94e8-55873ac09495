import React from "react";

export interface CustomSection {
  id: string;
  content: string;
  afterField: string;
}

export interface FormData {
  // Header
  fullName: string;
  targetPosition: string;
  profileImage: string;

  // Contact Information
  dateOfBirth: string;
  phoneNumber: string;
  emailAddress: string;
  address: string;
  linkedin: string;

  // Recipient Information
  hiringManagerName: string;
  hiringManagerTitle: string;
  companyName: string;
  companyAddress: string;
  applicationDate: string;
  jobTitle: string;

  // Greeting
  greeting: string;

  // Content Paragraphs
  openingParagraph: string;
  experienceSection: string;
  skillsSection: string;
  closingParagraph: string;

  // Signature
  signatureClosing: string;
  signatureName: string;

  // Custom sections
  customSections?: CustomSection[];
}

export interface CoverLetterState {
  formData: FormData;
  editingField: string | null;
  headerColor: string;
  contactColor: string;
  panelColor?: string;
  textColor?: string;
  showToolbar: string | null;
  fontSize: string;
  fontFamily: string;
  lineHeight: string;
  textAlign: string;
  isBold: boolean;
  isItalic: boolean;
  isUnderline: boolean;
}

export interface CoverLetterActions {
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  setEditingField: React.Dispatch<React.SetStateAction<string | null>>;
  setHeaderColor: React.Dispatch<React.SetStateAction<string>>;
  setContactColor: React.Dispatch<React.SetStateAction<string>>;
  setPanelColor?: React.Dispatch<React.SetStateAction<string>>;
  setTextColor?: React.Dispatch<React.SetStateAction<string>>;
  setShowToolbar: React.Dispatch<React.SetStateAction<string | null>>;
  setFontSize: React.Dispatch<React.SetStateAction<string>>;
  setFontFamily: React.Dispatch<React.SetStateAction<string>>;
  setLineHeight: React.Dispatch<React.SetStateAction<string>>;
  setTextAlign: React.Dispatch<React.SetStateAction<string>>;
  setIsBold: React.Dispatch<React.SetStateAction<boolean>>;
  setIsItalic: React.Dispatch<React.SetStateAction<boolean>>;
  setIsUnderline: React.Dispatch<React.SetStateAction<boolean>>;
}
