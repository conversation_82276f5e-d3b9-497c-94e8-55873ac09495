import { AiAgentRoadmapAiType } from '@common/enum/agentType.enum';

export interface RoadmapNode {
  id: string;
  type: string;
  position: {
    x: number;
    y: number;
  };
  data: {
    title: string;
    description: string;
    link?: string;
    estimatedTime?: string;
  };
}

export interface RoadmapEdge {
  id: string;
  source: string;
  target: string;
}

export interface RoadmapContent {
  summary: string;
  steps: string[];
  skills: string[];
  analysis: string;
  roadmapTitle: string;
  description: string;
  duration: string;
  initialNodes: RoadmapNode[];
  initialEdges: RoadmapEdge[];
}

export interface ResponseRoadmapAiByRoleDescriptionDto {
  _id: string;
  userId: string;
  content: RoadmapContent;
  agentType: AiAgentRoadmapAiType;
  cvFileUrl: string;
  cvText: string;
  roleDescription: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResponseRoadmapAiByJdDescriptionDto {
  _id: string;
  userId: string;
  content: RoadmapContent;
  agentType: AiAgentRoadmapAiType;
  cvFileUrl: string;
  cvText: string;
  jdFileUrl: string;
  jdText: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResponseRoadmapAiDto {
  _id: string;
  userId: string;
  content: RoadmapContent;
  agentType: AiAgentRoadmapAiType;
  cvFileUrl?: string;
  cvText?: string;
  jdFileUrl?: string;
  jdText?: string;
  roleDescription?: string;
  jobPosition?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateRoadmapRequest {
  cvFileUrl?: string;
  cvText?: string;
  jdFileUrl?: string;
  jdText?: string;
  roleDescription?: string;
  jobPosition?: string;
}
