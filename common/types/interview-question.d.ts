import { InterviewTypeEnum } from "../enums/interviewType.enum";

export interface InterviewQuestionSessionType {
  _id: string;
  userId: string;
  jobPosition: string;
  interviewDuration: string;
  interviewType: InterviewTypeEnum[];
  jobDescriptions: string;
  totalQuestion: number;
  interviewQuestions: InterviewQuestion[];
  jdFileUrl: string;
  createdAt: string;
  updatedAt: string;
  conversations: ConversationResponse[];
}

export interface InterviewQuestion {
  question: string;
  type: InterviewTypeEnum;
  estimated_time_minutes: number;
  tip: string;
}
