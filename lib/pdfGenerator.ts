import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  PDFGenerationOptions,
  CoverLetterPDFOptions,
  getPDFCSS,
  applyPDFStyling,
  hideToolbarElements,
  restoreToolbarElements,
} from "./pdfUtils";

const detectLargeFontAndSpacing = (element: HTMLElement): boolean => {
  try {
    const computedStyle = window.getComputedStyle(element);
    const fontSize = parseFloat(computedStyle.fontSize);
    const lineHeight = parseFloat(computedStyle.lineHeight);

    const lineHeightRatio =
      lineHeight > 10 ? lineHeight / fontSize : lineHeight;

    const hasLargeFont = fontSize >= 18;
    const hasHighLineSpacing = lineHeightRatio >= 1.8;

    const result = hasLargeFont && hasHighLineSpacing;

    return result;
  } catch {
    return false;
  }
};

const detectLargeFontFromClasses = (element: HTMLElement): boolean => {
  const classList = element.classList;
  const hasLargeFontClass = classList.contains("pdf-font-l");
  const hasHighLineSpacingClass = classList.contains("pdf-line-height-1-8");

  const result = hasLargeFontClass && hasHighLineSpacingClass;

  return result;
};

const createSmartPageCanvases = async (
  element: HTMLElement,
  options: { scale: number; useCORS: boolean; allowTaint: boolean },
  templateId?: string
): Promise<HTMLCanvasElement[]> => {
  const canvases: HTMLCanvasElement[] = [];

  // First, capture the full element to get total dimensions
  const fullCanvas = await html2canvas(element, {
    ...options,
    backgroundColor: "#ffffff",
    logging: false,
    removeContainer: true,
    imageTimeout: 15000,
    foreignObjectRendering: false,
    allowTaint: true,
    useCORS: true,
    scrollX: 0,
    scrollY: 0,
  });

  const totalHeight = fullCanvas.height;
  const imgWidth = 210;
  const pageHeight = 297;
  const imgHeight = (totalHeight * imgWidth) / fullCanvas.width;

  const isTemp1 = templateId === "temp1";
  const isTemp2Or3 = templateId === "temp2" || templateId === "temp3";

  const hasLargeFontAndSpacing = detectLargeFontAndSpacing(element);
  const hasLargeFontFromClasses = detectLargeFontFromClasses(element);

  const shouldForcePageBreaking =
    hasLargeFontAndSpacing || hasLargeFontFromClasses;

  if (shouldForcePageBreaking) {
  } else {
    if (isTemp2Or3) {
      canvases.push(fullCanvas);
      return canvases;
    }

    if (isTemp1) {
      canvases.push(fullCanvas);
      return canvases;
    }

    if (imgHeight <= pageHeight * 1.15) {
      canvases.push(fullCanvas);
      return canvases;
    }
  }

  const pageHeightInCanvasPixels = (pageHeight * fullCanvas.width) / imgWidth;

  const sections = element.querySelectorAll("[data-section]");
  const elementRect = element.getBoundingClientRect();
  const sectionInfo = Array.from(sections).map((section, index) => {
    const sectionRect = section.getBoundingClientRect();
    const sectionTop = sectionRect.top - elementRect.top;
    const sectionHeight = sectionRect.height;

    return {
      element: section,
      name: section.getAttribute("data-section"),
      index,
      topCanvas: (sectionTop * fullCanvas.height) / element.offsetHeight,
      heightCanvas: (sectionHeight * fullCanvas.height) / element.offsetHeight,
      bottomCanvas:
        ((sectionTop + sectionHeight) * fullCanvas.height) /
        element.offsetHeight,
    };
  });

  const breakPoints: number[] = [0];
  let currentPageStart = 0;

  const hasContactInfo = sectionInfo.some((s) => s.name === "contactInfo");
  const isMultiColumnLayout = hasContactInfo;

  if (shouldForcePageBreaking) {
    const skillsSection = sectionInfo.find((s) => s.name === "skillsSection");
    const experienceSection = sectionInfo.find(
      (s) => s.name === "experienceSection"
    );
    const closingSection = sectionInfo.find(
      (s) => s.name === "closingParagraph"
    );

    let breakPoint = null;
    const maxPage1Height = pageHeightInCanvasPixels * 0.85;

    if (skillsSection && skillsSection.bottomCanvas <= maxPage1Height) {
      breakPoint = skillsSection.bottomCanvas + 20;
    } else if (
      experienceSection &&
      experienceSection.bottomCanvas <= maxPage1Height
    ) {
      breakPoint = experienceSection.bottomCanvas + 20;
    } else if (closingSection) {
      breakPoint = closingSection.topCanvas - 10;
    } else {
      breakPoint = maxPage1Height;
    }

    if (breakPoint) {
      breakPoints.push(breakPoint);
    }
  } else if (isTemp1) {
    const signatureSection = sectionInfo.find((s) => s.name === "signature");
    if (signatureSection) {
      const contentBeforeSignature = signatureSection.topCanvas;
      if (contentBeforeSignature > pageHeightInCanvasPixels * 0.9) {
        breakPoints.push(signatureSection.topCanvas);
      }
    }
  } else {
    for (let i = 0; i < sectionInfo.length; i++) {
      const section = sectionInfo[i];
      const currentPageUsed = section.bottomCanvas - currentPageStart;

      if (isMultiColumnLayout && section.name === "contactInfo") {
        continue;
      }

      const pageThreshold = isMultiColumnLayout ? 0.85 : 0.9;

      if (currentPageUsed > pageHeightInCanvasPixels * pageThreshold) {
        const isTextSection = [
          "openingParagraph",
          "experienceSection",
          "skillsSection",
          "closingParagraph",
        ].includes(section.name || "");

        const isSignatureSection = section.name === "signature";

        if (isSignatureSection) {
          const closingSection = sectionInfo.find(
            (s) => s.name === "closingParagraph"
          );
          if (closingSection) {
            const combinedHeight =
              section.bottomCanvas - closingSection.topCanvas;
            if (combinedHeight < pageHeightInCanvasPixels * 0.4) {
              continue;
            }
          }
        }

        if (
          isTextSection &&
          section.heightCanvas > pageHeightInCanvasPixels * 0.5
        ) {
          const sectionElement = section.element as HTMLElement;
          const paragraphs = sectionElement.querySelectorAll(
            "p, div[data-field], .ProseMirror"
          );

          if (paragraphs.length > 1) {
            for (let p = 0; p < Math.min(paragraphs.length - 1, 2); p++) {
              const paragraph = paragraphs[p] as HTMLElement;
              const paraRect = paragraph.getBoundingClientRect();
              const paraEnd =
                ((paraRect.bottom - elementRect.top) * fullCanvas.height) /
                element.offsetHeight;

              if (
                paraEnd - currentPageStart < pageHeightInCanvasPixels * 0.75 &&
                paraEnd - currentPageStart > pageHeightInCanvasPixels * 0.3 // Ensure meaningful content
              ) {
                breakPoints.push(paraEnd);
                currentPageStart = paraEnd;
                continue;
              }
            }
          }
        }

        if (section.heightCanvas > pageHeightInCanvasPixels * 0.7) {
          if (section.topCanvas > currentPageStart + 100) {
            breakPoints.push(section.topCanvas);
            currentPageStart = section.topCanvas;
          }
        } else {
          breakPoints.push(section.topCanvas);
          currentPageStart = section.topCanvas;
        }
      }
    }
  }

  if (breakPoints.length === 1 && imgHeight > pageHeight * 1.2) {
    let currentY = pageHeightInCanvasPixels * 0.9;
    while (currentY < totalHeight) {
      breakPoints.push(currentY);
      currentY += pageHeightInCanvasPixels * 0.9;
    }
  }
  for (let i = 0; i < breakPoints.length; i++) {
    const startY = breakPoints[i];
    const endY = i < breakPoints.length - 1 ? breakPoints[i + 1] : totalHeight;
    const pageContentHeight = endY - startY;

    if (pageContentHeight < 100) {
      continue;
    }

    // For page content height, use the actual content height without artificial limits
    // This prevents large margins at the bottom of pages
    const actualPageHeight = pageContentHeight;

    // Create a new canvas for this page
    const pageCanvas = document.createElement("canvas");
    pageCanvas.width = fullCanvas.width;
    pageCanvas.height = actualPageHeight;

    const pageCtx = pageCanvas.getContext("2d");
    if (pageCtx) {
      // Ensure high quality rendering
      pageCtx.imageSmoothingEnabled = true;
      pageCtx.imageSmoothingQuality = "high";

      // Fill with white background
      pageCtx.fillStyle = "#ffffff";
      pageCtx.fillRect(0, 0, pageCanvas.width, pageCanvas.height);

      // Ensure we don't draw beyond the available content
      const sourceHeight = Math.min(actualPageHeight, totalHeight - startY);

      if (sourceHeight > 0) {
        // Draw the portion of the full canvas for this page
        pageCtx.drawImage(
          fullCanvas,
          0,
          startY, // Source x, y
          fullCanvas.width,
          sourceHeight, // Source width, height
          0,
          0, // Destination x, y
          fullCanvas.width,
          sourceHeight // Destination width, height
        );

        canvases.push(pageCanvas);
      }
    }

    if (canvases.length > 5) {
      break;
    }
  }

  // If no pages were created, return the full canvas
  if (canvases.length === 0) {
    canvases.push(fullCanvas);
  }

  return canvases;
};

// Enhanced PDF generation with smart page breaks
export const generatePDFFromElement = async (
  element: HTMLElement,
  options: PDFGenerationOptions = {},
  templateId?: string
): Promise<Blob> => {
  // Detect if we need to optimize for large font to prevent file size issues
  const hasLargeFontAndSpacing = detectLargeFontAndSpacing(element);
  const hasLargeFontFromClasses = detectLargeFontFromClasses(element);
  const shouldOptimizeForFileSize =
    hasLargeFontAndSpacing || hasLargeFontFromClasses;

  // Adjust scale and quality for large font to keep file size under 20MB
  // Use moderate scale for large font to balance quality and file size
  const optimizedScale = shouldOptimizeForFileSize ? 1.5 : 2; // Improved scale for better quality
  const { scale = optimizedScale, useCORS = true, allowTaint = true } = options;

  try {
    // Hide toolbar before capturing
    const hiddenElements = hideToolbarElements(element);

    // Use smart page breaking approach
    const pageCanvases = await createSmartPageCanvases(
      element,
      {
        scale,
        useCORS,
        allowTaint,
      },
      templateId
    );

    // Restore hidden elements
    restoreToolbarElements(hiddenElements);

    // Create PDF
    const pdf = new jsPDF("p", "mm", "a4");
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm (corrected)

    // Add each page canvas to PDF with optimized quality for large fonts
    pageCanvases.forEach((canvas: HTMLCanvasElement, index: number) => {
      if (index > 0) {
        pdf.addPage();
      }

      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Ensure the image fits within page bounds
      const finalHeight = Math.min(imgHeight, pageHeight);

      // Use high quality for better image clarity, optimize file size through other means
      const imageQuality = shouldOptimizeForFileSize ? 0.92 : 0.95; // High quality for better clarity
      const imageFormat = "PNG"; // Always use PNG to preserve colors and quality

      pdf.addImage(
        canvas.toDataURL(`image/${imageFormat.toLowerCase()}`, imageQuality),
        imageFormat,
        0,
        0,
        imgWidth,
        finalHeight
      );
    });

    // Return PDF as blob
    const blob = new Blob([pdf.output("arraybuffer")], {
      type: "application/pdf",
    });

    return blob;
  } catch {
    throw new Error("Failed to generate PDF");
  }
};

export const generateCoverLetterPDF = async (
  templateId: string,
  options: CoverLetterPDFOptions
): Promise<Blob> => {
  const element = document.getElementById(templateId);
  if (!element) {
    throw new Error(`Template element with ID "${templateId}" not found`);
  }

  try {
    // Clean up any existing PDF styles first
    const existingPDFStyles = document.querySelectorAll(
      "style[data-pdf-template]"
    );
    existingPDFStyles.forEach((style) => style.remove());

    // Get PDF-specific CSS for the template
    const pdfCSS = getPDFCSS(templateId);

    // Create a deep clone of the element to avoid affecting the original
    const clonedElement = element.cloneNode(true) as HTMLElement;

    // Create a temporary container for the cloned element
    const tempContainer = document.createElement("div");
    tempContainer.style.position = "absolute";
    tempContainer.style.left = "-9999px";
    tempContainer.style.top = "-9999px";
    tempContainer.style.width = "210mm";
    tempContainer.style.height = "297mm";
    tempContainer.appendChild(clonedElement);

    // Create and inject CSS styles with unique identifier
    const styleElement = document.createElement("style");
    styleElement.setAttribute("data-pdf-template", templateId);
    styleElement.innerHTML = pdfCSS;
    document.head.appendChild(styleElement);

    // Add container to document temporarily
    document.body.appendChild(tempContainer);

    // Apply PDF-specific styling and formatting
    applyPDFStyling(clonedElement, templateId, options);

    // Wait longer for CSS to be fully applied
    await new Promise((resolve) => setTimeout(resolve, 300));

    try {
      // Detect if we need to optimize for large font
      const hasLargeFontAndSpacing = detectLargeFontAndSpacing(clonedElement);
      const hasLargeFontFromClasses = detectLargeFontFromClasses(clonedElement);
      const shouldOptimizeForFileSize =
        hasLargeFontAndSpacing || hasLargeFontFromClasses;

      // Generate PDF from the styled clone with optimized settings
      const pdfBlob = await generatePDFFromElement(
        clonedElement,
        {
          ...options,
          scale: shouldOptimizeForFileSize ? 1.5 : 2, // Improved scale for better quality
          useCORS: true,
          allowTaint: true,
        },
        templateId
      );

      return pdfBlob;
    } finally {
      // Clean up: remove temporary container and styles
      if (tempContainer.parentNode) {
        document.body.removeChild(tempContainer);
      }
      if (styleElement.parentNode) {
        document.head.removeChild(styleElement);
      }
    }
  } catch (error) {
    throw new Error(
      `Failed to generate PDF for template ${templateId}: ${error}`
    );
  }
};
