export const handleViewFile = (fileUrl: string) => {
  if (fileUrl) {
    window.open(fileUrl, "_blank");
  }
};

export const handleDownloadFile = async (fileUrl: string, fileName: string) => {
  if (!fileUrl) return;

  try {
    const response = await fetch(fileUrl);

    if (response.ok) {
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = fileName;
      link.style.display = "none";

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(downloadUrl);
    } else {
      fallbackDownload(fileUrl, fileName);
    }
  } catch (error) {
    console.error("Download failed with fetch method:", error);
    fallbackDownload(fileUrl, fileName);
  }
};

const fallbackDownload = (fileUrl: string, fileName: string) => {
  const link = document.createElement("a");
  link.href = fileUrl;
  link.download = fileName;
  link.target = "_blank";

  link.setAttribute("download", fileName);
  link.rel = "noopener noreferrer";

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const handleDownloadFileIframe = (fileUrl: string) => {
  if (!fileUrl) return;

  const iframe = document.createElement("iframe");
  iframe.style.display = "none";
  iframe.src = fileUrl;

  document.body.appendChild(iframe);

  setTimeout(() => {
    document.body.removeChild(iframe);
  }, 1000);
};
