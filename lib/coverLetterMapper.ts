import { CoverLetter } from "@/services/api/cover-letter/types/cover-letter.d";
import { FormData } from "@/common/types/cover-letter";
import { parseSignature } from "./signatureParser";
import {
  COVER_LETTER_TEMP1_DEFAULTS,
  COVER_LETTER_TEMP1_DEFAULTS_VN,
  // COVER_LETTER_TEMP2_DEFAULTS,
  // COVER_LETTER_TEMP2_DEFAULTS_EN,
} from "@/features/cover-letter/constants/defaultFormData";

/**
 * Helper function to strip HTML tags for simple text fields
 */
const stripHtmlTags = (html: string): string => {
  if (!html) return "";
  return html.replace(/<[^>]*>/g, "").trim();
};

/**
 * Helper function to get default form data based on language
 */
const getDefaultFormData = (language?: string): FormData => {
  const isViet = language === "vietnamese";
  return isViet ? COVER_LETTER_TEMP1_DEFAULTS_VN : COVER_LETTER_TEMP1_DEFAULTS;
};

/**
 * Helper function to map API data to form data
 */
export const mapCoverLetterToFormData = (
  data?: CoverLetter,
  language?: string
): FormData => {
  const isViet = language === "vietnamese";

  if (!data) {
    return getDefaultFormData(language);
  }

  const content = data.content;
  const { signatureClosing, signatureName } = parseSignature(
    content.signature || ""
  );

  return {
    fullName:
      stripHtmlTags(content.header.fullName) ||
      (isViet ? "NGUYỄN VĂN A" : "JOHN SMITH"),
    targetPosition:
      stripHtmlTags(content.header.targetPosition) ||
      (isViet ? "Nhân Viên Kinh Doanh" : "Sales Representative"),
    profileImage: content.header.profileImage || "",

    dateOfBirth: stripHtmlTags(content.contactInfo.dateOfBirth) || "22/07/1992",
    phoneNumber:
      stripHtmlTags(content.contactInfo.phoneNumber) ||
      (isViet ? "[số điện thoại]" : "[Phone Number]"),
    emailAddress:
      stripHtmlTags(content.contactInfo.email) ||
      (isViet ? "[địa chỉ email]" : "[Email Address]"),
    address:
      stripHtmlTags(content.contactInfo.address) ||
      (isViet ? "Số 10, đường 10, Tp.HCM" : "123 Main Street, City"),
    linkedin: stripHtmlTags(content.contactInfo.linkedin) || "",

    hiringManagerName:
      stripHtmlTags(content.recipientInfo.hiringManagerName) ||
      (isViet ? "Kính gửi: Ông/Bà [Tên]" : "Dear: Mr./Ms. [Name]"),
    hiringManagerTitle:
      stripHtmlTags(content.recipientInfo.hiringManagerTitle) ||
      (isViet
        ? "Chức danh: [Vị trí / Phòng ban]"
        : "Title: [Position / Department]"),
    companyName:
      stripHtmlTags(content.recipientInfo.companyName) ||
      (isViet ? "Tên công ty: [Tên Công Ty]" : "Company Name: [Company Name]"),
    companyAddress:
      stripHtmlTags(content.recipientInfo.companyAddress) ||
      (isViet ? "Địa chỉ: [Địa chỉ]" : "Address: [Address]"),
    applicationDate:
      stripHtmlTags(content.recipientInfo.applicationDate) ||
      (isViet ? "Ngày: 05 tháng 07 năm 2025" : "Date: July 05, 2025"),
    jobTitle: (() => {
      if (content.recipientInfo.jobTitle) {
        const stripped = stripHtmlTags(content.recipientInfo.jobTitle);
        if (
          stripped.startsWith("Apply to:") ||
          stripped.startsWith("Ứng tuyển vị trí:")
        ) {
          return stripped;
        }
        return isViet
          ? `Ứng tuyển vị trí: ${stripped}`
          : `Apply to: ${stripped}`;
      }
      return isViet
        ? "Ứng tuyển vị trí: [Vị trí cần tuyển]"
        : "Apply to: [Position]";
    })(),

    greeting:
      stripHtmlTags(content.greetingLine) ||
      (isViet ? "Thưa Ông/Bà [Tên]," : "Dear Mr./Ms. [Name],"),

    openingParagraph: stripHtmlTags(content.openingParagraph) || "",
    experienceSection: stripHtmlTags(content.experienceAchievements) || "",
    skillsSection: stripHtmlTags(content.skillsStrengths) || "",
    closingParagraph: stripHtmlTags(content.closingParagraph) || "",
    signatureClosing:
      signatureClosing !== undefined
        ? signatureClosing
        : isViet
        ? "Trân trọng. Xin cám ơn!"
        : "Sincerely,",
    signatureName:
      signatureName !== undefined
        ? signatureName
        : isViet
        ? "Nguyễn Văn A"
        : "",
  };
};
