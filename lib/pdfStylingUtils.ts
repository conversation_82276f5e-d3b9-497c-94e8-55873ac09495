interface PDFStylingOptions {
  templateId: string;
  formData: any;
  hiddenSections?: string[];
  fontSize?: string;
  fontFamily?: string;
  lineHeight?: string;
  currentTheme?: string;
}

// Load PDF-specific CSS for a template
export const loadPDFCSS = (templateId: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const cssId = `pdf-css-${templateId}`;

    // Remove existing PDF CSS if any
    const existingCSS = document.getElementById(cssId);
    if (existingCSS) {
      existingCSS.remove();
    }

    // Create new link element for PDF CSS
    const link = document.createElement("link");
    link.id = cssId;
    link.rel = "stylesheet";
    link.type = "text/css";
    link.href = `/styles/customeStyle/pdf-CoverLetterTemp${templateId}.css`;

    link.onload = () => resolve();
    link.onerror = () =>
      reject(new Error(`Failed to load PDF CSS for template ${templateId}`));

    document.head.appendChild(link);
  });
};

// Remove PDF-specific CSS
export const removePDFCSS = (templateId: string): void => {
  const cssId = `pdf-css-${templateId}`;
  const existingCSS = document.getElementById(cssId);
  if (existingCSS) {
    existingCSS.remove();
  }
};

// Apply PDF styling to element
export const applyPDFStyling = (options: PDFStylingOptions) => {
  const { templateId, formData, hiddenSections = [] } = options;
  const element = document.getElementById(templateId);
  if (!element) return null;

  // Clone the element to avoid modifying the original
  const clonedElement = element.cloneNode(true) as HTMLElement;

  // Apply PDF container class
  clonedElement.classList.add("pdf-container");

  // Calculate content length to determine if compact mode is needed
  const contentLength = calculateContentLength(formData);
  if (contentLength > 2000) {
    clonedElement.classList.add("very-compact");
  } else if (contentLength > 1500) {
    clonedElement.classList.add("compact");
  }

  // Apply template-specific PDF styling
  switch (templateId) {
    case "temp1":
      applyTemp1PDFStyling(clonedElement);
      break;
    case "temp2":
      applyTemp2PDFStyling(clonedElement);
      break;
    case "temp3":
      applyTemp3PDFStyling(clonedElement);
      break;
  }

  // Hide sections that should be hidden
  hiddenSections.forEach((section) => {
    const sectionElement = clonedElement.querySelector(
      `[data-section="${section}"]`
    );
    if (sectionElement) {
      sectionElement.classList.add("pdf-hidden");
    }
  });

  return clonedElement;
};

const calculateContentLength = (formData: any): number => {
  const textFields = [
    "openingParagraph",
    "experienceSection",
    "skillsSection",
    "closingParagraph",
  ];

  return textFields.reduce((total, field) => {
    const content = formData[field] || "";
    return total + content.length;
  }, 0);
};

const applyTemp1PDFStyling = (element: HTMLElement) => {
  // Remove editor-specific classes and apply PDF classes

  // Header section
  const header = element.querySelector(
    ".flex.items-center.mb-4.p-4.rounded-lg"
  );
  if (header) {
    header.className = "pdf-header";

    // Profile image
    const profileContainer = header.querySelector(".w-20.h-20");
    if (profileContainer) {
      profileContainer.className = "pdf-profile-image";

      const img = profileContainer.querySelector("img");
      if (img) {
        img.style.cssText =
          "width: 60px; height: 60px; border-radius: 6px; object-fit: cover;";
      } else {
        profileContainer.className = "pdf-profile-placeholder";
      }
    }

    // Header info
    const headerInfo = header.querySelector(".flex-1");
    if (headerInfo) {
      headerInfo.className = "pdf-header-info";

      // Full name
      const fullName = headerInfo.querySelector('[data-field="fullName"]');
      if (fullName) {
        fullName.className = "pdf-full-name";
      }

      // Target position
      const targetPosition = headerInfo.querySelector(
        '[data-field="targetPosition"]'
      );
      if (targetPosition) {
        targetPosition.className = "pdf-target-position";
      }
    }
  }

  // Contact and recipient info grid
  const infoGrid = element.querySelector(".grid.grid-cols-1.md\\:grid-cols-2");
  if (infoGrid) {
    infoGrid.className = "pdf-info-grid";

    // Contact section
    const contactSection = infoGrid.querySelector(".rounded-2xl");
    if (contactSection) {
      contactSection.className = "pdf-contact-section";

      // Contact header
      const contactHeader = contactSection.querySelector(
        ".text-sm.font-semibold.text-orange-500"
      );
      if (contactHeader) {
        contactHeader.className = "pdf-contact-header";
      }

      // Contact items
      const contactItems =
        contactSection.querySelectorAll(".flex.items-center");
      contactItems.forEach((item) => {
        item.className = "pdf-contact-item";

        const icon = item.querySelector("svg");
        if (icon) {
          icon.parentElement!.className = "pdf-contact-icon";
        }

        const text = item.querySelector("[data-field]");
        if (text) {
          text.className = "pdf-contact-text";
        }
      });
    }

    // Recipient section
    const recipientSection = infoGrid.querySelector(".p-3.rounded-lg");
    if (recipientSection) {
      recipientSection.className = "pdf-recipient-section";

      const recipientItems = recipientSection.querySelectorAll("[data-field]");
      recipientItems.forEach((item) => {
        item.className = "pdf-recipient-item";
      });
    }
  }

  // Letter content
  const letterContent = element.querySelector(".space-y-3.p-3.rounded-lg");
  if (letterContent) {
    letterContent.className = "pdf-letter-content";

    // Content sections
    const sections = letterContent.querySelectorAll(".space-y-2");
    sections.forEach((section) => {
      const field = section.querySelector("[data-field]");
      if (field) {
        const fieldName = field.getAttribute("data-field");
        if (fieldName) {
          section.className = `pdf-${fieldName
            .replace(/([A-Z])/g, "-$1")
            .toLowerCase()}`;
        }
      }
    });
  }

  // Signature
  const signature = element.querySelector(".flex.flex-col.items-start");
  if (signature) {
    signature.className = "pdf-signature";

    const signatureClosing = signature.querySelector(
      '[data-field="signatureClosing"]'
    );
    if (signatureClosing) {
      signatureClosing.className = "pdf-signature-closing";
    }

    const signatureName = signature.querySelector(
      '[data-field="signatureName"]'
    );
    if (signatureName) {
      signatureName.className = "pdf-signature-name";
    }
  }
};

const applyTemp2PDFStyling = (element: HTMLElement) => {
  // Header section with horizontal layout
  const header = element.querySelector(
    ".flex.items-center.mb-4.p-4.rounded-lg"
  );
  if (header) {
    header.className = "pdf-header";

    const headerContent = document.createElement("div");
    headerContent.className = "pdf-header-content";

    // Profile section (left side)
    const profileSection = document.createElement("div");
    profileSection.className = "pdf-profile-section";

    // Contact section (right side)
    const contactSection = document.createElement("div");
    contactSection.className = "pdf-contact-section";
  }
};

const applyTemp3PDFStyling = (element: HTMLElement) => {
  // Apply PDF container class
  element.classList.add("pdf-container");

  // Header Section
  const header = element.querySelector('[data-section="header"]');
  if (header) {
    header.className = "pdf-header";

    // Profile image
    const profileImg = header.querySelector("img");
    if (profileImg) {
      profileImg.className = "pdf-profile-image";
    } else {
      const profilePlaceholder = header.querySelector(".w-20.h-20, .w-16.h-16");
      if (profilePlaceholder) {
        profilePlaceholder.className = "pdf-profile-placeholder";
      }
    }

    // Full name
    const fullName = header.querySelector('[data-field="fullName"]');
    if (fullName) {
      fullName.className = "pdf-full-name";
    }

    // Target position
    const targetPosition = header.querySelector(
      '[data-field="targetPosition"]'
    );
    if (targetPosition) {
      targetPosition.className = "pdf-target-position";
    }
  }

  // Contact Information
  const contactSection = element.querySelector('[data-section="contact"]');
  if (contactSection) {
    contactSection.className = "pdf-contact-section";

    // Contact header
    const contactHeader = contactSection.querySelector(
      ".text-sm.font-semibold, .font-semibold"
    );
    if (contactHeader) {
      contactHeader.className = "pdf-contact-header";
    }

    // Contact items
    const contactItems = contactSection.querySelectorAll("[data-field]");
    contactItems.forEach((item) => {
      const wrapper = document.createElement("div");
      wrapper.className = "pdf-contact-item";

      const icon = document.createElement("div");
      icon.className = "pdf-contact-icon";
      icon.textContent = "•";

      const text = document.createElement("div");
      text.className = "pdf-contact-text";
      text.textContent = item.textContent || "";

      wrapper.appendChild(icon);
      wrapper.appendChild(text);

      if (item.parentNode) {
        item.parentNode.replaceChild(wrapper, item);
      }
    });
  }

  // Recipient Information
  const recipientSection = element.querySelector('[data-section="recipient"]');
  if (recipientSection) {
    recipientSection.className = "pdf-recipient-section";

    const recipientItems = recipientSection.querySelectorAll("[data-field]");
    recipientItems.forEach((item) => {
      item.className = "pdf-recipient-item";
    });
  }

  // Letter Content
  const letterContent = element.querySelector(
    '[data-section="letter-content"]'
  );
  if (letterContent) {
    letterContent.className = "pdf-letter-content";
  }

  // Individual content sections
  const greeting = element.querySelector('[data-section="greeting"]');
  if (greeting) {
    greeting.className = "pdf-greeting";
  }

  const openingParagraph = element.querySelector(
    '[data-section="opening-paragraph"]'
  );
  if (openingParagraph) {
    openingParagraph.className = "pdf-opening-paragraph";
  }

  const experience = element.querySelector('[data-section="experience"]');
  if (experience) {
    experience.className = "pdf-experience-section";
  }

  const skills = element.querySelector('[data-section="skills"]');
  if (skills) {
    skills.className = "pdf-skills-section";
  }

  const closing = element.querySelector('[data-section="closing"]');
  if (closing) {
    closing.className = "pdf-closing-paragraph";
  }

  // Signature
  const signature = element.querySelector('[data-section="signature"]');
  if (signature) {
    signature.className = "pdf-signature";

    const signatureClosing = signature.querySelector(
      '[data-field="signatureClosing"]'
    );
    if (signatureClosing) {
      signatureClosing.className = "pdf-signature-closing";
    }

    const signatureName = signature.querySelector(
      '[data-field="signatureName"]'
    );
    if (signatureName) {
      signatureName.className = "pdf-signature-name";
    }
  }
};

export default applyPDFStyling;
