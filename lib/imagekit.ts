import ImageKit from "imagekit-javascript";
import { store } from "@/redux/store";
import { coverLetterAI } from "@/services/api/cover-letter";

// ImageKit configuration
const imagekit = new ImageKit({
  publicKey: process.env.NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY!,
  urlEndpoint: process.env.NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT!,
});

export interface ImageKitUploadOptions {
  fileName: string;
  folder?: string;
  useUniqueFileName?: boolean;
  tags?: string[];
}

export interface ImageKitUploadResponse {
  fileId: string;
  name: string;
  url: string;
  thumbnailUrl: string;
  height: number;
  width: number;
  size: number;
  filePath: string;
  tags: string[];
  isPrivateFile: boolean;
  customCoordinates: string | null;
  fileType: string;
}

export const uploadToImageKit = async (
  file: File | Blob,
  options: ImageKitUploadOptions
): Promise<ImageKitUploadResponse> => {
  const {
    fileName,
    folder = "cover-letters",
    useUniqueFileName = true,
    tags = ["cover-letter", "pdf"],
  } = options;

  try {
    // Get authentication parameters using direct fetch (fallback)
    const authResponse = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/imagekit/auth`
    );

    if (!authResponse.ok) {
      throw new Error("Failed to get ImageKit authentication");
    }

    const authData = await authResponse.json();

    const response = await imagekit.upload({
      file,
      fileName,
      folder,
      useUniqueFileName,
      tags,
      signature: authData.signature,
      token: authData.token,
      expire: authData.expire,
    });

    // Cleanup auth cache after successful upload
    store.dispatch(coverLetterAI.util.invalidateTags(["CoverLetter"]));

    return response as ImageKitUploadResponse;
  } catch {
    throw new Error("Failed to upload file to ImageKit");
  }
};

export const uploadPDFToImageKit = async (
  pdfBlob: Blob,
  fileName: string
): Promise<string> => {
  try {
    const pdfFile = new File(
      [pdfBlob],
      fileName.endsWith(".pdf") ? fileName : `${fileName}.pdf`,
      {
        type: "application/pdf",
      }
    );

    const response = await uploadToImageKit(pdfFile, {
      fileName: fileName.endsWith(".pdf") ? fileName : `${fileName}.pdf`,
      folder: "cover-letters",
      useUniqueFileName: true,
      tags: ["cover-letter", "pdf"],
    });

    return response.url;
  } catch {
    throw new Error("Failed to upload PDF to ImageKit");
  }
};

export const generateCoverLetterFileName = (
  coverLetterId: string,
  userFullName?: string
): string => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const sanitizedName = userFullName
    ? userFullName.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase()
    : "cover-letter";

  return `${sanitizedName}-${coverLetterId.slice(0, 8)}-${timestamp}`;
};

export default imagekit;
