// PDF CSS styles for all cover letter templates
// This file contains template-specific CSS for PDF generation

// PDF CSS for Template 1
export const getPDFCSSTemp1 = (): string => `
/* PDF-specific CSS for CoverLetterTemp1 - A4 Portrait Layout */
.pdf-export #temp1 {
  width: 210mm !important;
  max-width: 210mm !important;
  min-height: auto !important;
  max-height: none !important;
  margin: 0 !important;
  padding: 8mm !important;
  box-sizing: border-box !important;
  background: white !important;
  color: black !important;
  font-family: Arial, sans-serif !important;
  font-size: 9.5pt !important;
  line-height: 1.3 !important;
  word-spacing: 0.1em !important;
  letter-spacing: 0.02em !important;
  overflow: visible !important;
  page-break-after: avoid !important;
  page-break-inside: avoid !important;
}

/* Force all backgrounds to white in PDF - override any theme colors */
.pdf-export #temp1 * {
  background-color: white !important;
  background: white !important;
}

/* Exception: Keep text colors but force backgrounds to white */
.pdf-export #temp1 [style*="background"],
.pdf-export #temp1 .bg-blue-50,
.pdf-export #temp1 .bg-green-50,
.pdf-export #temp1 .bg-yellow-50,
.pdf-export #temp1 .bg-orange-50 {
  background-color: white !important;
  background: white !important;
}

/* Optimized spacing for better content fit */
.pdf-export [data-section] {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  margin-bottom: 4px !important;
}

.pdf-export .space-y-4 > div,
.pdf-export .space-y-6 > div {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  margin-bottom: 3px !important;
}

.pdf-export p,
.pdf-export .text-justify {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  orphans: 2 !important;
  widows: 2 !important;
  margin-bottom: 3px !important;
}

.pdf-export #temp1 * {
  box-shadow: none !important;
  text-shadow: none !important;
  background-image: none !important;
  border-radius: 0 !important;
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

.pdf-export #temp1 .flex.items-center.mb-4.p-4.rounded-lg {
  display: block !important;
  margin-bottom: 6mm !important;
  padding: 3mm !important;
  background: #f5f5f5 !important;
  border: 1px solid #ddd !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp1 .w-20.h-20 {
  width: 18mm !important;
  height: 18mm !important;
  float: left !important;
  margin-right: 4mm !important;
  margin-bottom: 2mm !important;
  background: #ddd !important;
  border: 1px solid #999 !important;
}

.pdf-export #temp1 .w-20.h-20 img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.pdf-export #temp1 .text-2xl.font-bold {
  font-size: 13pt !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 0.5mm !important;
  display: block !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp1 .text-blue-600.font-medium {
  font-size: 10pt !important;
  font-weight: normal !important;
  color: #666 !important;
  display: block !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp1 .grid.grid-cols-1.md\\\\:grid-cols-2 {
  display: block !important;
  margin-bottom: 5mm !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp1 .rounded-2xl.relative.group {
  display: block !important;
  margin-bottom: 3mm !important;
  padding: 2mm !important;
  background: #f9f9f9 !important;
  border: 1px solid #ddd !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp1 .text-sm.font-semibold.text-orange-500 {
  font-size: 10pt !important;
  font-weight: bold !important;
  color: #333 !important;
  background: #f0f0f0 !important;
  border: 1px solid #ccc !important;
  padding: 1mm 2mm !important;
  margin-bottom: 2mm !important;
  display: inline-block !important;
  position: static !important;
}

.pdf-export #temp1 .flex.items-center {
  display: block !important;
  margin-bottom: 1mm !important;
}

.pdf-export #temp1 .w-3.h-3 {
  width: 3mm !important;
  height: 3mm !important;
  display: inline-block !important;
  margin-right: 2mm !important;
  vertical-align: middle !important;
}

.pdf-export #temp1 .text-xs {
  font-size: 8pt !important;
  display: inline !important;
  vertical-align: middle !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp1 .space-y-3 {
  margin-bottom: 0 !important;
}

.pdf-export #temp1 .space-y-2 {
  margin-bottom: 3mm !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp1 .text-justify {
  text-align: justify !important;
  font-size: 10pt !important;
  line-height: 1.4 !important;
  margin-bottom: 2mm !important;
  word-spacing: 0.15em !important;
  letter-spacing: 0.03em !important;
}

/* Apply spacing to all content sections */
.pdf-export #temp1 .space-y-2 > div,
.pdf-export #temp1 .space-y-3 > div,
.pdf-export #temp1 .space-y-4 > div,
.pdf-export #temp1 p,
.pdf-export #temp1 .leading-relaxed,
.pdf-export #temp1 div[contenteditable],
.pdf-export #temp1 .editable-field {
  word-spacing: 0.15em !important;
  letter-spacing: 0.03em !important;
}

/* Apply spacing to all text content in Template 1 */
.pdf-export #temp1 * {
  word-spacing: 0.15em !important;
  letter-spacing: 0.03em !important;
}

.pdf-export #temp1 .leading-relaxed {
  line-height: 1.4 !important;
}

.pdf-export #temp1 .mt-6 {
  margin-top: 5mm !important;
}

.pdf-export #temp1 .font-semibold {
  font-weight: bold !important;
}

.pdf-export #temp1 .pdf-hidden {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.pdf-export.pdf-font-s #temp1 {
  font-size: 10pt !important;
}

.pdf-export.pdf-font-m #temp1 {
  font-size: 12pt !important;
}

.pdf-export.pdf-font-l #temp1 {
  font-size: 14pt !important;
}

.pdf-export.pdf-line-height-1-2 #temp1 {
  line-height: 1.2 !important;
}

.pdf-export.pdf-line-height-1-5 #temp1 {
  line-height: 1.5 !important;
}

.pdf-export.pdf-line-height-1-8 #temp1 {
  line-height: 1.8 !important;
}

/* Special optimization for large font + high line spacing to reduce file size */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 {
  font-size: 10pt !important; /* Even smaller font to fit more content on page 1 */
  line-height: 1.3 !important; /* Very tight line height */
  padding: 3mm !important; /* Minimal padding */
  word-spacing: 0.03em !important; /* Very tight word spacing */
  letter-spacing: 0.002em !important; /* Minimal letter spacing */
}

/* Optimize specific sections for page 1 content fitting */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="header"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="contactInfo"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="recipientInfo"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="greeting"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="openingParagraph"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="experienceSection"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="skillsSection"] {
  margin-bottom: 1mm !important; /* Minimal spacing between sections */
  line-height: 1.2 !important; /* Very tight for main content sections */
}

/* Extra tight spacing for text content in large font mode */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 .ProseMirror,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 .text-justify {
  line-height: 1.15 !important; /* Very tight for paragraph content */
  margin-bottom: 0.5mm !important; /* Minimal margin */
}

/* Reduce header spacing significantly */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 h1,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 h2,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 h3 {
  margin-bottom: 0.5mm !important; /* Minimal header margin */
  line-height: 1.05 !important; /* Very tight header line height */
}

/* Reduce spacing for contact info grid */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 .grid {
  gap: 1mm !important; /* Minimal grid gap */
}

/* Reduce spacing for all divs and paragraphs */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 div,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 p {
  margin-bottom: 0.5mm !important;
}

/* Ensure all main sections fit on page 1 by reducing their individual spacing */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section] {
  margin-bottom: 0.8mm !important; /* Very minimal spacing between sections */
}

/* Special handling for skills section to ensure it fits on page 1 */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp1 [data-section="skillsSection"] {
  margin-bottom: 1.5mm !important; /* Slightly more space after skills before page break */
}

.pdf-export.pdf-line-height-2 #temp1 {
  line-height: 2.0 !important;
}

.pdf-export.pdf-font-arial #temp1 {
  font-family: Arial, sans-serif !important;
}

.pdf-export.pdf-font-calibri #temp1 {
  font-family: Calibri, Arial, sans-serif !important;
}

.pdf-export.pdf-font-times #temp1 {
  font-family: "Times New Roman", serif !important;
}

.pdf-export.pdf-font-cambria #temp1 {
  font-family: Cambria, Georgia, serif !important;
}

/* Cambria font needs reduced letter-spacing for better readability */
.pdf-export.pdf-font-cambria #temp1 * {
  letter-spacing: 0.01em !important; /* Reduced from 0.03em */
}

/* Specifically for fullName field with Cambria font - no letter spacing */
.pdf-export.pdf-font-cambria #temp1 .text-2xl.font-bold,
.pdf-export.pdf-font-cambria #temp1 [field="fullName"],
.pdf-export.pdf-font-cambria #temp1 [data-field="fullName"],
.pdf-export.pdf-font-cambria #temp1 [data-field="fullName"] *,
.pdf-export.pdf-font-cambria #temp1 [data-field="fullName"] p {
  letter-spacing: 0 !important;
}

/* Force white background for all PDF exports - no theme colors */
.pdf-export #temp1 .flex.items-center.mb-4.p-4.rounded-lg,
.pdf-export #temp1 .rounded-2xl.relative.group,
.pdf-export #temp1 [data-section="header"],
.pdf-export #temp1 [data-section="contactInfo"] {
  background: white !important;
  border-color: #e5e7eb !important;
}

.pdf-export #temp1 .pdf-text-left {
  text-align: left !important;
}

.pdf-export #temp1 .pdf-text-center {
  text-align: center !important;
}

.pdf-export #temp1 .pdf-text-right {
  text-align: right !important;
}

.pdf-export #temp1 .pdf-text-justify {
  text-align: justify !important;
}

.pdf-export #temp1 .pdf-bold {
  font-weight: bold !important;
}

.pdf-export #temp1 .pdf-italic {
  font-style: italic !important;
}

.pdf-export #temp1 .pdf-underline {
  text-decoration: underline !important;
}

.pdf-export.pdf-content-overflow #temp1 {
  font-size: 10pt !important;
  line-height: 1.2 !important;
  padding: 10mm !important;
}

.pdf-export.pdf-content-overflow #temp1 .space-y-2,
.pdf-export.pdf-content-overflow #temp1 .space-y-3 {
  margin-bottom: 2mm !important;
}

.pdf-export.pdf-content-overflow #temp1 .text-justify {
  font-size: 9pt !important;
  margin-bottom: 2mm !important;
}

/* Signature section styling */
.pdf-export #temp1 .mt-6 .font-semibold {
  font-size: 10pt !important;
  font-weight: bold !important;
  line-height: 1.5 !important;
  min-height: 15px !important;
  display: block !important;
  word-spacing: 0.1em !important;
  margin-top: 2mm !important;
}

.pdf-export #temp1 .mt-6 {
  margin-top: 3mm !important;
  page-break-inside: avoid !important;
}
`;

// PDF CSS for Template 2
export const getPDFCSSTemp2 = (): string => `
/* PDF-specific CSS for CoverLetterTemp2 - A4 Portrait Layout */
.pdf-export #temp2 {
  width: 210mm !important;
  max-width: 210mm !important;
  min-height: auto !important;
  max-height: none !important;
  margin: 0 !important;
  padding: 8mm !important;
  box-sizing: border-box !important;
  background: white !important;
  color: black !important;
  font-family: Arial, sans-serif !important;
  font-size: 9.5pt !important;
  line-height: 1.3 !important;
  word-spacing: 0.1em !important;
  letter-spacing: 0.02em !important;
  overflow: visible !important;
  page-break-after: avoid !important;
  page-break-inside: avoid !important;
}

/* Force all backgrounds to white in PDF - override any theme colors */
.pdf-export #temp2 * {
  background-color: white !important;
  background: white !important;
}

/* Exception: Keep text colors but force backgrounds to white */
.pdf-export #temp2 [style*="background"],
.pdf-export #temp2 .bg-blue-50,
.pdf-export #temp2 .bg-green-50,
.pdf-export #temp2 .bg-yellow-50,
.pdf-export #temp2 .bg-orange-50 {
  background-color: white !important;
  background: white !important;
}

/* Enhanced page break controls for sections */
.pdf-export #temp2 [data-section] {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  margin-bottom: 8px !important;
}

.pdf-export #temp2 .space-y-4 > div,
.pdf-export #temp2 .space-y-6 > div {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

.pdf-export #temp2 p,
.pdf-export #temp2 .text-justify {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  orphans: 3 !important;
  widows: 3 !important;
}

.pdf-export #temp2 * {
  box-shadow: none !important;
  text-shadow: none !important;
  background-image: none !important;
  border-radius: 0 !important;
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

.pdf-export #temp2 .flex.justify-between.items-start.mb-6.p-4.rounded-lg {
  display: block !important;
  margin-bottom: 10mm !important;
  padding: 5mm !important;
  background: #f5f5f5 !important;
  border: 1px solid #ddd !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp2 .w-24.h-24 {
  width: 25mm !important;
  height: 25mm !important;
  float: left !important;
  margin-right: 5mm !important;
  margin-bottom: 3mm !important;
  background: #ddd !important;
  border: 1px solid #999 !important;
}

.pdf-export #temp2 .w-24.h-24 img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.pdf-export #temp2 .text-2xl.font-bold.text-blue-600 {
  font-size: 14pt !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 1mm !important;
  display: block !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp2 .text-lg.text-blue-600 {
  font-size: 11pt !important;
  font-weight: normal !important;
  color: #666 !important;
  display: block !important;
  margin-bottom: 2mm !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp2 .space-y-2 {
  margin-bottom: 3mm !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp2 .text-right {
  text-align: left !important;
}

.pdf-export #temp2 .flex.items-center {
  display: block !important;
  margin-bottom: 1mm !important;
}

.pdf-export #temp2 .w-4.h-4 {
  width: 3mm !important;
  height: 3mm !important;
  display: inline-block !important;
  margin-right: 2mm !important;
  vertical-align: middle !important;
}

.pdf-export #temp2 .text-gray-600 {
  font-size: 9pt !important;
  color: #666 !important;
  display: inline !important;
  vertical-align: middle !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp2 .mb-6 {
  margin-bottom: 5mm !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp2 .space-y-4 {
  margin-bottom: 0 !important;
}

.pdf-export #temp2 .text-justify {
  text-align: justify !important;
  font-size: 10pt !important;
  line-height: 1.4 !important;
  margin-bottom: 2mm !important;
  word-spacing: 0.15em !important;
  letter-spacing: 0.03em !important;
}

/* Apply spacing to all content sections for Template 2 */
.pdf-export #temp2 .space-y-2 > div,
.pdf-export #temp2 .space-y-3 > div,
.pdf-export #temp2 .space-y-4 > div,
.pdf-export #temp2 p,
.pdf-export #temp2 .leading-relaxed,
.pdf-export #temp2 div[contenteditable],
.pdf-export #temp2 .editable-field {
  word-spacing: 0.15em !important;
  letter-spacing: 0.03em !important;
}

/* Apply spacing to all text content in Template 2 */
.pdf-export #temp2 * {
  word-spacing: 0.15em !important;
  letter-spacing: 0.03em !important;
}

.pdf-export #temp2 .leading-relaxed {
  line-height: 1.4 !important;
}

.pdf-export #temp2 .mt-8 {
  margin-top: 5mm !important;
}

.pdf-export #temp2 .font-semibold {
  font-weight: bold !important;
}

.pdf-export #temp2 .text-gray-800 {
  color: #333 !important;
}

.pdf-export #temp2 .pdf-hidden {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.pdf-export.pdf-font-s #temp2 {
  font-size: 10pt !important;
}

.pdf-export.pdf-font-m #temp2 {
  font-size: 12pt !important;
}

.pdf-export.pdf-font-l #temp2 {
  font-size: 14pt !important;
}

.pdf-export.pdf-line-height-1-2 #temp2 {
  line-height: 1.2 !important;
}

.pdf-export.pdf-line-height-1-5 #temp2 {
  line-height: 1.5 !important;
}

.pdf-export.pdf-line-height-1-8 #temp2 {
  line-height: 1.8 !important;
}

/* Special optimization for large font + high line spacing to reduce file size */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 {
  font-size: 10pt !important; /* Even smaller font to fit more content on page 1 */
  line-height: 1.3 !important; /* Very tight line height */
  padding: 3mm !important; /* Minimal padding */
  word-spacing: 0.03em !important; /* Very tight word spacing */
  letter-spacing: 0.002em !important; /* Minimal letter spacing */
}

/* Optimize specific sections for page 1 content fitting */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 [data-section="header"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 [data-section="contactInfo"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 [data-section="recipientInfo"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 [data-section="greeting"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 [data-section="openingParagraph"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 [data-section="experienceSection"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 [data-section="skillsSection"] {
  margin-bottom: 1.5mm !important; /* Reduce spacing between sections */
  line-height: 1.25 !important; /* Even tighter for main content sections */
}

/* Extra tight spacing for text content in large font mode */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 .ProseMirror,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 .text-justify {
  line-height: 1.2 !important; /* Very tight for paragraph content */
  margin-bottom: 1mm !important;
}

/* Reduce header spacing significantly */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 h1,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 h2,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp2 h3 {
  margin-bottom: 1mm !important;
  line-height: 1.1 !important;
}

.pdf-export.pdf-line-height-2 #temp2 {
  line-height: 2.0 !important;
}

.pdf-export.pdf-font-arial #temp2 {
  font-family: Arial, sans-serif !important;
}

.pdf-export.pdf-font-calibri #temp2 {
  font-family: Calibri, Arial, sans-serif !important;
}

.pdf-export.pdf-font-times #temp2 {
  font-family: "Times New Roman", serif !important;
}

.pdf-export.pdf-font-cambria #temp2 {
  font-family: Cambria, Georgia, serif !important;
}

/* Cambria font needs reduced letter-spacing for better readability */
.pdf-export.pdf-font-cambria #temp2 * {
  letter-spacing: 0.01em !important; /* Reduced from default */
}

/* Specifically for fullName field with Cambria font - no letter spacing */
.pdf-export.pdf-font-cambria #temp2 .text-xl.font-bold,
.pdf-export.pdf-font-cambria #temp2 [field="fullName"],
.pdf-export.pdf-font-cambria #temp2 [data-field="fullName"],
.pdf-export.pdf-font-cambria #temp2 [data-field="fullName"] *,
.pdf-export.pdf-font-cambria #temp2 [data-field="fullName"] p {
  letter-spacing: 0 !important;
}

/* Force white background for all PDF exports - no theme colors */
.pdf-export #temp2 .flex.justify-between.items-start.mb-6.p-4.rounded-lg,
.pdf-export #temp2 [data-section="header"],
.pdf-export #temp2 [data-section="contactInfo"] {
  background: white !important;
  border-color: #e5e7eb !important;
}

.pdf-export #temp2 .pdf-text-left {
  text-align: left !important;
}

.pdf-export #temp2 .pdf-text-center {
  text-align: center !important;
}

.pdf-export #temp2 .pdf-text-right {
  text-align: right !important;
}

.pdf-export #temp2 .pdf-text-justify {
  text-align: justify !important;
}

.pdf-export #temp2 .pdf-bold {
  font-weight: bold !important;
}

.pdf-export #temp2 .pdf-italic {
  font-style: italic !important;
}

.pdf-export #temp2 .pdf-underline {
  text-decoration: underline !important;
}

.pdf-export.pdf-content-overflow #temp2 {
  font-size: 10pt !important;
  line-height: 1.2 !important;
  padding: 10mm !important;
}

.pdf-export.pdf-content-overflow #temp2 .space-y-2,
.pdf-export.pdf-content-overflow #temp2 .space-y-4 {
  margin-bottom: 2mm !important;
}

.pdf-export.pdf-content-overflow #temp2 .text-justify {
  font-size: 9pt !important;
  margin-bottom: 2mm !important;
}

.pdf-export #temp2 .absolute,
.pdf-export #temp2 .relative,
.pdf-export #temp2 .fixed,
.pdf-export #temp2 .sticky {
  position: static !important;
}

.pdf-export #temp2 .gap-8,
.pdf-export #temp2 .gap-6,
.pdf-export #temp2 .gap-4 {
  gap: 0 !important;
}

.pdf-export #temp2 .p-6 {
  padding: 3mm !important;
}

.pdf-export #temp2 .p-4 {
  padding: 2mm !important;
}

.pdf-export #temp2 .px-3 {
  padding-left: 1.5mm !important;
  padding-right: 1.5mm !important;
}

.pdf-export #temp2 .py-1 {
  padding-top: 0.5mm !important;
  padding-bottom: 0.5mm !important;
}

/* Signature section styling for Template 2 */
.pdf-export #temp2 .mt-8 .font-semibold {
  font-size: 10pt !important;
  font-weight: bold !important;
  line-height: 1.5 !important;
  min-height: 15px !important;
  display: block !important;
  word-spacing: 0.1em !important;
  margin-top: 2mm !important;
}

.pdf-export #temp2 .mt-8 {
  margin-top: 3mm !important;
  page-break-inside: avoid !important;
}
`;

// PDF CSS for Template 3
export const getPDFCSSTemp3 = (): string => `
/* PDF-specific CSS for CoverLetterTemp3 - A4 Portrait Layout */
.pdf-export #temp3 {
  width: 210mm !important;
  max-width: 210mm !important;
  min-height: auto !important;
  max-height: none !important;
  margin: 0 !important;
  padding: 8mm !important;
  box-sizing: border-box !important;
  background: white !important;
  color: black !important;
  font-family: Arial, sans-serif !important;
  font-size: 9.5pt !important;
  line-height: 1.3 !important;
  word-spacing: 0.1em !important;
  letter-spacing: 0.02em !important;
  overflow: visible !important;
  page-break-after: avoid !important;
  page-break-inside: avoid !important;
}

/* Force all backgrounds to white in PDF - override any theme colors */
.pdf-export #temp3 * {
  background-color: white !important;
  background: white !important;
}

/* Exception: Keep text colors but force backgrounds to white */
.pdf-export #temp3 [style*="background"],
.pdf-export #temp3 .bg-blue-50,
.pdf-export #temp3 .bg-green-50,
.pdf-export #temp3 .bg-yellow-50,
.pdf-export #temp3 .bg-orange-50 {
  background-color: white !important;
  background: white !important;
}

/* Enhanced page break controls for sections */
.pdf-export #temp3 [data-section] {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  margin-bottom: 8px !important;
}

.pdf-export #temp3 .space-y-4 > div,
.pdf-export #temp3 .space-y-6 > div {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

.pdf-export #temp3 p,
.pdf-export #temp3 .text-justify {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  orphans: 3 !important;
  widows: 3 !important;
}

.pdf-export #temp3 * {
  box-shadow: none !important;
  text-shadow: none !important;
  background-image: none !important;
  border-radius: 0 !important;
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

.pdf-export #temp3 .flex.items-start.gap-8 {
  display: block !important;
  gap: 0 !important;
}

.pdf-export #temp3 .w-80.bg-yellow-50.p-6.rounded-lg {
  display: block !important;
  width: 35% !important;
  float: left !important;
  margin-right: 15mm !important;
  margin-bottom: 5mm !important;
  padding: 5mm !important;
  background: #f9f9f9 !important;
  border: 1px solid #ddd !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp3 .w-32.h-32 {
  width: 25mm !important;
  height: 25mm !important;
  margin: 0 auto 3mm auto !important;
  background: #ddd !important;
  border: 1px solid #999 !important;
}

.pdf-export #temp3 .w-32.h-32 img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

.pdf-export #temp3 .text-3xl.font-bold.text-blue-600 {
  font-size: 14pt !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 2mm !important;
  display: block !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp3 .text-base.text-blue-600 {
  font-size: 11pt !important;
  font-weight: normal !important;
  color: #666 !important;
  display: block !important;
  margin-bottom: 2mm !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp3 .flex-1.ml-32 {
  margin-left: 0 !important;
  display: block !important;
}

.pdf-export #temp3 .w-6.h-6.bg-blue-600.rounded-full {
  display: none !important;
}

.pdf-export #temp3 .space-y-3 {
  margin-bottom: 3mm !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp3 .w-8.h-8.bg-blue-600.rounded-lg {
  width: 4mm !important;
  height: 4mm !important;
  background: #666 !important;
  display: inline-block !important;
  margin-right: 2mm !important;
  vertical-align: middle !important;
  border: 1px solid #999 !important;
}

.pdf-export #temp3 .text-gray-700.text-sm {
  font-size: 8pt !important;
  color: #666 !important;
  display: inline !important;
  vertical-align: middle !important;
  word-spacing: 0.1em !important;
}

.pdf-export #temp3 .bg-white.rounded-lg.p-6.border {
  display: block !important;
  margin-bottom: 5mm !important;
  padding: 3mm !important;
  background: #f9f9f9 !important;
  border: 1px solid #ddd !important;
  page-break-inside: avoid !important;
}

.pdf-export #temp3 .text-lg.font-semibold.text-blue-600 {
  font-size: 12pt !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 2mm !important;
  display: block !important;
}

.pdf-export #temp3 .space-y-4 {
  margin-bottom: 0 !important;
}

.pdf-export #temp3 .text-justify {
  text-align: justify !important;
  font-size: 10pt !important;
  line-height: 1.5 !important;
  margin-bottom: 3mm !important;
  word-spacing: 0.2em !important;
  letter-spacing: 0.04em !important;
}

/* Apply spacing to all content sections for Template 3 */
.pdf-export #temp3 .space-y-2 > div,
.pdf-export #temp3 .space-y-3 > div,
.pdf-export #temp3 .space-y-4 > div,
.pdf-export #temp3 p,
.pdf-export #temp3 .leading-relaxed,
.pdf-export #temp3 div[contenteditable],
.pdf-export #temp3 .editable-field {
  word-spacing: 0.2em !important;
  letter-spacing: 0.04em !important;
  line-height: 1.5 !important;
}

/* Apply spacing to all text content in Template 3 */
.pdf-export #temp3 * {
  word-spacing: 0.2em !important;
  letter-spacing: 0.04em !important;
}

.pdf-export #temp3 .leading-relaxed {
  line-height: 1.5 !important;
  word-spacing: 0.2em !important;
  letter-spacing: 0.04em !important;
}

.pdf-export #temp3 .mt-8 {
  margin-top: 5mm !important;
}

.pdf-export #temp3 .font-semibold {
  font-weight: bold !important;
}

.pdf-export #temp3 .text-gray-800 {
  color: #333 !important;
}

.pdf-export #temp3 .pdf-hidden {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.pdf-export.pdf-font-s #temp3 {
  font-size: 10pt !important;
}

.pdf-export.pdf-font-m #temp3 {
  font-size: 12pt !important;
}

.pdf-export.pdf-font-l #temp3 {
  font-size: 14pt !important;
}

.pdf-export.pdf-line-height-1-2 #temp3 {
  line-height: 1.2 !important;
}

.pdf-export.pdf-line-height-1-5 #temp3 {
  line-height: 1.5 !important;
}

.pdf-export.pdf-line-height-1-8 #temp3 {
  line-height: 1.8 !important;
}

/* Special optimization for large font + high line spacing to reduce file size */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 {
  font-size: 10pt !important; /* Even smaller font to fit more content on page 1 */
  line-height: 1.3 !important; /* Very tight line height */
  padding: 3mm !important; /* Minimal padding */
  word-spacing: 0.05em !important; /* Reduced word spacing for template 3 */
  letter-spacing: 0.002em !important; /* Minimal letter spacing */
}

/* Optimize specific sections for page 1 content fitting */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 [data-section="header"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 [data-section="contactInfo"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 [data-section="recipientInfo"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 [data-section="greeting"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 [data-section="openingParagraph"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 [data-section="experienceSection"],
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 [data-section="skillsSection"] {
  margin-bottom: 1.5mm !important; /* Reduce spacing between sections */
  line-height: 1.25 !important; /* Even tighter for main content sections */
}

/* Extra tight spacing for text content in large font mode */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 .ProseMirror,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 .text-justify {
  line-height: 1.2 !important; /* Very tight for paragraph content */
  margin-bottom: 1mm !important;
}

/* Reduce header spacing significantly */
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 h1,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 h2,
.pdf-export.pdf-font-l.pdf-line-height-1-8 #temp3 h3 {
  margin-bottom: 1mm !important;
  line-height: 1.1 !important;
}

.pdf-export.pdf-line-height-2 #temp3 {
  line-height: 2.0 !important;
}

.pdf-export.pdf-font-arial #temp3 {
  font-family: Arial, sans-serif !important;
}

.pdf-export.pdf-font-calibri #temp3 {
  font-family: Calibri, Arial, sans-serif !important;
}

.pdf-export.pdf-font-times #temp3 {
  font-family: "Times New Roman", serif !important;
}

.pdf-export.pdf-font-cambria #temp3 {
  font-family: Cambria, Georgia, serif !important;
}

/* Cambria font needs reduced letter-spacing for better readability */
.pdf-export.pdf-font-cambria #temp3 * {
  letter-spacing: 0.02em !important; /* Reduced from 0.04em */
}

/* Specifically for fullName field with Cambria font - no letter spacing */
.pdf-export.pdf-font-cambria #temp3 .text-xl.font-bold,
.pdf-export.pdf-font-cambria #temp3 [field="fullName"],
.pdf-export.pdf-font-cambria #temp3 [data-field="fullName"],
.pdf-export.pdf-font-cambria #temp3 [data-field="fullName"] *,
.pdf-export.pdf-font-cambria #temp3 [data-field="fullName"] p {
  letter-spacing: 0 !important;
}

/* Force white background for all PDF exports - no theme colors */
.pdf-export #temp3 .w-80.bg-yellow-50.p-6.rounded-lg,
.pdf-export #temp3 .bg-white.rounded-lg.p-6.border,
.pdf-export #temp3 [data-section="header"],
.pdf-export #temp3 [data-section="contactInfo"] {
  background: white !important;
  border-color: #e5e7eb !important;
}

.pdf-export #temp3 .pdf-text-left {
  text-align: left !important;
}

.pdf-export #temp3 .pdf-text-center {
  text-align: center !important;
}

.pdf-export #temp3 .pdf-text-right {
  text-align: right !important;
}

.pdf-export #temp3 .pdf-text-justify {
  text-align: justify !important;
}

.pdf-export #temp3 .pdf-bold {
  font-weight: bold !important;
}

.pdf-export #temp3 .pdf-italic {
  font-style: italic !important;
}

.pdf-export #temp3 .pdf-underline {
  text-decoration: underline !important;
}

.pdf-export.pdf-content-overflow #temp3 {
  font-size: 10pt !important;
  line-height: 1.2 !important;
  padding: 10mm !important;
}

.pdf-export.pdf-content-overflow #temp3 .space-y-3,
.pdf-export.pdf-content-overflow #temp3 .space-y-4 {
  margin-bottom: 2mm !important;
}

.pdf-export.pdf-content-overflow #temp3 .text-justify {
  font-size: 9pt !important;
  margin-bottom: 2mm !important;
}

.pdf-export #temp3 .absolute,
.pdf-export #temp3 .relative,
.pdf-export #temp3 .fixed,
.pdf-export #temp3 .sticky {
  position: static !important;
}

.pdf-export #temp3 .flex.items-start.gap-8 {
  display: block !important;
  gap: 0 !important;
}

.pdf-export #temp3 .flex.items-center {
  display: block !important;
  margin-bottom: 1mm !important;
}

.pdf-export #temp3 .p-6 {
  padding: 3mm !important;
}

.pdf-export #temp3 .px-3 {
  padding-left: 1.5mm !important;
  padding-right: 1.5mm !important;
}

.pdf-export #temp3 .py-1 {
  padding-top: 0.5mm !important;
  padding-bottom: 0.5mm !important;
}

/* Signature section styling for Template 3 */
.pdf-export #temp3 .mt-8 .font-semibold {
  font-size: 10pt !important;
  font-weight: bold !important;
  line-height: 1.5 !important;
  min-height: 15px !important;
  display: block !important;
  word-spacing: 0.1em !important;
  margin-top: 2mm !important;
}

.pdf-export #temp3 .mt-8 {
  margin-top: 3mm !important;
  page-break-inside: avoid !important;
}
`;
