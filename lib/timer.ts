import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

export const getTimeAgo = (time: string, locale: string = "en"): string => {
    dayjs.extend(relativeTime);
    dayjs.locale(locale);
    return dayjs(time).fromNow();
};

export const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

export const distinctTime = (timer1: number, timer2: number) => {
  const diff = Math.max(0, Math.abs(timer2 - timer1));
  const minutes = Math.floor(diff / 60);
  const remainingSeconds = diff % 60;

  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};
