import { Mail, Shield, Key, LucideIcon } from "lucide-react";

export const getStepInfo = (step: string, email?: string) => {
  let icon: LucideIcon | null = null;

  switch (step) {
    case "email":
      icon = Mail;
      return {
        title: "<PERSON>uên mật khẩu",
        description: "Nhập email để nhận mã OTP đặt lại mật khẩu",
        icon,
      };
    case "otp":
      icon = Shield;
      return {
        title: "X<PERSON>c thực OTP",
        description: `Nhập mã OTP đã được gửi tới ${email}`,
        icon,
      };
    case "reset-password":
      icon = Key;
      return {
        title: "Đặt lại mật khẩu",
        description: "Nhập mật khẩu mới cho tài khoản của bạn",
        icon,
      };
    default:
      return {
        title: "",
        description: "",
        icon,
      };
  }
};
