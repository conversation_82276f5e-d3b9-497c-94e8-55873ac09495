import { getPDFCSSTemp1, getPDFCSSTemp2, getPDFCSSTemp3 } from "./pdfStyles";

export interface PDFGenerationOptions {
  filename?: string;
  quality?: number;
  scale?: number;
  useCORS?: boolean;
  allowTaint?: boolean;
}

export interface CoverLetterPDFOptions extends PDFGenerationOptions {
  templateId: string;
  fontSize?: string;
  fontFamily?: string;
  lineHeight?: string;
  currentTheme?: string;
  hiddenSections?: Set<string>;
  sectionFormatting?: Record<string, any>;
  detailedFormatting?: Record<string, any>;
}

// Get PDF-specific CSS for a template
export const getPDFCSS = (templateId: string): string => {
  const templateNumber = templateId.replace("temp", "");

  switch (templateNumber) {
    case "1":
      return getPDFCSSTemp1();
    case "2":
      return getPDFCSSTemp2();
    case "3":
      return getPDFCSSTemp3();
    default:
      return getPDFCSSTemp1();
  }
};

// Apply PDF-specific styling to cloned element
export const applyPDFStyling = (
  clonedElement: HTMLElement,
  templateId: string,
  options: Partial<CoverLetterPDFOptions> = {}
) => {
  const {
    fontSize = "M",
    fontFamily = "Arial",
    lineHeight = "1.5",
    currentTheme = "blue",
    hiddenSections = new Set(),
    sectionFormatting = {},
  } = options || {};

  clonedElement.classList.remove("pdf-export");
  clonedElement.className = clonedElement.className
    .replace(/pdf-\w+/g, "")
    .trim();

  clonedElement.classList.add("pdf-export");
  const getFontFamilyClass = (fontFamily: string): string => {
    const fontMap: Record<string, string> = {
      arial: "arial",
      calibri: "calibri",
      "times new roman": "times",
      times: "times",
      cambria: "cambria",
    };
    return fontMap[fontFamily.toLowerCase()] || "arial";
  };

  clonedElement.classList.add(`pdf-font-${fontSize.toLowerCase()}`);
  clonedElement.classList.add(`pdf-font-${getFontFamilyClass(fontFamily)}`);
  clonedElement.classList.add(
    `pdf-line-height-${lineHeight.replace(".", "-")}`
  );
  clonedElement.classList.add(`pdf-theme-${currentTheme.toLowerCase()}`);

  const templateNumber = templateId.replace("temp", "");
  const getActualFontFamily = (fontFamily: string): string => {
    const fontMap: Record<string, string> = {
      arial: "Arial, sans-serif",
      calibri: "Calibri, Arial, sans-serif",
      "times new roman": '"Times New Roman", Times, serif',
      times: '"Times New Roman", Times, serif',
      cambria: "Cambria, Georgia, serif",
    };
    return fontMap[fontFamily.toLowerCase()] || "Arial, sans-serif";
  };

  const actualFontFamily = getActualFontFamily(fontFamily);
  if (templateNumber === "3") {
    // Template 3 needs stronger spacing, but reduce for Cambria
    clonedElement.style.wordSpacing = "0.2em";
    clonedElement.style.letterSpacing =
      fontFamily.toLowerCase() === "cambria" ? "0.02em" : "0.04em";
    clonedElement.style.fontFamily = actualFontFamily; // Force font for html2canvas
  } else {
    // Template 1 & 2, reduce letter-spacing for Cambria
    clonedElement.style.wordSpacing = "0.15em";
    clonedElement.style.letterSpacing =
      fontFamily.toLowerCase() === "cambria" ? "0.01em" : "0.03em";
    clonedElement.style.fontFamily = actualFontFamily; // Force font for html2canvas
  }

  // Hide sections that should be hidden
  hiddenSections.forEach((sectionId) => {
    const sectionElement = clonedElement.querySelector(
      `[data-section="${sectionId}"]`
    );
    if (sectionElement) {
      sectionElement.classList.add("pdf-hidden");
    }
  });

  // Apply section-specific formatting
  Object.entries(sectionFormatting).forEach(([sectionId, formatting]) => {
    const sectionElement = clonedElement.querySelector(
      `[data-section="${sectionId}"]`
    );
    if (sectionElement && formatting) {
      if (formatting.textAlign) {
        sectionElement.classList.add(`pdf-text-${formatting.textAlign}`);
      }
      if (formatting.isBold) {
        sectionElement.classList.add("pdf-bold");
      }
      if (formatting.isItalic) {
        sectionElement.classList.add("pdf-italic");
      }
      if (formatting.isUnderline) {
        sectionElement.classList.add("pdf-underline");
      }
      if (formatting.isHidden) {
        sectionElement.classList.add("pdf-hidden");
      }
    }
  });

  // Force apply spacing and font to all text elements
  forceApplyTextSpacing(clonedElement, templateNumber, fontFamily);

  // Check if content might overflow and apply overflow class
  const contentHeight = clonedElement.scrollHeight;
  const maxHeight = 297 * 3.78; // Convert mm to pixels (approximate)
  if (contentHeight > maxHeight) {
    clonedElement.classList.add("pdf-content-overflow");
  }
};

// Force apply text spacing AND font to all elements (html2canvas compatibility)
const forceApplyTextSpacing = (
  element: HTMLElement,
  templateNumber: string,
  fontFamily: string
) => {
  const wordSpacing = templateNumber === "3" ? "0.2em" : "0.15em";
  // Reduce letter-spacing for Cambria font
  const letterSpacing =
    templateNumber === "3"
      ? fontFamily.toLowerCase() === "cambria"
        ? "0.02em"
        : "0.04em"
      : fontFamily.toLowerCase() === "cambria"
      ? "0.01em"
      : "0.03em";

  // Get actual font family for inline styles
  const getActualFontFamily = (fontFamily: string): string => {
    const fontMap: Record<string, string> = {
      arial: "Arial, sans-serif",
      calibri: "Calibri, Arial, sans-serif",
      "times new roman": '"Times New Roman", Times, serif',
      times: '"Times New Roman", Times, serif',
      cambria: "Cambria, Georgia, serif",
    };
    return fontMap[fontFamily.toLowerCase()] || "Arial, sans-serif";
  };

  const actualFontFamily = getActualFontFamily(fontFamily);

  // Apply spacing AND font to all text-containing elements
  const textElements = element.querySelectorAll("*");
  textElements.forEach((el) => {
    const htmlEl = el as HTMLElement;
    if (htmlEl.textContent && htmlEl.textContent.trim()) {
      htmlEl.style.wordSpacing = wordSpacing;
      htmlEl.style.letterSpacing = letterSpacing;
      htmlEl.style.fontFamily = actualFontFamily; // Force font for html2canvas
      // DON'T set lineHeight here - CSS classes will handle it
    }
  });

  // Special handling for fullName field with Cambria font - remove letter spacing
  if (fontFamily.toLowerCase() === "cambria") {
    const fullNameElements = element.querySelectorAll(
      '[data-field="fullName"], [field="fullName"], .text-2xl.font-bold, .text-xl.font-bold'
    );
    fullNameElements.forEach((el) => {
      const htmlEl = el as HTMLElement;
      htmlEl.style.letterSpacing = "0 !important";

      // Also target child elements (EditorContent, p tags, etc.)
      const childElements = htmlEl.querySelectorAll("*");
      childElements.forEach((child) => {
        const childEl = child as HTMLElement;
        childEl.style.letterSpacing = "0 !important";
      });
    });
  }
};

// Create and inject CSS styles for PDF generation
export const injectPDFStyles = (templateId: string): HTMLStyleElement => {
  const pdfCSS = getPDFCSS(templateId);

  // Create style element
  const styleElement = document.createElement("style");
  styleElement.type = "text/css";
  styleElement.innerHTML = pdfCSS;

  // Add to document head
  document.head.appendChild(styleElement);

  return styleElement;
};

// Remove injected PDF styles
export const removePDFStyles = (styleElement: HTMLStyleElement) => {
  if (styleElement && styleElement.parentNode) {
    styleElement.parentNode.removeChild(styleElement);
  }
};

// Hide toolbar elements during PDF generation
export const hideToolbarElements = (element: HTMLElement) => {
  const toolbars = element.querySelectorAll(
    '[class*="toolbar"], [class*="Toolbar"]'
  );
  const bubbleMenus = element.querySelectorAll(
    '[class*="bubble"], [class*="Bubble"]'
  );
  const editableIndicators = element.querySelectorAll("[contenteditable]");

  const hiddenElements: HTMLElement[] = [];

  // Hide toolbars
  toolbars.forEach((toolbar) => {
    const htmlElement = toolbar as HTMLElement;
    if (htmlElement.style.display !== "none") {
      htmlElement.style.display = "none";
      hiddenElements.push(htmlElement);
    }
  });

  // Hide bubble menus
  bubbleMenus.forEach((menu) => {
    const htmlElement = menu as HTMLElement;
    if (htmlElement.style.display !== "none") {
      htmlElement.style.display = "none";
      hiddenElements.push(htmlElement);
    }
  });

  // Remove contenteditable attributes
  editableIndicators.forEach((indicator) => {
    const htmlElement = indicator as HTMLElement;
    htmlElement.removeAttribute("contenteditable");
  });

  return hiddenElements;
};

// Restore hidden toolbar elements
export const restoreToolbarElements = (hiddenElements: HTMLElement[]) => {
  hiddenElements.forEach((element) => {
    element.style.display = "";
  });
};

// Calculate content length for overflow detection
export const calculateContentLength = (formData: any): number => {
  if (!formData) return 0;

  let totalLength = 0;

  // Count characters in main content fields
  if (formData.openingParagraph)
    totalLength += formData.openingParagraph.length;
  if (formData.experienceAchievements)
    totalLength += formData.experienceAchievements.length;
  if (formData.skillsStrengths) totalLength += formData.skillsStrengths.length;
  if (formData.closingParagraph)
    totalLength += formData.closingParagraph.length;

  // Count characters in header fields
  if (formData.header?.fullName) totalLength += formData.header.fullName.length;
  if (formData.header?.targetPosition)
    totalLength += formData.header.targetPosition.length;

  // Count characters in contact info
  if (formData.contactInfo?.email)
    totalLength += formData.contactInfo.email.length;
  if (formData.contactInfo?.phone)
    totalLength += formData.contactInfo.phone.length;
  if (formData.contactInfo?.linkedin)
    totalLength += formData.contactInfo.linkedin.length;

  // Count characters in recipient info
  if (formData.recipientInfo?.hiringManagerName)
    totalLength += formData.recipientInfo.hiringManagerName.length;
  if (formData.recipientInfo?.hiringManagerTitle)
    totalLength += formData.recipientInfo.hiringManagerTitle.length;
  if (formData.recipientInfo?.companyName)
    totalLength += formData.recipientInfo.companyName.length;
  if (formData.recipientInfo?.companyAddress)
    totalLength += formData.recipientInfo.companyAddress.length;

  return totalLength;
};

// Determine if compact mode should be applied
export const shouldApplyCompactMode = (
  contentLength: number
): "normal" | "compact" | "very-compact" => {
  if (contentLength > 2000) {
    return "very-compact";
  } else if (contentLength > 1500) {
    return "compact";
  }
  return "normal";
};

// Apply compact mode classes
export const applyCompactMode = (
  element: HTMLElement,
  mode: "normal" | "compact" | "very-compact"
) => {
  element.classList.remove("compact", "very-compact");

  if (mode === "compact") {
    element.classList.add("compact");
  } else if (mode === "very-compact") {
    element.classList.add("very-compact");
  }
};
