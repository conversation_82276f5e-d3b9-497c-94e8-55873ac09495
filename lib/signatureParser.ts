/**
 * Helper function to strip HTML tags
 */
const stripHtmlTags = (html: string): string => {
  if (!html) return "";
  return html.replace(/<[^>]*>/g, "").trim();
};

/**
 * Parse signature to extract name and closing
 */
export const parseSignature = (
  signature: string
): { signatureClosing: string; signatureName: string } => {
  // Strip HTML first, then split by line breaks
  const cleanSignature = stripHtmlTags(signature);
  const signatureParts = cleanSignature
    .split("\n")
    .filter((part) => part.trim() !== "");

  if (signatureParts.length === 0) {
    // Empty signature
    return { signatureClosing: "", signatureName: "" };
  } else if (signatureParts.length === 1) {
    // Only one part - could be either closing or name
    const singlePart = signatureParts[0];

    // Check if it looks like a closing (contains common closing words)
    const closingWords = [
      "sincerely",
      "regards",
      "trân trọng",
      "k<PERSON><PERSON> thư",
      "cảm ơn",
      "xin cám ơn",
    ];
    const isClosing = closingWords.some((word) =>
      singlePart.toLowerCase().includes(word.toLowerCase())
    );

    if (isClosing) {
      return { signatureClosing: singlePart, signatureName: "" };
    } else {
      // Assume it's a name
      return { signatureClosing: "", signatureName: singlePart };
    }
  } else {
    // Multiple parts - first is closing, second is name
    return {
      signatureClosing: signatureParts[0],
      signatureName: signatureParts[1],
    };
  }
};
