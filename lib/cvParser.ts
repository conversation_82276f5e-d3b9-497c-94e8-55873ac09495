export const getCandidateName = (cvText?: string): string => {
  if (!cvText) return "N/A";

  const lines = cvText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line.length > 0);

  if (lines.length === 0) return "N/A";

  const firstLine = lines[0];

  if (
    firstLine &&
    firstLine.length < 50 &&
    /^[a-zA-ZÀ-ỹ\s]+$/.test(firstLine)
  ) {
    return firstLine;
  }

  for (let i = 1; i < Math.min(5, lines.length); i++) {
    const line = lines[i];
    if (
      line &&
      line.length < 50 &&
      /^[a-zA-ZÀ-ỹ\s]+$/.test(line) &&
      !line.toUpperCase().includes("DEVELOPER") &&
      !line.toUpperCase().includes("ENGINEER") &&
      !line.toUpperCase().includes("PROFILE") &&
      !line.toUpperCase().includes("OBJECTIVE") &&
      !line.toUpperCase().includes("INTERNSHIP") &&
      !line.toUpperCase().includes("DESIGNER") &&
      !line.toUpperCase().includes("ANALYST")
    ) {
      return line;
    }
  }

  return "N/A";
};

export const getPositionApplied = (cvText?: string): string => {
  if (!cvText) return "N/A";

  const lines = cvText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line.length > 0);

  const positionKeywords = [
    "DEVELOPER",
    "ENGINEER",
    "ANALYST",
    "MANAGER",
    "DESIGNER",
    "ARCHITECT",
    "CONSULTANT",
    "SPECIALIST",
    "COORDINATOR",
    "ADMINISTRATOR",
    "TECHNICIAN",
    "PROGRAMMER",
    "INTERN",
    "INTERNSHIP",
    "LEAD",
    "SENIOR",
    "JUNIOR",
    "TRAINEE",
  ];

  for (const line of lines) {
    const upperLine = line.toUpperCase();

    if (line.length < 100) {
      for (const keyword of positionKeywords) {
        if (
          upperLine.includes(keyword) &&
          !upperLine.includes("EXPERIENCE") &&
          !upperLine.includes("SKILLS") &&
          !upperLine.includes("EDUCATION") &&
          !upperLine.includes("PROJECT") &&
          !upperLine.includes("TECHNICAL") &&
          !upperLine.includes("PROGRAMMING") &&
          !upperLine.includes("WORK") &&
          !isPersonalInfo(line)
        ) {
          return line;
        }
      }
    }
  }

  for (let i = 1; i < Math.min(10, lines.length); i++) {
    const line = lines[i];
    if (line && line.length > 5 && line.length < 80) {
      const uppercaseRatio =
        (line.match(/[A-Z]/g) || []).length / line.replace(/\s/g, "").length;
      if (
        uppercaseRatio > 0.5 &&
        !line.toUpperCase().includes("PROFILE") &&
        !line.toUpperCase().includes("OBJECTIVE") &&
        !isPersonalInfo(line) &&
        !line.match(/^\d/) &&
        !line.includes("http") &&
        !isSectionHeader(line)
      ) {
        return line;
      }
    }
  }

  return "N/A";
};

const isPersonalInfo = (line: string): boolean => {
  const personalInfoPatterns = [
    /@/, // Email
    /\d{10,}/, // Phone number (10+ digits)
    /\+\d/, // International phone
    /https?:\/\//, // URLs
    /github\.com/, // GitHub
    /linkedin\.com/, // LinkedIn
  ];

  return personalInfoPatterns.some((pattern) => pattern.test(line));
};

const isSectionHeader = (line: string): boolean => {
  const sectionHeaders = [
    "TECHNICAL SKILLS",
    "PROGRAMMING LANGUAGES",
    "FRONT-END",
    "BACK-END",
    "DATABASE",
    "VERSION CONTROL",
    "TOOLS",
    "OBJECTIVE",
    "WORK EXPERIENCE",
    "EDUCATION",
    "HONORS & AWARDS",
    "CERTIFICATIONS",
    "INTERESTS",
    "LANGUAGES",
    "ACTIVITIES",
    "PROJECT",
    "PROFILE",
  ];

  const upperLine = line.toUpperCase();
  return sectionHeaders.some((header) => upperLine.includes(header));
};

export const parseCVBasicInfo = (cvText?: string) => {
  return {
    candidateName: getCandidateName(cvText),
    positionApplied: getPositionApplied(cvText),
  };
};
