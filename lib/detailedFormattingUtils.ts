/**
 * Utility functions for handling detailed character-level formatting
 */

export interface DetailedFormatRange {
  start: number;
  end: number;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
}

export type DetailedFormattingData = Record<string, DetailedFormatRange[]>;

/**
 * Extract detailed formatting from Tiptap editor HTML
 */
export const extractDetailedFormatting = (
  field: string,
  html: string,
  plainText: string
): DetailedFormatRange[] => {
  const ranges: DetailedFormatRange[] = [];

  if (!html || !plainText) return ranges;

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;

  let currentPosition = 0;
  const traverseNode = (node: Node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      const textContent = node.textContent || "";
      const startPos = currentPosition;
      const endPos = currentPosition + textContent.length;

      let parentElement = node.parentElement;
      let bold = false;
      let italic = false;
      let underline = false;

      while (parentElement && parentElement !== tempDiv) {
        const tagName = parentElement.tagName.toLowerCase();
        if (tagName === "strong" || tagName === "b") bold = true;
        if (tagName === "em" || tagName === "i") italic = true;
        if (tagName === "u") underline = true;
        parentElement = parentElement.parentElement;
      }

      if ((bold || italic || underline) && textContent.length > 0) {
        ranges.push({
          start: startPos,
          end: endPos,
          bold,
          italic,
          underline,
        });
      }

      currentPosition = endPos;
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      for (let i = 0; i < node.childNodes.length; i++) {
        traverseNode(node.childNodes[i]);
      }
    }
  };

  traverseNode(tempDiv);

  return ranges;
};

/**
 * Apply detailed formatting to Tiptap editor without triggering onUpdate
 */
export const applyDetailedFormatting = (
  editor: any,
  ranges: DetailedFormatRange[]
) => {
  if (!editor || !ranges || ranges.length === 0) return;

  try {
    const originalOnUpdate = editor.options.onUpdate;
    editor.options.onUpdate = null;

    const currentText = editor.getText();
    ranges.forEach((range) => {
      if (range.start >= range.end || range.end > currentText.length) {
        return;
      }

      const fromPos = range.start + 1;
      const toPos = range.end + 1;

      editor.chain().setTextSelection({ from: fromPos, to: toPos }).run();
      let chain = editor.chain();

      if (range.bold) {
        chain = chain.toggleBold();
      }
      if (range.italic) {
        chain = chain.toggleItalic();
      }
      if (range.underline) {
        chain = chain.toggleUnderline();
      }

      chain.run();
    });

    editor.commands.setTextSelection(editor.state.doc.content.size);

    setTimeout(() => {
      editor.options.onUpdate = originalOnUpdate;
    }, 50);
  } catch {}
};

/**
 * Merge overlapping or adjacent formatting ranges
 */
export const mergeFormattingRanges = (
  ranges: DetailedFormatRange[]
): DetailedFormatRange[] => {
  if (ranges.length <= 1) return ranges;

  const sortedRanges = [...ranges].sort((a, b) => a.start - b.start);
  const merged: DetailedFormatRange[] = [];

  for (const current of sortedRanges) {
    const last = merged[merged.length - 1];

    if (!last) {
      merged.push(current);
      continue;
    }

    if (
      current.start <= last.end &&
      current.bold === last.bold &&
      current.italic === last.italic &&
      current.underline === last.underline
    ) {
      last.end = Math.max(last.end, current.end);
    } else {
      merged.push(current);
    }
  }

  return merged;
};

/**
 * Convert detailed formatting to HTML for display purposes
 */
export const applyFormattingToText = (
  text: string,
  ranges: DetailedFormatRange[]
): string => {
  if (!text || !ranges || ranges.length === 0) return text;

  const sortedRanges = [...ranges].sort((a, b) => b.start - a.start);

  let result = text;

  sortedRanges.forEach((range) => {
    if (range.start >= range.end || range.end > text.length) return;

    const beforeText = result.substring(0, range.start);
    const targetText = result.substring(range.start, range.end);
    const afterText = result.substring(range.end);

    let formattedText = targetText;

    if (range.underline) formattedText = `<u>${formattedText}</u>`;
    if (range.italic) formattedText = `<em>${formattedText}</em>`;
    if (range.bold) formattedText = `<strong>${formattedText}</strong>`;

    result = beforeText + formattedText + afterText;
  });

  return result;
};

/**
 * Clean up detailed formatting data by removing empty or invalid ranges
 */
export const cleanDetailedFormatting = (
  detailedFormatting: DetailedFormattingData
): DetailedFormattingData => {
  const cleaned: DetailedFormattingData = {};

  Object.entries(detailedFormatting).forEach(([field, ranges]) => {
    const validRanges = ranges.filter(
      (range) =>
        range.start < range.end &&
        range.start >= 0 &&
        (range.bold || range.italic || range.underline)
    );

    if (validRanges.length > 0) {
      cleaned[field] = mergeFormattingRanges(validRanges);
    }
  });

  return cleaned;
};
