import React from 'react';
import RootNavigator from './src/components/stack/RootNavigator';
import './global.css';
import ToastManager from 'toastify-react-native';

import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { AppProvider } from '@/providers/AppProvider';
export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <AppProvider>
        <RootNavigator />
        <ToastManager />
      </AppProvider>
    </GestureHandlerRootView>
  );
}
