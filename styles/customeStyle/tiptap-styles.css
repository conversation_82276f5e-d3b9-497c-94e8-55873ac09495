/* Tiptap Editor Styles */
.tiptap-editor {
  /* Ensure text styles are inherited from parent */
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

.tiptap-editor [style*="font-size"] {
  /* Force font size to be inherited */
  font-size: inherit !important;
}

.tiptap-editor [style*="line-height"] {
  /* Force line height to be inherited */
  line-height: inherit !important;
}

.tiptap-editor [style*="font-family"] {
  /* Force font family to be inherited */
  font-family: inherit !important;
}

/* Font size classes */
.tiptap-font-small {
  font-size: 14px !important;
}

.tiptap-font-medium {
  font-size: 16px !important;
}

.tiptap-font-large {
  font-size: 18px !important;
}

/* Line height classes */
.tiptap-line-height-12 {
  line-height: 1.2 !important;
}

.tiptap-line-height-15 {
  line-height: 1.5 !important;
}

.tiptap-line-height-18 {
  line-height: 1.8 !important;
}

/* Ensure Tiptap content inherits styles properly */
.tiptap-editor .ProseMirror {
  outline: none;
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

.tiptap-editor .ProseMirror p {
  margin: 0.5em 0;
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

.tiptap-editor .ProseMirror p:first-child {
  margin-top: 0;
}

.tiptap-editor .ProseMirror p:last-child {
  margin-bottom: 0;
}

/* Override any inline styles that might interfere, but preserve formatting marks */
.tiptap-editor .ProseMirror *:not(strong):not(em):not(u) {
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

/* Allow formatting marks to work properly */
.tiptap-editor .ProseMirror strong {
  font-weight: bold !important;
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

.tiptap-editor .ProseMirror em {
  font-style: italic !important;
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

.tiptap-editor .ProseMirror u {
  text-decoration: underline !important;
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

/* Override Tailwind CSS font-size classes */
.tiptap-editor.text-xs,
.tiptap-editor.text-sm,
.tiptap-editor.text-base,
.tiptap-editor.text-lg,
.tiptap-editor.text-xl,
.tiptap-editor .text-xs,
.tiptap-editor .text-sm,
.tiptap-editor .text-base,
.tiptap-editor .text-lg,
.tiptap-editor .text-xl {
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

/* Override any text color classes that might have font-size */
.tiptap-editor .text-gray-600,
.tiptap-editor .text-blue-600,
.tiptap-editor .text-orange-500,
.tiptap-editor .text-cyan-600,
.tiptap-editor .text-gray-500,
.tiptap-editor .text-gray-700,
.tiptap-editor .text-gray-800,
.tiptap-editor .text-gray-900 {
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

/* Override leading classes */
.tiptap-editor .leading-relaxed,
.tiptap-editor .leading-loose,
.tiptap-editor .leading-tight,
.tiptap-editor .leading-normal {
  line-height: inherit !important;
}

/* Override any specific className combinations that might be used, but preserve inline formatting */
.tiptap-editor .text-xs.flex-1,
.tiptap-editor .text-sm.flex-1,
.tiptap-editor .text-justify {
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}

/* Only override class-based font weights, not inline formatting */
.tiptap-editor .font-medium:not(strong),
.tiptap-editor .font-semibold:not(strong),
.tiptap-editor .font-bold:not(strong) {
  font-weight: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  font-family: inherit !important;
}
