@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html, body {
    overflow-x: hidden;
    max-width: 100vw;
    width: 100%;
  }

  * {
    box-sizing: border-box;
  }

  /* Prevent any element from causing horizontal overflow */
  body > * {
    max-width: 100vw;
  }

  /* Hide horizontal scrollbar completely */
  ::-webkit-scrollbar:horizontal {
    display: none;
  }

  /* For Firefox */
  html {
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-in-left {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slide-in-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out;
  }

  .animate-slide-in-left {
    animation: slide-in-left 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scale-in 0.5s ease-out forwards;
    opacity: 0;
  }

  .gradient-text {
    background: linear-gradient(-45deg, #6366f1, #8b5cf6, #06b6d4, #10b981);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease infinite;
  }

  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }
}

@layer components {
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  .fade-in-delay {
    animation: fadeIn 1s ease-in-out 5s forwards;
  }
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .react-flow__edge-path {
    stroke: #E5E7EB !important;
    stroke-width: 2px !important;
  }
  
  .react-flow__edge.selected .react-flow__edge-path {
    stroke: #9CA3AF !important;
    stroke-width: 3px !important;
  }
  
  .react-flow__arrowhead {
    fill: #9CA3AF !important;
    stroke: #9CA3AF !important;
  }
  
  .react-flow__node:hover {
    transform: translateY(-1px);
  }
  
  .react-flow__controls {
    background: white;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .react-flow__controls button {
    background: white;
    border-bottom: 1px solid #E5E7EB;
    color: #6B7280;
  }
  
  .react-flow__controls button:hover {
    background: #F9FAFB;
    color: #374151;
  }
  
  .react-flow__controls button:last-child {
    border-bottom: none;
  }
}

@layer components {
  .react-flow__edge-path {
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: stroke-width 0.2s ease, stroke 0.2s ease;
  }
  
  .react-flow__edge.react-flow__edge-smoothstep .react-flow__edge-path {
    filter: drop-shadow(0 1px 3px rgba(99, 102, 241, 0.1));
  }
  
  .react-flow__edge:hover .react-flow__edge-path {
    stroke-width: 3px !important;
    stroke: #4F46E5 !important;
  }
  
  .react-flow__edge.animated .react-flow__edge-path {
    stroke-dasharray: 5 5;
    animation: edgeAnimation 2s linear infinite;
  }
  
  @keyframes edgeAnimation {
    to {
      stroke-dashoffset: -10;
    }
  }
  
  .react-flow__arrowhead {
    fill: #6366F1;
    stroke: #6366F1;
    transition: fill 0.2s ease;
  }
  
  .react-flow__edge:hover .react-flow__arrowhead {
    fill: #4F46E5;
    stroke: #4F46E5;
  }
  
  .react-flow__pane {
    cursor: grab;
  }
  
  .react-flow__pane:active {
    cursor: grabbing;
  }
  .react-flow__node {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  
  .react-flow__node:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  
  .react-flow__node.selected {
    box-shadow: none;
  }
  
  .react-flow__controls {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .react-flow__controls button {
    background: transparent;
    border: none;
    color: #6B7280;
    transition: color 0.2s ease, background-color 0.2s ease;
  }
  
  .react-flow__controls button:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366F1;
  }
}

@layer components {
  .react-flow__edge-path {
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: stroke-width 0.2s ease-in-out;
  }
  
  .react-flow__edge-path:hover {
    stroke-width: 3px !important;
  }
  
  .react-flow__edge.selected .react-flow__edge-path {
    stroke: #059669 !important;
    stroke-width: 3px !important;
  }
  
  .react-flow__arrowhead {
    fill: #10b981;
    stroke: #10b981;
  }
  
  .react-flow__pane {
    cursor: grab;
  }
  
  .react-flow__pane:active {
    cursor: grabbing;
  }

}

.react-flow__minimap {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.react-flow__minimap-mask {
  fill: rgba(59, 130, 246, 0.1) !important;
  stroke: #3b82f6 !important;
  stroke-width: 2 !important;
  stroke-dasharray: 5, 5 !important;
}

.react-flow__minimap-node {
  transition: all 0.2s ease !important;
}

.react-flow__minimap-node:hover {
  opacity: 0.8 !important;
  transform: scale(1.1) !important;
}

.react-flow__node.selected + .react-flow__minimap-node {
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5) !important;
}

.react-flow__node.selected,
.react-flow__node.selected:hover,
.react-flow__node.selected:focus {
  box-shadow: none !important;
  outline: none !important;
  border: none !important;
}

.react-flow__node:focus {
  outline: none !important;
}

.react-flow__node.selected .roadmap-node-selected {
  box-shadow: 
    0 0 0 2px #3B82F6,
    0 0 20px rgba(59, 130, 246, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border-color: #3B82F6 !important;
  transform: scale(1.02);
}

@keyframes node-glow {
  0% {
    box-shadow: 
      0 0 0 2px #3B82F6,
      0 0 15px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 
      0 0 0 2px #3B82F6,
      0 0 25px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow: 
      0 0 0 2px #3B82F6,
      0 0 15px rgba(59, 130, 246, 0.3);
  }
}

.roadmap-node-glow {
  animation: node-glow 2s ease-in-out infinite;
}
