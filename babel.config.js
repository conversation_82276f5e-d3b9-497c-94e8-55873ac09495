module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      ['babel-preset-expo', { jsxImportSource: 'nativewind' }],
      'nativewind/babel',
    ],
    plugins: [
      [
        'module-resolver',
        {
          alias: {
            'react-native-webrtc':
              './node_modules/@daily-co/react-native-webrtc',
          },
        },
      ],
      'react-native-reanimated/plugin',
    ],
  };
};
