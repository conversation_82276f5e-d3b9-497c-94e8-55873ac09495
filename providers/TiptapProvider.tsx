"use client";
import React, { createContext, useContext, useRef, useState } from "react";
import { Editor } from "@tiptap/react";

interface TiptapContextType {
  activeEditor: Editor | null;
  setActiveEditor: (editor: Editor | null) => void;
  activeField: string | null;
  setActiveField: (field: string | null) => void;
  editorRefs: React.MutableRefObject<Record<string, Editor | null>>;
  addSectionAfter: (fieldName: string) => void;
  onAddSection?: (afterField: string) => void;
}

const TiptapContext = createContext<TiptapContextType | null>(null);

export const TiptapProvider: React.FC<{
  children: React.ReactNode;
  onAddSection?: (afterField: string) => void;
}> = ({ children, onAddSection }) => {
  const [activeEditor, setActiveEditor] = useState<Editor | null>(null);
  const [activeField, setActiveField] = useState<string | null>(null);
  const editorRefs = useRef<Record<string, Editor | null>>({});

  const addSectionAfter = (fieldName: string) => {
    // Call the parent component's callback to add a new section
    if (onAddSection) {
      onAddSection(fieldName);
    } else {
      console.log(`Adding section after: ${fieldName}`);
    }
  };

  return (
    <TiptapContext.Provider
      value={{
        activeEditor,
        setActiveEditor,
        activeField,
        setActiveField,
        editorRefs,
        addSectionAfter,
      }}
    >
      {children}
    </TiptapContext.Provider>
  );
};

export const useTiptap = () => {
  const context = useContext(TiptapContext);
  if (!context) {
    throw new Error("useTiptap must be used within a TiptapProvider");
  }
  return context;
};
