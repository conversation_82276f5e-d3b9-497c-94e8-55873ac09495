{"name": "ai-interviewer", "main": "index.ts", "version": "1.0.0", "private": true, "scripts": {"dev": "expo start", "build:web": "expo export --platform web", "dev:tunnel": "expo start --tunnel", "lint": "expo lint", "android": "expo run:android", "ios": "expo run:ios", "postinstall": "patch-package"}, "dependencies": {"@config-plugins/react-native-webrtc": "12.0.0", "@daily-co/config-plugin-rn-daily-js": "0.0.9", "@daily-co/react-native-daily-js": "0.76.0", "@daily-co/react-native-webrtc": "118.0.3-daily.4", "@expo-google-fonts/inter": "0.4.0", "@expo/metro-runtime": "~5.0.4", "@expo/ngrok": "4.1.3", "@expo/vector-icons": "14.1.0", "@hookform/resolvers": "5.1.1", "@lucide/lab": "0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.2", "@react-navigation/bottom-tabs": "7.2.0", "@react-navigation/drawer": "7.3.9", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.3.16", "@reduxjs/toolkit": "2.8.2", "@rn-primitives/accordion": "1.2.0", "@rn-primitives/checkbox": "1.2.0", "@rn-primitives/label": "1.2.0", "@rn-primitives/portal": "1.3.0", "@rn-primitives/slot": "1.2.0", "@rn-primitives/tabs": "1.2.0", "@rn-primitives/types": "1.2.0", "@vapi-ai/react-native": "0.2.4", "axios": "1.10.0", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-react": "7.34.0", "expo": "53.0.16", "expo-audio": "~0.4.7", "expo-av": "15.1.6", "expo-blur": "~14.1.5", "expo-camera": "~16.1.10", "expo-constants": "~17.1.3", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.2", "expo-document-picker": "13.1.5", "expo-font": "13.3.1", "expo-haptics": "~14.1.3", "expo-image-picker": "16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.6", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "i18next": "25.2.1", "lucide-react-native": "0.511.0", "nativewind": "4.1.23", "prettier": "3.5.3", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.57.0", "react-i18next": "15.5.2", "react-native": "0.79.5", "react-native-background-timer": "2.4.1", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "1.5.1", "react-native-url-polyfill": "2.0.0", "react-native-web": "0.20.0", "react-native-webview": "13.13.5", "react-redux": "9.2.0", "redux-persist": "6.0.0", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "toastify-react-native": "7.2.0", "uuid": "11.1.0", "zod": "3.25.67"}, "devDependencies": {"@babel/core": "7.25.2", "@types/react": "~19.0.10", "@types/uuid": "10.0.0", "babel-plugin-module-resolver": "5.0.2", "patch-package": "8.0.0", "react-native-reanimated": "~3.17.4", "tailwindcss": "3.4.17", "typescript": "~5.8.3"}, "overrides": {"@daily-co/config-plugin-rn-daily-js": {"expo": ">=50.0.0"}}, "resolutions": {"glob": "10.3.10", "rimraf": "4.4.1"}}