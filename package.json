{"name": "ai-voice-agent-edution", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@tiptap/extension-bubble-menu": "^2.25.0", "@tiptap/extension-font-family": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@vapi-ai/web": "^2.3.6", "async-mutex": "^0.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.18.1", "html2canvas": "^1.4.1", "imagekit-javascript": "^4.0.1", "jspdf": "^3.0.1", "lucide-react": "^0.513.0", "next": "14.2.28", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.59.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "reactflow": "^11.11.4", "redux-persist": "^6.0.0", "sharp": "^0.34.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.28", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}