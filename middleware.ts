import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;

    // Public routes (no authentication)
    const publicRoutes = ["/", "/login", "/register"];

    // Routes that need authentication
    const protectedRoutes = ["/dashboard"];

    // Lấy token từ cookie hoặc header
    const isPublicRoute = publicRoutes.includes(pathname);
    const isProtectedRoute = protectedRoutes.some((route) =>
        pathname.startsWith(route)
    );

    // If you are on a protected route, you don't need to do anything in the middleware
    // Because the main authentication logic will be handled on the client-side with AuthGuard
    if (isProtectedRoute) {
        return NextResponse.next();
    }

    // Allow access to public routes
    if (isPublicRoute) {
        return NextResponse.next();
    }

    return NextResponse.next();
}

export const config = {
    matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};
